<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
  <dict>
    <key>CFBundleIdentifier</key>
    <string>com.github.Electron.helper</string>
    <key>CFBundleName</key>
    <string><PERSON><PERSON><PERSON> Helper (GPU)</string>
    <key>CFBundlePackageType</key>
    <string>APPL</string>
    <key>DTCompiler</key>
    <string>com.apple.compilers.llvm.clang.1_0</string>
    <key>DTSDKBuild</key>
    <string>24F74</string>
    <key>DTSDKName</key>
    <string>macosx15.5</string>
    <key>DTXcode</key>
    <string>1640</string>
    <key>DTXcodeBuild</key>
    <string>16F6</string>
    <key>LSEnvironment</key>
    <dict>
      <key>MallocNanoZone</key>
      <string>0</string>
    </dict>
    <key>LSUIElement</key>
    <true/>
    <key>NSSupportsAutomaticGraphicsSwitching</key>
    <true/>
    <key>ElectronAsarIntegrity</key>
    <dict>
      <key>Contents/Resources/app/node_modules.asar</key>
      <dict>
        <key>algorithm</key>
        <string>SHA256</string>
        <key>hash</key>
        <string>50a2ddc9d65eb326f127c0b2a3df7182c2b3da69589abb673f25cdf4cb18b901</string>
      </dict>
    </dict>
  </dict>
</plist>