<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>Resources/Info.plist</key>
		<data>
		Ed56t85vWE1N0Y+WCSVge0uhYto=
		</data>
		<key>Resources/MainMenu.nib</key>
		<data>
		8vZwWbxqzsKMQ1nd2hqwWHK4HT8=
		</data>
		<key>Resources/af.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			Bedlhy9ec94suCoO9M99dQOYNcs=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/am.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			bfqDq0FsAr/ofgKj9e5sxigTm3A=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ar.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			NEkQ9Uok79R3CYuDOeWnboAGMdE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/bg.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			mNm91pDh2LxbNrANKld9e1b/4eo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/bn.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			CJas1ninkgdF2dVktDDtC0Gcawg=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ca.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			B5+vLgvTtB7pbM8oFQyKzgw1KVc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/chrome_100_percent.pak</key>
		<data>
		EjNKJIQA6uJ6SKtMB6RWV3+sf6o=
		</data>
		<key>Resources/chrome_200_percent.pak</key>
		<data>
		hKFERZua3g2rJPTs+FYjy5UZovk=
		</data>
		<key>Resources/cs.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			iBiCwwVXbvamhCNwpmu8CZup2rg=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/da.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			W556kXAJkxNafoLIx7CM9MMveLM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/de.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			Vu5eOGk+KDBYcuBDjvlGSBx0ll8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/el.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			sq/2oYOyHN9KhpLKSVZB5Bt2LLo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			eil/FbMtYzsGmKiIeoOJ8aA9UU8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en_GB.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			aKvRNNxphIFjQfWR5/OoqqLGC4I=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/es.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			+0V6r0gTL5B6zGtGNouu9HQMoyQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/es_419.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			zo+bmodKdUhEY+DPLx+zQgkAaVg=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/et.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			r3mgCYTfxRhyj8ZrTVkUpR8WAgo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/fa.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			1ZRyUZDMer4WSI3bEtrC3UVxLE8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/fi.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			3+6WjsAGyFsqLSh2tfILVxmTqx8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/fil.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			G/uOwq+UFWOsk3JmapSAB+sBmBE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/fr.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			Avwm+xeY2FH3tLHlQKT7rajthJg=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/gu.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			VhZLap/k99Mh0Y4hZwi8fj5B7BE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/he.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			mz7Uhu51fOZNe6Ei8KNdPLV8U6A=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/hi.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			/yj0MNEIptwZ74a3HOEnCqjelO0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/hr.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			Hu11Cfl/4UlyFx9xdlSd9y73Ll8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/hu.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			IdZu+0AW4iXofbtNkluNeSQoP7E=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/icudtl.dat</key>
		<data>
		qrmO3aMQ5t3T5LcflbYiXE3vp/w=
		</data>
		<key>Resources/id.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			RA5w3RM2kSy0Jt5vA2nxRg9tVkA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/it.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			ENV1jC8xMaJ/mC+ITbegBxxmdGM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ja.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			5cQYkEEYRCtgnHSpNW8VDdHW/fU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/kn.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			kn3EDilKYDcxpzWuKj/GjrAwBCU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ko.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			bo5vjnlffZyC7HkLQcVO5EmgAKY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/lt.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			hg9k/tVqexprnx3TnfvSLcwu3L4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/lv.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			LK7v0YpZZnfSnzXXsuvzHdVtZ+s=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ml.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			Q5ZpVF/jNTc0QFn/8IWHA+khZSM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/mr.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			H6XOrsNqZ3VTwHytaKmFP+XBQEQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ms.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			JOxV9brHXZ0OXvUBE2JGauAML6w=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/nb.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			NQS6AH3heKnY2gIYa9/U6fN2ZeY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/nl.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			PRHx8hLAA3PXM7zkQRNRTFw1XMo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/pl.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			BN/X07Reb6VH1UYP+JtQ8OV3Cj0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/pt_BR.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			8qxJYGQQT1HiozECRIMFK+ecAuU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/pt_PT.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			QlnI2AUc3cd2NbKZcmzHgwD811k=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/resources.pak</key>
		<data>
		Srfz1L0EuuN2lvwn6YOrQxMiVuc=
		</data>
		<key>Resources/ro.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			v3F5EbyznD/U7XfAW4mllZ3hXfE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ru.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			rqwel9QAVBA8/aEA20c/3uD+2x8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/sk.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			kUfhYbBzr6qZc6NOCNRkjmnnzo0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/sl.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			tLhOw76DuH7bUhVvm9nIdFL5rWQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/sr.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			w/NI+gTFDdGxcvw7kzhj6uMPtac=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/sv.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			ANeNnIOY6msdlrKFayKfKHHZq9A=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/sw.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			MAbF5PLrAcfIkvoJxXwrK1MDHqg=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ta.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			5tKNxJ5RJrmZ4Xk/cu2zIpOTdvo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/te.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			d0SQg/zYdx4vRLKMqWxXaCJRQXY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/th.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			AjDlbw2Cx77i53BziK4CW17/X8M=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/tr.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			sa88ei86GRdq31cZhw0icgnQR4A=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/uk.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			HhAchcT6CO7Txk+KfwoIXqm/bO0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ur.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			qSRrXOag4qY34EDp7moHgvZH3AM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/v8_context_snapshot.arm64.bin</key>
		<data>
		na3TI/vjdyjVFdX8QFEb4lgd1fE=
		</data>
		<key>Resources/v8_context_snapshot.x86_64.bin</key>
		<data>
		KDX0SWrCZCgT5HFBHv1IXAN/WTk=
		</data>
		<key>Resources/vi.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			1OiQNQbpE7ckGmQ7Y8Qzrchsxc0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/zh_CN.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			wMBS7mc2z5HnB+sYqxtxPFnCNdI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/zh_TW.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			HQtJApAVq5aVjKnuKpD4W172rjI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
	</dict>
	<key>files2</key>
	<dict>
		<key>Helpers/chrome_crashpad_handler</key>
		<dict>
			<key>cdhash</key>
			<data>
			oojUsE0lMtiUPW56enAyXvIU+YI=
			</data>
			<key>requirement</key>
			<string>identifier "chrome_crashpad_handler" and anchor apple generic and certificate 1[field.1.2.840.113635.100.6.2.6] /* exists */ and certificate leaf[field.1.2.840.113635.100.6.1.13] /* exists */ and certificate leaf[subject.OU] = UBF8T346G9</string>
		</dict>
		<key>Libraries/libEGL.dylib</key>
		<dict>
			<key>hash2</key>
			<data>
			H5EsDOKNuIfozjzNEWo+53uw5Cavx+79DlWGtyywvzk=
			</data>
		</dict>
		<key>Libraries/libGLESv2.dylib</key>
		<dict>
			<key>hash2</key>
			<data>
			LownO3hiBZXkYKV+StdUC94zIvWp93h9rb/WBQF4Ydw=
			</data>
		</dict>
		<key>Libraries/libffmpeg.dylib</key>
		<dict>
			<key>hash2</key>
			<data>
			ZTNWh3mcJxOSupz+BTxFeuGGQzXcr4hPISR5ZTi040k=
			</data>
		</dict>
		<key>Libraries/libvk_swiftshader.dylib</key>
		<dict>
			<key>hash2</key>
			<data>
			+hINIn1QbjotzKVPGwkXOouRMn7LP0pnRuvbuSZMT7I=
			</data>
		</dict>
		<key>Libraries/vk_swiftshader_icd.json</key>
		<dict>
			<key>hash2</key>
			<data>
			1xfZFeMefCeUi4CzarNOLYl4iBFMXH0K+DX5PrU+WPU=
			</data>
		</dict>
		<key>Resources/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			vblE7dVhwVWUYEu07Kd8Dbp8NTCuhD4gmaOuUpS9cs0=
			</data>
		</dict>
		<key>Resources/MainMenu.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			SyFo3i1tmgmyD6qiMTnV8l9zsllWcd4lhy0ZG2k52Bk=
			</data>
		</dict>
		<key>Resources/af.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			r7ef0QfAxhzYy+CcBbII2turskZGkAPggCvZe7ME8p4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/am.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			0pT6eXhL8RF/oAeJCGqIPnyDfeLc3W16hiQ/IGyi2Qs=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ar.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			Eigcjqiy52FSNUaOK7KxGfck7bS8GV0UlbSL57f2zkk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/bg.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			n4Ub0TGQ/Ur05S7GA1PoWXKCcxClEE+TnVj0XdUUIUY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/bn.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			L+4a4Up8dap5dqANVDICUsG+nYrpJuVC2xV9nZa0rkA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ca.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			qXVgFV6DsoMPWSr0IadzEVQRJFT0H/8FhMluyHrF4V4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/chrome_100_percent.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			oKXJMw9TmYUBTNQlTw/AyQ2WFs8jQqlXGyKeMv1c+ow=
			</data>
		</dict>
		<key>Resources/chrome_200_percent.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			dd+dJ8924mbpMFurtsflyB319G5KVFWyzkPzpo5ypD4=
			</data>
		</dict>
		<key>Resources/cs.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			Z1qDbWBdI0GEEkQZvMt4cU5/rEjRY6yjFjagQDuiRlg=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/da.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			vkGbmSkAplCKVOEQ6P/rD4yX4JLcRSIa9Hl9GBoBZBc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/de.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			CfQgGxE9T+YbCFbjCunBb4udyR2oJNdp69LZS1iIpQo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/el.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			8Uy0I29kTdQO4N4+VQ0B9glt+PrXQgATekJ9K4oSF2w=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			r0QPZP5RxPTNyosQsFDbTBr7ywpYepVOQtr5YvXCS7g=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en_GB.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			ZDux19glhG1zx8DIbR9XWkh4zh3srIomiP45ouuqaVA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/es.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			nipTGIM0/mYwNrJYb55JklWLwgX+niwPF91lTUxeJok=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/es_419.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			DOhBnr50FhmLUgEBDLvRfs2nCgWZussYLZoYD5qGoaw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/et.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			1V4+aCrftCWAXYqPam91c7SdUjnBmFlHRd+FvXZ8Unk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/fa.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			GKw55bM5qdK7icm8qMIzzVN2fdmgDPeiF/bgtMtwtBQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/fi.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			WaaTH9hd9nufSIkpUYyE2O9Op9WgbN/4pfkpGrub1RI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/fil.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			AuwbPMV40QG6Q2UKMsbXx1ThDGoNKyYcAHgJTUrPjOk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/fr.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			LHM0zjXqq9vAwPQkDzK7QLVW2sbTnp+idhMTbXa+258=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/gu.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			jkRpV3Z6NZmJhoZAxShewl1zpI+JcfaF4vaijgL9B7g=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/he.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			EtODmnOJ5U3ZNJbiKe5D2GFyOROLZlgFzm1sW/RSLw4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/hi.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			PNmibG0K4pUbltRKimJ9RsuxmIoBrmsoouzrkTkGYPk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/hr.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			0+CLe9Kv5wEmtIYto3L5jAieDo8ZwAydJSzPFCWjVpA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/hu.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			fFliQMcpEJXgMb3jO9YiYQh37erR+pDtSDwMOdqkjQU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/icudtl.dat</key>
		<dict>
			<key>hash2</key>
			<data>
			UHDemfvYpIN4VDUR+3oHL4W/NvlLT04dQg1UvhQ11vE=
			</data>
		</dict>
		<key>Resources/id.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			LB43TfzfrJZT4cved30maW3e4D25VtpBMC56dtDCc9Y=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/it.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			B6Xr8OUZzxFmCzNDobemq7USw9f3DCi1D2K1266cfuw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ja.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			MbzEwv7TN2QI68U6wgpsfBbnyudiAsL/BUcPPJ+F9oQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/kn.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			U3DbU8IluZAjc5mMrwhRDS0KHt0e48X2O0cl6a+P69s=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ko.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			2145UkAoC6ghn/88gouceHoEpSiNs+T9UzW6IM3H0i4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/lt.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			bOiazt1sl3s73ZeX0Ml7AxNczO8+fSSHVFPu7Ubnd8Y=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/lv.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			sQs07kuLDqJl2oTu9g6j7HgbLLO8e+11teVyKhbNPqA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ml.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			b79LGzTKIZVWIfyRuY7y6+e2Z2IAwCbSBUM+7acovI4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/mr.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			m/gKqroQ/bJeM+f1fwIgmYGMev0E04jfi2LH3jPNbd4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ms.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			dC8QT9FU0g7dkRHir4bPzHqFod5xLCxe0A4vtj/QMFc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/nb.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			KZKtCqFoou17S4FoOMO9839VFhKKjFqTy3DWKBBW0sY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/nl.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			VDanHwgoiTBa53aXelpeplSTR49wYxxrVmQNN7Hi4Og=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/pl.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			XlIMQabCaJCrZqcICRMZFOV2exmvJh4TqYFA02aRNVA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/pt_BR.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			RzEasV57+1ejT9omSGb1JgvMpw7shgG0zeniXqCkMjg=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/pt_PT.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			/lG0yJ2EYrd9fqYP7mF1UDSkVJeIwllD/oSqSrTbhfM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/resources.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			UVX+80s3WoDTk2EChwuJKV2yFlgV1Fk605k8d+inntY=
			</data>
		</dict>
		<key>Resources/ro.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			4vlviIN7R7tFpwbmwEF1ysYHDZgtLWvGPGSes4ciVZs=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ru.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			T3YjeXJvdbU78nmu+qPVU+jI2/j+Vw0fj+3raFZZ0cc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/sk.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			CIxjieUx35/ztrJeKQVmBFQ8E/RmILIMSu9Q63cxOqY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/sl.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			Ik3tcswy7e3/bZp60nmdZlf75JOahA+vt0KJMdICgTg=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/sr.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			8bfkk7YD1qxM0fAVNbSzMdZBds9kECiEmd/+4Nm4izI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/sv.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			qPrupezRVDUo6Qm3m68eg0pj57pO2Ga4tlfPcc4U9C4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/sw.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			fvxR/UZ5IBTlVivlGCU2D5JFYVMh7+aujvqM6rE7hEw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ta.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			0FLloAUBUBknRvQVILafqANAjUvMw19Ya5jRzy1VuNI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/te.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			RmQUSTd8D1ig9VpV+tvSrwHhnubvnNRkf3venNinE4o=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/th.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			aDE6cjg3iwNgfTWCk34KgFbXlAPKQVn+aGlmuc2gBRU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/tr.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			25ZwMrReg9iiaiG+1mWYx/vnqMea3jpHIYqqoTwVbDo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/uk.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			gWrXf/RNSmmAWmoIcpRZB+9PE83ipl1kIluhERPCwfk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ur.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			t9BQ+iUsHsnRnksnLwpvSFHTLuDYdLAZUMi7r+1AEno=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/v8_context_snapshot.arm64.bin</key>
		<dict>
			<key>hash2</key>
			<data>
			gNW9r0USWgj2/nnz/cCEkO8qKopkue1pn/lbwyx8dfE=
			</data>
		</dict>
		<key>Resources/v8_context_snapshot.x86_64.bin</key>
		<dict>
			<key>hash2</key>
			<data>
			btPsT2cd6uS9KbpqhkFzFfhUgsBkaThjrIhyYOWV1kQ=
			</data>
		</dict>
		<key>Resources/vi.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			Ft5KS1RGqEFF0VguqVybRO/WRl+vTklmpjkS6rBg+ns=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/zh_CN.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			5CRy+ZAwdnPQNFKgIgAddLhdgL0W6iyHIWaQUITNDF8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/zh_TW.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			LLxMYxANCrsBNjYdDlju4RW7ukQIgXqzXR2emSHryjg=
			</data>
			<key>optional</key>
			<true/>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^Resources/</key>
		<true/>
		<key>^Resources/.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^Resources/.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Resources/Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^(Frameworks|SharedFrameworks|PlugIns|Plug-ins|XPCServices|Helpers|MacOS|Library/(Automator|Spotlight|LoginItems))/</key>
		<dict>
			<key>nested</key>
			<true/>
			<key>weight</key>
			<real>10</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^Resources/</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^Resources/.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^Resources/.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Resources/Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^[^/]+$</key>
		<dict>
			<key>nested</key>
			<true/>
			<key>weight</key>
			<real>10</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
