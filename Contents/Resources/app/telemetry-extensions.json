{"events": {"typescript-language-features/typeScriptExtension.trace": {"owner": "dirkb", "point": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "The trace point.", "endPoint": "none"}, "traceid": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "The traceId is used to correlate the request with other trace points.", "endPoint": "none"}, "data": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "Additional data", "endPoint": "none"}, "version": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}}, "typescript-language-features/tsserver.spawned": {"owner": "mjbvz", "localtypescriptversion": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}, "typescriptversionsource": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}, "version": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}, "hasglobalplugins": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}, "globalpluginnamehashes": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}}, "typescript-language-features/tsserver.error": {"owner": "mjbvz", "version": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}, "hasglobalplugins": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}, "globalpluginnamehashes": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}}, "typescript-language-features/tsserver.exitWithCode": {"owner": "mjbvz", "code": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "signal": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "version": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}, "hasglobalplugins": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}, "globalpluginnamehashes": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}}, "typescript-language-features/serviceExited": {"owner": "mjbvz", "version": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}}, "typescript-language-features/fatalError": {"owner": "mjbvz", "command": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}, "version": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}, "serverid": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "sanitizedstack": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "badclient": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none"}}, "typescript-language-features/typingsInstalled": {"owner": "mjbvz", "installedpackages": {"classification": "PublicNonPersonalData", "purpose": "FeatureInsight", "endPoint": "none"}, "installsuccess": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "typingsinstallerversion": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "version": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}}, "typescript-language-features/refactor.execute": {"owner": "mjbvz", "action": {"classification": "PublicNonPersonalData", "purpose": "FeatureInsight", "endPoint": "none"}, "trigger": {"classification": "PublicNonPersonalData", "purpose": "FeatureInsight", "endPoint": "none"}, "version": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}}, "typescript-language-features/organizeImports.execute": {"owner": "mjbvz", "version": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}}, "typescript-language-features/quickFix.execute": {"owner": "mjbvz", "fixname": {"classification": "PublicNonPersonalData", "purpose": "FeatureInsight", "endPoint": "none"}, "version": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}}, "typescript-language-features/quickFixAll.execute": {"owner": "mjbvz", "fixname": {"classification": "PublicNonPersonalData", "purpose": "FeatureInsight", "endPoint": "none"}, "version": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}}, "typescript-language-features/diagnostics.performance": {"owner": "mjbvz", "syntaxdiagduration": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "semanticdiagduration": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "suggestiondiagduration": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "regionsemanticdiagduration": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "filelinecount": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "version": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}}, "typescript-language-features/typescript.diagnostics": {"owner": "aiday-mar", "diagnosticcodes": {"classification": "PublicNonPersonalData", "purpose": "FeatureInsight", "endPoint": "none"}, "version": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}}, "typescript-language-features/inlayHints.provide": {"owner": "mjbvz", "version": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}}, "typescript-language-features/completions.accept": {"owner": "mjbvz", "ispackagejsonimport": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}, "isimportstatementcompletion": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}, "version": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}}, "typescript-language-features/completions.execute": {"owner": "mjbvz", "duration": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}, "type": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}, "count": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}, "flags": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}, "updategraphdurationms": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}, "createautoimportproviderprogramdurationms": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}, "includespackagejsonimport": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}, "includesimportstatementcompletion": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}, "version": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}}, "typescript-language-features/aiQuickfix.execute": {"owner": "mjbvz", "action": {"classification": "PublicNonPersonalData", "purpose": "FeatureInsight", "endPoint": "none"}, "version": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}}, "typescript-language-features/aiRefactor.execute": {"owner": "mjbvz", "action": {"classification": "PublicNonPersonalData", "purpose": "FeatureInsight", "endPoint": "none"}, "version": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}}, "typescript-language-features/languageServiceErrorResponse": {"owner": "mjbvz", "version": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}, "command": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}, "serverid": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "sanitizedstack": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "badclient": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none"}}, "typescript-language-features/js.hintProjectExcludes": {"owner": "mjbvz", "version": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}}, "git/git.repositoryInitialScan": {"owner": "lszomoru", "autorepositorydetection": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "Setting that controls the initial repository scan", "endPoint": "none"}, "repositorycount": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "Number of repositories opened during initial repository scan", "endPoint": "none", "isMeasurement": true}}, "git/git.missing": {"owner": "lszomoru"}, "git/statusLimit": {"owner": "lszomoru", "ignoresubmodules": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "Setting indicating whether submodules are ignored", "endPoint": "none"}, "limit": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "Setting indicating the limit of status entries", "endPoint": "none", "isMeasurement": true}, "statuslength": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "Total number of status entries", "endPoint": "none", "isMeasurement": true}, "totaltime": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "Total number of ms the operation took", "endPoint": "none", "isMeasurement": true}}, "git/statusSlow": {"owner": "digitarald", "comment": "Reports when git status is slower than 5s", "expiration": "1.73", "ignoresubmodules": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "Setting indicating whether submodules are ignored", "endPoint": "none"}, "didhitlimit": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "Total number of status entries", "endPoint": "none"}, "didwarnaboutlimit": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "True when the user was warned about slow git status", "endPoint": "none"}, "statuslength": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "Total number of status entries", "endPoint": "none", "isMeasurement": true}, "totaltime": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "Total number of ms the operation took", "endPoint": "none", "isMeasurement": true}}, "git/clone": {"owner": "lszomoru", "outcome": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "The outcome of the git operation", "endPoint": "none"}, "openfolder": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "comment": "Indicates whether the folder is opened following the clone operation", "endPoint": "none", "isMeasurement": true}}, "git/git.command": {"owner": "lszomoru", "command": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "The command id of the command being executed", "endPoint": "none"}}, "json-language-features/json.schema": {"owner": "a<PERSON><PERSON><PERSON>", "comment": "Measure the use of the Azure resource manager schemas", "schemaurl": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "comment": "The azure schema URL that was requested.", "endPoint": "none"}}, "ms-vscode.node/ClientRequest/initialize": {"successful": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "exceptiontype": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "!exceptionmessage": {"classification": "CallstackOrException", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "exceptionname": {"classification": "CallstackOrException", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "!exceptionstack": {"classification": "CallstackOrException", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "!exceptionid": {"classification": "CallstackOrException", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "starttime": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "timetakeninmilliseconds": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "versions.debugadaptercore": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}}, "ms-vscode.node/ClientRequest/configurationDone": {"successful": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "exceptiontype": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "!exceptionmessage": {"classification": "CallstackOrException", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "exceptionname": {"classification": "CallstackOrException", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "!exceptionstack": {"classification": "CallstackOrException", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "!exceptionid": {"classification": "CallstackOrException", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "starttime": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "timetakeninmilliseconds": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "versions.debugadaptercore": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}}, "ms-vscode.node/ClientRequest/launch": {"successful": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "exceptiontype": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "!exceptionmessage": {"classification": "CallstackOrException", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "exceptionname": {"classification": "CallstackOrException", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "!exceptionstack": {"classification": "CallstackOrException", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "!exceptionid": {"classification": "CallstackOrException", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "starttime": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "timetakeninmilliseconds": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "versions.debugadaptercore": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}}, "ms-vscode.node/debugStarted": {"request": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}, "args": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}, "versions.debugadaptercore": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}}, "ms-vscode.node/ClientRequest/attach": {"successful": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "exceptiontype": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "!exceptionmessage": {"classification": "CallstackOrException", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "exceptionname": {"classification": "CallstackOrException", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "!exceptionstack": {"classification": "CallstackOrException", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "!exceptionid": {"classification": "CallstackOrException", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "starttime": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "timetakeninmilliseconds": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "versions.debugadaptercore": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}}, "ms-vscode.node/debugStopped": {"reason": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}, "versions.debugadaptercore": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}}, "ms-vscode.node/target/notification/onPaused": {"successful": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "exceptiontype": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "!exceptionmessage": {"classification": "CallstackOrException", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "exceptionname": {"classification": "CallstackOrException", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "!exceptionstack": {"classification": "CallstackOrException", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "!exceptionid": {"classification": "CallstackOrException", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "starttime": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "timetakeninmilliseconds": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "versions.debugadaptercore": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}}, "ms-vscode.node/target/notification/onScriptParsed": {"successful": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "exceptiontype": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "!exceptionmessage": {"classification": "CallstackOrException", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "exceptionname": {"classification": "CallstackOrException", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "!exceptionstack": {"classification": "CallstackOrException", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "!exceptionid": {"classification": "CallstackOrException", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "starttime": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "timetakeninmilliseconds": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "versions.debugadaptercore": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}}, "ms-vscode.node/ClientRequest/exceptionInfo": {"successful": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "exceptiontype": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "!exceptionmessage": {"classification": "CallstackOrException", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "exceptionname": {"classification": "CallstackOrException", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "!exceptionstack": {"classification": "CallstackOrException", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "!exceptionid": {"classification": "CallstackOrException", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "starttime": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "timetakeninmilliseconds": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "versions.debugadaptercore": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}}, "ms-vscode.node/ClientRequest/toggleSmartStep": {"successful": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "exceptiontype": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "!exceptionmessage": {"classification": "CallstackOrException", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "exceptionname": {"classification": "CallstackOrException", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "!exceptionstack": {"classification": "CallstackOrException", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "!exceptionid": {"classification": "CallstackOrException", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "starttime": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "timetakeninmilliseconds": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "versions.debugadaptercore": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}}, "ms-vscode.node/ClientRequest/toggleSkipFileStatus": {"successful": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "exceptiontype": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "!exceptionmessage": {"classification": "CallstackOrException", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "exceptionname": {"classification": "CallstackOrException", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "!exceptionstack": {"classification": "CallstackOrException", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "!exceptionid": {"classification": "CallstackOrException", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "starttime": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "timetakeninmilliseconds": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "versions.debugadaptercore": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}}, "ms-vscode.node/ClientRequest/loadedSources": {"successful": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "exceptiontype": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "!exceptionmessage": {"classification": "CallstackOrException", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "exceptionname": {"classification": "CallstackOrException", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "!exceptionstack": {"classification": "CallstackOrException", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "!exceptionid": {"classification": "CallstackOrException", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "starttime": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "timetakeninmilliseconds": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "versions.debugadaptercore": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}}, "ms-vscode.node/ClientRequest/disconnect": {"successful": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "exceptiontype": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "!exceptionmessage": {"classification": "CallstackOrException", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "exceptionname": {"classification": "CallstackOrException", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "!exceptionstack": {"classification": "CallstackOrException", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "!exceptionid": {"classification": "CallstackOrException", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "starttime": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "timetakeninmilliseconds": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "versions.debugadaptercore": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}}, "ms-vscode.node/ClientRequest/setBreakpoints": {"successful": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "exceptiontype": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "!exceptionmessage": {"classification": "CallstackOrException", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "exceptionname": {"classification": "CallstackOrException", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "!exceptionstack": {"classification": "CallstackOrException", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "!exceptionid": {"classification": "CallstackOrException", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "starttime": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "timetakeninmilliseconds": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "versions.debugadaptercore": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}}, "ms-vscode.node/setBreakpointsRequest": {"fileext": {"classification": "Customer<PERSON><PERSON>nt", "purpose": "FeatureInsight", "endPoint": "none"}, "versions.debugadaptercore": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}}, "ms-vscode.node/ClientRequest/setExceptionBreakpoints": {"successful": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "exceptiontype": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "!exceptionmessage": {"classification": "CallstackOrException", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "exceptionname": {"classification": "CallstackOrException", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "!exceptionstack": {"classification": "CallstackOrException", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "!exceptionid": {"classification": "CallstackOrException", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "starttime": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "timetakeninmilliseconds": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "versions.debugadaptercore": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}}, "ms-vscode.node/ClientRequest/continue": {"successful": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "exceptiontype": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "!exceptionmessage": {"classification": "CallstackOrException", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "exceptionname": {"classification": "CallstackOrException", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "!exceptionstack": {"classification": "CallstackOrException", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "!exceptionid": {"classification": "CallstackOrException", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "starttime": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "timetakeninmilliseconds": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "versions.debugadaptercore": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}}, "ms-vscode.node/continueRequest": {"versions.debugadaptercore": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}}, "ms-vscode.node/ClientRequest/next": {"successful": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "exceptiontype": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "!exceptionmessage": {"classification": "CallstackOrException", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "exceptionname": {"classification": "CallstackOrException", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "!exceptionstack": {"classification": "CallstackOrException", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "!exceptionid": {"classification": "CallstackOrException", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "starttime": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "timetakeninmilliseconds": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "versions.debugadaptercore": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}}, "ms-vscode.node/nextRequest": {"versions.debugadaptercore": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}}, "ms-vscode.node/ClientRequest/stepIn": {"successful": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "exceptiontype": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "!exceptionmessage": {"classification": "CallstackOrException", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "exceptionname": {"classification": "CallstackOrException", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "!exceptionstack": {"classification": "CallstackOrException", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "!exceptionid": {"classification": "CallstackOrException", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "starttime": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "timetakeninmilliseconds": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "versions.debugadaptercore": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}}, "ms-vscode.node/stepInRequest": {"versions.debugadaptercore": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}}, "ms-vscode.node/ClientRequest/stepOut": {"successful": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "exceptiontype": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "!exceptionmessage": {"classification": "CallstackOrException", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "exceptionname": {"classification": "CallstackOrException", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "!exceptionstack": {"classification": "CallstackOrException", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "!exceptionid": {"classification": "CallstackOrException", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "starttime": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "timetakeninmilliseconds": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "versions.debugadaptercore": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}}, "ms-vscode.node/stepOutRequest": {"versions.debugadaptercore": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}}, "ms-vscode.node/ClientRequest/stepBack": {"successful": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "exceptiontype": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "!exceptionmessage": {"classification": "CallstackOrException", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "exceptionname": {"classification": "CallstackOrException", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "!exceptionstack": {"classification": "CallstackOrException", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "!exceptionid": {"classification": "CallstackOrException", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "starttime": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "timetakeninmilliseconds": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "versions.debugadaptercore": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}}, "ms-vscode.node/ClientRequest/reverseContinue": {"successful": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "exceptiontype": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "!exceptionmessage": {"classification": "CallstackOrException", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "exceptionname": {"classification": "CallstackOrException", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "!exceptionstack": {"classification": "CallstackOrException", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "!exceptionid": {"classification": "CallstackOrException", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "starttime": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "timetakeninmilliseconds": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "versions.debugadaptercore": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}}, "ms-vscode.node/ClientRequest/pause": {"successful": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "exceptiontype": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "!exceptionmessage": {"classification": "CallstackOrException", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "exceptionname": {"classification": "CallstackOrException", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "!exceptionstack": {"classification": "CallstackOrException", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "!exceptionid": {"classification": "CallstackOrException", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "starttime": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "timetakeninmilliseconds": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "versions.debugadaptercore": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}}, "ms-vscode.node/pauseRequest": {"versions.debugadaptercore": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}}, "ms-vscode.node/ClientRequest/stackTrace": {"successful": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "exceptiontype": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "!exceptionmessage": {"classification": "CallstackOrException", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "exceptionname": {"classification": "CallstackOrException", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "!exceptionstack": {"classification": "CallstackOrException", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "!exceptionid": {"classification": "CallstackOrException", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "starttime": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "timetakeninmilliseconds": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "versions.debugadaptercore": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}}, "ms-vscode.node/ClientRequest/scopes": {"successful": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "exceptiontype": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "!exceptionmessage": {"classification": "CallstackOrException", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "exceptionname": {"classification": "CallstackOrException", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "!exceptionstack": {"classification": "CallstackOrException", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "!exceptionid": {"classification": "CallstackOrException", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "starttime": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "timetakeninmilliseconds": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "versions.debugadaptercore": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}}, "ms-vscode.node/ClientRequest/variables": {"successful": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "exceptiontype": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "!exceptionmessage": {"classification": "CallstackOrException", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "exceptionname": {"classification": "CallstackOrException", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "!exceptionstack": {"classification": "CallstackOrException", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "!exceptionid": {"classification": "CallstackOrException", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "starttime": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "timetakeninmilliseconds": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "versions.debugadaptercore": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}}, "ms-vscode.node/ClientRequest/source": {"successful": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "exceptiontype": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "!exceptionmessage": {"classification": "CallstackOrException", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "exceptionname": {"classification": "CallstackOrException", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "!exceptionstack": {"classification": "CallstackOrException", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "!exceptionid": {"classification": "CallstackOrException", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "starttime": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "timetakeninmilliseconds": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "versions.debugadaptercore": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}}, "ms-vscode.node/ClientRequest/threads": {"successful": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "exceptiontype": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "!exceptionmessage": {"classification": "CallstackOrException", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "exceptionname": {"classification": "CallstackOrException", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "!exceptionstack": {"classification": "CallstackOrException", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "!exceptionid": {"classification": "CallstackOrException", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "starttime": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "timetakeninmilliseconds": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "versions.debugadaptercore": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}}, "ms-vscode.node/ClientRequest/evaluate": {"successful": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "exceptiontype": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "!exceptionmessage": {"classification": "CallstackOrException", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "exceptionname": {"classification": "CallstackOrException", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "!exceptionstack": {"classification": "CallstackOrException", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "!exceptionid": {"classification": "CallstackOrException", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "starttime": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "timetakeninmilliseconds": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "versions.debugadaptercore": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}}, "ms-vscode.node/ClientRequest/setVariable": {"successful": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "exceptiontype": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "!exceptionmessage": {"classification": "CallstackOrException", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "exceptionname": {"classification": "CallstackOrException", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "!exceptionstack": {"classification": "CallstackOrException", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "!exceptionid": {"classification": "CallstackOrException", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "starttime": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "timetakeninmilliseconds": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "versions.debugadaptercore": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}}, "ms-vscode.node/ClientRequest/restartFrame": {"successful": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "exceptiontype": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "!exceptionmessage": {"classification": "CallstackOrException", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "exceptionname": {"classification": "CallstackOrException", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "!exceptionstack": {"classification": "CallstackOrException", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "!exceptionid": {"classification": "CallstackOrException", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "starttime": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "timetakeninmilliseconds": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "versions.debugadaptercore": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}}, "ms-vscode.node/ClientRequest/completions": {"successful": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "exceptiontype": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "!exceptionmessage": {"classification": "CallstackOrException", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "exceptionname": {"classification": "CallstackOrException", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "!exceptionstack": {"classification": "CallstackOrException", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "!exceptionid": {"classification": "CallstackOrException", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "starttime": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "timetakeninmilliseconds": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "versions.debugadaptercore": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}}, "ms-vscode.node/error": {"successful": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "exceptiontype": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "!exceptionmessage": {"classification": "CallstackOrException", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "exceptionname": {"classification": "CallstackOrException", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "!exceptionstack": {"classification": "CallstackOrException", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "!exceptionid": {"classification": "CallstackOrException", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "starttime": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "timetakeninmilliseconds": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "versions.debugadaptercore": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}}, "ms-vscode.node/report-start-up-timings": {"requestedcontentwasnotdetectedreason": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}, "steps": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}, "all": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}, "${wildcard}": [{"${prefix}": "request.", "${classification}": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}}, {"${prefix}": "waitingafter", "${classification}": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}}], "attach": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}, "attach.configuredebuggingsession.internal": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}, "attach.configuredebuggingsession.target": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}, "attach.configuredebuggingsession.end": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}, "attach.requestdebuggertargetsinformation": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}, "attach.processdebuggertargetsinformation": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}, "attach.attachtotargetdebuggerwebsocket": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}, "beforefirststep": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}, "afterlaststep": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}, "versions.debugadaptercore": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}}, "ms-vscode.node/error-while-adding-custom-global-property": {"successful": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "exceptiontype": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "!exceptionmessage": {"classification": "CallstackOrException", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "exceptionname": {"classification": "CallstackOrException", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "!exceptionstack": {"classification": "CallstackOrException", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "!exceptionid": {"classification": "CallstackOrException", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "starttime": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none"}, "timetakeninmilliseconds": {"classification": "SystemMetaData", "purpose": "PerformanceAndHealth", "endPoint": "none"}}, "ms-vscode.node/targetCount": {"numtargets": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none", "isMeasurement": true}, "versions.debugadaptercore": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}}, "ms-vscode.node/targetDebugProtocolVersion": {"debugprotocolversion": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}, "versions.debugadaptercore": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}}, "ms-vscode.node/nodeVersion": {"version": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}}, "ms-vscode.node/debugProtocolErrorResponse": {"error": {"classification": "CallstackOrException", "purpose": "PerformanceAndHealth"}}, "ms-vscode.node/optinstatus": {"optin": {"classification": "SystemMetaData", "purpose": "BusinessInsight", "isMeasurement": true}}}, "commonProperties": {"common.os": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}, "common.nodearch": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}, "common.platformversion": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}, "common.extname": {"classification": "PublicNonPersonalData", "purpose": "FeatureInsight", "endPoint": "none"}, "common.extversion": {"classification": "PublicNonPersonalData", "purpose": "FeatureInsight", "endPoint": "none"}, "common.vscodemachineid": {"classification": "EndUserPseudonymizedInformation", "purpose": "FeatureInsight", "endPoint": "MacAddressHash"}, "common.vscodesessionid": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}, "common.vscodecommithash": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}, "common.vscodeversion": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}, "common.uikind": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}, "common.remotename": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}, "common.isnewappinstall": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}, "common.product": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}, "common.telemetryclientversion": {"classification": "SystemMetaData", "purpose": "FeatureInsight", "endPoint": "none"}}}