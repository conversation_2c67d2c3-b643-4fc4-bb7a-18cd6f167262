export const __webpack_id__=555;export const __webpack_ids__=[555];export const __webpack_modules__={6555:(e,r,t)=>{t.d(r,{graphql:()=>d});var a=t(3698),s=t(5407),o=class extends Error{constructor(e,r,t){super("Request failed due to following response errors:\n"+t.errors.map(e=>` - ${e.message}`).join("\n")),this.request=e,this.headers=r,this.response=t,this.errors=t.errors,this.data=t.data,Error.captureStackTrace&&Error.captureStackTrace(this,this.constructor)}name="GraphqlResponseError";errors;data},n=["method","baseUrl","url","headers","request","query","mediaType","operationName"],i=["query","method","url"],c=/\/api\/v3\/?$/;var d=function e(r,t){const a=r.defaults(t);return Object.assign((e,r)=>function(e,r,t){if(t){if("string"==typeof r&&"query"in t)return Promise.reject(new Error('[@octokit/graphql] "query" cannot be used as variable name'));for(const e in t)if(i.includes(e))return Promise.reject(new Error(`[@octokit/graphql] "${e}" cannot be used as variable name`))}const a="string"==typeof r?Object.assign({query:r},t):r,s=Object.keys(a).reduce((e,r)=>n.includes(r)?(e[r]=a[r],e):(e.variables||(e.variables={}),e.variables[r]=a[r],e),{}),d=a.baseUrl||e.endpoint.DEFAULTS.baseUrl;return c.test(d)&&(s.url=d.replace(c,"/api/graphql")),e(s).then(e=>{if(e.data.errors){const r={};for(const t of Object.keys(e.headers))r[t]=e.headers[t];throw new o(s,r,e.data)}return e.data.data})}(a,e,r),{defaults:e.bind(null,a),endpoint:a.endpoint})}(a.E,{headers:{"user-agent":`octokit-graphql.js/0.0.0-development ${(0,s.$)()}`},method:"POST",url:"/graphql"})}};
//# sourceMappingURL=https://main.vscode-cdn.net/sourcemaps/360a4e4fd251bfce169a4ddf857c7d25d1ad40da/extensions/github/dist/555.js.map