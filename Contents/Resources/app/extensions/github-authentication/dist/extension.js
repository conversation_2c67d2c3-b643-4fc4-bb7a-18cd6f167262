/*! For license information please see extension.js.LICENSE.txt */
(()=>{"use strict";var e={87:(e,t,n)=>{n.d(t,{Dt:()=>a,Y8:()=>u,bb:()=>s,vv:()=>c});var r=n(269),i=n(5130),o="";function a(e,t){return void 0===t&&(t=!1),null==e?t:"true"===e.toString()[i.OL]()}function s(e){(isNaN(e)||e<0)&&(e=0),e=Math.round(e);var t=o+e%1e3,n=o+Math.floor(e/1e3)%60,r=o+Math.floor(e/6e4)%60,a=o+Math.floor(e/36e5)%24,s=Math.floor(e/864e5);return t=1===t[i.oI]?"00"+t:2===t[i.oI]?"0"+t:t,n=n[i.oI]<2?"0"+n:n,r=r[i.oI]<2?"0"+r:r,a=a[i.oI]<2?"0"+a:a,(s>0?s+".":o)+a+":"+r+":"+n+"."+t}function u(e,t){var n=null;return(0,r.Iuo)(e,function(e){if(e.identifier===t)return n=e,-1}),n}function c(e,t,n,i,o){return!o&&(0,r.KgX)(e)&&("Script error."===e||"Script error"===e)}},269:(e,t,n)=>{function r(e,t){return e||t}function i(e,t){return e[t]}n.d(t,{$8:()=>le,$PY:()=>z,$XS:()=>B,AHH:()=>dn,Cv9:()=>Yt,DA8:()=>bt,EHq:()=>zt,Edw:()=>k,EtT:()=>X,FJj:()=>Jt,GuU:()=>qe,Gvm:()=>F,HzD:()=>tn,Iuo:()=>_t,JKf:()=>ut,KTd:()=>Ot,KVm:()=>Pt,KgX:()=>U,KhI:()=>W,Lln:()=>ct,Lmq:()=>V,Lok:()=>dt,N6t:()=>ye,Nq2:()=>xt,O9V:()=>L,P0f:()=>Ne,QdQ:()=>Xt,R3R:()=>Wt,SZ2:()=>D,Tnt:()=>H,UUD:()=>Qt,UxO:()=>te,Vdv:()=>et,W$7:()=>Et,WSA:()=>Se,Wtk:()=>Qe,Y0g:()=>ht,YEm:()=>Ye,Yny:()=>St,ZHX:()=>me,ZWZ:()=>Ue,aqQ:()=>Nt,b07:()=>N,bJ7:()=>$,cGk:()=>ge,cyL:()=>j,dRz:()=>ln,eCG:()=>gt,f0d:()=>Mt,fn0:()=>Ie,gBW:()=>O,hKY:()=>Me,hXl:()=>M,isD:()=>ne,jjc:()=>pt,jsL:()=>be,kgX:()=>G,mS$:()=>We,mmD:()=>ce,nRs:()=>Be,oJg:()=>se,rDm:()=>It,raO:()=>re,sSX:()=>Rt,tGl:()=>nn,twz:()=>it,v0u:()=>Z,vE3:()=>pe,vF1:()=>ie,vKV:()=>fn,w3n:()=>rt,w9M:()=>nt,woc:()=>q,xZI:()=>yt,zS2:()=>Je,zav:()=>Q,zkX:()=>tt,zkd:()=>fe,zwS:()=>Gt,zzB:()=>K});var o,a=void 0,s=null,u="",c="function",l="object",f="prototype",d="__proto__",v="undefined",h="constructor",p="Symbol",g="_polyfill",m="length",y="name",b="call",w="toString",S=r(Object),_=i(S,f),I=r(String),E=i(I,f),P=r(Math),T=r(Array),C=i(T,f),x=i(C,"slice");function O(e,t){try{return{v:e.apply(this,t)}}catch(e){return{e}}}function R(e){return function(t){return typeof t===e}}function A(e){var t="[object "+e+"]";return function(e){return!(!e||D(e)!==t)}}function D(e){return _[w].call(e)}function k(e,t){return typeof e===t}function N(e){return typeof e===v||e===v}function M(e){return e===s||N(e)}function L(e){return!!e||e!==a}var U=R("string"),H=R(c);function F(e){return!(!e&&M(e)||!e||typeof e!==l)}var j=i(T,"isArray"),z=A("Date"),X=R("number"),V=R("boolean"),$=A("Error");function B(e){return!!(e&&e.then&&H(e.then))}function q(e){return!e||!K(e)}function K(e){return!(!e||(t=function(){return!(e&&0+e)},n=!e,r=O(t),r.e?n:r.v));var t,n,r}var G=i(S,"getOwnPropertyDescriptor");function Z(e,t){return!!e&&_.hasOwnProperty[b](e,t)}var W=r(i(S,"hasOwn"),J);function J(e,t){return Z(e,t)||!!G(e,t)}function Q(e,t,n){if(e&&F(e))for(var r in e)if(W(e,r)&&-1===t[b](n||e,r,e[r]))break}var Y={e:"enumerable",c:"configurable",v:"value",w:"writable",g:"get",s:"set"};function ee(e){var t={};if(t[Y.c]=!0,t[Y.e]=!0,e.l){t.get=function(){return e.l.v};var n=G(e.l,"v");n&&n.set&&(t.set=function(t){e.l.v=t})}return Q(e,function(e,n){t[Y[e]]=L(n)?n:t[Y[e]]}),t}var te=i(S,"defineProperty"),ne=i(S,"defineProperties");function re(e,t,n,r,i,o){var a={e:o,c:i};return n&&(a.g=n),r&&(a.s=r),te(e,t,ee(a))}function ie(e,t,n){return te(e,t,ee(n))}function oe(e,t,n,r,i){var o={};return Q(e,function(e,r){ae(o,e,t?r:e,i),ae(o,r,n?r:e,i)}),r?r(o):o}function ae(e,t,n,r){te(e,t,{value:n,enumerable:!0,writable:!!r})}var se=r(I),ue="[object Error]";function ce(e,t){var n=u,r=_[w][b](e);r===ue&&(e={stack:se(e.stack),message:se(e.message),name:se(e.name)});try{n=((n=JSON.stringify(e,s,t?"number"==typeof t?t:4:a))?n.replace(/"(\w+)"\s*:\s{0,1}/g,"$1: "):s)||se(e)}catch(e){n=" - "+ce(e,t)}return r+": "+n}function le(e){throw new Error(e)}function fe(e){throw new TypeError(e)}var de=i(S,"freeze");function ve(e){return e}function he(e){return e[d]||s}var pe=i(S,"assign"),ge=i(S,"keys");function me(e){return de&&Q(e,function(e,t){(j(t)||F(t))&&me(t)}),ye(e)}var ye=r(de,ve),be=r(i(S,"seal"),ve),we=r(i(S,"getPrototypeOf"),he);function Se(e){return oe(e,1,0,ye)}function _e(e){return oe(e,0,0,ye)}function Ie(e){return function(e){var t={};return Q(e,function(e,n){ae(t,e,n[1]),ae(t,n[0],n[1])}),ye(t)}(e)}var Ee,Pe=_e({asyncIterator:0,hasInstance:1,isConcatSpreadable:2,iterator:3,match:4,matchAll:5,replace:6,search:7,species:8,split:9,toPrimitive:10,toStringTag:11,unscopables:12}),Te="__tsUtils$gblCfg";function Ce(){var e;return typeof globalThis!==v&&(e=globalThis),e||typeof self===v||(e=self),e||typeof window===v||(e=window),e||typeof global===v||(e=global),e}function xe(){if(!Ee){var e=O(Ce).v||{};Ee=e[Te]=e[Te]||{}}return Ee}var Oe=Re;function Re(e,t,n){var r=t?t[e]:s;return function(t){var i=(t?t[e]:s)||r;if(i||n){var o=arguments;return(i||n).apply(t,i?x[b](o,1):o)}fe('"'+se(e)+'" not defined for '+ce(t))}}function Ae(e){return function(t){return t[e]}}var De=i(P,"max"),ke=Oe("slice",E),Ne=Oe("substring",E),Me=Re("substr",E,Le);function Le(e,t,n){return M(e)&&fe("Invalid "+ce(e)),n<0?u:((t=t||0)<0&&(t=De(t+e[m],0)),N(n)?ke(e,t):ke(e,t,t+n))}function Ue(e,t){return Ne(e,0,t)}var He,Fe,je,ze="_urid";function Xe(e){var t={description:se(e),toString:function(){return p+"("+e+")"}};return t[g]=!0,t}function Ve(e){var t=function(){if(!He){var e=xe();He=e.gblSym=e.gblSym||{k:{},s:{}}}return He}();if(!W(t.k,e)){var n=Xe(e),r=ge(t.s).length;n[ze]=function(){return r+"_"+n[w]()},t.k[e]=n,t.s[n[ze]()]=se(e)}return t.k[e]}function $e(){je=xe()}function Be(e){var t={};return!je&&$e(),t.b=je.lzy,te(t,"v",{configurable:!0,get:function(){var n=e();return je.lzy||te(t,"v",{value:n}),t.b=je.lzy,n}}),t}function qe(e){return te({toJSON:function(){return e}},"v",{value:e})}var Ke,Ge="window";function Ze(e,t){var n;return function(){return!je&&$e(),n&&!je.lzy||(n=qe(O(e,t).v)),n.v}}function We(e){return!je&&$e(),Ke&&!1!==e&&!je.lzy||(Ke=qe(O(Ce).v||s)),Ke.v}function Je(e,t){var n;if((n=Ke&&!1!==t?Ke.v:We(t))&&n[e])return n[e];if(e===Ge)try{return window}catch(e){}return s}function Qe(){return!!Ye()}var Ye=Ze(Je,["document"]);function et(){return!!tt()}var tt=Ze(Je,[Ge]);function nt(){return!!rt()}var rt=Ze(Je,["navigator"]);function it(){return!!ut()}var ot,at,st,ut=Ze(Je,["history"]),ct=Ze(function(){return!!O(function(){return process&&(process.versions||{}).node}).v});function lt(){return ot=qe(O(Je,[p]).v)}function ft(e){var t=(je.lzy?0:ot)||lt();return t.v?t.v[e]:a}function dt(){return!!vt()}function vt(){return!je&&$e(),((je.lzy?0:ot)||lt()).v}function ht(e,t){var n=Pe[e];!je&&$e();var r=(je.lzy?0:ot)||lt();return r.v?r.v[n||e]:t?a:function(e){var t;!Fe&&(Fe={});var n=Pe[e];return n&&(t=Fe[n]=Fe[n]||Xe(p+"."+n)),t}(e)}function pt(e,t){!je&&$e();var n=(je.lzy?0:ot)||lt();return n.v?n.v(e):t?s:Xe(e)}function gt(e){return!je&&$e(),((at=(je.lzy?0:at)||qe(O(ft,["for"]).v)).v||Ve)(e)}function mt(e){return!!e&&H(e.next)}function yt(e){return!function(e){return e===s||!L(e)}(e)&&H(e[ht(3)])}function bt(e,t,n){if(e&&(mt(e)||(!st&&(st=qe(ht(3))),e=e[st.v]?e[st.v]():s),mt(e))){var r=a,i=a;try{for(var o=0;!(i=e.next()).done&&-1!==t[b](n||e,i.value,o,e);)o++}catch(t){r={e:t},e.throw&&(i=s,e.throw(r))}finally{try{i&&!i.done&&e.return&&e.return(i)}finally{if(r)throw r.e}}}}function wt(e,t,n){return e.apply(t,n)}function St(e,t){return!N(t)&&e&&(j(t)?wt(e.push,e,t):mt(t)||yt(t)?bt(t,function(t){e.push(t)}):e.push(t)),e}function _t(e,t,n){if(e)for(var r=e[m]>>>0,i=0;i<r&&(!(i in e)||-1!==t[b](n||e,e[i],i,e));i++);}var It=Oe("indexOf",C),Et=Oe("map",C);function Pt(e,t,n){return((e?e.slice:s)||x).apply(e,x[b](arguments,1))}function Tt(e,t,n){return-1!==It(e,t,n)}var Ct,xt=Re("includes",C,Tt),Ot=Oe("reduce",C),Rt=r(i(S,"create"),At);function At(e){if(!e)return{};var t=typeof e;function n(){}return t!==l&&t!==c&&fe("Prototype must be an Object or function: "+ce(e)),n[f]=e,new n}function Dt(e,t){return(S.setPrototypeOf||function(e,t){var n;!Ct&&(Ct=qe(((n={})[d]=[],n instanceof Array))),Ct.v?e[d]=t:Q(t,function(t,n){return e[t]=n})})(e,t)}function kt(e,t){t&&(e[y]=t)}function Nt(e,t,n){var r=n||Error,i=r[f][y],o=Error.captureStackTrace;return function(e,t,n){function r(){this[h]=t,O(ie,[this,y,{v:e,c:!0,e:!1}])}return O(ie,[t,y,{v:e,c:!0,e:!1}]),(t=Dt(t,n))[f]=n===s?Rt(n):(r[f]=n[f],new r),t}(e,function(){var n=this,a=arguments;try{O(kt,[r,e]);var s=wt(r,n,x[b](a))||n;if(s!==n){var u=we(n);u!==we(s)&&Dt(s,u)}return o&&o(s,n[h]),t&&t(s,a),s}finally{O(kt,[r,i])}},r)}function Mt(){return(Date.now||Lt)()}function Lt(){return(new Date).getTime()}function Ut(e){return function(t){return M(t)&&fe("strTrim called ["+ce(t)+"]"),t&&t.replace&&(t=t.replace(e,u)),t}}var Ht,Ft,jt,zt=Re("trim",E,Ut(/^\s+|(?=\s)\s+$/g));function Xt(e){if(!e||typeof e!==l)return!1;jt||(jt=!et()||tt());var t=!1;if(e!==jt){Ft||(Ht=Function[f][w],Ft=Ht[b](S));try{var n=we(e);(t=!n)||(Z(n,h)&&(n=n[h]),t=!(!n||typeof n!==c||Ht[b](n)!==Ft))}catch(e){}}return t}function Vt(e){return e.value&&Kt(e),!0}var $t=[function(e){var t=e.value;if(j(t)){var n=e.result=[];return n.length=t.length,e.copyTo(n,t),!0}return!1},Kt,function(e){return e.type===c},function(e){var t=e.value;return!!z(t)&&(e.result=new Date(t.getTime()),!0)}];function Bt(e,t,n,r){var i=n.handler,a=n.path?r?n.path.concat(r):n.path:[],u={handler:n.handler,src:n.src,path:a},c=typeof t,f=!1,d=t===s;d||(t&&c===l?f=Xt(t):d=function(e){return!o&&(o=["string","number","boolean",v,"symbol","bigint"]),!(e===l||-1===o.indexOf(e))}(c));var h={type:c,isPrim:d,isPlain:f,value:t,result:t,path:a,origin:n.src,copy:function(t,r){return Bt(e,t,r?u:n,r)},copyTo:function(t,n){return qt(e,t,n,u)}};return h.isPrim?i&&i[b](n,h)?h.result:t:function(e,t){var r;return _t(e,function(e){if(e.k===t)return r=e,-1}),r||(r={k:t,v:t},e.push(r),function(e){ie(h,"result",{g:function(){return e.v},s:function(t){e.v=t}});for(var t=0,r=i;!(r||(t<$t.length?$t[t++]:Vt))[b](n,h);)r=s}(r)),r.v}(e,t)}function qt(e,t,n,r){if(!M(n))for(var i in n)t[i]=Bt(e,n[i],r,i);return t}function Kt(e){var t=e.value;if(t&&e.isPlain){var n=e.result={};return e.copyTo(n,t),!0}return!1}function Gt(e,t,n,r,i,o,a){return function(e,t){return _t(t,function(t){!function(e,t){qt([],e,t,{handler:void 0,src:t,path:[]})}(e,t)}),e}(Bt([],s=e,{handler:undefined,src:s})||{},x[b](arguments));var s}var Zt,Wt=Ae(m);function Jt(){return!je&&$e(),Zt&&!je.lzy||(Zt=qe(O(Je,["performance"]).v)),Zt.v}function Qt(){var e=Jt();return e&&e.now?e.now():Mt()}dt();var Yt=Re("endsWith",E,en);function en(e,t,n){U(e)||fe("'"+ce(e)+"' is not a string");var r=U(t)?t:se(t),i=!N(n)&&n<e[m]?n:e[m];return Ne(e,i-r[m],i)===r}var tn=Oe("indexOf",E),nn=Re("startsWith",E,rn);function rn(e,t,n){U(e)||fe("'"+ce(e)+"' is not a string");var r=U(t)?t:se(t),i=n>0?n:0;return Ne(e,i,i+r[m])===r}var on="ref",an="unref",sn="hasRef",un="enabled";function cn(e,t,n){var r=j(t),i=r?t.length:0,o=(i>0?t[0]:r?a:t)||setTimeout,u=(i>1?t[1]:a)||clearTimeout,c=n[0];n[0]=function(){l.dn(),wt(c,a,x[b](arguments))};var l=function(e,t,n){var r,i=!0,o=e?t(s):s;function a(){return i=!1,o&&o[an]&&o[an](),r}function u(){o&&n(o),o=s}function c(){return o=t(o),i||a(),r}return(r={cancel:u,refresh:c})[sn]=function(){return o&&o[sn]?o[sn]():i},r[on]=function(){return i=!0,o&&o[on]&&o[on](),r},r[an]=a,{h:r=te(r,un,{get:function(){return!!o},set:function(e){!e&&o&&u(),e&&!o&&c()}}),dn:function(){o=s}}}(e,function(e){if(e){if(e.refresh)return e.refresh(),e;wt(u,a,[e])}return wt(o,a,n)},function(e){wt(u,a,[e])});return l.h}function ln(e,t){return cn(!0,a,x[b](arguments))}function fn(e,t,n){return cn(!0,e,x[b](arguments,1))}function dn(e,t){return cn(!1,a,x[b](arguments))}},295:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.Keychain=void 0,t.Keychain=class{constructor(e,t,n){this.context=e,this.serviceId=t,this.Logger=n}async setToken(e){try{return await this.context.secrets.store(this.serviceId,e)}catch(e){this.Logger.error(`Setting token failed: ${e}`)}}async getToken(){try{const e=await this.context.secrets.get(this.serviceId);return e&&"[]"!==e&&this.Logger.trace("Token acquired from secret storage."),e}catch(e){return this.Logger.error(`Getting token failed: ${e}`),Promise.resolve(void 0)}}async deleteToken(){try{return await this.context.secrets.delete(this.serviceId)}catch(e){return this.Logger.error(`Deleting token failed: ${e}`),Promise.resolve(void 0)}}}},380:(e,t,n)=>{n.d(t,{Cr:()=>c,Xc:()=>f,pI:()=>l,u7:()=>d});var r=n(269),i=n(6182),o=n(4276),a=n(6492),s=n(1864),u=(0,o.T)("plugin");function c(e){return u.get(e,"state",{},!0)}function l(e,t){for(var n,o=[],s=null,u=e[i.uR]();u;){var l=u[i.AP]();if(l){s&&s[i.YH]&&l[a.qT]&&s[i.YH](l);var f=!!(n=c(l))[i.tZ];l[i.tZ]&&(f=l[i.tZ]()),f||o[i.y5](l),s=l,u=u[i.uR]()}}(0,r.Iuo)(o,function(r){var o=e[a.eT]();r[i.mE](e.getCfg(),o,t,e[i.uR]()),n=c(r),r[a.eT]||n[a.eT]||(n[a.eT]=o),n[i.tZ]=!0,delete n[i.Ik]})}function f(e){return e.sort(function(e,t){var n=0;if(t){var r=t[a.qT];e[a.qT]?n=r?e[a.Vo]-t[a.Vo]:1:r&&(n=-1)}else n=e?1:-1;return n})}function d(e){var t={};return{getName:function(){return t[i.RS]},setName:function(n){e&&e.setName(n),t[i.RS]=n},getTraceId:function(){return t[i.P5]},setTraceId:function(n){e&&e.setTraceId(n),(0,s.hX)(n)&&(t[i.P5]=n)},getSpanId:function(){return t[i.wi]},setSpanId:function(n){e&&e.setSpanId(n),(0,s.wN)(n)&&(t[i.wi]=n)},getTraceFlags:function(){return t[i.Rr]},setTraceFlags:function(n){e&&e.setTraceFlags(n),t[i.Rr]=n}}}},659:(e,t,n)=>{n.d(t,{Im:()=>a,qU:()=>u,vz:()=>c});var r=n(269),i=n(5664),o=(((0,r.mS$)()||{}).Symbol,((0,r.mS$)()||{}).Reflect,"hasOwnProperty"),a=r.vE3||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var a in t=arguments[n])i.Wy[o].call(t,a)&&(e[a]=t[a]);return e},s=function(e,t){return s=i.s6.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t[o](n)&&(e[n]=t[n])},s(e,t)};function u(e,t){function n(){this.constructor=e}typeof t!==i.hW&&null!==t&&(0,r.zkd)("Class extends value "+String(t)+" is not a constructor or null"),s(e,t),e[i.vR]=null===t?(0,r.sSX)(t):(n[i.vR]=t[i.vR],new n)}function c(e,t){for(var n=0,r=t.length,i=e.length;n<r;n++,i++)e[i]=t[n];return e}},740:(e,t,n)=>{n.r(t),n.d(t,{AnalyticsPluginIdentifier:()=>j,BreezeChannelIdentifier:()=>F,ConfigurationManager:()=>P,ConnectionStringParser:()=>d.F,ContextTagKeys:()=>T.o,CtxTagKeys:()=>R.O,DEFAULT_BREEZE_ENDPOINT:()=>h._G,DEFAULT_BREEZE_PATH:()=>h.wc,Data:()=>I.B,DisabledPropertyName:()=>h.xF,DistributedTracingModes:()=>A.uG,Envelope:()=>p.L,Event:()=>g.J,EventPersistence:()=>A.iD,Exception:()=>m.WJ,Extensions:()=>R.F,HttpMethod:()=>h.ym,Metric:()=>y.J,PageView:()=>b.h,PageViewPerformance:()=>_.H,ProcessLegacy:()=>h.jp,PropertiesPluginIdentifier:()=>H,RemoteDependencyData:()=>w.A,RequestHeaders:()=>v.a,SampleRate:()=>h.tU,SeverityLevel:()=>E.O,TelemetryItemCreator:()=>O,ThrottleMgr:()=>f,Trace:()=>S.C,correlationIdCanIncludeCorrelationHeader:()=>r.Rs,correlationIdGetCorrelationContext:()=>r.pg,correlationIdGetCorrelationContextValue:()=>r.mD,correlationIdGetPrefix:()=>r.mp,correlationIdSetPrefix:()=>r.Wt,createDistributedTraceContextFromTrace:()=>r.Ft,createDomEvent:()=>M,createOfflineListener:()=>U.G,createTelemetryItem:()=>x,createTraceParent:()=>N.wk,dataSanitizeException:()=>C.Vt,dataSanitizeId:()=>C.HQ,dataSanitizeInput:()=>C._T,dataSanitizeKey:()=>C.lq,dataSanitizeKeyAndAddUniqueness:()=>C.zx,dataSanitizeMeasurements:()=>C.Vj,dataSanitizeMessage:()=>C.Vk,dataSanitizeProperties:()=>C.xP,dataSanitizeString:()=>C.Rr,dataSanitizeUrl:()=>C.pJ,dateTimeUtilsDuration:()=>r.jj,dateTimeUtilsNow:()=>r.lt,dsPadNumber:()=>C.qW,findAllScripts:()=>N.V5,findW3cTraceParent:()=>N.ef,formatTraceParent:()=>N.L0,getExtensionByName:()=>D.Y8,isBeaconApiSupported:()=>k.Uf,isCrossOriginError:()=>D.vv,isInternalApplicationInsightsEndpoint:()=>r.Qu,isSampledFlag:()=>N.N7,isValidSpanId:()=>N.wN,isValidTraceId:()=>N.hX,isValidTraceParent:()=>N.mJ,msToTimeSpan:()=>D.bb,parseConnectionString:()=>d.H,parseTraceParent:()=>N.ZI,strNotSpecified:()=>h.R2,stringToBoolOrDefault:()=>D.Dt,urlGetAbsoluteUrl:()=>L.wX,urlGetCompleteUrl:()=>L.k6,urlGetPathName:()=>L.Gz,urlParseFullHost:()=>L.M0,urlParseHost:()=>L.PS,urlParseUrl:()=>L.cM,utlCanUseLocalStorage:()=>c.BW,utlCanUseSessionStorage:()=>c.AN,utlDisableStorage:()=>c.Nu,utlEnableStorage:()=>c.iw,utlGetLocalStorage:()=>c.Se,utlGetSessionStorage:()=>c.vH,utlGetSessionStorageKeys:()=>c.T9,utlRemoveSessionStorage:()=>c.v7,utlRemoveStorage:()=>c.tm,utlSetLocalStorage:()=>c._M,utlSetSessionStorage:()=>c.Dt,utlSetStoragePrefix:()=>c.vh});var r=n(2318),i=n(269),o=n(3775),a=n(3673),s=n(9749),u=n(6535),c=n(4658),l=n(5130),f=function(e,t){var n,r,f,d,v,h,p,g=this,m=!1,y=!1;function b(e,t,i,o){if(m){var a=function(e){try{var t=w(e);return(0,u.Z1)(1e6)<=t.limit.samplingRate}catch(e){}return!1}(e);if(!a)return;var s=w(e),c=x(e),f=_(s,n,c),d=!1,h=0,p=O(e);try{f&&!p?(h=Math.min(s.limit.maxSendNumber,c[l.F2]+1),c[l.F2]=0,d=!0,v[e]=!0,c[l.Jm]=new Date):(v[e]=f,c[l.F2]+=1);var g=I(e);P(r,g,c);for(var y=0;y<h;y++)C(e,r,t,i)}catch(e){}return{isThrottled:d,throttleNum:h}}return o&&R(e)[l.y5]({msgID:e,message:t,severity:i}),null}function w(e){return f[e]||f[109]}function S(e,t){var n,r,o,a,s;try{var u=t||{},c={};c[l.Cx]=!!u[l.Cx];var d=u[l.zw]||{};y=(null==d?void 0:d.daysOfMonth)&&(null==d?void 0:d.daysOfMonth[l.oI])>0,c[l.zw]=(a=null===(o=(o=d)||{})||void 0===o?void 0:o.monthInterval,s=null==o?void 0:o.dayInterval,(0,i.hXl)(a)&&(0,i.hXl)(s)&&(o.monthInterval=3,y||(o[l.i9]=[28],y=!0)),o={monthInterval:null==o?void 0:o.monthInterval,dayInterval:null==o?void 0:o.dayInterval,daysOfMonth:null==o?void 0:o.daysOfMonth});var v={samplingRate:(null===(n=u.limit)||void 0===n?void 0:n.samplingRate)||100,maxSendNumber:(null===(r=u.limit)||void 0===r?void 0:r.maxSendNumber)||1};c.limit=v,f[e]=c}catch(e){}}function _(e,t,n){if(e&&!e[l.Cx]&&t&&(0,a.Gh)(n)){var r=E(),o=n[l.C9],s=e[l.zw],u=1;if(null==s?void 0:s.monthInterval){var c=12*(r.getUTCFullYear()-o.getUTCFullYear())+r.getUTCMonth()-o.getUTCMonth();u=T(s.monthInterval,0,c)}var f=1;if(y)f=(0,i.rDm)(s[l.i9],r[l.$e]());else if(null==s?void 0:s.dayInterval){var d=Math.floor((r.getTime()-o.getTime())/864e5);f=T(s.dayInterval,0,d)}return u>=0&&f>=0}return!1}function I(e,t){var n=(0,a.Gh)(t)?t:"";return e?"appInsightsThrottle"+n+"-"+e:null}function E(e){try{if(!e)return new Date;var t=new Date(e);if(!isNaN(t.getDate()))return t}catch(e){}return null}function P(e,t,n){try{return(0,c._M)(e,t,(0,i.EHq)(JSON[l.Jj](n)))}catch(e){}return!1}function T(e,t,n){return e<=0?1:n>=t&&(n-t)%e==0?Math.floor((n-t)/e)+1:-1}function C(e,t,n,r){(0,o.ZP)(t,r||1,e,n)}function x(e){try{var t=d[e];if(!t){var n=I(e,h);t=function(e,t,n){try{var r={date:E(),count:0};if(e){var i=JSON.parse(e);return{date:E(i[l.C9])||r[l.C9],count:i[l.F2]||r[l.F2],preTriggerDate:i.preTriggerDate?E(i[l.Jm]):void 0}}return P(t,n,r),r}catch(e){}return null}((0,c.Se)(r,n),r,n),d[e]=t}return d[e]}catch(e){}return null}function O(e){var t=v[e];if((0,i.hXl)(t)){t=!1;var n=x(e);n&&(t=function(e){try{if(e){var t=new Date;return e.getUTCFullYear()===t.getUTCFullYear()&&e.getUTCMonth()===t.getUTCMonth()&&e[l.$e]()===t[l.$e]()}}catch(e){}return!1}(n[l.Jm])),v[e]=t}return v[e]}function R(e){return p=p||{},(0,i.hXl)(p[e])&&(p[e]=[]),p[e]}r=(0,o.y0)(e),v={},d={},p={},f={},S(109),h=(0,a.Gh)(t)?t:"",e.addUnloadHook((0,s.a)(e.config,function(e){var t=e.cfg;n=(0,c.BW)();var r=t.throttleMgrCfg||{};(0,i.zav)(r,function(e,t){S(parseInt(e),t)})})),g._getDbgPlgTargets=function(){return[p]},g.getConfig=function(){return f},g.canThrottle=function(e){var t=x(e);return _(w(e),n,t)},g.isTriggered=function(e){return O(e)},g.isReady=function(){return m},g.flush=function(e){try{var t=R(e);if(t&&t[l.oI]>0){var n=t.slice(0);return p[e]=[],(0,i.Iuo)(n,function(e){b(e.msgID,e[l.pM],e.severity,!1)}),!0}}catch(e){}return!1},g.flushAll=function(){try{if(p){var e=!0;return(0,i.zav)(p,function(t){var n=g.flush(parseInt(t));e=e&&n}),e}}catch(e){}return!1},g.onReadyState=function(e,t){return void 0===t&&(t=!0),(m=!!(0,i.hXl)(e)||e)&&t?g.flushAll():null},g.sendMessage=function(e,t,n){return b(e,t,n,!0)}},d=n(4484),v=n(2910),h=n(5025),p=n(1062),g=n(3072),m=n(5397),y=n(5014),b=n(1448),w=n(1365),S=n(2445),_=n(4164),I=n(7358),E=n(9762),P=function(){function e(){}return e.getConfig=function(e,t,n,r){var o;return void 0===r&&(r=!1),o=n&&e[l.up]&&e[l.up][n]&&!(0,i.hXl)(e[l.up][n][t])?e[l.up][n][t]:e[t],(0,i.hXl)(o)?r:o},e}(),T=n(8596),C=n(7975);function x(e,t,n,r,o,s){var u;n=(0,C.Rr)(r,n)||h.R2,((0,i.hXl)(e)||(0,i.hXl)(t)||(0,i.hXl)(n))&&(0,i.$8)("Input doesn't contain all required fields");var c="";e[h.ks]&&(c=e[h.ks],delete e[h.ks]);var f=((u={})[l.RS]=n,u.time=(0,a._u)(new Date),u.iKey=c,u.ext=s||{},u.tags=[],u.data={},u.baseType=t,u.baseData=e,u);return(0,i.hXl)(o)||(0,i.zav)(o,function(e,t){f.data[e]=t}),f}var O=function(){function e(){}return e.create=x,e}(),R=n(1575),A=n(7374),D=n(87),k=n(7292),N=n(1864);function M(e){var t=null;if((0,i.Tnt)(Event))t=new Event(e);else{var n=(0,i.YEm)();n&&n.createEvent&&(t=n.createEvent("Event")).initEvent(e,!0,!0)}return t}var L=n(9354),U=n(5571),H="AppInsightsPropertiesPlugin",F="AppInsightsChannelPlugin",j="ApplicationInsightsAnalytics"},836:(e,t,n)=>{n.d(t,{P:()=>a});var r=n(269),i=n(6182),o=n(3775);function a(){var e=[];return{add:function(t){t&&e[i.y5](t)},run:function(t,n){(0,r.Iuo)(e,function(e){try{e(t,n)}catch(e){(0,o.ZP)(t[i.e4](),2,73,"Unexpected error calling unload handler - "+(0,r.mmD)(e))}}),e=[]}}}},856:(e,t,n)=>{n.d(t,{v:()=>h});var r=n(8279),i=n(8205),o=n(269),a=n(6182),s=n(7847),u=n(3775),c=n(7292),l=n(3673),f="",d="&NoResponseBody=true",v="POST",h=function(){function e(){var t,n,h,p,g,m,y,b,w,S,_,I,E,P,T=0;(0,r.A)(e,this,function(e,r){var C=!0;function x(e,t){(0,u.ZP)(h,2,26,"Failed to send telemetry.",{message:e}),R(t,400,{})}function O(e){x("No endpoint url is provided for the batch",e)}function R(e,t,n,r){try{e&&e(t,n,r)}catch(e){}}function A(e,t){var n=(0,o.w3n)(),r=e[a.Vq];if(!r)return O(t),!0;r=e[a.Vq]+(E?d:f);var i=e[a.Cd],s=p?i:new Blob([i],{type:"text/plain;charset=UTF-8"});return n.sendBeacon(r,s)}function D(e,t,n){var r=e[a.Cd];try{if(r)if(A(e,t))R(t,200,{},f);else{var i=g&&g.beaconOnRetry;i&&(0,o.Tnt)(i)?i(e,t,A):(b&&b[a.L](e,t,!0),(0,u.ZP)(h,2,40,". Failed to send telemetry with Beacon API, retried with normal sender."))}}catch(e){p&&(0,u.OG)(h,"Failed to send telemetry using sendBeacon API. Ex:"+(0,o.mmD)(e)),R(t,p?0:400,{},f)}}function k(e,n,r){var s,u,c,d=e[a.c1]||{};!r&&t&&(s=(0,i.Qo)(function(e,t){u=e,c=t})),p&&r&&e.disableXhrSync&&(r=!1);var h=e[a.Vq];if(!h)return O(n),void(u&&u(!1));var m=(0,l.H$)(v,h,C,!0,r,e[a.do]);function y(t){var r=g&&g.xhrOnComplete;if(r&&(0,o.Tnt)(r))r(t,n,e);else{var i=(0,l.Lo)(t);R(n,t[a.cV],(0,l.w3)(t,p),i)}}return p||m[a.yy]("Content-type","application/json"),(0,o.Iuo)((0,o.cGk)(d),function(e){m[a.yy](e,d[e])}),m.onreadystatechange=function(){p||(y(m),4===m.readyState&&u&&u(!0))},m.onload=function(){p&&y(m)},m.onerror=function(e){R(n,p?m[a.cV]:400,(0,l.w3)(m,p),p?f:(0,l.r4)(m)),c&&c(e)},m.ontimeout=function(){R(n,p?m[a.cV]:500,(0,l.w3)(m,p),p?f:(0,l.r4)(m)),u&&u(!1)},m.send(e[a.Cd]),s}function N(e,n,r){var u,c,l,h,m=e[a.Vq],b=e[a.Cd],w=p?b:new Blob([b],{type:"application/json"}),S=new Headers,_=b[a.oI],I=!1,x=!1,A=e[a.c1]||{},D=((u={method:v,body:w})[s.x]=!0,u);e.headers&&(0,o.cGk)(e.headers)[a.oI]>0&&((0,o.Iuo)((0,o.cGk)(A),function(e){S.append(e,A[e])}),D[a.c1]=S),y?D.credentials=y:C&&p&&(D.credentials="include"),r&&(D.keepalive=!0,T+=_,p?2===e._sendReason&&(I=!0,E&&(m+=d)):I=!0);var k=new Request(m,D);try{k[s.x]=!0}catch(e){}if(!r&&t&&(c=(0,i.Qo)(function(e,t){l=e,h=t})),!m)return O(n),void(l&&l(!1));function N(e){R(n,p?0:400,{},p?f:e)}function M(e,t,r){var i=e[a.cV],s=g.fetchOnComplete;s&&(0,o.Tnt)(s)?s(e,n,r||f,t):R(n,i,{},r||f)}try{(0,i.Dv)(fetch(p?m:k,p?D:null),function(t){if(r&&(T-=_,_=0),!x)if(x=!0,t.rejected)N(t.reason&&t.reason[a.pM]),h&&h(t.reason);else{var n=t[a.pF];try{p||n.ok?p&&!n.body?(M(n,null,f),l&&l(!0)):(0,i.Dv)(n.text(),function(t){M(n,e,t[a.pF]),l&&l(!0)}):(N(n.statusText),l&&l(!1))}catch(e){N((0,o.mmD)(e)),h&&h(e)}}})}catch(e){x||(N((0,o.mmD)(e)),h&&h(e))}return I&&!x&&(x=!0,R(n,200,{}),l&&l(!0)),p&&!x&&e[a.do]>0&&P&&P.set(function(){x||(x=!0,R(n,500,{}),l&&l(!0))},e[a.do]),c}function M(e,t,n){var r=(0,o.zkX)(),i=new XDomainRequest,s=e[a.Cd];i.onload=function(){var n=(0,l.Lo)(i),r=g&&g.xdrOnComplete;r&&(0,o.Tnt)(r)?r(i,t,e):R(t,200,{},n)},i.onerror=function(){R(t,400,{},p?f:(0,l.HU)(i))},i.ontimeout=function(){R(t,500,{})},i.onprogress=function(){};var c=r&&r.location&&r.location[a.Qg]||"",d=e[a.Vq];if(d){if(!p&&0!==d.lastIndexOf(c,0)){var m="Cannot send XDomain request. The endpoint URL protocol doesn't match the hosting page protocol.";return(0,u.ZP)(h,2,40,". "+m),void x(m,t)}var y=p?d:d[a.W7](/^(https?:)/,"");i.open(v,y),e[a.do]&&(i[a.do]=e[a.do]),i.send(s),p&&n?P&&P.set(function(){i.send(s)},0):i.send(s)}else O(t)}function L(){T=0,n=!1,t=!1,h=null,p=null,g=null,m=null,y=null,b=null,w=!1,S=!1,_=!1,I=!1,E=!1,P=null}L(),e[a.mE]=function(t,r){h=r,n&&(0,u.ZP)(h,1,28,"Sender is already initialized"),e.SetConfig(t),n=!0},e._getDbgPlgTargets=function(){return[n,p,m,t]},e.SetConfig=function(e){try{if(g=e.senderOnCompleteCallBack||{},m=!!e.disableCredentials,y=e.fetchCredentials,p=!!e.isOneDs,t=!!e.enableSendPromise,w=!!e.disableXhr,S=!!e.disableBeacon,_=!!e.disableBeaconSync,P=e.timeWrapper,E=!!e.addNoResponse,I=!!e.disableFetchKeepAlive,b={sendPOST:k},p||(C=!1),m){var n=(0,c.g$)();n&&n.protocol&&"file:"===n.protocol[a.OL]()&&(C=!1)}return!0}catch(e){}return!1},e.getSyncFetchPayload=function(){return T},e.getSenderInst=function(e,t){return e&&e[a.oI]?function(e,t){for(var n,r=0,i=null,o=0;null==i&&o<e[a.oI];)r=e[o],w||1!==r?2!==r||!(0,c.R7)(t)||t&&I?3!==r||!(0,c.Uf)()||(t?_:S)||(i=D):i=N:(0,c.PV)()?i=M:(0,c.xk)()&&(i=k),o++;return i?((n={_transport:r,_isSync:t})[a.L]=i,n):null}(e,t):null},e.getFallbackInst=function(){return b},e[a.tn]=function(e,t){L()}})}return e.__ieDyn=1,e}()},857:e=>{e.exports=require("os")},928:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.CANCELLATION_ERROR=t.NETWORK_ERROR=t.USER_CANCELLATION_ERROR=t.TIMED_OUT_ERROR=void 0,t.TIMED_OUT_ERROR="Timed out",t.USER_CANCELLATION_ERROR="User Cancelled",t.NETWORK_ERROR="network error",t.CANCELLATION_ERROR="Cancelled"},937:(e,t,n)=>{n.d(t,{S:()=>i,_0:()=>a,hj:()=>o,m5:()=>r});var r="",i="https://browser.events.data.microsoft.com/OneCollector/1.0/",o="version",a="properties"},956:(e,t,n)=>{n.r(t),n.d(t,{ActiveStatus:()=>x.f,AppInsightsCore:()=>h,BaseTelemetryPlugin:()=>P.s,DiagnosticLogger:()=>u.wq,EventLatency:()=>m,EventPersistence:()=>b,EventPropertyType:()=>y,EventsDiscardedReason:()=>C.x,FullVersionString:()=>f.xE,InternalAppInsightsCore:()=>c._,LoggingSeverity:()=>I,MinChannelPriorty:()=>_,NotificationManager:()=>E.h,PerfEvent:()=>a.Q6,PerfManager:()=>a.NS,ProcessTelemetryContext:()=>T.W0,SenderPostManager:()=>j.v,TraceLevel:()=>w,Undefined:()=>N.bA,ValueKind:()=>g,ValueSanitizer:()=>S,Version:()=>f.Rx,_InternalLogMessage:()=>u.WD,__getRegisteredEvents:()=>O.El,_appendHeader:()=>R.LU,_getAllResponseHeaders:()=>R.w3,_logInternalMessage:()=>u.Oc,_testHookMaxUnloadHooksCb:()=>X.d,_throwInternal:()=>u.ZP,_warnToConsole:()=>u.OG,addEventHandler:()=>O.So,addEventListeners:()=>O.lQ,addPageHideEventListener:()=>O.Fc,addPageShowEventListener:()=>O.oS,addPageUnloadEventListener:()=>O.ee,areCookiesSupported:()=>M.gi,arrForEach:()=>o.Iuo,arrIndexOf:()=>o.rDm,arrMap:()=>o.W$7,arrReduce:()=>o.KTd,attachEvent:()=>O.Q3,blockDynamicConversion:()=>F.V9,convertAllHeadersToMap:()=>R.IL,cookieAvailable:()=>M.gi,createCookieMgr:()=>M.xN,createDynamicConfig:()=>s.e,createEnumStyle:()=>p.H,createGuid:()=>f.gj,createProcessTelemetryContext:()=>T.i8,createTraceParent:()=>H.wk,createUniqueNamespace:()=>L.Z,createUnloadHandlerContainer:()=>U.P,dateNow:()=>o.f0d,detachEvent:()=>O.Ym,disallowsSameSiteNone:()=>M.It,doPerf:()=>a.r2,dumpObj:()=>o.mmD,eventOff:()=>O.ML,eventOn:()=>O.mB,extend:()=>f.X$,findW3cTraceParent:()=>H.ef,forceDynamicConversion:()=>F.Hf,formatErrorMessageXdr:()=>R.HU,formatErrorMessageXhr:()=>R.r4,formatTraceParent:()=>H.L0,generateW3CId:()=>A.cL,getCommonSchemaMetaData:()=>f.Go,getConsole:()=>k.U5,getCookieValue:()=>f.UM,getCrypto:()=>k.MY,getDocument:()=>o.YEm,getDynamicConfigHandler:()=>F.QA,getExceptionName:()=>R.lL,getFieldValueType:()=>f.cq,getGlobal:()=>o.mS$,getGlobalInst:()=>o.zS2,getHistory:()=>o.JKf,getIEVersion:()=>k.L0,getISOString:()=>R._u,getJSON:()=>k.hm,getLocation:()=>k.g$,getMsCrypto:()=>k.iN,getNavigator:()=>o.w3n,getPerformance:()=>o.FJj,getResponseText:()=>R.Lo,getSetValue:()=>R.c2,getTenantId:()=>f.EO,getTime:()=>f.WB,getWindow:()=>o.zkX,hasDocument:()=>o.Wtk,hasHistory:()=>o.twz,hasJSON:()=>k.Z,hasNavigator:()=>o.w9M,hasOwnProperty:()=>o.v0u,hasWindow:()=>o.Vdv,isArray:()=>o.cyL,isArrayValid:()=>f.wJ,isBeaconsSupported:()=>k.Uf,isBoolean:()=>o.Lmq,isChromium:()=>f.F2,isDate:()=>o.$PY,isDocumentObjectAvailable:()=>f.g8,isError:()=>o.bJ7,isFetchSupported:()=>k.R7,isFunction:()=>o.Tnt,isGreaterThanZero:()=>f.ei,isIE:()=>k.lT,isLatency:()=>f.Hh,isNotTruthy:()=>o.woc,isNullOrUndefined:()=>o.hXl,isNumber:()=>o.EtT,isObject:()=>o.Gvm,isReactNative:()=>k.lV,isSampledFlag:()=>H.N7,isString:()=>o.KgX,isTruthy:()=>o.zzB,isTypeof:()=>o.Edw,isUint8ArrayAvailable:()=>f.h3,isUndefined:()=>o.b07,isValidSpanId:()=>H.wN,isValidTraceId:()=>H.hX,isValidTraceParent:()=>H.mJ,isValueAssigned:()=>f.yD,isValueKind:()=>f.m0,isWindowObjectAvailable:()=>f.P$,isXhrSupported:()=>k.xk,mergeEvtNamespace:()=>O.Hm,newGuid:()=>A.aq,newId:()=>D.Si,normalizeJsName:()=>R.cH,objDefineAccessors:()=>o.raO,objForEachKey:()=>o.zav,objFreeze:()=>o.N6t,objKeys:()=>o.cGk,objSeal:()=>o.jsL,onConfigChange:()=>s.a,openXhr:()=>f.H$,optimizeObject:()=>R.hW,parseResponse:()=>z.x,parseTraceParent:()=>H.ZI,perfNow:()=>o.UUD,prependTransports:()=>R.jL,proxyAssign:()=>R.qz,proxyFunctionAs:()=>R.RF,proxyFunctions:()=>R.o$,random32:()=>D.VN,randomValue:()=>D.Z1,removeEventHandler:()=>O.zh,removeEventListeners:()=>O.Wg,removePageHideEventListener:()=>O.sq,removePageShowEventListener:()=>O.vF,removePageUnloadEventListener:()=>O.Ds,safeGetCookieMgr:()=>M.um,safeGetLogger:()=>u.y0,sanitizeProperty:()=>f.TC,setEnableEnvMocks:()=>k.cU,setProcessTelemetryTimings:()=>f.u9,setValue:()=>R.KY,strContains:()=>R.Ju,strEndsWith:()=>o.Cv9,strFunction:()=>N.hW,strObject:()=>N._1,strPrototype:()=>N.vR,strStartsWith:()=>o.tGl,strTrim:()=>o.EHq,strUndefined:()=>N.bA,throwError:()=>o.$8,toISOString:()=>R._u,useXDomainRequest:()=>k.PV});var r=n(659),i=n(8279),o=n(269),a=n(8156),s=n(9749),u=n(3775),c=n(2774),l=n(937),f=n(4822),d=n(1739),v=(0,o.ZHX)({endpointUrl:l.S,propertyStorageOverride:{isVal:function(e){return!e||e.getProperty&&e.setProperty||(0,o.$8)("Invalid property storage override passed."),!0}}}),h=function(e){function t(){var n=e.call(this)||this;return(0,i.A)(t,n,function(e,t){e[d.mE]=function(n,r,i,c){(0,a.r2)(e,function(){return"AppInsightsCore.initialize"},function(){try{t[d.mE]((0,s.e)(n,v,i||e[d.Uw],!1).cfg,r,i,c)}catch(t){var a=e[d.Uw],l=(0,o.mmD)(t);-1!==l[d.Sj]("channels")&&(l+="\n - Channels must be provided through config.channels only!"),(0,u.ZP)(a,1,514,"SDK Initialization Failed - no telemetry will be sent: "+l)}},function(){return{config:n,extensions:r,logger:i,notificationManager:c}})},e.track=function(n){(0,a.r2)(e,function(){return"AppInsightsCore.track"},function(){var r=n;if(r){r[d.dg]=r[d.dg]||{},r[d.dg].trackStart=(0,f.WB)(),(0,f.Hh)(r.latency)||(r.latency=1);var i=r.ext=r.ext||{};i.sdk=i.sdk||{},i.sdk.ver=f.xE;var o=r.baseData=r.baseData||{};o[l._0]=o[l._0]||{};var a=o[l._0];a[l.hj]=a[l.hj]||e.pluginVersionString||l.m5}t.track(r)},function(){return{item:n}},!n.sync)},e[d.h4]=function(e){return t[d.h4](e||"InternalLog")}}),n}return(0,r.qU)(t,e),t.__ieDyn=1,t}(c._),p=n(4282),g=(0,p.H)({NotSet:0,Pii_DistinguishedName:1,Pii_GenericData:2,Pii_IPV4Address:3,Pii_IPv6Address:4,Pii_MailSubject:5,Pii_PhoneNumber:6,Pii_QueryString:7,Pii_SipAddress:8,Pii_SmtpAddress:9,Pii_Identity:10,Pii_Uri:11,Pii_Fqdn:12,Pii_IPV4AddressLegacy:13,Pii_IPv6ScrubLastHextets:14,Pii_DropValue:15,CustomerContent_GenericContent:32}),m=(0,p.H)({Normal:1,CostDeferred:2,RealTime:3,Immediate:4}),y=(0,p.H)({Unspecified:0,String:1,Int32:2,UInt32:3,Int64:4,UInt64:5,Double:6,Bool:7,Guid:8,DateTime:9}),b=(0,p.H)({Normal:1,Critical:2}),w=(0,p.H)({NONE:0,ERROR:1,WARNING:2,INFORMATION:3}),S=function(){function e(e){var t=this,n={},r=[],i=[];function a(e,t){var a,s=n[e];if(s&&(a=s[t]),!a&&null!==a){if((0,o.KgX)(e)&&(0,o.KgX)(t))if(i[d.oI]>0){for(var u=0;u<i[d.oI];u++)if(i[u][d.hF](e,t)){a={canHandle:!0,fieldHandler:i[u]};break}}else 0===r[d.oI]&&(a={canHandle:!0});if(!a&&null!==a)for(a=null,u=0;u<r[d.oI];u++)if(r[u][d.hF](e,t)){a={canHandle:!0,handler:r[u],fieldHandler:null};break}s||(s=n[e]={}),s[t]=a}return a}function s(e,t,n,r,i,a){if(e.handler)return e.handler.property(t,n,i,a);if(!(0,o.hXl)(i[d.QV])){if(!(4096&~r&&(0,f.m0)(i[d.QV])))return null;i[d.pF]=i[d.pF].toString()}return c(e.fieldHandler,t,n,r,i)}function u(e,t,n){return(0,f.yD)(n)?{value:n}:null}function c(e,n,r,i,a){if(a&&e){var s=e.getSanitizer(n,r,i,a[d.QV],a.propertyType);if(s)if(4===i){var l={},v=a[d.pF];(0,o.zav)(v,function(t,i){var o=n+"."+r;if((0,f.yD)(i)){var a=u(0,0,i);(a=c(e,o,t,(0,f.cq)(i),a))&&(l[t]=a[d.pF])}}),a[d.pF]=l}else{var h={path:n,name:r,type:i,prop:a,sanitizer:t};a=s.call(t,h)}}return a}e&&i.push(e),t.clearCache=function(){n={}},t.addSanitizer=function(e){e&&((0,o.Nq2)(r,e)||r.push(e),n={})},t.addFieldSanitizer=function(e){e&&((0,o.Nq2)(i,e)||i.push(e),n={})},t[d.Rl]=function(e){if(e){var t=(0,o.rDm)(r,e);-1!==t&&(r.splice(t,1),n={}),(0,o.Iuo)(r,function(t){t&&t[d.Rl]&&t[d.Rl](e)})}},t[d.Mr]=function(e){if(e){var t=(0,o.rDm)(i,e);-1!==t&&(i.splice(t,1),n={}),(0,o.Iuo)(r,function(t){t&&t[d.Mr]&&t[d.Mr](e)})}},t.isEmpty=function(){return(0,o.R3R)(r)+(0,o.R3R)(i)===0},t[d.hF]=function(e,t){var n=a(e,t);return!!n&&n[d.nw]},t[d.pF]=function(e,t,n,r){var i=a(e,t);if(i&&i[d.nw]){if(!i||!i[d.nw])return null;if(i.handler)return i.handler[d.pF](e,t,n,r);if(!(0,o.KgX)(t)||(0,o.hXl)(n)||n===l.m5)return null;var c=null,v=(0,f.cq)(n);if(8192&~v)1!==v&&2!==v&&3!==v&&4096&~v?4===v&&(c=u(0,0,r?JSON.stringify(n):n)):c=u(0,0,n);else{var h=-8193&v;if(c=n,!(0,f.yD)(c[d.pF])||1!==h&&2!==h&&3!==h&&4096&~h)return null}if(c)return s(i,e,t,v,c,r)}return null},t.property=function(e,t,n,r){var i=a(e,t);if(!i||!i[d.nw])return null;if(!(0,o.KgX)(t)||(0,o.hXl)(n)||!(0,f.yD)(n[d.pF]))return null;var u=(0,f.cq)(n[d.pF]);return 0===u?null:s(i,e,t,u,n,r)}}return e.getFieldType=f.cq,e}(),_=100,I=(0,p.H)({DISABLED:0,CRITICAL:1,WARNING:2,DEBUG:3}),E=n(1356),P=n(8257),T=n(2317),C=n(3662),x=n(4875),O=n(6149),R=n(3673),A=n(9882),D=n(6535),k=n(7292),N=n(5664),M=n(5034),L=n(4276),U=n(836),H=n(1864),F=n(9147),j=n(856),z=n(1190),X=n(8969)},991:(e,t,n)=>{n.d(t,{q:()=>u});var r=n(269),i=n(6182);function o(e){return e&&(0,r.Gvm)(e)&&(e.isVal||e.fb||(0,r.KhI)(e,"v")||(0,r.KhI)(e,"mrg")||(0,r.KhI)(e,"ref")||e.set)}function a(e,t,n){var o,a=n.dfVal||r.O9V;if(t&&n.fb){var s=n.fb;(0,r.cyL)(s)||(s=[s]);for(var u=0;u<s[i.oI];u++){var c=s[u],l=t[c];if(a(l)?o=l:e&&(a(l=e.cfg[c])&&(o=l),e.set(e.cfg,(0,r.oJg)(c),l)),a(o))break}}return!a(o)&&a(n.v)&&(o=n.v),o}function s(e,t,n){var u,c=n;return n&&o(n)&&(c=a(e,t,n)),c&&(o(c)&&(c=s(e,t,c)),(0,r.cyL)(c)?(u=[])[i.oI]=c[i.oI]:(0,r.QdQ)(c)&&(u={}),u&&((0,r.zav)(c,function(n,r){r&&o(r)&&(r=s(e,t,r)),u[n]=r}),c=u)),c}function u(e,t,n,c){var l,f,d,v,h,p,g,m,y=c;o(y)?(l=y.isVal,f=y.set,p=y[i.XW],g=y[i.JQ],v=y.mrg,!(h=y.ref)&&(0,r.b07)(h)&&(h=!!v),d=a(e,t,y)):d=c,g&&e[i.JQ](t,n);var b=!0,w=t[n];!w&&(0,r.hXl)(w)||(m=w,b=!1,l&&m!==d&&!l(m)&&(m=d,b=!0),f&&(b=(m=f(m,d,t))===d)),b?m=d?s(e,t,d):d:((0,r.QdQ)(m)||(0,r.cyL)(d))&&v&&d&&((0,r.QdQ)(d)||(0,r.cyL)(d))&&(0,r.zav)(d,function(t,n){u(e,m,t,n)}),e.set(t,n,m),h&&e.ref(t,n),p&&e[i.XW](t,n)}},1062:(e,t,n)=>{n.d(t,{L:()=>s});var r=n(3673),i=n(5025),o=n(5130),a=n(7975),s=function(e,t,n){var s=this,u=this;u.ver=1,u.sampleRate=100,u.tags={},u[o.RS]=(0,a.Rr)(e,n)||i.R2,u.data=t,u.time=(0,r._u)(new Date),u.aiDataContract={time:1,iKey:1,name:1,sampleRate:function(){return 100===s.sampleRate?4:1},tags:1,data:1}}},1170:function(e,t,n){var r=this&&this.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n);var i=Object.getOwnPropertyDescriptor(t,n);i&&!("get"in i?!t.__esModule:i.writable||i.configurable)||(i={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,i)}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),i=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),o=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)"default"!==n&&Object.prototype.hasOwnProperty.call(e,n)&&r(t,e,n);return i(t,e),t};Object.defineProperty(t,"__esModule",{value:!0});const a=o(n(5692)),s=o(n(857)),u=o(n(1398)),c=n(5396),l=n(2468),f=n(6548),d=n(2396),v=n(8393);function h(){return{sendPOST:(e,t)=>{const n={method:"POST",headers:{...e.headers,"Content-Type":"application/json","Content-Length":Buffer.byteLength(e.data)}};try{const r=a.request(e.urlString,n,e=>{e.on("data",function(n){t(e.statusCode??200,e.headers,n.toString())}),e.on("error",function(){t(0,{})})});r.write(e.data,e=>{e&&t(0,{})}),r.end()}catch{t(0,{})}}}}class p extends f.BaseTelemetryReporter{constructor(e,t){let n=e=>(0,l.appInsightsClientFactory)(e,u.env.machineId,u.env.sessionId,h(),t);v.TelemetryUtil.shouldUseOneDataSystemSDK(e)&&(n=e=>(0,c.oneDataSystemClientFactory)(e,u,h()));const r={release:s.release(),platform:s.platform(),architecture:s.arch()},i=new d.BaseTelemetrySender(e,n);if(e&&0===e.indexOf("AIF-"))throw new Error("AIF keys are no longer supported. Please switch to 1DS keys for 1st party extensions");super(i,u,{additionalCommonProperties:v.TelemetryUtil.getAdditionalCommonProperties(r)})}}t.default=p},1190:(e,t,n)=>{n.d(t,{x:()=>s});var r=n(269),i=n(6182),o=n(3775),a=n(7292);function s(e,t){try{if(e&&""!==e){var n=(0,a.hm)().parse(e);if(n&&n[i.cp]&&n[i.cp]>=n.itemsAccepted&&n.itemsReceived-n.itemsAccepted===n.errors[i.oI])return n}}catch(n){(0,o.ZP)(t,1,43,"Cannot parse the response. "+(n[i.RS]||(0,r.mmD)(n)),{response:e})}return null}},1354:function(e,t,n){var r,i=this&&this.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n);var i=Object.getOwnPropertyDescriptor(t,n);i&&!("get"in i?!t.__esModule:i.writable||i.configurable)||(i={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,i)}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),o=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),a=this&&this.__importStar||(r=function(e){return r=Object.getOwnPropertyNames||function(e){var t=[];for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[t.length]=n);return t},r(e)},function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n=r(e),a=0;a<n.length;a++)"default"!==n[a]&&i(t,e,n[a]);return o(t,e),t}),s=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.GitHubAuthenticationProvider=t.UriEventHandler=t.AuthProviderType=void 0;const u=a(n(1398)),c=s(n(1170)),l=n(295),f=n(7921),d=n(7066),v=n(6208),h=n(4947),p=n(4577),g=n(928),m=n(3772);var y;!function(e){e.github="github",e.githubEnterprise="github-enterprise"}(y||(t.AuthProviderType=y={}));class b extends u.EventEmitter{constructor(){super(...arguments),this._pendingNonces=new Map,this._codeExchangePromises=new Map,this.handleEvent=(e,t)=>(n,r,i)=>{const o=new URLSearchParams(n.query),a=o.get("code"),s=o.get("nonce");a?s?(this._pendingNonces.get(t)||[]).includes(s)?r(a):e.info("Nonce not found in accepted nonces. Skipping this execution..."):i(new Error("No nonce")):i(new Error("No code"))}}handleUri(e){this.fire(e)}async waitForCode(e,t,n,r){const i=this._pendingNonces.get(t)||[];this._pendingNonces.set(t,[...i,n]);let o=this._codeExchangePromises.get(t);o||(o=(0,d.promiseFromEvent)(this.event,this.handleEvent(e,t)),this._codeExchangePromises.set(t,o));try{return await Promise.race([o.promise,new Promise((e,t)=>setTimeout(()=>t(g.TIMED_OUT_ERROR),3e5)),(0,d.promiseFromEvent)(r.onCancellationRequested,(e,t,n)=>{n(g.USER_CANCELLATION_ERROR)}).promise])}finally{this._pendingNonces.delete(t),o?.cancel.fire(),this._codeExchangePromises.delete(t)}}}t.UriEventHandler=b,t.GitHubAuthenticationProvider=class{constructor(e,t,n){this.context=e,this._sessionChangeEmitter=new u.EventEmitter,this._accountsSeen=new Set;const{aiKey:r}=e.extension.packageJSON;this._telemetryReporter=new v.ExperimentationTelemetry(e,new c.default(r));const i=n?y.githubEnterprise:y.github;this._logger=new h.Log(i),this._keychain=new l.Keychain(this.context,i===y.github?`${i}.auth`:`${n?.authority}${n?.path}.ghes.auth`,this._logger),this._githubServer=new f.GitHubServer(this._logger,this._telemetryReporter,t,e.extension.extensionKind,n),this._sessionsPromise=this.readSessions().then(e=>(setTimeout(()=>e.forEach(e=>this.afterSessionLoad(e)),1e3),e)),this._disposable=u.Disposable.from(this._telemetryReporter,u.authentication.registerAuthenticationProvider(i,this._githubServer.friendlyName,this,{supportsMultipleAccounts:!0,supportedAuthorizationServers:[n??u.Uri.parse("https://github.com/login/oauth")]}),this.context.secrets.onDidChange(()=>this.checkForUpdates()))}dispose(){this._disposable?.dispose()}get onDidChangeSessions(){return this._sessionChangeEmitter.event}async getSessions(e,t){const n=e?.sort()||[];this._logger.info(`Getting sessions for ${n.length?n.join(","):"all scopes"}...`);const r=await this._sessionsPromise,i=t?.account?r.filter(e=>e.account.label===t.account?.label):r,o=n.length?i.filter(e=>(0,d.arrayEquals)([...e.scopes].sort(),n)):i;return this._logger.info(`Got ${o.length} sessions for ${n?.join(",")??"all scopes"}...`),o}async afterSessionLoad(e){this._accountsSeen.has(e.account.id)||(this._accountsSeen.add(e.account.id),this._githubServer.sendAdditionalTelemetryInfo(e))}async checkForUpdates(){const e=await this._sessionsPromise;this._sessionsPromise=this.readSessions();const t=await this._sessionsPromise,n=[],r=[];t.forEach(t=>{e.some(e=>e.id===t.id)||(this._logger.info("Adding session found in keychain"),n.push(t))}),e.forEach(e=>{t.some(t=>t.id===e.id)||(this._logger.info("Removing session no longer found in keychain"),r.push(e))}),(n.length||r.length)&&this._sessionChangeEmitter.fire({added:n,removed:r,changed:[]})}async readSessions(){let e;try{this._logger.info("Reading sessions from keychain...");const t=await this._keychain.getToken();if(!t)return[];this._logger.info("Got stored sessions!");try{e=JSON.parse(t)}catch(e){throw await this._keychain.deleteToken(),e}}catch(e){return this._logger.error(`Error reading token: ${e}`),[]}let t=!1;const n=new Set,r=e.map(async e=>{const r=[...e.scopes].sort().join(" ");let i,o;if(!e.account)try{i=await this._githubServer.getUserInfo(e.accessToken),this._logger.info(`Verified session with the following scopes: ${r}`)}catch(e){if("Unauthorized"===e.message)return}return this._logger.trace(`Read the following session from the keychain with the following scopes: ${r}`),n.add(r),e.account?.id?("number"==typeof e.account.id&&(t=!0),o=`${e.account.id}`):o=i?.id??"<unknown>",{id:e.id,account:{label:e.account?e.account.label??e.account.displayName??"<unknown>":i?.accountName??"<unknown>",id:o},scopes:e.scopes,accessToken:e.accessToken}}),i=(await Promise.allSettled(r)).filter(e=>"fulfilled"===e.status).map(e=>e.value).filter(e=>Boolean(e));return this._logger.info(`Got ${i.length} verified sessions.`),(t||i.length!==e.length)&&await this.storeSessions(i),i}async storeSessions(e){this._logger.info(`Storing ${e.length} sessions...`),this._sessionsPromise=Promise.resolve(e),await this._keychain.setToken(JSON.stringify(e)),this._logger.info(`Stored ${e.length} sessions!`)}async createSession(e,t){try{const n=[...e].sort();this._telemetryReporter?.sendTelemetryEvent("login",{scopes:JSON.stringify(e)});const r=await this._sessionsPromise,i=t?.account?.label,o=(0,m.isSocialSignInProvider)(t?.provider)?t.provider:void 0;this._logger.info(`Logging in with${o?` ${o}, `:""} '${i||"any"}' account...`);const a=n.join(" "),s=await this._githubServer.login(a,o,i),u=await this.tokenToSession(s,e);this.afterSessionLoad(u);const c=r.findIndex(e=>e.account.id===u.account.id&&(0,d.arrayEquals)([...e.scopes].sort(),n)),l=new Array;return c>-1?l.push(...r.splice(c,1,u)):r.push(u),await this.storeSessions(r),this._sessionChangeEmitter.fire({added:[u],removed:l,changed:[]}),this._logger.info("Login success!"),u}catch(e){if("Cancelled"===e||"Cancelled"===e.message)throw this._telemetryReporter?.sendTelemetryEvent("loginCancelled"),e;throw this._telemetryReporter?.sendTelemetryEvent("loginFailed"),u.window.showErrorMessage(u.l10n.t("Sign in failed: {0}",`${e}`)),this._logger.error(e),e}}async tokenToSession(e,t){const n=await this._githubServer.getUserInfo(e);return{id:p.crypto.getRandomValues(new Uint32Array(2)).reduce((e,t)=>e+t.toString(16),""),accessToken:e,account:{label:n.accountName,id:n.id},scopes:t}}async removeSession(e){try{this._telemetryReporter?.sendTelemetryEvent("logout"),this._logger.info(`Logging out of ${e}`);const t=await this._sessionsPromise,n=t.findIndex(t=>t.id===e);if(n>-1){const e=t[n];t.splice(n,1),await this.storeSessions(t),await this._githubServer.logout(e),this._sessionChangeEmitter.fire({added:[],removed:[e],changed:[]})}else this._logger.error("Session not found")}catch(e){throw this._telemetryReporter?.sendTelemetryEvent("logoutFailed"),u.window.showErrorMessage(u.l10n.t("Sign out failed: {0}",`${e}`)),this._logger.error(e),e}}}},1356:(e,t,n)=>{n.d(t,{h:()=>d});var r=n(8279),i=n(8205),o=n(269),a=n(9749),s=n(6182),u=n(6492),c={perfEvtsSendAll:!1};function l(e){e.h=null;var t=e.cb;e.cb=[],(0,o.Iuo)(t,function(e){(0,o.gBW)(e.fn,[e.arg])})}function f(e,t,n,r){(0,o.Iuo)(e,function(e){e&&e[t]&&(n?(n.cb[s.y5]({fn:r,arg:e}),n.h=n.h||(0,o.dRz)(l,0,n)):(0,o.gBW)(r,[e]))})}var d=function(){function e(t){var n,l;this.listeners=[];var d=[],v={h:null,cb:[]},h=(0,a.e)(t,c);l=h[s.x6](function(e){n=!!e.cfg.perfEvtsSendAll}),(0,r.A)(e,this,function(e){(0,o.vF1)(e,"listeners",{g:function(){return d}}),e[s.vR]=function(e){d[s.y5](e)},e[s.h3]=function(e){for(var t=(0,o.rDm)(d,e);t>-1;)d[s.Ic](t,1),t=(0,o.rDm)(d,e)},e[u.fc]=function(e){f(d,u.fc,v,function(t){t[u.fc](e)})},e[u.Yp]=function(e,t){f(d,u.Yp,v,function(n){n[u.Yp](e,t)})},e[u.dI]=function(e,t){f(d,u.dI,t?v:null,function(n){n[u.dI](e,t)})},e[u.l0]=function(e){e&&(!n&&e[s.Zu]()||f(d,u.l0,null,function(t){e[s.tI]?(0,o.dRz)(function(){return t[u.l0](e)},0):t[u.l0](e)}))},e[u.s4]=function(e){e&&e[s.oI]&&f(d,u.s4,v,function(t){t[u.s4](e)})},e[u.Vj]=function(e){e&&e[s.Cd]&&f(d,u.Vj,v,function(t){t[u.Vj](e)})},e[u.Ev]=function(e,t){if(e>0){var n=t||0;f(d,u.Ev,v,function(t){t[u.Ev](e,n)})}},e[s.M5]=function(e){var t,n=function(){l&&l.rm(),l=null,d=[],v.h&&v.h[s._w](),v.h=null,v.cb=[]};if(f(d,"unload",null,function(n){var r=n[s.M5](e);r&&(t||(t=[]),t[s.y5](r))}),t)return(0,i.Qo)(function(e){return(0,i.Dv)((0,i.Xf)(t),function(){n(),e()})});n()}})}return e.__ieDyn=1,e}()},1365:(e,t,n)=>{n.d(t,{A:()=>s});var r=n(87),i=n(2318),o=n(5130),a=n(7975),s=function(){function e(e,t,n,s,u,c,l,f,d,v,h,p){void 0===d&&(d="Ajax"),this.aiDataContract={id:1,ver:1,name:0,resultCode:0,duration:0,success:0,data:0,target:0,type:0,properties:0,measurements:0,kind:0,value:0,count:0,min:0,max:0,stdDev:0,dependencyKind:0,dependencySource:0,commandName:0,dependencyTypeName:0};var g=this;g.ver=2,g.id=t,g[o.qd]=(0,r.bb)(u),g.success=c,g.resultCode=l+"",g.type=(0,a.Rr)(e,d);var m=(0,i._U)(e,n,f,s);g.data=(0,a.pJ)(e,s)||m.data,g.target=(0,a.Rr)(e,m.target),v&&(g.target="".concat(g.target," | ").concat(v)),g[o.RS]=(0,a.Rr)(e,m[o.RS]),g[o.$y]=(0,a.xP)(e,h),g[o.XA]=(0,a.Vj)(e,p)}return e.envelopeType="Microsoft.ApplicationInsights.{0}.RemoteDependency",e.dataType="RemoteDependencyData",e}()},1398:e=>{e.exports=require("vscode")},1448:(e,t,n)=>{n.d(t,{h:()=>s});var r=n(5025),i=n(87),o=n(5130),a=n(7975),s=function(){function e(e,t,n,s,u,c,l){this.aiDataContract={ver:1,name:0,url:0,duration:0,properties:0,measurements:0,id:0};var f=this;f.ver=2,f.id=(0,a.HQ)(e,l),f.url=(0,a.pJ)(e,n),f[o.RS]=(0,a.Rr)(e,t)||r.R2,isNaN(s)||(f[o.qd]=(0,i.bb)(s)),f[o.$y]=(0,a.xP)(e,u),f[o.XA]=(0,a.Vj)(e,c)}return e.envelopeType="Microsoft.ApplicationInsights.{0}.Pageview",e.dataType="PageviewData",e}()},1566:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.TargetPopulation=t.getExperimentationServiceAsync=t.getExperimentationService=void 0;var r=n(2493);Object.defineProperty(t,"getExperimentationService",{enumerable:!0,get:function(){return r.getExperimentationService}}),Object.defineProperty(t,"getExperimentationServiceAsync",{enumerable:!0,get:function(){return r.getExperimentationServiceAsync}});var i=n(8967);Object.defineProperty(t,"TargetPopulation",{enumerable:!0,get:function(){return i.TargetPopulation}})},1575:(e,t,n)=>{n.d(t,{F:()=>i,O:()=>o});var r=n(8596),i={UserExt:"user",DeviceExt:"device",TraceExt:"trace",WebExt:"web",AppExt:"app",OSExt:"os",SessionExt:"ses",SDKExt:"sdk"},o=new r.o},1589:function(e,t,n){var r,i=this&&this.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n);var i=Object.getOwnPropertyDescriptor(t,n);i&&!("get"in i?!t.__esModule:i.writable||i.configurable)||(i={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,i)}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),o=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),a=this&&this.__importStar||(r=function(e){return r=Object.getOwnPropertyNames||function(e){var t=[];for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[t.length]=n);return t},r(e)},function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n=r(e),a=0;a<n.length;a++)"default"!==n[a]&&i(t,e,n[a]);return o(t,e),t});Object.defineProperty(t,"__esModule",{value:!0}),t.LoopbackAuthServer=void 0;const s=a(n(8611)),u=n(7016),c=a(n(9896)),l=a(n(6928)),f=n(6982),d=n(1398);function v(e,t){const n=t.endsWith(".svg");c.readFile(t,(t,r)=>{t?(console.error(t),e.writeHead(404),e.end()):(n&&e.setHeader("Content-Type","image/svg+xml"),e.setHeader("content-length",r.length),e.writeHead(200),e.end(r))})}t.LoopbackAuthServer=class{set state(e){e?this._startingRedirect.searchParams.set("state",e):this._startingRedirect.searchParams.delete("state")}get state(){return this._startingRedirect.searchParams.get("state")??void 0}constructor(e,t,n){if(this.nonce=(0,f.randomBytes)(16).toString("base64"),!e)throw new Error("serveRoot must be defined");if(!t)throw new Error("startingRedirect must be defined");let r;this._startingRedirect=new u.URL(t),this._resultPromise=new Promise((e,t)=>r={resolve:e,reject:t});const i=`&app_name=${encodeURIComponent(d.env.appName)}`;this._server=s.createServer((t,o)=>{const a=new u.URL(t.url,`http://${t.headers.host}`);switch(a.pathname){case"/signin":(a.searchParams.get("nonce")??"").replace(/ /g,"+")!==this.nonce&&(o.writeHead(302,{location:`/?error=${encodeURIComponent("Nonce does not match.")}${i}`}),o.end()),o.writeHead(302,{location:this._startingRedirect.toString()}),o.end();break;case"/callback":{const e=a.searchParams.get("code")??void 0,t=a.searchParams.get("state")??void 0,s=(a.searchParams.get("nonce")??"").replace(/ /g,"+");if(!e||!t||!s)return o.writeHead(400),void o.end();if(this.state!==t)throw o.writeHead(302,{location:`/?error=${encodeURIComponent("State does not match.")}${i}`}),o.end(),new Error("State does not match.");if(this.nonce!==s)throw o.writeHead(302,{location:`/?error=${encodeURIComponent("Nonce does not match.")}${i}`}),o.end(),new Error("Nonce does not match.");r.resolve({code:e,state:t}),o.writeHead(302,{location:`/?redirect_uri=${encodeURIComponent(n)}${i}`}),o.end();break}case"/":v(o,l.join(e,"index.html"));break;default:v(o,l.join(e,a.pathname.substring(1)))}})}start(){return new Promise((e,t)=>{if(this._server.listening)throw new Error("Server is already started");const n=setTimeout(()=>{t(new Error("Timeout waiting for port"))},5e3);this._server.on("listening",()=>{const t=this._server.address();if("string"==typeof t)this.port=parseInt(t);else{if(!(t instanceof Object))throw new Error("Unable to determine port");this.port=t.port}clearTimeout(n),this.state=`http://127.0.0.1:${this.port}/callback?nonce=${encodeURIComponent(this.nonce)}`,e(this.port)}),this._server.on("error",e=>{t(new Error(`Error listening to server: ${e}`))}),this._server.on("close",()=>{t(new Error("Closed"))}),this._server.listen(0,"127.0.0.1")})}stop(){return new Promise((e,t)=>{if(!this._server.listening)throw new Error("Server is not started");this._server.close(n=>{n?t(n):e()})})}waitForOAuthResponse(){return this._resultPromise}}},1714:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ExperimentationServiceBase=void 0;const r=n(8823);t.ExperimentationServiceBase=class{get features(){return this._features}set features(e){this._features=e,this.telemetry&&this.telemetry.setSharedProperty(this.assignmentContextTelemetryPropertyName,this.features.assignmentContext)}constructor(e,t,n,i,o){this.telemetry=e,this.assignmentContextTelemetryPropertyName=t,this.telemetryEventName=n,this.storageKey=i,this.storage=o,this.featuresConsumed=!1,this.cachedTelemetryEvents=[],this._features={features:[],assignmentContext:"",configs:[]},this.storageKey||(this.storageKey="ABExp.Features"),this.storage||(o=new r.MemoryKeyValueStorage),this.loadCachePromise=this.loadCachedFeatureData(),this.initializePromise=this.loadCachePromise,this.initialFetch=new Promise((e,t)=>{this.resolveInitialFetchPromise=e})}async getFeaturesAsync(e=!1){if(null!=this.fetchPromise){try{await this.fetchPromise}catch(e){}return this.features}if(!this.featureProviders||0===this.featureProviders.length)return Promise.resolve({features:[],assignmentContext:"",configs:[]});try{this.fetchPromise=Promise.all(this.featureProviders.map(async e=>await e.getFeatures()));const t=await this.fetchPromise;this.updateFeatures(t,e)}catch(e){}return this.fetchPromise=void 0,this.resolveInitialFetchPromise&&(this.resolveInitialFetchPromise(),this.resolveInitialFetchPromise=void 0),this.features}updateFeatures(e,t=!1){let n={features:[],assignmentContext:"",configs:[]};for(let t of e){for(let e of t.features)n.features.includes(e)||n.features.push(e);for(let e of t.configs){const t=n.configs.find(t=>t.Id===e.Id);t?t.Parameters=Object.assign(Object.assign({},t.Parameters),e.Parameters):n.configs.push(e)}n.assignmentContext+=t.assignmentContext}!t&&this.featuresConsumed||(this.features=n),this.storage&&this.storage.setValue(this.storageKey,n)}async loadCachedFeatureData(){let e;this.storage&&(e=await this.storage.getValue(this.storageKey),void 0!==e&&void 0===e.configs&&(e.configs=[])),0===this.features.features.length&&(this.features=e||{features:[],assignmentContext:"",configs:[]})}isFlightEnabled(e){return this.featuresConsumed=!0,this.PostEventToTelemetry(e),this.features.features.includes(e)}async isCachedFlightEnabled(e){return await this.loadCachePromise,this.featuresConsumed=!0,this.PostEventToTelemetry(e),this.features.features.includes(e)}async isFlightEnabledAsync(e){const t=await this.getFeaturesAsync(!0);return this.featuresConsumed=!0,this.PostEventToTelemetry(e),t.features.includes(e)}getTreatmentVariable(e,t){this.featuresConsumed=!0,this.PostEventToTelemetry(`${e}.${t}`);const n=this.features.configs.find(t=>t.Id===e);return null==n?void 0:n.Parameters[t]}async getTreatmentVariableAsync(e,t,n){if(n){const n=this.featuresConsumed,r=this.getTreatmentVariable(e,t);if(void 0!==r)return r;this.featuresConsumed=n}return await this.getFeaturesAsync(!0),this.getTreatmentVariable(e,t)}PostEventToTelemetry(e){this.cachedTelemetryEvents.includes(e)||(this.telemetry.postEvent(this.telemetryEventName,new Map([["ABExp.queriedFeature",e]])),this.cachedTelemetryEvents.push(e))}invokeInit(){this.init()}addFeatureProvider(...e){if(null!=e&&null!=this.featureProviders)for(let t of e)this.featureProviders.push(t)}}},1739:(e,t,n)=>{n.d(t,{Jg:()=>f,Mr:()=>h,QV:()=>c,Rl:()=>v,Sj:()=>o,Uw:()=>i,dg:()=>a,h4:()=>s,hF:()=>d,mE:()=>r,nw:()=>p,oI:()=>l,pF:()=>u});var r="initialize",i="logger",o="indexOf",a="timings",s="pollInternalLogs",u="value",c="kind",l="length",f="processTelemetryStart",d="handleField",v="rmSanitizer",h="rmFieldSanitizer",p="canHandle"},1864:(e,t,n)=>{n.d(t,{L0:()=>I,N7:()=>_,V5:()=>P,ZI:()=>y,ef:()=>E,hX:()=>b,mJ:()=>S,wN:()=>w,wk:()=>m});var r=n(269),i=n(6182),o=n(9882),a=n(7292),s=n(6492),u=/^([\da-f]{2})-([\da-f]{32})-([\da-f]{16})-([\da-f]{2})(-[^\s]{1,64})?$/i,c="00",l="ff",f="00000000000000000000000000000000",d="0000000000000000",v=1;function h(e,t,n){return!(!e||e[i.oI]!==t||e===n||!e.match(/^[\da-f]*$/i))}function p(e,t,n){return h(e,t)?e:n}function g(e){(isNaN(e)||e<0||e>255)&&(e=1);for(var t=e.toString(16);t[i.oI]<2;)t="0"+t;return t}function m(e,t,n,a){var s;return(s={})[i.s]=h(a,2,l)?a:c,s[i.P5]=b(e)?e:(0,o.cL)(),s[i.wi]=w(t)?t:(0,r.ZWZ)((0,o.cL)(),16),s.traceFlags=n>=0&&n<=255?n:1,s}function y(e,t){var n;if(!e)return null;if((0,r.cyL)(e)&&(e=e[0]||""),!e||!(0,r.KgX)(e)||e[i.oI]>8192)return null;if(-1!==e.indexOf(",")){var o=e[i.sY](",");e=o[t>0&&o[i.oI]>t?t:0]}var a=u.exec((0,r.EHq)(e));return a&&a[1]!==l&&a[2]!==f&&a[3]!==d?((n={version:(a[1]||s.m5)[i.OL](),traceId:(a[2]||s.m5)[i.OL](),spanId:(a[3]||s.m5)[i.OL]()})[i.Rr]=parseInt(a[4],16),n):null}function b(e){return h(e,32,f)}function w(e){return h(e,16,d)}function S(e){return!!(e&&h(e[i.s],2,l)&&h(e[i.P5],32,f)&&h(e[i.wi],16,d)&&h(g(e[i.Rr]),2))}function _(e){return!!S(e)&&(e[i.Rr]&v)===v}function I(e){if(e){var t=g(e[i.Rr]);h(t,2)||(t="01");var n=e[i.s]||c;return"00"!==n&&"ff"!==n&&(n=c),"".concat(n.toLowerCase(),"-").concat(p(e.traceId,32,f).toLowerCase(),"-").concat(p(e.spanId,16,d).toLowerCase(),"-").concat(t.toLowerCase())}return""}function E(e){var t="traceparent",n=y((0,a.$Z)(t),e);return n||(n=y((0,a.Iu)(t),e)),n}function P(e){var t=e.getElementsByTagName("script"),n=[];return(0,r.Iuo)(t,function(e){var t=e[i.NA]("src");if(t){var r=e[i.NA]("crossorigin"),o=!0===e.hasAttribute("async"),a=!0===e.hasAttribute("defer"),s=e[i.NA]("referrerpolicy"),u={url:t};r&&(u.crossOrigin=r),o&&(u.async=o),a&&(u.defer=a),s&&(u.referrerPolicy=s),n[i.y5](u)}}),n}},1924:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.HttpClient=t.FetchError=void 0;class n extends Error{constructor(e,t,n){super(e),this.responseReceived=t,this.responseOk=n}}t.FetchError=n,t.HttpClient=class{constructor(e){this.endpoint=e}async get(e){const t=await fetch(this.endpoint,{method:"GET",headers:null==e?void 0:e.headers});if(!t)throw new n("No response received",!1);if(!t.ok)throw new n("Response not ok",!0,!1);const r=await t.json();if(!r)throw new n("No data received",!1);return{data:r}}}},2317:(e,t,n)=>{n.d(t,{PV:()=>w,W0:()=>S,i8:()=>m,nU:()=>b,tS:()=>y});var r=n(269),i=n(991),o=n(9749),a=n(6182),s=n(3775),u=n(3673),c=n(6492),l=n(8156),f=n(380),d="TelemetryPluginChain",v="_hasRun",h="_getTelCtx",p=0;function g(e,t,n,u){var l=null,f=[];t||(t=(0,o.e)({},null,n[a.Uw])),null!==u&&(l=u?function(e,t,n){for(;e;){if(e[a.AP]()===n)return e;e=e[a.uR]()}return w([n],t.config||{},t)}(e,n,u):e);var d={_next:function(){var e=l;if(l=e?e[a.uR]():null,!e){var t=f;t&&t[a.oI]>0&&((0,r.Iuo)(t,function(e){try{e.func.call(e.self,e.args)}catch(e){(0,s.ZP)(n[a.Uw],2,73,"Unexpected Exception during onComplete - "+(0,r.mmD)(e))}}),f=[])}return e},ctx:{core:function(){return n},diagLog:function(){return(0,s.y0)(n,t.cfg)},getCfg:function(){return t.cfg},getExtCfg:function(e,n){var o=v(e,!0);return n&&(0,r.zav)(n,function(e,n){if((0,r.hXl)(o[e])){var a=t.cfg[e];!a&&(0,r.hXl)(a)||(o[e]=a)}(0,i.q)(t,o,e,n)}),t[a.h0](o,n)},getConfig:function(e,n,i){void 0===i&&(i=!1);var o,a=v(e,!1),s=t.cfg;return!a||!a[n]&&(0,r.hXl)(a[n])?!s[n]&&(0,r.hXl)(s[n])||(o=s[n]):o=a[n],o||!(0,r.hXl)(o)?o:i},hasNext:function(){return!!l},getNext:function(){return l},setNext:function(e){l=e},iterate:function(e){for(var t;t=d._next();){var n=t[a.AP]();n&&e(n)}},onComplete:function(e,t){for(var n=[],i=2;i<arguments.length;i++)n[i-2]=arguments[i];e&&f[a.y5]({func:e,self:(0,r.b07)(t)?d.ctx:t,args:n})}}};function v(e,n){var r=null,i=t.cfg;if(i&&e){var o=i[c.Bw];!o&&n&&(o={}),i[c.Bw]=o,(o=t.ref(i,c.Bw))&&(!(r=o[e])&&n&&(r={}),o[e]=r,r=t.ref(o,e))}return r}return d}function m(e,t,n,i){var s=(0,o.e)(t),u=g(e,s,n,i),l=u.ctx;return l[a.$5]=function(e){var t=u._next();return t&&t[c.qT](e,l),!t},l[a.$o]=function(e,t){return void 0===e&&(e=null),(0,r.cyL)(e)&&(e=w(e,s.cfg,n,t)),m(e||l[a.uR](),s.cfg,n,t)},l}function y(e,t,n){var i=(0,o.e)(t.config),s=g(e,i,t,n),u=s.ctx;return u[a.$5]=function(e){var t=s._next();return t&&t[a.M5](u,e),!t},u[a.$o]=function(e,n){return void 0===e&&(e=null),(0,r.cyL)(e)&&(e=w(e,i.cfg,t,n)),y(e||u[a.uR](),t,n)},u}function b(e,t,n){var i=(0,o.e)(t.config),s=g(e,i,t,n).ctx;return s[a.$5]=function(e){return s.iterate(function(t){(0,r.Tnt)(t[a.HC])&&t[a.HC](s,e)})},s[a.$o]=function(e,n){return void 0===e&&(e=null),(0,r.cyL)(e)&&(e=w(e,i.cfg,t,n)),b(e||s[a.uR](),t,n)},s}function w(e,t,n,i){var o=null,u=!i;if((0,r.cyL)(e)&&e[a.oI]>0){var g=null;(0,r.Iuo)(e,function(e){if(u||i!==e||(u=!0),u&&e&&(0,r.Tnt)(e[c.qT])){var y=function(e,t,n){var i,o=null,u=(0,r.Tnt)(e[c.qT]),g=(0,r.Tnt)(e[a.YH]),y={getPlugin:function(){return e},getNext:function(){return o},processTelemetry:function(i,s){b(s=s||function(){var i;return e&&(0,r.Tnt)(e[h])&&(i=e[h]()),i||(i=m(y,t,n)),i}(),function(t){if(!e||!u)return!1;var n=(0,f.Cr)(e);return!n[a.Ik]&&!n[c.Hr]&&(g&&e[a.YH](o),e[c.qT](i,t),!0)},"processTelemetry",function(){return{item:i}},!i.sync)||s[a.$5](i)},unload:function(t,n){b(t,function(){var r=!1;if(e){var i=(0,f.Cr)(e),o=e[c.eT]||i[c.eT];!e||o&&o!==t.core()||i[a.Ik]||(i[c.eT]=null,i[a.Ik]=!0,i[a.tZ]=!1,e[a.Ik]&&!0===e[a.Ik](t,n)&&(r=!0))}return r},"unload",function(){},n[a.tI])||t[a.$5](n)},update:function(t,n){b(t,function(){var r=!1;if(e){var i=(0,f.Cr)(e),o=e[c.eT]||i[c.eT];!e||o&&o!==t.core()||i[a.Ik]||e[a.HC]&&!0===e[a.HC](t,n)&&(r=!0)}return r},"update",function(){},!1)||t[a.$5](n)},_id:i=e?e[a.Ju]+"-"+e[c.Vo]+"-"+p++:"Unknown-0-"+p++,_setNext:function(e){o=e}};function b(t,n,u,f,h){var p=!1,g=e?e[a.Ju]:d,m=t[v];return m||(m=t[v]={}),t.setNext(o),e&&(0,l.r2)(t[c.eT](),function(){return g+":"+u},function(){m[i]=!0;try{var e=o?o._id:c.m5;e&&(m[e]=!1),p=n(t)}catch(e){var l=!o||m[o._id];l&&(p=!0),o&&l||(0,s.ZP)(t[a.e4](),1,73,"Plugin ["+g+"] failed during "+u+" - "+(0,r.mmD)(e)+", run flags: "+(0,r.mmD)(m))}},f,h),p}return(0,r.N6t)(y)}(e,t,n);o||(o=y),g&&g._setNext(y),g=y}})}return i&&!o?w([i],t,n):o}var S=function(e,t,n,i){var o=m(e,t,n,i);(0,u.o$)(this,o,(0,r.cGk)(o))}},2318:(e,t,n)=>{n.d(t,{Ft:()=>S,Qu:()=>d,Rs:()=>p,Wt:()=>v,_U:()=>y,jj:()=>w,lt:()=>b,mD:()=>m,mp:()=>h,pg:()=>g});var r=n(269),i=n(1864),o=n(5025),a=n(2910),s=n(7975),u=n(9354),c=n(5130),l=[o._G+o.wc,"https://breeze.aimon.applicationinsights.io"+o.wc,"https://dc-int.services.visualstudio.com"+o.wc],f="cid-v1:";function d(e){return-1!==(0,r.rDm)(l,e[c.OL]())}function v(e){f=e}function h(){return f}function p(e,t,n){if(!t||e&&e.disableCorrelationHeaders)return!1;if(e&&e[c.Ol])for(var i=0;i<e.correlationHeaderExcludePatterns[c.oI];i++)if(e[c.Ol][i].test(t))return!1;var o=(0,u.cM)(t).host[c.OL]();if(!o||-1===(0,r.HzD)(o,":443")&&-1===(0,r.HzD)(o,":80")||(o=((0,u.M0)(t,!0)||"")[c.OL]()),(!e||!e.enableCorsCorrelation)&&o&&o!==n)return!1;var a,s=e&&e.correlationHeaderDomains;if(s&&((0,r.Iuo)(s,function(e){var t=new RegExp(e.toLowerCase().replace(/\\/g,"\\\\").replace(/\./g,"\\.").replace(/\*/g,".*"));a=a||t.test(o)}),!a))return!1;var l=e&&e.correlationHeaderExcludedDomains;if(!l||0===l[c.oI])return!0;for(i=0;i<l[c.oI];i++)if(new RegExp(l[i].toLowerCase().replace(/\\/g,"\\\\").replace(/\./g,"\\.").replace(/\*/g,".*")).test(o))return!1;return o&&o[c.oI]>0}function g(e){if(e){var t=m(e,a.a[1]);if(t&&t!==f)return t}}function m(e,t){if(e)for(var n=e[c.sY](","),r=0;r<n[c.oI];++r){var i=n[r][c.sY]("=");if(2===i[c.oI]&&i[0]===t)return i[1]}}function y(e,t,n,r){var i,o=r,a=r;if(t&&t[c.oI]>0){var l=(0,u.cM)(t);if(i=l.host,!o)if(null!=l[c.Ue]){var f=0===l.pathname[c.oI]?"/":l[c.Ue];"/"!==f.charAt(0)&&(f="/"+f),a=l[c.Ue],o=(0,s.Rr)(e,n?n+" "+f:f)}else o=(0,s.Rr)(e,t)}else i=r,o=r;return{target:i,name:o,data:a}}function b(){var e=(0,r.FJj)();if(e&&e.now&&e.timing){var t=e.now()+e.timing.navigationStart;if(t>0)return t}return(0,r.f0d)()}function w(e,t){var n=null;return 0===e||0===t||(0,r.hXl)(e)||(0,r.hXl)(t)||(n=t-e),n}function S(e,t){var n=e||{};return{getName:function(){return n[c.RS]},setName:function(e){t&&t.setName(e),n[c.RS]=e},getTraceId:function(){return n.traceID},setTraceId:function(e){t&&t.setTraceId(e),(0,i.hX)(e)&&(n.traceID=e)},getSpanId:function(){return n.parentID},setSpanId:function(e){t&&t.setSpanId(e),(0,i.wN)(e)&&(n.parentID=e)},getTraceFlags:function(){return n.traceFlags},setTraceFlags:function(e){t&&t.setTraceFlags(e),n.traceFlags=e}}}},2396:(e,t)=>{var n;Object.defineProperty(t,"__esModule",{value:!0}),t.BaseTelemetrySender=void 0,function(e){e[e.NOT_INSTANTIATED=0]="NOT_INSTANTIATED",e[e.INSTANTIATING=1]="INSTANTIATING",e[e.INSTANTIATED=2]="INSTANTIATED"}(n||(n={})),t.BaseTelemetrySender=class{constructor(e,t){this._instantiationStatus=n.NOT_INSTANTIATED,this._eventQueue=[],this._exceptionQueue=[],this._clientFactory=t,this._key=e}sendEventData(e,t){this._telemetryClient?this._telemetryClient.logEvent(e,t):this._instantiationStatus!==n.INSTANTIATED&&this._eventQueue.push({eventName:e,data:t})}sendErrorData(e,t){if(!this._telemetryClient)return void(this._instantiationStatus!==n.INSTANTIATED&&this._exceptionQueue.push({exception:e,data:t}));const r={stack:e.stack,message:e.message,name:e.name};if(t){const e=t.properties||t;t.properties={...e,...r}}else t={properties:r};this._telemetryClient.logEvent("unhandlederror",t)}async flush(){return this._telemetryClient?.flush()}async dispose(){this._telemetryClient&&(await this._telemetryClient.dispose(),this._telemetryClient=void 0)}_flushQueues(){this._eventQueue.forEach(({eventName:e,data:t})=>this.sendEventData(e,t)),this._eventQueue=[],this._exceptionQueue.forEach(({exception:e,data:t})=>this.sendErrorData(e,t)),this._exceptionQueue=[]}instantiateSender(){this._instantiationStatus===n.NOT_INSTANTIATED&&(this._instantiationStatus=n.INSTANTIATING,this._clientFactory(this._key).then(e=>{this._telemetryClient=e,this._instantiationStatus=n.INSTANTIATED,this._flushQueues()}).catch(e=>{console.error(e),this._instantiationStatus=n.INSTANTIATED}))}}},2445:(e,t,n)=>{n.d(t,{C:()=>a});var r=n(5025),i=n(5130),o=n(7975),a=function(){function e(e,t,n,a,s){this.aiDataContract={ver:1,message:1,severityLevel:0,properties:0};var u=this;u.ver=2,t=t||r.R2,u[i.pM]=(0,o.Vk)(e,t),u[i.$y]=(0,o.xP)(e,a),u[i.XA]=(0,o.Vj)(e,s),n&&(u[i.Ur]=n)}return e.envelopeType="Microsoft.ApplicationInsights.{0}.Message",e.dataType="MessageData",e}()},2468:function(e,t,n){var r=this&&this.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n);var i=Object.getOwnPropertyDescriptor(t,n);i&&!("get"in i?!t.__esModule:i.writable||i.configurable)||(i={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,i)}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),i=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),o=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)"default"!==n&&Object.prototype.hasOwnProperty.call(e,n)&&r(t,e,n);return i(t,e),t};Object.defineProperty(t,"__esModule",{value:!0}),t.appInsightsClientFactory=void 0;const a=n(740),s=n(8393);t.appInsightsClientFactory=async(e,t,r,i,u)=>{let c;try{const t=await Promise.resolve().then(()=>o(n(5927))),r={};if(i){const e={alwaysUseXhrOverride:!0,httpXHROverride:i};r[a.BreezeChannelIdentifier]=e}let s;e.startsWith("InstrumentationKey=")||(s=e);const u=s?{instrumentationKey:s}:{connectionString:e};c=new t.ApplicationInsights({...u,disableAjaxTracking:!0,disableExceptionTracking:!0,disableFetchTracking:!0,disableCorrelationHeaders:!0,disableCookiesUsage:!0,autoTrackPageVisitTime:!1,emitLineDelimitedJson:!1,disableInstrumentationKeyValidation:!0,extensionConfig:r})}catch(e){return Promise.reject(e)}return{logEvent:(e,n)=>{const i={...n?.properties,...n?.measurements};u?.length&&s.TelemetryUtil.applyReplacements(i,u),c?.track({name:e,data:i,baseType:"EventData",ext:{user:{id:t,authId:t},app:{sesId:r}},baseData:{name:e,properties:n?.properties,measurements:n?.measurements}})},flush:async()=>{c?.flush(!1)},dispose:async()=>new Promise(e=>{c?.unload(!0,()=>{e(),c=void 0},1e3)})}}},2475:(e,t,n)=>{n.d(t,{DD:()=>u,Lx:()=>s,NU:()=>a});var r=n(269),i=n(6182);function o(e,t,n){return!e&&(0,r.hXl)(e)?t:(0,r.Lmq)(e)?e:"true"===(0,r.oJg)(e)[i.OL]()}function a(e){return{mrg:!0,v:e}}function s(e,t,n){return{fb:n,isVal:e,v:t}}function u(e,t){return{fb:t,set:o,v:!!e}}},2493:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.getExperimentationServiceAsync=t.getExperimentationService=void 0;const r=n(8967),i=n(2909),o=n(1398),a=n(8142),s=n(9052);function u(e,t,n,u,c,...l){if(!c)throw new Error("Memento storage was not provided.");const f=o.workspace.getConfiguration("telemetry");if(!(void 0===o.env.isTelemetryEnabled?f.get("enableTelemetry",!0):o.env.isTelemetryEnabled))return new s.default;const d=[new r.VSCodeFilterProvider(e,t,n),...l],v=new a.MementoKeyValueStorage(c);return new i.ExperimentationService({filterProviders:d,telemetry:u,storageKey:"VSCode.ABExp.FeatureData",keyValueStorage:v,featuresTelemetryPropertyName:"",assignmentContextTelemetryPropertyName:"abexp.assignmentcontext",telemetryEventName:"query-expfeature",endpoint:"https://default.exp-tas.com/vscode/ab",refetchInterval:18e5})}t.getExperimentationService=u,t.getExperimentationServiceAsync=async function(e,t,n,r,i,...o){const a=u(e,t,n,r,i,...o);return await a.initializePromise,a}},2774:(e,t,n)=>{n.d(t,{_:()=>k});var r,i=n(659),o=n(8279),a=n(8205),s=n(269),u=n(9749),c=n(4875),l=n(6182),f=n(4013),d=n(7847),v=n(5034),h=n(4276),p=n(7867),g=n(3775),m=n(3673),y=n(6492),b=n(1356),w=n(8156),S=n(2317),_=n(380),I=function(e){function t(){var n,r,i=e.call(this)||this;function a(){n=0,r=[]}return i.identifier="TelemetryInitializerPlugin",i.priority=199,a(),(0,o.A)(t,i,function(e,t){e.addTelemetryInitializer=function(e){return function(e,t,n){var r={id:t,fn:n};return(0,s.Yny)(e,r),{remove:function(){(0,s.Iuo)(e,function(t,n){if(t.id===r.id)return e[l.Ic](n,1),-1})}}}(r,n++,e)},e[y.qT]=function(t,n){(function(e,t,n){for(var r=!1,i=e[l.oI],o=0;o<i;++o){var a=e[o];if(a)try{if(!1===a.fn[l.y9](null,[t])){r=!0;break}}catch(e){(0,g.ZP)(n,2,64,"Telemetry initializer failed: "+(0,m.lL)(e),{exception:(0,s.mmD)(e)},!0)}}return!r})(r,t,n?n[l.e4]():e[l.e4]())&&e[l.$5](t,n)},e[l.tn]=function(){a()}}),i}return(0,i.qU)(t,e),t.__ieDyn=1,t}(n(8257).s),E=n(836),P=n(8969),T="Plugins must provide initialize method",C="SDK is still unloading...",x=(0,s.ZHX)(((r={cookieCfg:{}})[y.jy]={rdOnly:!0,ref:!0,v:[]},r[y.LZ]={rdOnly:!0,ref:!0,v:[]},r[y.Bw]={ref:!0,v:{}},r[y.Yd]=y.HP,r.loggingLevelConsole=0,r.diagnosticLogInterval=y.HP,r));function O(e,t){return new w.NS(t)}function R(e,t){var n=!1;return(0,s.Iuo)(t,function(t){if(t===e)return n=!0,-1}),n}function A(e,t,n,r){n&&(0,s.zav)(n,function(n,i){r&&(0,s.QdQ)(i)&&(0,s.QdQ)(t[n])&&A(e,t[n],i,r),r&&(0,s.QdQ)(i)&&(0,s.QdQ)(t[n])?A(e,t[n],i,r):e.set(t,n,i)})}function D(e,t){var n=null,r=-1;return(0,s.Iuo)(e,function(e,i){if(e.w===t)return n=e,r=i,-1}),{i:r,l:n}}var k=function(){function e(){var t,n,r,k,N,M,L,U,H,F,j,z,X,V,$,B,q,K,G,Z,W,J,Q,Y,ee,te,ne,re,ie,oe,ae,se;(0,o.A)(e,this,function(e){function o(){ie=!0,(0,s.hXl)(W)?(te=c.f[l.Yq],(0,g.ZP)(r,1,112,"ikey can't be resolved from promises")):te=c.f.ACTIVE,ue()}function ue(){n&&(e.releaseQueue(),e[l.h4]())}function ce(e){return oe&&oe[l.XM]||se||(e||r&&r.queue[l.oI]>0)&&(ae||(ae=!0,_e(t[l.x6](function(e){var t=e.cfg.diagnosticLogInterval;t&&t>0||(t=1e4);var n=!1;oe&&(n=oe[l.XM],oe[l._w]()),(oe=(0,s.AHH)(me,t)).unref(),oe[l.XM]=n}))),oe[l.XM]=!0),oe}function le(){var e={};Y=[];var t=function(t){t&&(0,s.Iuo)(t,function(t){if(t[l.Ju]&&t[l.s]&&!e[t.identifier]){var n=t[l.Ju]+"="+t[l.s];Y[l.y5](n),e[t.identifier]=t}})};t(z),j&&(0,s.Iuo)(j,function(e){t(e)}),t(F)}function fe(){n=!1,(t=(0,u.e)({},x,e[l.Uw])).cfg[l.Bl]=1,(0,s.vF1)(e,"config",{g:function(){return t.cfg},s:function(t){e.updateCfg(t,!1)}}),(0,s.vF1)(e,"pluginVersionStringArr",{g:function(){return Y||le(),Y}}),(0,s.vF1)(e,"pluginVersionString",{g:function(){return ee||(Y||le(),ee=Y.join(";")),ee||y.m5}}),(0,s.vF1)(e,"logger",{g:function(){return r||(r=new g.wq(t.cfg),t[l.Uw]=r),r},s:function(e){t[l.Uw]=e,r!==e&&((0,f.K)(r,!1),r=e)}}),e[l.Uw]=new g.wq(t.cfg),Q=[];var i=e.config[y.jy]||[];i.splice(0,i[l.oI]),(0,s.Yny)(i,Q),V=new I,k=[],(0,f.K)(N,!1),N=null,M=null,L=null,(0,f.K)(U,!1),U=null,H=null,F=[],j=null,z=null,X=!1,$=null,B=(0,h.Z)("AIBaseCore",!0),q=(0,E.P)(),Z=null,W=null,K=(0,P.w)(),J=[],ee=null,Y=null,se=!1,oe=null,ae=!1,te=0,ne=null,re=null,ie=!1}function de(){var n=(0,S.i8)(pe(),t.cfg,e);return n[l.by](ce),n}function ve(t){var n=function(e,t,n){var r,i=[],o=[],a={};return(0,s.Iuo)(n,function(n){((0,s.hXl)(n)||(0,s.hXl)(n[l.mE]))&&(0,s.$8)(T);var r=n[y.Vo],u=n[l.Ju];n&&r&&((0,s.hXl)(a[r])?a[r]=u:(0,g.OG)(e,"Two extensions have same priority #"+r+" - "+a[r]+", "+u)),!r||r<t?i[l.y5](n):o[l.y5](n)}),(r={})[y.eT]=i,r[y.LZ]=o,r}(e[l.Uw],d.i,F);H=null,ee=null,Y=null,z=(j||[])[0]||[],z=(0,_.Xc)((0,s.Yny)(z,n[y.LZ]));var r=(0,s.Yny)((0,_.Xc)(n[y.eT]),z);Q=(0,s.N6t)(r);var i=e.config[y.jy]||[];i.splice(0,i[l.oI]),(0,s.Yny)(i,Q);var o=de();z&&z[l.oI]>0&&(0,_.pI)(o[l.$o](z),r),(0,_.pI)(o,r),t&&be(t)}function he(e){var t=null,n=null,r=[];return(0,s.Iuo)(Q,function(t){if(t[l.Ju]===e&&t!==V)return n=t,-1;t.getChannel&&r[l.y5](t)}),!n&&r[l.oI]>0&&(0,s.Iuo)(r,function(t){if(!(n=t.getChannel(e)))return-1}),n&&(t={plugin:n,setEnabled:function(e){(0,_.Cr)(n)[y.Hr]=!e},isEnabled:function(){var e=(0,_.Cr)(n);return!e[l.Ik]&&!e[y.Hr]},remove:function(e,t){var r;void 0===e&&(e=!0);var i=[n],o=((r={reason:1})[l.tI]=e,r);ge(i,o,function(e){e&&ve({reason:32,removed:i}),t&&t(e)})}}),t}function pe(){if(!H){var n=(Q||[]).slice();-1===(0,s.rDm)(n,V)&&n[l.y5](V),H=(0,S.PV)((0,_.Xc)(n),t.cfg,e)}return H}function ge(n,r,i){if(n&&n[l.oI]>0){var o=(0,S.PV)(n,t.cfg,e),a=(0,S.tS)(o,e);a[l.by](function(){var e=!1,t=[];(0,s.Iuo)(F,function(r,i){R(r,n)?e=!0:t[l.y5](r)}),F=t,ee=null,Y=null;var r=[];j&&((0,s.Iuo)(j,function(t,i){var o=[];(0,s.Iuo)(t,function(t){R(t,n)?e=!0:o[l.y5](t)}),r[l.y5](o)}),j=r),i&&i(e),ce()}),a[l.$5](r)}else i(!1)}function me(){if(r&&r.queue){var t=r.queue.slice(0);r.queue[l.oI]=0,(0,s.Iuo)(t,function(t){var n,r=((n={})[l.RS]=$||"InternalMessageId: "+t[l.JR],n[l.FI]=W,n[l.fA]=(0,m._u)(new Date),n.baseType=g.WD.dataType,n.baseData={message:t[l.pM]},n);e.track(r)})}}function ye(e,t,n,r){var i=1,o=!1,a=null;function u(){i--,o&&0===i&&(a&&a[l._w](),a=null,t&&t(o),t=null)}return r=r||5e3,z&&z[l.oI]>0&&de()[l.$o](z).iterate(function(t){if(t.flush){i++;var o=!1;t.flush(e,function(){o=!0,u()},n)||o||(e&&null==a?a=(0,s.dRz)(function(){a=null,u()},r):u())}}),o=!0,u(),!0}function be(t){var n=(0,S.nU)(pe(),e);n[l.by](ce),e._updateHook&&!0===e._updateHook(n,t)||n[l.$5](t)}function we(t){var n=e[l.Uw];n?((0,g.ZP)(n,2,73,t),ce()):(0,s.$8)(t)}function Se(t){var n=e[l.RF]();n&&n[y.Yp]([t],2)}function _e(e){K.add(e)}fe(),e._getDbgPlgTargets=function(){return[Q,k]},e[l.tZ]=function(){return n},e.activeStatus=function(){return te},e._setPendingStatus=function(){te=3},e[l.mE]=function(f,d,v,h){var b;X&&(0,s.$8)(C),e[l.tZ]()&&(0,s.$8)("Core cannot be initialized more than once"),t=(0,u.e)(f,x,v||e[l.Uw],!1),f=t.cfg,_e(t[l.x6](function(e){var t=e.cfg;if(3!==te){re=t.initInMemoMaxSize||100;var i=t[l.sl],u=t.endpointUrl;if((0,s.hXl)(i)){W=null,te=c.f[l.Yq];var d="Please provide instrumentation key";n?((0,g.ZP)(r,1,100,d),ue()):(0,s.$8)(d)}else{var v=[];if((0,s.$XS)(i)?(v[l.y5](i),W=null):W=i,(0,s.$XS)(u)?(v[l.y5](u),ne=null):ne=u,v[l.oI]){ie=!1,te=3;var h=(0,m.Gh)(t.initTimeOut)?t.initTimeOut:5e4,p=(0,a.lh)(v);(0,s.dRz)(function(){ie||o()},h),(0,a.Dv)(p,function(e){try{if(ie)return;if(!e.rejected){var t=e[l.pF];if(t&&t[l.oI]){var n=t[0];if(W=n&&n[l.pF],t[l.oI]>1){var r=t[1];ne=r&&r[l.pF]}}W&&(f[l.sl]=W,f.endpointUrl=ne)}o()}catch(e){ie||o()}})}else o();var b=e.ref(e.cfg,y.Bw);(0,s.zav)(b,function(t){e.ref(b,t)})}}})),G=function(e,t,n,r){return t.add(e[l.x6](function(e){var t=e.cfg.disableDbgExt;!0===t&&r&&(n[l.h3](r),r=null),n&&!r&&!0!==t&&(r=(0,p.M)(e.cfg),n[l.vR](r))})),r}(t,K,(N=h)&&e[l.RF](),G),_e(t[l.x6](function(t){if(t.cfg.enablePerfMgr){var n=t.cfg[y.Yd];b===n&&b||(n||(n=O),(0,m.c2)(t.cfg,y.Yd,n),b=n,L=null),M||L||!(0,s.Tnt)(n)||(L=n(e,e[l.RF]()))}else L=null,b=null})),e[l.Uw]=v;var w=f[y.jy];if((F=[])[l.y5].apply(F,(0,i.vz)((0,i.vz)([],d,!1),w,!1)),j=f[y.LZ],ve(null),z&&0!==z[l.oI]||(0,s.$8)("No "+y.LZ+" available"),j&&j[l.oI]>1){var S=e[l.AP]("TeeChannelController");S&&S.plugin||(0,g.ZP)(r,1,28,"TeeChannel required")}!function(e,t,n){(0,s.Iuo)(t,function(t){var r=(0,u.a)(e,t.w,n);delete t.w,t.rm=function(){r.rm()}})}(f,J,r),J=null,n=!0,te===c.f.ACTIVE&&ue()},e.getChannels=function(){var e=[];return z&&(0,s.Iuo)(z,function(t){e[l.y5](t)}),(0,s.N6t)(e)},e.track=function(t){(0,w.r2)(e[y.kI](),function(){return"AppInsightsCore:track"},function(){null===t&&(Se(t),(0,s.$8)("Invalid telemetry item")),!t[l.RS]&&(0,s.hXl)(t[l.RS])&&(Se(t),(0,s.$8)("telemetry name required")),t[l.FI]=t[l.FI]||W,t[l.fA]=t[l.fA]||(0,m._u)(new Date),t.ver=t.ver||"4.0",!X&&e[l.tZ]()&&te===c.f.ACTIVE?de()[l.$5](t):te!==c.f[l.Yq]&&k[l.oI]<=re&&k[l.y5](t)},function(){return{item:t}},!t.sync)},e[l.DI]=de,e[l.RF]=function(){return N||(N=new b.h(t.cfg),e._notificationManager=N),N},e[l.vR]=function(t){e.getNotifyMgr()[l.vR](t)},e[l.h3]=function(e){N&&N[l.h3](e)},e.getCookieMgr=function(){return U||(U=(0,v.xN)(t.cfg,e[l.Uw])),U},e.setCookieMgr=function(e){U!==e&&((0,f.K)(U,!1),U=e)},e[y.kI]=function(){return M||L||(0,w.Z4)()},e.setPerfMgr=function(e){M=e},e.eventCnt=function(){return k[l.oI]},e.releaseQueue=function(){if(n&&k[l.oI]>0){var e=k;k=[],2===te?(0,s.Iuo)(e,function(e){e[l.FI]=e[l.FI]||W,de()[l.$5](e)}):(0,g.ZP)(r,2,20,"core init status is not active")}},e[l.h4]=function(e){return $=e||null,se=!1,oe&&oe[l._w](),ce(!0)},e[l.Di]=function(){se=!0,oe&&oe[l._w](),me()},(0,m.o$)(e,function(){return V},["addTelemetryInitializer"]),e[l.M5]=function(t,i,o){var u;void 0===t&&(t=!0),n||(0,s.$8)("SDK is not initialized"),X&&(0,s.$8)(C);var c,d=((u={reason:50})[l.tI]=t,u.flushComplete=!1,u);t&&!i&&(c=(0,a.Qo)(function(e){i=e}));var v=(0,S.tS)(pe(),e);function h(t){d.flushComplete=t,X=!0,q.run(v,d),e[l.Di](),v[l.$5](d)}return v[l.by](function(){K.run(e[l.Uw]),(0,f.k)([U,N,r],t,function(){fe(),i&&i(d)})},e),me(),ye(t,h,6,o)||h(!1),c},e[l.AP]=he,e.addPlugin=function(e,t,n,r){if(!e)return r&&r(!1),void we(T);var i=he(e[l.Ju]);if(i&&!t)return r&&r(!1),void we("Plugin ["+e[l.Ju]+"] is already loaded!");var o={reason:16};function a(t){F[l.y5](e),o.added=[e],ve(o),r&&r(!0)}if(i){var s=[i.plugin];ge(s,{reason:2,isAsync:!!n},function(e){e?(o.removed=s,o.reason|=32,a()):r&&r(!1)})}else a()},e.updateCfg=function(n,r){var i;if(void 0===r&&(r=!0),e[l.tZ]()){i={reason:1,cfg:t.cfg,oldCfg:(0,s.zwS)({},t.cfg),newConfig:(0,s.zwS)({},n),merge:r},n=i.newConfig;var o=t.cfg;n[y.jy]=o[y.jy],n[y.LZ]=o[y.LZ]}t._block(function(e){var t=e.cfg;A(e,t,n,r),r||(0,s.zav)(t,function(r){(0,s.KhI)(n,r)||e.set(t,r,y.HP)}),e[l.h0](t,x)},!0),t[l.zs](),i&&be(i)},e.evtNamespace=function(){return B},e.flush=ye,e.getTraceCtx=function(e){return Z||(Z=(0,_.u7)()),Z},e.setTraceCtx=function(e){Z=e||null},e.addUnloadHook=_e,(0,m.RF)(e,"addUnloadCb",function(){return q},"add"),e.onCfgChange=function(r){var i,o,a,c;return n?i=(0,u.a)(t.cfg,r,e[l.Uw]):((c=D(o=J,a=r).l)||(c={w:a,rm:function(){var e=D(o,a);-1!==e.i&&o[l.Ic](e.i,1)}},o[l.y5](c)),i=c),function(e){return(0,s.vF1)({rm:function(){e.rm()}},"toJSON",{v:function(){return"aicore::onCfgChange<"+JSON.stringify(e)+">"}})}(i)},e.getWParam=function(){return(0,s.Wtk)()||t.cfg.enableWParam?0:-1}})}return e.__ieDyn=1,e}()},2845:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.TasApiFeatureProvider=t.TASAPI_FETCHERROR_EVENTNAME=void 0;const r=n(4111);t.TASAPI_FETCHERROR_EVENTNAME="call-tas-error";const i="ErrorType";class o extends r.FilteredFeatureProvider{constructor(e,t,n){super(t,n),this.httpClient=e,this.telemetry=t,this.filterProviders=n}async fetch(){let e,n=this.getFilters(),r={};for(let e of n.keys()){const t=n.get(e);r[e]=t}try{e=await this.httpClient.get({headers:r})}catch(e){const n=e,r=new Map;n.responseReceived&&!n.responseOk?r.set(i,"ServerError"):!1===n.responseReceived?r.set(i,"NoResponse"):r.set(i,"GenericError"),this.telemetry.postEvent(t.TASAPI_FETCHERROR_EVENTNAME,r)}if(!e)throw Error(t.TASAPI_FETCHERROR_EVENTNAME);n.keys.length>0&&this.PostEventToTelemetry(r);const o=e.data;let a=o.Configs,s=[];for(let e of a)if(e.Parameters)for(let t of Object.keys(e.Parameters)){const n=t+(e.Parameters[t]?"":"cf");s.includes(n)||s.push(n)}return{features:s,assignmentContext:o.AssignmentContext,configs:a}}}t.TasApiFeatureProvider=o},2866:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.base64Encode=function(e){return Buffer.from(e,"binary").toString("base64")}},2909:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ExperimentationService=void 0;var r=n(7291);Object.defineProperty(t,"ExperimentationService",{enumerable:!0,get:function(){return r.ExperimentationService}})},2910:(e,t,n)=>{n.d(t,{a:()=>r});var r=(0,n(4282).o)({requestContextHeader:[0,"Request-Context"],requestContextTargetKey:[1,"appId"],requestContextAppIdFormat:[2,"appId=cid-v1:"],requestIdHeader:[3,"Request-Id"],traceParentHeader:[4,"traceparent"],traceStateHeader:[5,"tracestate"],sdkContextHeader:[6,"Sdk-Context"],sdkContextHeaderAppIdRequest:[7,"appId"],requestContextHeaderLowerCase:[8,"request-context"]})},3034:(e,t,n)=>{let r;Object.defineProperty(t,"__esModule",{value:!0}),t.fetching=void 0;try{r=n(4482).net.fetch}catch{r=fetch}t.fetching=r},3072:(e,t,n)=>{n.d(t,{J:()=>a});var r=n(5025),i=n(5130),o=n(7975),a=function(){function e(e,t,n,a){this.aiDataContract={ver:1,name:1,properties:0,measurements:0};var s=this;s.ver=2,s[i.RS]=(0,o.Rr)(e,t)||r.R2,s[i.$y]=(0,o.xP)(e,n),s[i.XA]=(0,o.Vj)(e,a)}return e.envelopeType="Microsoft.ApplicationInsights.{0}.Event",e.dataType="EventData",e}()},3257:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.PollingService=void 0,t.PollingService=class{constructor(e){this.fetchInterval=e}StopPolling(){clearInterval(this.intervalHandle),this.intervalHandle=void 0}OnPollTick(e){this.onTick=e}StartPolling(e=!1){this.intervalHandle&&this.StopPolling(),null!=this.onTick&&(e&&this.onTick().then(()=>{}).catch(()=>{}),this.intervalHandle=setInterval(async()=>{await this.onTick()},this.fetchInterval),this.intervalHandle.unref&&this.intervalHandle.unref())}}},3662:(e,t,n)=>{n.d(t,{x:()=>i});var r=n(4282),i=(0,r.H)({Unknown:0,NonRetryableStatus:1,InvalidEvent:2,SizeLimitExceeded:3,KillSwitch:4,QueueFull:5});(0,r.H)({Unknown:0,NonRetryableStatus:1,CleanStorage:2,MaxInStorageTimeExceeded:3})},3673:(e,t,n)=>{n.d(t,{CP:()=>I,Gh:()=>l,H$:()=>A,HU:()=>P,IL:()=>D,Ju:()=>d,KY:()=>p,LU:()=>k,Lo:()=>E,RF:()=>b,SZ:()=>S,_u:()=>v,c2:()=>g,cH:()=>f,hW:()=>_,jL:()=>C,lL:()=>h,o$:()=>w,qz:()=>y,r4:()=>T,w3:()=>U});var r=n(269),i=n(5664),o=n(6182),a=n(6492),s=/-([a-z])/g,u=/([^\w\d_$])/g,c=/^(\d+[\w\d_$])/;function l(e){return!(0,r.hXl)(e)}function f(e){var t=e;return t&&(0,r.KgX)(t)&&(t=(t=(t=t[o.W7](s,function(e,t){return t.toUpperCase()}))[o.W7](u,"_"))[o.W7](c,function(e,t){return"_"+t})),t}function d(e,t){return!(!e||!t)&&-1!==(0,r.HzD)(e,t)}function v(e){return e&&e.toISOString()||""}function h(e){return(0,r.bJ7)(e)?e[o.RS]:a.m5}function p(e,t,n,r,i){var o=n;return e&&((o=e[t])===n||i&&!i(o)||r&&!r(n)||(o=n,e[t]=o)),o}function g(e,t,n){var i;return e?!(i=e[t])&&(0,r.hXl)(i)&&(i=(0,r.b07)(n)?{}:n,e[t]=i):i=(0,r.b07)(n)?{}:n,i}function m(e,t){var n=null,i=null;return(0,r.Tnt)(e)?n=e:i=e,function(){var e=arguments;if(n&&(i=n()),i)return i[t][o.y9](i,e)}}function y(e,t,n){if(e&&t&&(0,r.Gvm)(e)&&(0,r.Gvm)(t)){var i=function(i){if((0,r.KgX)(i)){var o=t[i];(0,r.Tnt)(o)?n&&!n(i,!0,t,e)||(e[i]=m(t,i)):n&&!n(i,!1,t,e)||((0,r.KhI)(e,i)&&delete e[i],(0,r.vF1)(e,i,{g:function(){return t[i]},s:function(e){t[i]=e}}))}};for(var o in t)i(o)}return e}function b(e,t,n,i,o){e&&t&&n&&(!1!==o||(0,r.b07)(e[t]))&&(e[t]=m(n,i))}function w(e,t,n,i){return e&&t&&(0,r.Gvm)(e)&&(0,r.cyL)(n)&&(0,r.Iuo)(n,function(n){(0,r.KgX)(n)&&b(e,n,t,n,i)}),e}function S(e){return function(){var t=this;e&&(0,r.zav)(e,function(e,n){t[e]=n})}}function _(e){return e&&r.vE3&&(e=(0,i.s6)((0,r.vE3)({},e))),e}function I(e,t,n,i,a,s){var u=arguments,c=u[0]||{},l=u[o.oI],f=!1,d=1;for(l>0&&(0,r.Lmq)(c)&&(f=c,c=u[d]||{},d++),(0,r.Gvm)(c)||(c={});d<l;d++){var v=u[d],h=(0,r.cyL)(v),p=(0,r.Gvm)(v);for(var g in v)if(h&&g in v||p&&(0,r.KhI)(v,g)){var m=v[g],y=void 0;if(f&&m&&((y=(0,r.cyL)(m))||(0,r.QdQ)(m))){var b=c[g];y?(0,r.cyL)(b)||(b=[]):(0,r.QdQ)(b)||(b={}),m=I(f,b,m)}void 0!==m&&(c[g]=m)}}return c}function E(e){try{return e.responseText}catch(e){}return null}function P(e,t){return e?"XDomainRequest,Response:"+E(e)||0:t}function T(e,t){return e?"XMLHttpRequest,Status:"+e[o.cV]+",Response:"+E(e)||0:t}function C(e,t){return t&&((0,r.EtT)(t)?e=[t].concat(e):(0,r.cyL)(t)&&(e=t.concat(e))),e}Object.getPrototypeOf;var x="Microsoft_ApplicationInsights_BypassAjaxInstrumentation",O="withCredentials",R="timeout";function A(e,t,n,r,i,o){function a(e,t,n){try{e[t]=n}catch(e){}}void 0===r&&(r=!1),void 0===i&&(i=!1);var s=new XMLHttpRequest;return r&&a(s,x,r),n&&a(s,O,n),s.open(e,t,!i),n&&a(s,O,n),!i&&o&&a(s,R,o),s}function D(e){var t={};if((0,r.KgX)(e)){var n=(0,r.EHq)(e)[o.sY](/[\r\n]+/);(0,r.Iuo)(n,function(e){if(e){var n=e.indexOf(": ");if(-1!==n){var i=(0,r.EHq)(e.substring(0,n))[o.OL](),a=(0,r.EHq)(e.substring(n+1));t[i]=a}else t[(0,r.EHq)(e)]=1}})}return t}function k(e,t,n){if(!e[n]&&t&&t[o.Az]){var i=t[o.Az](n);i&&(e[n]=(0,r.EHq)(i))}return e}var N="kill-duration",M="kill-duration-seconds",L="time-delta-millis";function U(e,t){var n={};return e[o.wJ]?n=D(e[o.wJ]()):t&&(n=k(n,e,L),n=k(n,e,N),n=k(n,e,M)),n}},3772:function(e,t,n){var r,i=this&&this.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n);var i=Object.getOwnPropertyDescriptor(t,n);i&&!("get"in i?!t.__esModule:i.writable||i.configurable)||(i={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,i)}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),o=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),a=this&&this.__importStar||(r=function(e){return r=Object.getOwnPropertyNames||function(e){var t=[];for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[t.length]=n);return t},r(e)},function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n=r(e),a=0;a<n.length;a++)"default"!==n[a]&&i(t,e,n[a]);return o(t,e),t});Object.defineProperty(t,"__esModule",{value:!0}),t.getFlows=function(e){return g.filter(t=>{let n=!0;switch(e.target){case 0:n&&(n=t.options.supportsGitHubDotCom);break;case 1:n&&(n=t.options.supportsGitHubEnterpriseServer);break;case 2:n&&(n=t.options.supportsHostedGitHubEnterprise)}switch(e.extensionHost){case 1:n&&(n=t.options.supportsRemoteExtensionHost);break;case 0:n&&(n=t.options.supportsWebWorkerExtensionHost)}return c.Config.gitHubClientSecret||n&&(n=t.options.supportsNoClientSecret),e.isSupportedClient?n&&(n=t.options.supportsSupportedClients||0!==e.target):n&&(n=t.options.supportsUnsupportedClients),n})},t.isSocialSignInProvider=function(e){return"google"===e||"apple"===e};const s=a(n(6928)),u=n(1398),c=n(8547),l=n(3034),f=n(1589),d=n(7066),v=n(8016),h=n(928);async function p(e,t,n,r,i){e.info("Exchanging code for token...");const o=c.Config.gitHubClientSecret;if(!o)throw new Error("No client secret configured for GitHub authentication.");const a=new URLSearchParams([["code",r],["client_id",c.Config.gitHubClientId],["redirect_uri",n.toString(!0)],["client_secret",o]]);i&&a.append("github_enterprise",i.toString(!0));const s=await(0,l.fetching)(t.toString(!0),{method:"POST",headers:{Accept:"application/json","Content-Type":"application/x-www-form-urlencoded"},body:a.toString()});if(s.ok){const t=await s.json();return e.info("Token exchange success!"),t.access_token}{const e=await s.text(),t=new Error(e);throw t.name="GitHubTokenExchangeError",t}}const g=[new class{constructor(){this.label=u.l10n.t("local server"),this.options={supportsGitHubDotCom:!0,supportsGitHubEnterpriseServer:!1,supportsHostedGitHubEnterprise:!0,supportsRemoteExtensionHost:!1,supportsWebWorkerExtensionHost:!1,supportsNoClientSecret:!1,supportsSupportedClients:!0,supportsUnsupportedClients:!0}}async trigger({scopes:e,baseUri:t,redirectUri:n,callbackUri:r,enterpriseUri:i,signInProvider:o,existingLogin:a,logger:l}){return l.info(`Trying with local server... (${e})`),await u.window.withProgress({location:u.ProgressLocation.Notification,title:u.l10n.t({message:"Signing in to {0}...",args:[t.authority],comment:["The {0} will be a url, e.g. github.com"]}),cancellable:!0},async(v,g)=>{const m=new URLSearchParams([["client_id",c.Config.gitHubClientId],["redirect_uri",n.toString(!0)],["scope",e]]);a?m.append("login",a):m.append("prompt","select_account"),o&&m.append("provider",o);const y=t.with({path:"/login/oauth/authorize",query:m.toString()}),b=new f.LoopbackAuthServer(s.join(__dirname,"../media"),y.toString(!0),r.toString(!0)),w=await b.start();let S;try{u.env.openExternal(u.Uri.parse(`http://127.0.0.1:${w}/signin?nonce=${encodeURIComponent(b.nonce)}`));const{code:e}=await Promise.race([b.waitForOAuthResponse(),new Promise((e,t)=>setTimeout(()=>t(h.TIMED_OUT_ERROR),3e5)),(0,d.promiseFromEvent)(g.onCancellationRequested,(e,t,n)=>{n(h.USER_CANCELLATION_ERROR)}).promise]);S=e}finally{setTimeout(()=>{b.stop()},5e3)}return await p(l,t.with({path:"/login/oauth/access_token"}),n,S,i)})}},new class{constructor(){this.label=u.l10n.t("url handler"),this.options={supportsGitHubDotCom:!0,supportsGitHubEnterpriseServer:!1,supportsHostedGitHubEnterprise:!0,supportsRemoteExtensionHost:!0,supportsWebWorkerExtensionHost:!0,supportsNoClientSecret:!1,supportsSupportedClients:!0,supportsUnsupportedClients:!1}}async trigger({scopes:e,baseUri:t,redirectUri:n,callbackUri:r,enterpriseUri:i,nonce:o,signInProvider:a,uriHandler:s,existingLogin:l,logger:f}){return f.info(`Trying without local server... (${e})`),await u.window.withProgress({location:u.ProgressLocation.Notification,title:u.l10n.t({message:"Signing in to {0}...",args:[t.authority],comment:["The {0} will be a url, e.g. github.com"]}),cancellable:!0},async(d,v)=>{const h=s.waitForCode(f,e,o,v),g=new URLSearchParams([["client_id",c.Config.gitHubClientId],["redirect_uri",n.toString(!0)],["scope",e],["state",encodeURIComponent(r.toString(!0))]]);l?g.append("login",l):g.append("prompt","select_account"),a&&g.append("provider",a);const m=u.Uri.parse(t.with({path:"/login/oauth/authorize",query:g.toString()}).toString(!0));await u.env.openExternal(m);const y=await h,b=await u.commands.executeCommand("workbench.getCodeExchangeProxyEndpoints"),w=b?.github?u.Uri.parse(`${b.github}login/oauth/access_token`):t.with({path:"/login/oauth/access_token"});return await p(f,w,n,y,i)})}},new class{constructor(){this.label=u.l10n.t("device code"),this.options={supportsGitHubDotCom:!0,supportsGitHubEnterpriseServer:!0,supportsHostedGitHubEnterprise:!0,supportsRemoteExtensionHost:!0,supportsWebWorkerExtensionHost:!1,supportsNoClientSecret:!0,supportsSupportedClients:!0,supportsUnsupportedClients:!0}}async trigger({scopes:e,baseUri:t,signInProvider:n,logger:r}){r.info(`Trying device code flow... (${e})`);const i=t.with({path:"/login/device/code",query:`client_id=${c.Config.gitHubClientId}&scope=${e}`}),o=await(0,l.fetching)(i.toString(!0),{method:"POST",headers:{Accept:"application/json"}});if(!o.ok)throw new Error(`Failed to get one-time code: ${await o.text()}`);const a=await o.json(),s=u.l10n.t("Copy & Continue to {0}",n?m[n]:u.l10n.t("GitHub"));if(await u.window.showInformationMessage(u.l10n.t({message:"Your Code: {0}",args:[a.user_code],comment:["The {0} will be a code, e.g. 123-456"]}),{modal:!0,detail:u.l10n.t("To finish authenticating, navigate to GitHub and paste in the above one-time code.")},s)!==s)throw new Error(h.USER_CANCELLATION_ERROR);await u.env.clipboard.writeText(a.user_code);let f=u.Uri.parse(a.verification_uri);if(n){const e=new URLSearchParams(f.query);e.set("provider",n),f=f.with({query:e.toString()})}const d=await u.env.asExternalUri(f);return await u.env.openExternal(d),await this.waitForDeviceCodeAccessToken(t,a)}async waitForDeviceCodeAccessToken(e,t){return await u.window.withProgress({location:u.ProgressLocation.Notification,cancellable:!0,title:u.l10n.t({message:"Open [{0}]({0}) in a new tab and paste your one-time code: {1}",args:[t.verification_uri,t.user_code],comment:["The [{0}]({0}) will be a url and the {1} will be a code, e.g. 123-456",'{Locked="[{0}]({0})"}']})},async(n,r)=>{const i=e.with({path:"/login/oauth/access_token",query:`client_id=${c.Config.gitHubClientId}&device_code=${t.device_code}&grant_type=urn:ietf:params:oauth:grant-type:device_code`}),o=120/t.interval;for(let e=0;e<o;e++){if(await new Promise(e=>setTimeout(e,1e3*t.interval)),r.isCancellationRequested)throw new Error(h.USER_CANCELLATION_ERROR);let e;try{e=await(0,l.fetching)(i.toString(!0),{method:"POST",headers:{Accept:"application/json"}})}catch{continue}if(!e.ok)continue;const n=await e.json();if("authorization_pending"!==n.error){if(n.error)throw new Error(n.error_description);return n.access_token}}throw new Error(h.TIMED_OUT_ERROR)})}},new class{constructor(){this.label=u.l10n.t("personal access token"),this.options={supportsGitHubDotCom:!0,supportsGitHubEnterpriseServer:!0,supportsHostedGitHubEnterprise:!0,supportsRemoteExtensionHost:!0,supportsWebWorkerExtensionHost:!0,supportsNoClientSecret:!0,supportsSupportedClients:!1,supportsUnsupportedClients:!0}}async trigger({scopes:e,baseUri:t,logger:n,enterpriseUri:r}){n.info(`Trying to retrieve PAT... (${e})`);const i=u.l10n.t("Continue to GitHub");if(await u.window.showInformationMessage(u.l10n.t("Continue to GitHub to create a Personal Access Token (PAT)"),{modal:!0,detail:u.l10n.t("To finish authenticating, navigate to GitHub to create a PAT then paste the PAT into the input box.")},i)!==i)throw new Error(h.USER_CANCELLATION_ERROR);const o=`${u.env.appName} (${e})`,a=await u.env.asExternalUri(t.with({path:"/settings/tokens/new",query:`description=${o}&scopes=${e.split(" ").join(",")}`}));await u.env.openExternal(a);const s=await u.window.showInputBox({placeHolder:"ghp_1a2b3c4...",prompt:`GitHub Personal Access Token - ${e}`,ignoreFocusOut:!0});if(!s)throw new Error(h.USER_CANCELLATION_ERROR);const c=!r||(0,v.isHostedGitHubEnterprise)(r)?u.Uri.parse(`${t.scheme}://api.${t.authority}`):u.Uri.parse(`${t.scheme}://${t.authority}/api/v3`),l=await this.getScopes(s,c,n);if(!e.split(" ").every(e=>{const t=l.includes(e);return t||!e.includes(":")?t:e.split(":").some(e=>l.includes(e))}))throw new Error(`The provided token does not match the requested scopes: ${e}`);return s}async getScopes(e,t,n){try{n.info("Getting token scopes...");const r=await(0,l.fetching)(t.toString(),{headers:{Authorization:`token ${e}`,"User-Agent":`${u.env.appName} (${u.env.appHost})`}});if(r.ok){const e=r.headers.get("X-OAuth-Scopes");return e?e.split(",").map(e=>e.trim()):[]}throw n.error(`Getting scopes failed: ${r.statusText}`),new Error(r.statusText)}catch(e){throw n.error(e.message),new Error(h.NETWORK_ERROR)}}}],m={google:u.l10n.t("Google"),apple:u.l10n.t("Apple")}},3775:(e,t,n)=>{n.d(t,{OG:()=>S,Oc:()=>_,WD:()=>g,ZP:()=>w,wq:()=>y,y0:()=>m});var r,i=n(8279),o=n(269),a=n(9749),s=n(6182),u=n(7867),c=n(7292),l=n(6492),f="warnToConsole",d={loggingLevelConsole:0,loggingLevelTelemetry:1,maxMessageLimit:25,enableDebug:!1},v=((r={})[0]=null,r[1]="errorToConsole",r[2]=f,r[3]="debugToConsole",r);function h(e){return e?'"'+e[s.W7](/\"/g,l.m5)+'"':l.m5}function p(e,t){var n=(0,c.U5)();if(n){var r="log";n[e]&&(r=e),(0,o.Tnt)(n[r])&&n[r](t)}}var g=function(){function e(e,t,n,r){void 0===n&&(n=!1);var i=this;i[s.JR]=e,i[s.pM]=(n?"AI: ":"AI (Internal): ")+e;var o=l.m5;(0,c.Z)()&&(o=(0,c.hm)().stringify(r));var a=(t?" message:"+h(t):l.m5)+(r?" props:"+h(o):l.m5);i[s.pM]+=a}return e.dataType="MessageData",e}();function m(e,t){return(e||{})[s.Uw]||new y(t)}var y=function(){function e(t){this.identifier="DiagnosticLogger",this.queue=[];var n,r,c,l,h,m=0,y={};(0,i.A)(e,this,function(e){function i(t,n){if(!(m>=c)){var i=!0,o="AITR_"+n[s.JR];if(y[o]?i=!1:y[o]=!0,i&&(t<=r&&(e.queue[s.y5](n),m++,b(1===t?"error":"warn",n)),m===c)){var a="Internal events throttle limit per PageView reached for this app.",u=new g(23,a,!1);e.queue[s.y5](u),1===t?e.errorToConsole(a):e[s.on](a)}}}function b(e,n){var r=(0,u.$)(t||{});r&&r[s.e4]&&r[s.e4](e,n)}h=function(t){return(0,a.a)((0,a.e)(t,d,e).cfg,function(e){var t=e.cfg;n=t[s.Bl],r=t.loggingLevelTelemetry,c=t.maxMessageLimit,l=t.enableDebug})}(t||{}),e.consoleLoggingLevel=function(){return n},e[s.ih]=function(t,r,a,u,c){void 0===c&&(c=!1);var d=new g(r,a,c,u);if(l)throw(0,o.mmD)(d);var h=v[t]||f;if((0,o.b07)(d[s.pM]))b("throw"+(1===t?"Critical":"Warning"),d);else{if(c){var p=+d[s.JR];!y[p]&&n>=t&&(e[h](d[s.pM]),y[p]=!0)}else n>=t&&e[h](d[s.pM]);i(t,d)}},e.debugToConsole=function(e){p("debug",e),b("warning",e)},e[s.on]=function(e){p("warn",e),b("warning",e)},e.errorToConsole=function(e){p("error",e),b("error",e)},e.resetInternalMessageCount=function(){m=0,y={}},e[s.sx]=i,e[s.M5]=function(e){h&&h.rm(),h=null}})}return e.__ieDyn=1,e}();function b(e){return e||new y}function w(e,t,n,r,i,o){void 0===o&&(o=!1),b(e)[s.ih](t,n,r,i,o)}function S(e,t){b(e)[s.on](t)}function _(e,t,n){b(e)[s.sx](t,n)}},4013:(e,t,n)=>{n.d(t,{K:()=>a,k:()=>s});var r=n(8205),i=n(269),o=n(6182);function a(e,t){if(e&&e[o.M5])return e[o.M5](t)}function s(e,t,n){var o;return n||(o=(0,r.Qo)(function(e){n=e})),e&&(0,i.R3R)(e)>0?(0,r.Dv)(a(e[0],t),function(){s((0,i.KVm)(e,1),t,n)}):n(),o}},4111:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.FilteredFeatureProvider=void 0;const r=n(6790);class i extends r.BaseFeatureProvider{constructor(e,t){super(e),this.telemetry=e,this.filterProviders=t,this.cachedTelemetryEvents=[]}getFilters(){let e=new Map;for(let t of this.filterProviders){let n=t.getFilters();for(let t of n.keys()){let r=n.get(t);e.set(t,r)}}return e}PostEventToTelemetry(e){if(this.cachedTelemetryEvents.includes(e))return;const t=JSON.stringify(e);this.telemetry.postEvent("report-headers",new Map([["ABExp.headers",t]])),this.cachedTelemetryEvents.push(e)}}t.FilteredFeatureProvider=i},4164:(e,t,n)=>{n.d(t,{H:()=>a});var r=n(5025),i=n(5130),o=n(7975),a=function(){function e(e,t,n,a,s,u,c){this.aiDataContract={ver:1,name:0,url:0,duration:0,perfTotal:0,networkConnect:0,sentRequest:0,receivedResponse:0,domProcessing:0,properties:0,measurements:0};var l=this;l.ver=2,l.url=(0,o.pJ)(e,n),l[i.RS]=(0,o.Rr)(e,t)||r.R2,l[i.$y]=(0,o.xP)(e,s),l[i.XA]=(0,o.Vj)(e,u),c&&(l.domProcessing=c.domProcessing,l[i.qd]=c[i.qd],l.networkConnect=c.networkConnect,l.perfTotal=c.perfTotal,l[i.fd]=c[i.fd],l.sentRequest=c.sentRequest)}return e.envelopeType="Microsoft.ApplicationInsights.{0}.PageviewPerformance",e.dataType="PageviewPerformanceData",e}()},4276:(e,t,n)=>{n.d(t,{T:()=>v,Z:()=>d});var r=n(269),i=n(6182),o=n(3673),a=n(6492),s=n(6535),u="3.3.4",c="."+(0,s.Si)(6),l=0;function f(e){return 1===e[i.re]||9===e[i.re]||!+e[i.re]}function d(e,t){return void 0===t&&(t=!1),(0,o.cH)(e+l+++(t?"."+u:a.m5)+c)}function v(e){var t={id:d("_aiData-"+(e||a.m5)+"."+u),accept:function(e){return f(e)},get:function(e,n,i,a){var s=e[t.id];return s?s[(0,o.cH)(n)]:(a&&(s=function(e,t){var n=t[e.id];if(!n){n={};try{f(t)&&(0,r.vF1)(t,e.id,{e:!1,v:n})}catch(e){}}return n}(t,e),s[(0,o.cH)(n)]=i),i)},kill:function(e,t){if(e&&e[t])try{delete e[t]}catch(e){}}};return t}},4282:(e,t,n)=>{n.d(t,{H:()=>i,o:()=>o});var r=n(269),i=r.WSA,o=r.fn0},4482:e=>{e.exports=require("electron")},4484:(e,t,n)=>{n.d(t,{F:()=>c,H:()=>u});var r=n(269),i=n(5025),o=n(5130),a=";",s="=";function u(e){if(!e)return{};var t=e[o.sY](a),n=(0,r.KTd)(t,function(e,t){var n=t[o.sY](s);if(2===n[o.oI]){var r=n[0][o.OL](),i=n[1];e[r]=i}return e},{});if((0,r.cGk)(n)[o.oI]>0){if(n.endpointsuffix){var u=n.location?n.location+".":"";n[o.zV]=n[o.zV]||"https://"+u+"dc."+n.endpointsuffix}n[o.zV]=n[o.zV]||i._G,(0,r.Cv9)(n[o.zV],"/")&&(n[o.zV]=n[o.zV].slice(0,-1))}return n}var c={parse:u}},4577:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.crypto=void 0;const r=n(6982);t.crypto=r.webcrypto},4658:(e,t,n)=>{n.d(t,{AN:()=>S,BW:()=>m,Dt:()=>E,Nu:()=>h,Se:()=>y,T9:()=>_,_M:()=>b,iw:()=>g,tm:()=>w,v7:()=>P,vH:()=>I,vh:()=>p});var r=n(269),i=n(3775),o=n(3673),a=n(7374),s=n(5130),u=void 0,c=void 0,l="";function f(){return m()?d(a.eL.LocalStorage):null}function d(e){try{if((0,r.hXl)((0,r.mS$)()))return null;var t=(new Date)[s.xE](),n=(0,r.zS2)(e===a.eL.LocalStorage?"localStorage":"sessionStorage"),i=l+t;n.setItem(i,t);var o=n.getItem(i)!==t;if(n[s.AZ](i),!o)return n}catch(e){}return null}function v(){return S()?d(a.eL.SessionStorage):null}function h(){u=!1,c=!1}function p(e){l=e||""}function g(){u=m(!0),c=S(!0)}function m(e){return(e||void 0===u)&&(u=!!d(a.eL.LocalStorage)),u}function y(e,t){var n=f();if(null!==n)try{return n.getItem(t)}catch(t){u=!1,(0,i.ZP)(e,2,1,"Browser failed read of local storage. "+(0,o.lL)(t),{exception:(0,r.mmD)(t)})}return null}function b(e,t,n){var a=f();if(null!==a)try{return a.setItem(t,n),!0}catch(t){u=!1,(0,i.ZP)(e,2,3,"Browser failed write to local storage. "+(0,o.lL)(t),{exception:(0,r.mmD)(t)})}return!1}function w(e,t){var n=f();if(null!==n)try{return n[s.AZ](t),!0}catch(t){u=!1,(0,i.ZP)(e,2,5,"Browser failed removal of local storage item. "+(0,o.lL)(t),{exception:(0,r.mmD)(t)})}return!1}function S(e){return(e||void 0===c)&&(c=!!d(a.eL.SessionStorage)),c}function _(){var e=[];return S()&&(0,r.zav)((0,r.zS2)("sessionStorage"),function(t){e[s.y5](t)}),e}function I(e,t){var n=v();if(null!==n)try{return n.getItem(t)}catch(t){c=!1,(0,i.ZP)(e,2,2,"Browser failed read of session storage. "+(0,o.lL)(t),{exception:(0,r.mmD)(t)})}return null}function E(e,t,n){var a=v();if(null!==a)try{return a.setItem(t,n),!0}catch(t){c=!1,(0,i.ZP)(e,2,4,"Browser failed write to session storage. "+(0,o.lL)(t),{exception:(0,r.mmD)(t)})}return!1}function P(e,t){var n=v();if(null!==n)try{return n[s.AZ](t),!0}catch(t){c=!1,(0,i.ZP)(e,2,6,"Browser failed removal of session storage item. "+(0,o.lL)(t),{exception:(0,r.mmD)(t)})}return!1}},4822:(e,t,n)=>{n.d(t,{EO:()=>S,F2:()=>N,Go:()=>P,H$:()=>M,Hh:()=>I,P$:()=>b,Rx:()=>l,TC:()=>E,UM:()=>T,WB:()=>O,X$:()=>x,cq:()=>k,ei:()=>L,g8:()=>y,gj:()=>C,h3:()=>_,m0:()=>R,u9:()=>D,wJ:()=>A,xE:()=>f,yD:()=>w});var r,i=n(269),o=n(7292),a=n(9882),s=n(5664),u=n(937),c=n(1739),l="4.3.4",f="1DS-Web-JS-"+l,d=s.Wy.hasOwnProperty,v="Microsoft_ApplicationInsights_BypassAjaxInstrumentation",h="withCredentials",p="timeout",g=((r={})[0]=0,r[2]=6,r[1]=1,r[3]=7,r[4098]=6,r[4097]=1,r[4099]=7,r),m=null,y=(0,i.Wtk)(),b=(0,i.Vdv)();function w(e){return!(e===u.m5||(0,i.hXl)(e))}function S(e){if(e){var t=(0,i.HzD)(e,"-");if(t>-1)return(0,i.ZWZ)(e,t)}return u.m5}function _(){return null===m&&(m=!(0,i.b07)(Uint8Array)&&!function(){var e=(0,i.w3n)();if(!(0,i.b07)(e)&&e.userAgent){var t=e.userAgent.toLowerCase();if((t[c.Sj]("safari")>=0||t[c.Sj]("firefox")>=0)&&t[c.Sj]("chrome")<0)return!0}return!1}()&&!(0,o.lV)()),m}function I(e){return!!(e&&(0,i.EtT)(e)&&e>=1&&e<=4)}function E(e,t,n){if(!t&&!w(t)||"string"!=typeof e)return null;var r=typeof t;if("string"===r||"number"===r||"boolean"===r||(0,i.cyL)(t))t={value:t};else if("object"!==r||d.call(t,"value")){if((0,i.hXl)(t[c.pF])||t[c.pF]===u.m5||!(0,i.KgX)(t[c.pF])&&!(0,i.EtT)(t[c.pF])&&!(0,i.Lmq)(t[c.pF])&&!(0,i.cyL)(t[c.pF]))return null}else t={value:n?JSON.stringify(t):t};if((0,i.cyL)(t[c.pF])&&!A(t[c.pF]))return null;if(!(0,i.hXl)(t[c.QV])){if((0,i.cyL)(t[c.pF])||!R(t[c.QV]))return null;t[c.pF]=t[c.pF].toString()}return t}function P(e,t,n){var r=-1;if(!(0,i.b07)(e))if(t>0&&(32===t?r=8192:t<=13&&(r=t<<5)),function(e){return e>=0&&e<=9}(n))-1===r&&(r=0),r|=n;else{var o=g[k(e)]||-1;-1!==r&&-1!==o?r|=o:6===o&&(r=o)}return r}function T(e,t,n){var r;return void 0===n&&(n=!0),e&&(r=e.get(t),n&&r&&decodeURIComponent&&(r=decodeURIComponent(r))),r||u.m5}function C(e){void 0===e&&(e="D");var t=(0,a.aq)();return"B"===e?t="{"+t+"}":"P"===e?t="("+t+")":"N"===e&&(t=t.replace(/-/g,u.m5)),t}function x(e,t,n,r,o){var a={},s=!1,u=0,l=arguments[c.oI],f=arguments;for((0,i.Lmq)(f[0])&&(s=f[0],u++);u<l;u++)e=f[u],(0,i.zav)(e,function(e,t){s&&t&&(0,i.Gvm)(t)?(0,i.cyL)(t)?(a[e]=a[e]||[],(0,i.Iuo)(t,function(t,n){t&&(0,i.Gvm)(t)?a[e][n]=x(!0,a[e][n],t):a[e][n]=t})):a[e]=x(!0,a[e],t):a[e]=t});return a}var O=i.UUD;function R(e){return 0===e||e>0&&e<=13||32===e}function A(e){return e[c.oI]>0}function D(e,t){var n=e;n[c.dg]=n[c.dg]||{},n[c.dg][c.Jg]=n[c.dg][c.Jg]||{},n[c.dg][c.Jg][t]=O()}function k(e){var t=0;if(null!=e){var n=typeof e;"string"===n?t=1:"number"===n?t=2:"boolean"===n?t=3:n===s._1&&(t=4,(0,i.cyL)(e)?(t=4096,e[c.oI]>0&&(t|=k(e[0]))):d.call(e,"value")&&(t=8192|k(e[c.pF])))}return t}function N(){return!!(0,i.zS2)("chrome")}function M(e,t,n,r,i,o){function a(e,t,n){try{e[t]=n}catch(e){}}void 0===r&&(r=!1),void 0===i&&(i=!1);var s=new XMLHttpRequest;return r&&a(s,v,r),n&&a(s,h,n),s.open(e,t,!i),n&&a(s,h,n),!i&&o&&a(s,p,o),s}function L(e){return e>0}},4875:(e,t,n)=>{n.d(t,{f:()=>r});var r=(0,n(4282).H)({NONE:0,PENDING:3,INACTIVE:1,ACTIVE:2})},4947:function(e,t,n){var r,i=this&&this.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n);var i=Object.getOwnPropertyDescriptor(t,n);i&&!("get"in i?!t.__esModule:i.writable||i.configurable)||(i={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,i)}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),o=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),a=this&&this.__importStar||(r=function(e){return r=Object.getOwnPropertyNames||function(e){var t=[];for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[t.length]=n);return t},r(e)},function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n=r(e),a=0;a<n.length;a++)"default"!==n[a]&&i(t,e,n[a]);return o(t,e),t});Object.defineProperty(t,"__esModule",{value:!0}),t.Log=void 0;const s=a(n(1398)),u=n(1354);t.Log=class{constructor(e){this.type=e;const t=this.type===u.AuthProviderType.github?"GitHub":"GitHub Enterprise";this.output=s.window.createOutputChannel(`${t} Authentication`,{log:!0})}trace(e){this.output.trace(e)}info(e){this.output.info(e)}error(e){this.output.error(e)}warn(e){this.output.warn(e)}}},5014:(e,t,n)=>{n.d(t,{J:()=>s});var r=n(5025),i=n(5130),o=function(){this.aiDataContract={name:1,kind:0,value:1,count:0,min:0,max:0,stdDev:0},this.kind=0},a=n(7975),s=function(){function e(e,t,n,s,u,c,l,f,d){this.aiDataContract={ver:1,metrics:1,properties:0};var v=this;v.ver=2;var h=new o;h[i.F2]=s>0?s:void 0,h.max=isNaN(c)||null===c?void 0:c,h.min=isNaN(u)||null===u?void 0:u,h[i.RS]=(0,a.Rr)(e,t)||r.R2,h.value=n,h.stdDev=isNaN(l)||null===l?void 0:l,v.metrics=[h],v[i.$y]=(0,a.xP)(e,f),v[i.XA]=(0,a.Vj)(e,d)}return e.envelopeType="Microsoft.ApplicationInsights.{0}.Metric",e.dataType="MetricData",e}()},5025:(e,t,n)=>{n.d(t,{R2:()=>c,_G:()=>s,jp:()=>o,ks:()=>l,tU:()=>i,wc:()=>u,xF:()=>r,ym:()=>a});var r="Microsoft_ApplicationInsights_BypassAjaxInstrumentation",i="sampleRate",o="ProcessLegacy",a="http.method",s="https://dc.services.visualstudio.com",u="/v2/track",c="not_specified",l="iKey"},5034:(e,t,n)=>{n.d(t,{It:()=>F,gi:()=>k,um:()=>A,xN:()=>D});var r,i,o,a=n(269),s=n(2475),u=n(9749),c=n(6182),l=n(3775),f=n(7292),d=n(3673),v=n(6492),h="toGMTString",p="toUTCString",g="cookie",m="expires",y="isCookieUseDisabled",b="disableCookiesUsage",w="_ckMgr",S=null,_=null,I=null,E={},P={},T=((r={cookieCfg:(0,s.NU)((i={},i[v.Fk]={fb:"cookieDomain",dfVal:d.Gh},i.path={fb:"cookiePath",dfVal:d.Gh},i.enabled=v.HP,i.ignoreCookies=v.HP,i.blockedCookies=v.HP,i)),cookieDomain:v.HP,cookiePath:v.HP})[b]=v.HP,r);function C(){!o&&(o=(0,a.nRs)(function(){return(0,a.YEm)()}))}function x(e){return!e||e.isEnabled()}function O(e,t){return!!(t&&e&&(0,a.cyL)(e.ignoreCookies))&&-1!==(0,a.rDm)(e.ignoreCookies,t)}function R(e,t){var n=t[c.XM];if((0,a.hXl)(n)){var r=void 0;(0,a.b07)(e[y])||(r=!e[y]),(0,a.b07)(e[b])||(r=!e[b]),n=r}return n}function A(e,t){var n;if(e)n=e.getCookieMgr();else if(t){var r=t.cookieCfg;n=r&&r[w]?r[w]:D(t)}return n||(n=function(e,t){var n=D[w]||P[w];return n||(n=D[w]=D(e,t),P[w]=n),n}(t,(e||{})[c.Uw])),n}function D(e,t){var n,r,i,o,s,l,g,y,b;e=(0,u.e)(e||P,null,t).cfg,s=(0,u.a)(e,function(t){t[c.h0](t.cfg,T),r=t.ref(t.cfg,"cookieCfg"),i=r[v.QW]||"/",o=r[v.Fk],l=!1!==R(e,r),g=r.getCookie||U,y=r.setCookie||H,b=r.delCookie||H},t);var S=((n={isEnabled:function(){var n=!1!==R(e,r)&&l&&k(t),i=P[w];return n&&i&&S!==i&&(n=x(i)),n},setEnabled:function(e){l=!1!==e,r[c.XM]=e},set:function(e,t,n,s,u){var l=!1;if(x(S)&&!function(e,t){return!!(t&&e&&(0,a.cyL)(e.blockedCookies)&&-1!==(0,a.rDm)(e.blockedCookies,t))||O(e,t)}(r,e)){var g={},b=(0,a.EHq)(t||v.m5),w=(0,a.HzD)(b,";");if(-1!==w&&(b=(0,a.EHq)((0,a.ZWZ)(t,w)),g=N((0,a.P0f)(t,w+1))),(0,d.KY)(g,v.Fk,s||o,a.zzB,a.b07),!(0,a.hXl)(n)){var I=(0,f.lT)();if((0,a.b07)(g[m])){var E=(0,a.f0d)()+1e3*n;if(E>0){var P=new Date;P.setTime(E),(0,d.KY)(g,m,M(P,I?h:p)||M(P,I?h:p)||v.m5,a.zzB)}}I||(0,d.KY)(g,"max-age",v.m5+n,null,a.b07)}var T=(0,f.g$)();T&&"https:"===T[c.Qg]&&((0,d.KY)(g,"secure",null,null,a.b07),null===_&&(_=!F(((0,a.w3n)()||{})[c.tX])),_&&(0,d.KY)(g,"SameSite","None",null,a.b07)),(0,d.KY)(g,v.QW,u||i,null,a.b07),y(e,L(b,g)),l=!0}return l},get:function(e){var t=v.m5;return x(S)&&!O(r,e)&&(t=g(e)),t},del:function(e,t){var n=!1;return x(S)&&(n=S.purge(e,t)),n},purge:function(e,n){var r,i=!1;if(k(t)){var o=((r={})[v.QW]=n||"/",r[m]="Thu, 01 Jan 1970 00:00:01 GMT",r);(0,f.lT)()||(o["max-age"]="0"),b(e,L(v.m5,o)),i=!0}return i}})[c.M5]=function(e){s&&s.rm(),s=null},n);return S[w]=S,S}function k(e){if(null===S){S=!1,!o&&C();try{var t=o.v||{};S=void 0!==t[g]}catch(t){(0,l.ZP)(e,2,68,"Cannot access document.cookie - "+(0,d.lL)(t),{exception:(0,a.mmD)(t)})}}return S}function N(e){var t={};if(e&&e[c.oI]){var n=(0,a.EHq)(e)[c.sY](";");(0,a.Iuo)(n,function(e){if(e=(0,a.EHq)(e||v.m5)){var n=(0,a.HzD)(e,"=");-1===n?t[e]=null:t[(0,a.EHq)((0,a.ZWZ)(e,n))]=(0,a.EHq)((0,a.P0f)(e,n+1))}})}return t}function M(e,t){return(0,a.Tnt)(e[t])?e[t]():null}function L(e,t){var n=e||v.m5;return(0,a.zav)(t,function(e,t){n+="; "+e+((0,a.hXl)(t)?v.m5:"="+t)}),n}function U(e){var t=v.m5;if(!o&&C(),o.v){var n=o.v[g]||v.m5;I!==n&&(E=N(n),I=n),t=(0,a.EHq)(E[e]||v.m5)}return t}function H(e,t){!o&&C(),o.v&&(o.v[g]=e+"="+t)}function F(e){return!(!(0,a.KgX)(e)||!(0,d.Ju)(e,"CPU iPhone OS 12")&&!(0,d.Ju)(e,"iPad; CPU OS 12")&&!((0,d.Ju)(e,"Macintosh; Intel Mac OS X 10_14")&&(0,d.Ju)(e,"Version/")&&(0,d.Ju)(e,"Safari"))&&(!(0,d.Ju)(e,"Macintosh; Intel Mac OS X 10_14")||!(0,a.Cv9)(e,"AppleWebKit/605.1.15 (KHTML, like Gecko)"))&&!(0,d.Ju)(e,"Chrome/5")&&!(0,d.Ju)(e,"Chrome/6")&&(!(0,d.Ju)(e,"UnrealEngine")||(0,d.Ju)(e,"Chrome"))&&!(0,d.Ju)(e,"UCBrowser/12")&&!(0,d.Ju)(e,"UCBrowser/11"))}},5130:(e,t,n)=>{n.d(t,{$e:()=>y,$y:()=>P,AZ:()=>c,Av:()=>L,C9:()=>m,Cx:()=>h,F2:()=>d,Fq:()=>R,IE:()=>N,J$:()=>C,Jj:()=>b,Jm:()=>v,OK:()=>I,OL:()=>o,Ol:()=>S,QE:()=>k,RS:()=>l,Ue:()=>w,Ur:()=>O,XA:()=>T,fd:()=>j,h_:()=>H,i9:()=>g,lW:()=>M,lx:()=>U,oI:()=>i,on:()=>E,pM:()=>f,qd:()=>F,qg:()=>x,r1:()=>A,sY:()=>r,up:()=>_,vu:()=>D,xE:()=>s,y5:()=>u,zV:()=>a,zw:()=>p});var r="split",i="length",o="toLowerCase",a="ingestionendpoint",s="toString",u="push",c="removeItem",l="name",f="message",d="count",v="preTriggerDate",h="disabled",p="interval",g="daysOfMonth",m="date",y="getUTCDate",b="stringify",w="pathname",S="correlationHeaderExcludePatterns",_="extensionConfig",I="exceptions",E="parsedStack",P="properties",T="measurements",C="sizeInBytes",x="typeName",O="severityLevel",R="problemGroup",A="isManual",D="CreateFromInterface",k="assembly",N="fileName",M="hasFullStack",L="level",U="method",H="line",F="duration",j="receivedResponse"},5256:function(e,t,n){var r,i=this&&this.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n);var i=Object.getOwnPropertyDescriptor(t,n);i&&!("get"in i?!t.__esModule:i.writable||i.configurable)||(i={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,i)}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),o=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),a=this&&this.__importStar||(r=function(e){return r=Object.getOwnPropertyNames||function(e){var t=[];for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[t.length]=n);return t},r(e)},function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n=r(e),a=0;a<n.length;a++)"default"!==n[a]&&i(t,e,n[a]);return o(t,e),t});Object.defineProperty(t,"__esModule",{value:!0}),t.activate=function(e){const t=new u.UriEventHandler;e.subscriptions.push(t),e.subscriptions.push(s.window.registerUriHandler(t)),e.subscriptions.push(new u.GitHubAuthenticationProvider(e,t));let n=s.workspace.getConfiguration().get("github-enterprise.uri"),r=d(e,t);e.subscriptions.push(s.workspace.onDidChangeConfiguration(i=>{if(i.affectsConfiguration("github-enterprise.uri")){const i=s.workspace.getConfiguration().get("github-enterprise.uri");n!==i&&(r?.dispose(),n=i,r=d(e,t))}}))};const s=a(n(1398)),u=n(1354),c='"github-enterprise.uri" not set',l='"github-enterprise.uri" invalid';class f{constructor(e){this._errorMessage=e,this._onDidChangeSessions=new s.EventEmitter,this.onDidChangeSessions=this._onDidChangeSessions.event,this._disposable=s.authentication.registerAuthenticationProvider("github-enterprise","GitHub Enterprise",this)}createSession(){throw new Error(this._errorMessage)}getSessions(){return Promise.resolve([])}removeSession(){throw new Error(this._errorMessage)}dispose(){this._onDidChangeSessions.dispose(),this._disposable.dispose()}}function d(e,t){const n=s.workspace.getConfiguration().get("github-enterprise.uri");if(!n){const t=new f(c);return e.subscriptions.push(t),t}let r;try{r=s.Uri.parse(n,!0)}catch(t){s.window.showErrorMessage(s.l10n.t("GitHub Enterprise Server URI is not a valid URI: {0}",t.message??t));const n=new f(l);return e.subscriptions.push(n),n}const i=new u.GitHubAuthenticationProvider(e,t,r);return e.subscriptions.push(i),i}},5396:function(e,t,n){var r=this&&this.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n);var i=Object.getOwnPropertyDescriptor(t,n);i&&!("get"in i?!t.__esModule:i.writable||i.configurable)||(i={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,i)}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),i=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),o=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)"default"!==n&&Object.prototype.hasOwnProperty.call(e,n)&&r(t,e,n);return i(t,e),t};Object.defineProperty(t,"__esModule",{value:!0}),t.oneDataSystemClientFactory=void 0,t.oneDataSystemClientFactory=async(e,t,r)=>{let i=await(async(e,t,r)=>{const i=await Promise.resolve().then(()=>o(n(956))),a=await Promise.resolve().then(()=>o(n(8916))),s=new i.AppInsightsCore,u=new a.PostChannel,c={instrumentationKey:e,endpointUrl:"https://mobile.events.data.microsoft.com/OneCollector/1.0",loggingLevelTelemetry:0,loggingLevelConsole:0,disableCookiesUsage:!0,disableDbgExt:!0,disableInstrumentationKeyValidation:!0,channels:[[u]]};if(r){c.extensionConfig={};const e={alwaysUseXhrOverride:!0,httpXHROverride:r};c.extensionConfig[u.identifier]=e}const l=t.workspace.getConfiguration("telemetry").get("internalTesting");return s.initialize(c,[]),s.addTelemetryInitializer(e=>{e.ext=e.ext??{},e.ext.web=e.ext.web??{},e.ext.web.consentDetails='{"GPC_DataSharingOptIn":false}',l&&(e.ext.utc=e.ext.utc??{},e.ext.utc.flags=8462029)}),s})(e,t,r);return{logEvent:(e,t)=>{try{i?.track({name:e,baseData:{name:e,properties:t?.properties,measurements:t?.measurements}})}catch(e){throw new Error("Failed to log event to app insights!\n"+e.message)}},flush:async()=>{try{return new Promise((e,t)=>{i?i.flush(!0,e=>{e||t("Failed to flush app 1DS!")}):e()})}catch(e){throw new Error("Failed to flush 1DS!\n"+e.message)}},dispose:async()=>new Promise(e=>{i?i.unload(!1,()=>{e(),i=void 0},1e3):e()})}}},5397:(e,t,n)=>{n.d(t,{WJ:()=>S});var r=n(659),i=n(269),o=n(5025),a=n(5130),s=n(7975),u="error",c="stack",l="stackDetails",f="errorSrc",d="message",v="description";function h(e,t){var n=e;return n&&!(0,i.KgX)(n)&&(JSON&&JSON[a.Jj]?(n=JSON[a.Jj](e),!t||n&&"{}"!==n||(n=(0,i.Tnt)(e[a.xE])?e[a.xE]():""+e)):n=e+" - (Missing JSON.stringify)"),n||""}function p(e,t){var n=e;return e&&(n&&!(0,i.KgX)(n)&&(n=e[d]||e[v]||n),n&&!(0,i.KgX)(n)&&(n=h(n,!0)),e.filename&&(n=n+" @"+(e.filename||"")+":"+(e.lineno||"?")+":"+(e.colno||"?"))),t&&"String"!==t&&"Object"!==t&&"Error"!==t&&-1===(0,i.HzD)(n||"",t)&&(n=t+": "+n),n||""}function g(e){return e&&e.src&&(0,i.KgX)(e.src)&&e.obj&&(0,i.cyL)(e.obj)}function m(e){var t=e||"";(0,i.KgX)(t)||(t=(0,i.KgX)(t[c])?t[c]:""+t);var n=t[a.sY]("\n");return{src:t,obj:n}}function y(e){var t=null;if(e)try{if(e[c])t=m(e[c]);else if(e[u]&&e[u][c])t=m(e[u][c]);else if(e.exception&&e.exception[c])t=m(e.exception[c]);else if(g(e))t=e;else if(g(e[l]))t=e[l];else if((0,i.zkX)()&&(0,i.zkX)().opera&&e[d])t=function(e){for(var t=[],n=e[a.sY]("\n"),r=0;r<n[a.oI];r++){var i=n[r];n[r+1]&&(i+="@"+n[r+1],r++),t[a.y5](i)}return{src:e,obj:t}}(e[a.pM]);else if(e.reason&&e.reason[c])t=m(e.reason[c]);else if((0,i.KgX)(e))t=m(e);else{var n=e[d]||e[v]||"";(0,i.KgX)(e[f])&&(n&&(n+="\n"),n+=" from "+e[f]),n&&(t=m(n))}}catch(e){t=m(e)}return t||{src:"",obj:null}}function b(e){var t="";if(e&&!(t=e.typeName||e[a.RS]||""))try{var n=/function (.{1,200})\(/.exec(e.constructor[a.xE]());t=n&&n[a.oI]>1?n[1]:""}catch(e){}return t}function w(e){if(e)try{if(!(0,i.KgX)(e)){var t=b(e),n=h(e,!1);return n&&"{}"!==n||(e[u]&&(t=b(e=e[u])),n=h(e,!0)),0!==(0,i.HzD)(n,t)&&"String"!==t?t+":"+n:n}}catch(e){}return""+(e||"")}var S=function(){function e(e,t,n,r,o,u){this.aiDataContract={ver:1,exceptions:1,severityLevel:0,properties:0,measurements:0};var c=this;c.ver=2,function(e){try{if((0,i.Gvm)(e))return"ver"in e&&"exceptions"in e&&"properties"in e}catch(e){}return!1}(t)?(c[a.OK]=t[a.OK]||[],c[a.$y]=t[a.$y],c[a.XA]=t[a.XA],t[a.Ur]&&(c[a.Ur]=t[a.Ur]),t.id&&(c.id=t.id,t[a.$y].id=t.id),t[a.Fq]&&(c[a.Fq]=t[a.Fq]),(0,i.hXl)(t[a.r1])||(c[a.r1]=t[a.r1])):(n||(n={}),u&&(n.id=u),c[a.OK]=[new _(e,t,n)],c[a.$y]=(0,s.xP)(e,n),c[a.XA]=(0,s.Vj)(e,r),o&&(c[a.Ur]=o),u&&(c.id=u))}return e.CreateAutoException=function(e,t,n,r,i,o,s,u){var c,l=b(i||o||e);return(c={})[a.pM]=p(e,l),c.url=t,c.lineNumber=n,c.columnNumber=r,c.error=w(i||o||e),c.evt=w(o||e),c[a.qg]=l,c.stackDetails=y(s||i||o),c.errorSrc=u,c},e.CreateFromInterface=function(t,n,o,s){var u=n[a.OK]&&(0,i.W$7)(n[a.OK],function(e){return _[a.vu](t,e)});return new e(t,(0,r.Im)((0,r.Im)({},n),{exceptions:u}),o,s)},e.prototype.toInterface=function(){var e,t=this,n=t.exceptions,r=t.properties,o=t.measurements,s=t.severityLevel,u=t.problemGroup,c=t.id,l=t.isManual,f=n instanceof Array&&(0,i.W$7)(n,function(e){return e.toInterface()})||void 0;return(e={ver:"4.0"})[a.OK]=f,e.severityLevel=s,e.properties=r,e.measurements=o,e.problemGroup=u,e.id=c,e.isManual=l,e},e.CreateSimpleException=function(e,t,n,r,i,o){var s;return{exceptions:[(s={},s[a.lW]=!0,s.message=e,s.stack=i,s.typeName=t,s)]}},e.envelopeType="Microsoft.ApplicationInsights.{0}.Exception",e.dataType="ExceptionData",e.formatError=w,e}(),_=function(){function e(e,t,n){this.aiDataContract={id:0,outerId:0,typeName:1,message:1,hasFullStack:0,stack:0,parsedStack:2};var r=this;if(function(e){try{if((0,i.Gvm)(e))return"hasFullStack"in e&&"typeName"in e}catch(e){}return!1}(t))r[a.qg]=t[a.qg],r[a.pM]=t[a.pM],r[c]=t[c],r[a.on]=t[a.on]||[],r[a.lW]=t[a.lW];else{var f=t,d=f&&f.evt;(0,i.bJ7)(f)||(f=f[u]||d||f),r[a.qg]=(0,s.Rr)(e,b(f))||o.R2,r[a.pM]=(0,s.Vk)(e,p(t||f,r[a.qg]))||o.R2;var v=t[l]||y(t);r[a.on]=function(e){var t,n=e.obj;if(n&&n[a.oI]>0){t=[];var r=0,o=0;if((0,i.Iuo)(n,function(e){var n=e[a.xE]();if(I.regex.test(n)){var i=new I(n,r++);o+=i[a.J$],t[a.y5](i)}}),o>32768)for(var s=0,u=t[a.oI]-1,c=0,l=s,f=u;s<u;){if((c+=t[s][a.J$]+t[u][a.J$])>32768){var d=f-l+1;t.splice(l,d);break}l=s,f=u,s++,u--}}return t}(v),(0,i.cyL)(r[a.on])&&(0,i.W$7)(r[a.on],function(t){t[a.QE]=(0,s.Rr)(e,t[a.QE]),t[a.IE]=(0,s.Rr)(e,t[a.IE])}),r[c]=(0,s.Vt)(e,function(e){var t="";return e&&(e.obj?(0,i.Iuo)(e.obj,function(e){t+=e+"\n"}):t=e.src||""),t}(v)),r.hasFullStack=(0,i.cyL)(r.parsedStack)&&r.parsedStack[a.oI]>0,n&&(n[a.qg]=n[a.qg]||r[a.qg])}}return e.prototype.toInterface=function(){var e,t=this,n=t[a.on]instanceof Array&&(0,i.W$7)(t[a.on],function(e){return e.toInterface()});return(e={id:t.id,outerId:t.outerId,typeName:t[a.qg],message:t[a.pM],hasFullStack:t[a.lW],stack:t[c]})[a.on]=n||void 0,e},e.CreateFromInterface=function(t,n){var o=n[a.on]instanceof Array&&(0,i.W$7)(n[a.on],function(e){return I[a.vu](e)})||n[a.on];return new e(t,(0,r.Im)((0,r.Im)({},n),{parsedStack:o}))},e}(),I=function(){function e(t,n){this.aiDataContract={level:1,method:1,assembly:0,fileName:0,line:0};var r=this;if(r[a.J$]=0,"string"==typeof t){var o=t;r[a.Av]=n,r[a.lx]="<no_method>",r[a.QE]=(0,i.EHq)(o),r[a.IE]="",r[a.h_]=0;var s=o.match(e.regex);s&&s[a.oI]>=5&&(r[a.lx]=(0,i.EHq)(s[2])||r[a.lx],r[a.IE]=(0,i.EHq)(s[4]),r[a.h_]=parseInt(s[5])||0)}else r[a.Av]=t[a.Av],r[a.lx]=t[a.lx],r[a.QE]=t[a.QE],r[a.IE]=t[a.IE],r[a.h_]=t[a.h_],r[a.J$]=0;r.sizeInBytes+=r.method[a.oI],r.sizeInBytes+=r.fileName[a.oI],r.sizeInBytes+=r.assembly[a.oI],r[a.J$]+=e.baseSize,r.sizeInBytes+=r.level.toString()[a.oI],r.sizeInBytes+=r.line.toString()[a.oI]}return e.CreateFromInterface=function(t){return new e(t,null)},e.prototype.toInterface=function(){var e=this;return{level:e[a.Av],method:e[a.lx],assembly:e[a.QE],fileName:e[a.IE],line:e[a.h_]}},e.regex=/^([\s]+at)?[\s]{0,50}([^\@\()]+?)[\s]{0,50}(\@|\()([^\(\n]+):([0-9]+):([0-9]+)(\)?)$/,e.baseSize=58,e}()},5571:(e,t,n)=>{n.d(t,{G:()=>u});var r=n(6149),i=n(269),o=n(4276),a=n(5130);function s(e,t){(0,r.ML)(e,null,null,t)}function u(e){var t=(0,i.YEm)(),n=(0,i.w3n)(),u=!1,c=[],l=1;!n||(0,i.hXl)(n.onLine)||n.onLine||(l=2);var f=0,d=g(),v=(0,r.Hm)((0,o.Z)("OfflineListener"),e);try{if(p((0,i.zkX)())&&(u=!0),t){var h=t.body||t;h.ononline&&p(h)&&(u=!0)}}catch(e){u=!1}function p(e){var t=!1;return e&&(t=(0,r.mB)(e,"online",y,v))&&(0,r.mB)(e,"offline",b,v),t}function g(){return 2!==f&&2!==l}function m(){var e=g();d!==e&&(d=e,(0,i.Iuo)(c,function(e){var t={isOnline:d,rState:l,uState:f};try{e(t)}catch(e){}}))}function y(){l=1,m()}function b(){l=2,m()}return{isOnline:function(){return d},isListening:function(){return u},unload:function(){var e=(0,i.zkX)();if(e&&u){if(s(e,v),t){var n=t.body||t;(0,i.b07)(n.ononline)||s(n,v)}u=!1}},addListener:function(e){return c[a.y5](e),{rm:function(){var t=c.indexOf(e);return t>-1?c.splice(t,1):void 0}}},setOnlineState:function(e){f=e,m()}}}},5664:(e,t,n)=>{n.d(t,{Wy:()=>u,_1:()=>i,bA:()=>o,hW:()=>r,s6:()=>s,vR:()=>a});var r="function",i="object",o="undefined",a="prototype",s=Object,u=s[a]},5692:e=>{e.exports=require("https")},5761:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ExperimentationServiceAutoPolling=void 0;const r=n(1714),i=n(3257);class o extends r.ExperimentationServiceBase{constructor(e,t,n,r,o,a,s){if(super(e,r,o,a,s),this.telemetry=e,this.filterProviders=t,this.refreshRateMs=n,this.assignmentContextTelemetryPropertyName=r,this.telemetryEventName=o,this.storageKey=a,this.storage=s,n<1e3&&0!==n)throw new Error("The minimum refresh rate for polling is 1000 ms (1 second). If you wish to deactivate this auto-polling use value of 0.");n>0&&(this.pollingService=new i.PollingService(n),this.pollingService.OnPollTick(async()=>{await super.getFeaturesAsync()}))}init(){this.pollingService?this.pollingService.StartPolling(!0):super.getFeaturesAsync()}async getFeaturesAsync(e=!1){if(this.pollingService){this.pollingService.StopPolling();let t=await super.getFeaturesAsync(e);return this.pollingService.StartPolling(),t}return await super.getFeaturesAsync(e)}}t.ExperimentationServiceAutoPolling=o},5927:(e,t,n)=>{n.r(t),n.d(t,{AppInsightsCore:()=>mt._,ApplicationInsights:()=>xt,Sender:()=>pt,SeverityLevel:()=>Pt.O,arrForEach:()=>S.Iuo,isNullOrUndefined:()=>S.hXl,proxyFunctions:()=>w.o$,throwError:()=>S.$8});var r=n(8279),i=n(659),o=n(5025),a=n(3072),s=n(2445),u=n(1448),c=n(4164),l=n(5397),f=n(5014),d=n(1365),v=n(740),h=n(5571),p=n(4658),g=n(2318),m=n(2910),y=n(2475),b=n(3775),w=n(3673),S=n(269),_=n(6149),I=n(4276),E=n(9749),P=n(2317),T=n(4875),C=n(7292),x=n(856),O=n(4013),R=n(1190),A=n(8257),D=n(1575),k=n(7975),N=n(1062),M=n(7358),L="duration",U="tags",H="deviceType",F="data",j="name",z="traceID",X="length",V="stringify",$="measurements",B="dataType",q="envelopeType",K="toString",G="_get",Z="enqueue",W="count",J="eventsLimitInMem",Q="push",Y="item",ee="emitLineDelimitedJson",te="clear",ne="createNew",re="markAsSent",ie="clearSent",oe="bufferOverride",ae="BUFFER_KEY",se="SENT_BUFFER_KEY",ue="concat",ce="MAX_BUFFER_SIZE",le="triggerSend",fe="diagLog",de="initialize",ve="_sender",he="endpointUrl",pe="instrumentationKey",ge="customHeaders",me="maxBatchSizeInBytes",ye="onunloadDisableBeacon",be="isBeaconApiDisabled",we="alwaysUseXhrOverride",Se="disableXhr",_e="enableSessionStorageBuffer",Ie="_buffer",Ee="onunloadDisableFetch",Pe="disableSendBeaconSplit",Te="enableSendPromise",Ce="getSenderInst",xe="unloadTransports",Oe="convertUndefined",Re="maxBatchInterval",Ae="serialize",De="_onError",ke="_onPartialSuccess",Ne="_onSuccess",Me="itemsReceived",Le="itemsAccepted",Ue="oriPayload",He="baseType",Fe="sampleRate",je="eventsSendRequest",ze="getSamplingScore",Xe="baseType",Ve="baseData",$e="properties",Be="true";function qe(e,t,n){return(0,w.KY)(e,t,n,S.zzB)}function Ke(e,t,n){(0,S.hXl)(e)||(0,S.zav)(e,function(e,r){(0,S.EtT)(r)?n[e]=r:(0,S.KgX)(r)?t[e]=r:(0,C.Z)()&&(t[e]=(0,C.hm)()[V](r))})}function Ge(e,t){(0,S.hXl)(e)||(0,S.zav)(e,function(n,r){e[n]=r||t})}function Ze(e,t,n,r){var a=new N.L(e,r,t);qe(a,"sampleRate",n[o.tU]),(n[Ve]||{}).startTime&&(a.time=(0,w._u)(n[Ve].startTime)),a.iKey=n.iKey;var s=n.iKey.replace(/-/g,"");return a[j]=a[j].replace("{0}",s),function(e,t,n){var r=n[U]=n[U]||{},o=t.ext=t.ext||{},a=t[U]=t[U]||[],s=o.user;s&&(qe(r,D.O.userAuthUserId,s.authId),qe(r,D.O.userId,s.id||s.localId));var u=o.app;u&&qe(r,D.O.sessionId,u.sesId);var c=o.device;c&&(qe(r,D.O.deviceId,c.id||c.localId),qe(r,D.O[H],c.deviceClass),qe(r,D.O.deviceIp,c.ip),qe(r,D.O.deviceModel,c.model),qe(r,D.O[H],c[H]));var l=t.ext.web;if(l){qe(r,D.O.deviceLanguage,l.browserLang),qe(r,D.O.deviceBrowserVersion,l.browserVer),qe(r,D.O.deviceBrowser,l.browser);var f=n[F]=n[F]||{},d=f[Ve]=f[Ve]||{},v=d[$e]=d[$e]||{};qe(v,"domain",l.domain),qe(v,"isManual",l.isManual?Be:null),qe(v,"screenRes",l.screenRes),qe(v,"userConsent",l.userConsent?Be:null)}var h=o.os;h&&(qe(r,D.O.deviceOS,h[j]),qe(r,D.O.deviceOSVersion,h.osVer));var p=o.trace;p&&(qe(r,D.O.operationParentId,p.parentID),qe(r,D.O.operationName,(0,k.Rr)(e,p[j])),qe(r,D.O.operationId,p[z]));for(var g={},m=a[X]-1;m>=0;m--){var y=a[m];(0,S.zav)(y,function(e,t){g[e]=t}),a.splice(m,1)}(0,S.zav)(a,function(e,t){g[e]=t});var b=(0,i.Im)((0,i.Im)({},r),g);b[D.O.internalSdkVersion]||(b[D.O.internalSdkVersion]=(0,k.Rr)(e,"javascript:".concat(Je.Version),64)),n[U]=(0,w.hW)(b)}(e,n,a),n[U]=n[U]||[],(0,w.hW)(a)}function We(e,t){(0,S.hXl)(t[Ve])&&(0,b.ZP)(e,1,46,"telemetryItem.baseData cannot be null.")}var Je={Version:"3.3.4"};function Qe(e,t,n){We(e,t);var r={},i={};t[Xe]!==a.J[B]&&(r.baseTypeSource=t[Xe]),t[Xe]===a.J[B]?(r=t[Ve][$e]||{},i=t[Ve][$]||{}):t[Ve]&&Ke(t[Ve],r,i),Ke(t[F],r,i),(0,S.hXl)(n)||Ge(r,n);var o=t[Ve][j],s=new a.J(e,o,r,i),u=new M.B(a.J[B],s);return Ze(e,a.J[q],t,u)}var Ye,et,tt=function(){function e(t,n){var i=[],o=!1,a=n.maxRetryCnt;this[G]=function(){return i},this._set=function(e){return i=e},(0,r.A)(e,this,function(e){e[Z]=function(r){e[W]()>=n[J]?o||((0,b.ZP)(t,2,105,"Maximum in-memory buffer size reached: "+e[W](),!0),o=!0):(r.cnt=r.cnt||0,!(0,S.hXl)(a)&&r.cnt>a||i[Q](r))},e[W]=function(){return i[X]},e.size=function(){for(var e=i[X],t=0;t<i[X];t++)e+=i[t].item[X];return n[ee]||(e+=2),e},e[te]=function(){i=[],o=!1},e.getItems=function(){return i.slice(0)},e.batchPayloads=function(e){if(e&&e[X]>0){var t=[];return(0,S.Iuo)(e,function(e){t[Q](e[Y])}),n[ee]?t.join("\n"):"["+t.join(",")+"]"}return null},e[ne]=function(e,n,r){var o=i.slice(0);e=e||t,n=n||{};var a=r?new it(e,n):new nt(e,n);return(0,S.Iuo)(o,function(e){a[Z](e)}),a}})}return e.__ieDyn=1,e}(),nt=function(e){function t(n,i){var o=e.call(this,n,i)||this;return(0,r.A)(t,o,function(e,t){e[re]=function(e){t[te]()},e[ie]=function(e){}}),o}return(0,i.qU)(t,e),t.__ieDyn=1,t}(tt),rt=["AI_buffer","AI_sentBuffer"],it=function(e){function t(n,i){var o=e.call(this,n,i)||this,a=!1,s=null==i?void 0:i.namePrefix,u=i[oe]||{getItem:p.vH,setItem:p.Dt},c=u.getItem,l=u.setItem,f=i.maxRetryCnt;return(0,r.A)(t,o,function(e,r){var i=g(t[ae]),o=g(t[se]),u=function(){var e=[];try{return(0,S.Iuo)(rt,function(t){var n=_(t);if(e=e[ue](n),s){var r=_(s+"_"+t);e=e[ue](r)}}),e}catch(e){(0,b.ZP)(n,2,41,"Transfer events from previous buffers: "+(0,w.lL)(e)+". previous Buffer items can not be removed",{exception:(0,S.mmD)(e)})}return[]}(),d=o[ue](u),v=e._set(i[ue](d));function h(e,t){var n=[],r=[];return(0,S.Iuo)(e,function(e){r[Q](e[Y])}),(0,S.Iuo)(t,function(e){(0,S.Tnt)(e)||-1!==(0,S.rDm)(r,e[Y])||n[Q](e)}),n}function g(e){return m(s?s+"_"+e:e)}function m(e){try{var t=c(n,e);if(t){var r=(0,C.hm)().parse(t);if((0,S.KgX)(r)&&(r=(0,C.hm)().parse(r)),r&&(0,S.cyL)(r))return r}}catch(t){(0,b.ZP)(n,1,42," storage key: "+e+", "+(0,w.lL)(t),{exception:(0,S.mmD)(t)})}return[]}function y(e,t){var r=e;try{r=s?s+"_"+r:r;var i=JSON[V](t);l(n,r,i)}catch(e){l(n,r,JSON[V]([])),(0,b.ZP)(n,2,41," storage key: "+r+", "+(0,w.lL)(e)+". Buffer cleared",{exception:(0,S.mmD)(e)})}}function _(e){try{var t=m(e),r=[];return(0,S.Iuo)(t,function(e){var t={item:e,cnt:0};r[Q](t)}),(0,p.v7)(n,e),r}catch(e){}return[]}v[X]>t[ce]&&(v[X]=t[ce]),y(t[se],[]),y(t[ae],v),e[Z]=function(i){e[W]()>=t[ce]?a||((0,b.ZP)(n,2,67,"Maximum buffer size reached: "+e[W](),!0),a=!0):(i.cnt=i.cnt||0,!(0,S.hXl)(f)&&i.cnt>f||(r[Z](i),y(t.BUFFER_KEY,e[G]())))},e[te]=function(){r[te](),y(t.BUFFER_KEY,e[G]()),y(t[se],[]),a=!1},e[re]=function(r){y(t[ae],e._set(h(r,e[G]())));var i=g(t[se]);i instanceof Array&&r instanceof Array&&((i=i[ue](r))[X]>t[ce]&&((0,b.ZP)(n,1,67,"Sent buffer reached its maximum size: "+i[X],!0),i[X]=t[ce]),y(t[se],i))},e[ie]=function(e){var n=g(t[se]);n=h(e,n),y(t[se],n)},e[ne]=function(r,i,o){o=!!o;var a=e[G]().slice(0),s=g(t[se]).slice(0);r=r||n,i=i||{},e[te]();var u=o?new t(r,i):new nt(r,i);return(0,S.Iuo)(a,function(e){u[Z](e)}),o&&u[re](s),u}}),o}var n;return(0,i.qU)(t,e),n=t,t.VERSION="_1",t.BUFFER_KEY="AI_buffer"+n.VERSION,t.SENT_BUFFER_KEY="AI_sentBuffer"+n.VERSION,t.MAX_BUFFER_SIZE=2e3,t}(tt),ot=function(){function e(t){(0,r.A)(e,this,function(e){function n(e,o){var a="__aiCircularRefCheck",s={};if(!e)return(0,b.ZP)(t,1,48,"cannot serialize object because it is null or undefined",{name:o},!0),s;if(e[a])return(0,b.ZP)(t,2,50,"Circular reference detected while serializing object",{name:o},!0),s;if(!e.aiDataContract){if("measurements"===o)s=i(e,"number",o);else if("properties"===o)s=i(e,"string",o);else if("tags"===o)s=i(e,"string",o);else if((0,S.cyL)(e))s=r(e,o);else{(0,b.ZP)(t,2,49,"Attempting to serialize an object which does not implement ISerializable",{name:o},!0);try{(0,C.hm)()[V](e),s=e}catch(e){(0,b.ZP)(t,1,48,e&&(0,S.Tnt)(e[K])?e[K]():"Error serializing object",null,!0)}}return s}return e[a]=!0,(0,S.zav)(e.aiDataContract,function(i,a){var u=(0,S.Tnt)(a)?1&a():1&a,c=(0,S.Tnt)(a)?4&a():4&a,l=2&a,f=void 0!==e[i],d=(0,S.Gvm)(e[i])&&null!==e[i];if(!u||f||l){if(!c){var v;void 0!==(v=d?l?r(e[i],i):n(e[i],i):e[i])&&(s[i]=v)}}else(0,b.ZP)(t,1,24,"Missing required field specification. The field is required but not present on source",{field:i,name:o})}),delete e[a],s}function r(e,r){var i;if(e)if((0,S.cyL)(e)){i=[];for(var o=0;o<e[X];o++){var a=n(e[o],r+"["+o+"]");i[Q](a)}}else(0,b.ZP)(t,1,54,"This field was specified as an array in the contract but the item is not an array.\r\n",{name:r},!0);return i}function i(e,n,r){var i;return e&&(i={},(0,S.zav)(e,function(e,o){if("string"===n)void 0===o?i[e]="undefined":null===o?i[e]="null":o[K]?i[e]=o[K]():i[e]="invalid field: toString() is not defined.";else if("number"===n)if(void 0===o)i[e]="undefined";else if(null===o)i[e]="null";else{var a=parseFloat(o);i[e]=a}else i[e]="invalid field: "+r+" is of unknown type.",(0,b.ZP)(t,1,i[e],null,!0)})),i}e[Ae]=function(e){var r=n(e,"root");try{return(0,C.hm)()[V](r)}catch(e){(0,b.ZP)(t,1,48,e&&(0,S.Tnt)(e[K])?e[K]():"Error serializing object",null,!0)}}})}return e.__ieDyn=1,e}(),at=n(8596),st=function(){function e(){}return e.prototype.getHashCodeScore=function(t){return this.getHashCode(t)/e.INT_MAX_VALUE*100},e.prototype.getHashCode=function(e){if(""===e)return 0;for(;e[X]<8;)e=e[ue](e);for(var t=5381,n=0;n<e[X];++n)t=(t<<5)+t+e.charCodeAt(n),t&=t;return Math.abs(t)},e.INT_MAX_VALUE=2147483647,e}(),ut=function(){var e=new st,t=new at.o;this[ze]=function(n){return n[U]&&n[U][t.userId]?e.getHashCodeScore(n[U][t.userId]):n.ext&&n.ext.user&&n.ext.user.id?e.getHashCodeScore(n.ext.user.id):n[U]&&n[U][t.operationId]?e.getHashCodeScore(n[U][t.operationId]):n.ext&&n.ext.telemetryTrace&&n.ext.telemetryTrace[z]?e.getHashCodeScore(n.ext.telemetryTrace[z]):100*Math.random()}},ct=function(){function e(e,t){this.INT_MAX_VALUE=2147483647;var n=t||(0,b.y0)(null);(e>100||e<0)&&(n.throwInternal(2,58,"Sampling rate is out of range (0..100). Sampling will be disabled, you may be sending too much data which may affect your AI service level.",{samplingRate:e},!0),e=100),this[Fe]=e,this.samplingScoreGenerator=new ut}return e.prototype.isSampledIn=function(e){var t=this[Fe];return null==t||t>=100||e.baseType===f.J[B]||this.samplingScoreGenerator[ze](e)<t},e}(),lt=void 0;function ft(e){try{return e.responseText}catch(e){}return null}var dt,vt=(0,S.ZHX)(((Ye={endpointUrl:(0,y.Lx)(S.zzB,o._G+o.wc)})[ee]=(0,y.DD)(),Ye[Re]=15e3,Ye[me]=102400,Ye.disableTelemetry=(0,y.DD)(),Ye[_e]=(0,y.DD)(!0),Ye.isRetryDisabled=(0,y.DD)(),Ye[be]=(0,y.DD)(!0),Ye[Pe]=(0,y.DD)(!0),Ye[Se]=(0,y.DD)(),Ye[Ee]=(0,y.DD)(),Ye[ye]=(0,y.DD)(),Ye[pe]=lt,Ye.namePrefix=lt,Ye.samplingPercentage=(0,y.Lx)(function(e){return!isNaN(e)&&e>0&&e<=100},100),Ye[ge]=lt,Ye[Oe]=lt,Ye[J]=1e4,Ye[oe]=!1,Ye.httpXHROverride={isVal:function(e){return e&&e.sendPOST},v:lt},Ye[we]=(0,y.DD)(),Ye.transports=lt,Ye.retryCodes=lt,Ye.maxRetryCnt={isVal:S.EtT,v:10},Ye)),ht=((et={})[a.J.dataType]=Qe,et[s.C.dataType]=function(e,t,n){We(e,t);var r=t[Ve].message,i=t[Ve].severityLevel,o=t[Ve][$e]||{},a=t[Ve][$]||{};Ke(t[F],o,a),(0,S.hXl)(n)||Ge(o,n);var u=new s.C(e,r,i,o,a),c=new M.B(s.C[B],u);return Ze(e,s.C[q],t,c)},et[u.h.dataType]=function(e,t,n){var r;We(e,t);var i=t[Ve];(0,S.hXl)(i)||(0,S.hXl)(i[$e])||(0,S.hXl)(i[$e][L])?(0,S.hXl)(t[F])||(0,S.hXl)(t[F][L])||(r=t[F][L],delete t[F][L]):(r=i[$e][L],delete i[$e][L]);var o,a=t[Ve];((t.ext||{}).trace||{})[z]&&(o=t.ext.trace[z]);var s=a.id||o,c=a[j],l=a.uri,f=a[$e]||{},d=a[$]||{};if((0,S.hXl)(a.refUri)||(f.refUri=a.refUri),(0,S.hXl)(a.pageType)||(f.pageType=a.pageType),(0,S.hXl)(a.isLoggedIn)||(f.isLoggedIn=a.isLoggedIn[K]()),!(0,S.hXl)(a[$e])){var v=a[$e];(0,S.zav)(v,function(e,t){f[e]=t})}Ke(t[F],f,d),(0,S.hXl)(n)||Ge(f,n);var h=new u.h(e,c,l,r,f,d,s),p=new M.B(u.h[B],h);return Ze(e,u.h[q],t,p)},et[c.H.dataType]=function(e,t,n){We(e,t);var r=t[Ve],i=r[j],o=r.uri||r.url,a=r[$e]||{},s=r[$]||{};Ke(t[F],a,s),(0,S.hXl)(n)||Ge(a,n);var u=new c.H(e,i,o,void 0,a,s,r),l=new M.B(c.H[B],u);return Ze(e,c.H[q],t,l)},et[l.WJ.dataType]=function(e,t,n){We(e,t);var r=t[Ve][$]||{},i=t[Ve][$e]||{};Ke(t[F],i,r),(0,S.hXl)(n)||Ge(i,n);var o=t[Ve],a=l.WJ.CreateFromInterface(e,o,i,r),s=new M.B(l.WJ[B],a);return Ze(e,l.WJ[q],t,s)},et[f.J.dataType]=function(e,t,n){We(e,t);var r=t[Ve],i=r[$e]||{},o=r[$]||{};Ke(t[F],i,o),(0,S.hXl)(n)||Ge(i,n);var a=new f.J(e,r[j],r.average,r.sampleCount,r.min,r.max,r.stdDev,i,o),s=new M.B(f.J[B],a);return Ze(e,f.J[q],t,s)},et[d.A.dataType]=function(e,t,n){We(e,t);var r=t[Ve][$]||{},i=t[Ve][$e]||{};Ke(t[F],i,r),(0,S.hXl)(n)||Ge(i,n);var a=t[Ve];if((0,S.hXl)(a))return(0,b.OG)(e,"Invalid input for dependency data"),null;var s=a[$e]&&a[$e][o.ym]?a[$e][o.ym]:"GET",u=new d.A(e,a.id,a.target,a[j],a[L],a.success,a.responseCode,s,a.type,a.correlationContext,i,r),c=new M.B(d.A[B],u);return Ze(e,d.A[q],t,c)},et),pt=function(e){function t(){var n,i,a,s,u,c,l,f=e.call(this)||this;f.priority=1001,f.identifier=v.BreezeChannelIdentifier;var d,y,A,D,k,N,M,L,H,j,z,V,$,B,q,K,G,J,ee,ae,se,ue,ce,ze,Xe,Ve,$e,Be=0;return(0,r.A)(t,f,function(e,r){function v(t,r){var i=ft(t);if(!t||i+""!="200"&&""!==i){var o=(0,R.x)(i);o&&o[Me]&&o[Me]>o[Le]&&!B?e[ke](r,o):e[De](r,(0,w.HU)(t))}else n=0,e[Ne](r,0)}function qe(e,t,n){4===e.readyState&&at(e.status,t,e.responseURL,n,(0,w.r4)(e),ft(e)||e.response)}function Ke(e){try{if(e){var t=e[Ue];return t&&t[X]?t:null}}catch(e){}return null}function Ge(t,n){return!(z||(t?t.baseData&&!t[He]?(n&&(0,b.ZP)(n,1,70,"Cannot send telemetry without baseData and baseType"),1):(t[He]||(t[He]="EventData"),e[ve]?(r=t,e._sample.isSampledIn(r)?(t[o.tU]=e._sample[Fe],0):(n&&(0,b.ZP)(n,2,33,"Telemetry item was sampled out and not sent",{SampleRate:e._sample[Fe]}),1)):(n&&(0,b.ZP)(n,1,28,"Sender was not initialized"),1)):(n&&(0,b.ZP)(n,1,7,"Cannot send empty telemetry"),1)));var r}function Ze(e,n){var r=e.iKey||V,i=t.constructEnvelope(e,r,n,$);if(i){var a=!1;if(e[U]&&e[U][o.jp]&&((0,S.Iuo)(e[U][o.jp],function(e){try{e&&!1===e(i)&&(a=!0,(0,b.OG)(n,"Telemetry processor check returns false"))}catch(e){(0,b.ZP)(n,1,64,"One of telemetry initializers failed, telemetry item will not be sent: "+(0,w.lL)(e),{exception:(0,S.mmD)(e)},!0)}}),delete e[U][o.jp]),!a)return i}else(0,b.ZP)(n,1,47,"Unable to create an AppInsights envelope")}function We(t){var n="",r=e[fe]();try{var i=Ge(t,r),o=null;i&&(o=Ze(t,r)),o&&(n=u[Ae](o))}catch(e){}return n}function Je(e){var t="";return e&&e[X]&&(t="["+e.join(",")+"]"),t}function Qe(e){var t,n=tt();return(t={urlString:D})[F]=e,t.headers=n,t}function Ye(t,n,r,i){void 0===i&&(i=!0);var o=et(n),a=t&&t.sendPOST;return a&&o?(i&&e._buffer[re](n),a(o,function(t,r,i){return function(t,n,r,i){200===n&&t?e._onSuccess(t,t[X]):i&&e[De](t,i)}(n,t,0,i)},!r)):null}function et(t){var n;if((0,S.cyL)(t)&&t[X]>0){var r=e[Ie].batchPayloads(t),i=tt();return(n={})[F]=r,n.urlString=D,n.headers=i,n.disableXhrSync=se,n.disableFetchKeepAlive=!ue,n[Ue]=t,n}return null}function tt(){try{var e=l||{};return(0,g.Qu)(D)&&(e[m.a[6]]=m.a[7]),e}catch(e){}return null}function rt(t){var n=t?t[X]:0;return e[Ie].size()+n>N&&(y&&!y.isOnline()||e[le](!0,null,10),!0)}function at(t,r,i,o,a,s){var u=null;if(e._appId||(u=(0,R.x)(s))&&u.appId&&(e._appId=u.appId),(t<200||t>=300)&&0!==t){if((301===t||307===t||308===t)&&!st(i))return void e[De](r,a);if(y&&!y.isOnline())return void(B||(pt(r,10),(0,b.ZP)(e[fe](),2,40,". Offline - Response Code: ".concat(t,". Offline status: ").concat(!y.isOnline(),". Will retry to send ").concat(r.length," items."))));!B&&yt(t)?(pt(r),(0,b.ZP)(e[fe](),2,40,". Response code "+t+". Will retry to send "+r[X]+" items.")):e[De](r,a)}else st(i),206===t?(u||(u=(0,R.x)(s)),u&&!B?e[ke](r,u):e[De](r,a)):(n=0,e[Ne](r,o))}function st(e){return!(c>=10||(0,S.hXl)(e)||""===e||e===D||(D=e,++c,0))}function ut(e,t){if(!d)return Ye(Ve&&Ve[Ce]([3],!0),e,t);d(e,!1)}function dt(e){try{if(e&&e[X])return(0,S.KgX)(e[0])}catch(e){}return null}function ht(t,n){var r=null;if((0,S.cyL)(t)){for(var i=t[X],o=0;o<t[X];o++)i+=t[o].item[X];return Ve.getSyncFetchPayload()+i<=65e3?r=2:(0,C.Uf)()?r=3:(r=1,(0,b.ZP)(e[fe](),2,40,". Failed to send telemetry with Beacon API, retried with xhrSender.")),Ye(Ve&&Ve[Ce]([r],!0),t,n)}return null}function pt(t,r){if(void 0===r&&(r=1),t&&0!==t[X]){var o=e[Ie];o[ie](t),n++;for(var a=0,s=t;a<s.length;a++){var u=s[a];u.cnt=u.cnt||0,u.cnt++,o[Z](u)}!function(e){var t;if(n<=1)t=10;else{var r=(Math.pow(2,n)-1)/2,o=Math.floor(Math.random()*r*10)+1;o*=e,t=Math.max(Math.min(o,3600),10)}var a=(0,S.f0d)()+1e3*t;i=a}(r),gt()}}function gt(){if(!s&&!a){var t=i?Math.max(0,i-(0,S.f0d)()):0,n=Math.max(q,t);s=(0,S.dRz)(function(){s=null,e[le](!0,null,1)},n)}}function mt(){s&&s.cancel(),s=null,i=null}function yt(e){return(0,S.hXl)($e)?401===e||408===e||429===e||500===e||502===e||503===e||504===e:$e[X]&&$e.indexOf(e)>-1}function bt(){e[ve]=null,e[Ie]=null,e._appId=null,e._sample=null,l={},y=null,n=0,i=null,a=!1,s=null,u=null,c=0,Be=0,d=null,A=null,D=null,k=null,N=0,M=!1,j=null,z=!1,V=null,$=lt,B=!1,K=null,J=lt,se=!1,ue=!1,Xe=!1,ce=null,ze=null,Ve=null,(0,S.vF1)(e,"_senderConfig",{g:function(){return(0,w.CP)({},vt)}})}bt(),e.pause=function(){mt(),a=!0},e.resume=function(){a&&(a=!1,i=null,rt(),gt())},e.flush=function(t,n,r){if(void 0===t&&(t=!0),!a){mt();try{return e[le](t,null,r||1)}catch(t){(0,b.ZP)(e[fe](),1,22,"flush failed, telemetry will not be collected: "+(0,w.lL)(t),{exception:(0,S.mmD)(t)})}}},e.onunloadFlush=function(){if(!a)if(M||ae)try{return e[le](!0,ut,2)}catch(t){(0,b.ZP)(e[fe](),1,20,"failed to flush with beacon sender on page unload, telemetry will not be collected: "+(0,w.lL)(t),{exception:(0,S.mmD)(t)})}else e.flush(!1)},e.addHeader=function(e,t){l[e]=t},e[de]=function(t,o,a,s){e.isInitialized()&&(0,b.ZP)(e[fe](),1,28,"Sender is already initialized"),r[de](t,o,a,s);var m=e.identifier;u=new ot(o.logger),n=0,i=null,e[ve]=null,c=0;var O=e[fe]();A=(0,_.Hm)((0,I.Z)("Sender"),o.evtNamespace&&o.evtNamespace()),y=(0,h.G)(A),e._addHook((0,E.a)(t,function(t){var n=t.cfg;n.storagePrefix&&(0,p.vh)(n.storagePrefix);var r=(0,P.i8)(null,n,o).getExtCfg(m,vt),i=r[he];if(D&&i===D){var a=n[he];a&&a!==i&&(r[he]=a)}(0,S.$XS)(r[pe])&&(r[pe]=n[pe]),(0,S.vF1)(e,"_senderConfig",{g:function(){return r}}),k!==r[he]&&(D=k=r[he]),o.activeStatus()===T.f.PENDING?e.pause():o.activeStatus()===T.f.ACTIVE&&e.resume(),j&&j!==r[ge]&&(0,S.Iuo)(j,function(e){delete l[e.header]}),N=r[me],M=(!1===r[ye]||!1===r[be])&&(0,C.Uf)(),L=!1===r[ye]&&(0,C.Uf)(),H=!1===r[be]&&(0,C.Uf)(),ae=r[we],se=!!r[Se],$e=r.retryCodes;var s=r[oe],u=!!r[_e]&&(!!s||(0,p.AN)()),c=r.namePrefix,h=u!==K||u&&J!==c||u&&G!==s;if(e[Ie]){if(h)try{e._buffer=e._buffer[ne](O,r,u)}catch(t){(0,b.ZP)(e[fe](),1,12,"failed to transfer telemetry to different buffer storage, telemetry will be lost: "+(0,w.lL)(t),{exception:(0,S.mmD)(t)})}rt()}else e[Ie]=u?new it(O,r):new nt(O,r);J=c,K=u,G=s,ue=!r[Ee]&&(0,C.R7)(!0),Xe=!!r[Pe],e._sample=new ct(r.samplingPercentage,O),V=r[pe],(0,S.$XS)(V)||function(e,t){var n=t.disableInstrumentationKeyValidation;return!((0,S.hXl)(n)||!n)||new RegExp("^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$").test(e)}(V,n)||(0,b.ZP)(O,1,100,"Invalid Instrumentation key "+V),j=r[ge],(0,S.KgX)(D)&&!(0,g.Qu)(D)&&j&&j[X]>0?(0,S.Iuo)(j,function(e){f.addHeader(e.header,e.value)}):j=null,ee=r[Te];var y=function(){var t;try{var n={xdrOnComplete:function(e,t,n){var r=Ke(n);if(r)return v(e,r)},fetchOnComplete:function(e,t,n,r){var i=Ke(r);if(i)return at(e.status,i,e.url,i[X],e.statusText,n||"")},xhrOnComplete:function(e,t,n){var r=Ke(n);if(r)return qe(e,r,r[X])},beaconOnRetry:function(t,n,r){return function(t,n,r){var i=t&&t[Ue];if(Xe)ze&&ze(i,!0),(0,b.ZP)(e[fe](),2,40,". Failed to send telemetry with Beacon API, retried with normal sender.");else{for(var o=[],a=0;a<i[X];a++){var s=i[a],u=[s];r(et(u),n)?e._onSuccess(u,u[X]):o[Q](s)}o[X]>0&&(ze&&ze(o,!0),(0,b.ZP)(e[fe](),2,40,". Failed to send telemetry with Beacon API, retried with normal sender."))}}(t,n,r)}};return(t={})[Te]=ee,t.isOneDs=!1,t.disableCredentials=!1,t[Se]=se,t.disableBeacon=!H,t.disableBeaconSync=!L,t.senderOnCompleteCallBack=n,t}catch(e){}return null}();Ve?Ve.SetConfig(y):(Ve=new x.v)[de](y,O);var _=r.httpXHROverride,I=null,E=null,R=(0,w.jL)([3,1,2],r.transports);I=Ve&&Ve[Ce](R,!1);var A=Ve&&Ve.getFallbackInst();ce=function(e,t){return Ye(A,e,t)},ze=function(e,t){return Ye(A,e,t,!1)},I=ae?_:I||_||A,e[ve]=function(e,t){return Ye(I,e,t)},ue&&(d=ht);var U=(0,w.jL)([3,1],r[xe]);ue||(U=U.filter(function(e){return 2!==e})),E=Ve&&Ve[Ce](U,!0),E=ae?_:E||_,(ae||r[xe]||!d)&&E&&(d=function(e,t){return Ye(E,e,t)}),d||(d=ce),z=r.disableTelemetry,$=r[Oe]||lt,B=r.isRetryDisabled,q=r[Re]}))},e.processTelemetry=function(t,n){var r,i=(n=e._getTelCtx(n))[fe]();try{if(!Ge(t,i))return;var o=Ze(t,i);if(!o)return;var a=u[Ae](o),s=e[Ie];rt(a);var c=((r={})[Y]=a,r.cnt=0,r);s[Z](c),gt()}catch(e){(0,b.ZP)(i,2,12,"Failed adding telemetry to the sender's buffer, some telemetry will be lost: "+(0,w.lL)(e),{exception:(0,S.mmD)(e)})}e.processNext(t,n)},e.isCompletelyIdle=function(){return!a&&0===Be&&0===e._buffer[W]()},e.getOfflineListener=function(){return y},e._xhrReadyStateChange=function(e,t,n){if(!dt(t))return qe(e,t,n)},e[le]=function(t,n,r){var i;if(void 0===t&&(t=!0),!a)try{var o=e[Ie];if(z)o[te]();else{if(o[W]()>0){var s=o.getItems();!function(t,n){var r,i=(r="getNotifyMgr",e.core[r]?e.core[r]():e.core._notificationManager);if(i&&i[je])try{i[je](t,n)}catch(t){(0,b.ZP)(e[fe](),1,74,"send request notification failed: "+(0,w.lL)(t),{exception:(0,S.mmD)(t)})}}(r||0,t),i=n?n.call(e,s,t):e[ve](s,t)}new Date}mt()}catch(t){var u=(0,C.L0)();(!u||u>9)&&(0,b.ZP)(e[fe](),1,40,"Telemetry transmission failed, some telemetry will be lost: "+(0,w.lL)(t),{exception:(0,S.mmD)(t)})}return i},e.getOfflineSupport=function(){var e;return(e={getUrl:function(){return D},createPayload:Qe})[Ae]=We,e.batch=Je,e.shouldProcess=function(e){return!!Ge(e)},e},e._doTeardown=function(t,n){e.onunloadFlush(),(0,O.K)(y,!1),bt()},e[De]=function(t,n,r){if(!dt(t))return function(t,n){(0,b.ZP)(e[fe](),2,26,"Failed to send telemetry.",{message:n}),e._buffer&&e._buffer[ie](t)}(t,n)},e[ke]=function(t,n){if(!dt(t))return function(t,n){for(var r=[],i=[],o=0,a=n.errors.reverse();o<a.length;o++){var s=a[o],u=t.splice(s.index,1)[0];yt(s.statusCode)?i[Q](u):r[Q](u)}t[X]>0&&e[Ne](t,n[Le]),r[X]>0&&e[De](r,(0,w.r4)(null,["partial success",n[Le],"of",n.itemsReceived].join(" "))),i[X]>0&&(pt(i),(0,b.ZP)(e[fe](),2,40,"Partial success. Delivered: "+t[X]+", Failed: "+r[X]+". Will retry to send "+i[X]+" our of "+n[Me]+" items"))}(t,n)},e[Ne]=function(t,n){if(!dt(t))return function(t){e._buffer&&e._buffer[ie](t)}(t)},e._xdrOnLoad=function(e,t){if(!dt(t))return v(e,t)}}),f}return(0,i.qU)(t,e),t.constructEnvelope=function(e,t,n,r){var o;return o=t===e.iKey||(0,S.hXl)(t)?e:(0,i.Im)((0,i.Im)({},e),{iKey:t}),(ht[o.baseType]||Qe)(n,o,r)},t}(A.s),gt=n(4484),mt=n(2774),yt=n(8205),bt="instrumentationKey",wt="connectionString",St="instrumentationkey",_t="endpointUrl",It="ingestionendpoint",Et="userOverrideEndpointUrl",Pt=n(9762),Tt=void 0,Ct=((dt={diagnosticLogInterval:(0,y.Lx)(function(e){return e&&e>0},1e4)})[wt]=Tt,dt[_t]=Tt,dt[bt]=Tt,dt.extensionConfig={},dt),xt=function(){function e(t){var n,i=new mt._;function a(e){e&&(e.baseData=e.baseData||{},e.baseType=e.baseType||"EventData"),i.track(e)}((0,S.hXl)(t)||(0,S.hXl)(t[bt])&&(0,S.hXl)(t[wt]))&&(0,S.$8)("Invalid input configuration"),(0,r.A)(e,this,function(e){function r(){var e=(0,E.e)(t||{},Ct);n=e.cfg,i.addUnloadHook((0,E.a)(e,function(){var e=n[wt];if((0,S.$XS)(e)){var t=(0,yt.Rf)(function(t,r){(0,yt.Dv)(e,function(e){var r=e.value,i=n[bt];!e.rejected&&r&&(n[wt]=r,i=(0,gt.H)(r)[St]||i),t(i)})}),r=(0,yt.Rf)(function(t,r){(0,yt.Dv)(e,function(e){var r=e.value,i=n[_t];if(!e.rejected&&r){var a=(0,gt.H)(r)[It];i=a?a+o.wc:i}t(i)})});n[bt]=t,n[_t]=n[Et]||r}if((0,S.KgX)(e)){var i=(0,gt.H)(e),a=i[It];n[_t]=n[Et]?n[Et]:a+o.wc,n[bt]=i[St]||n[bt]}n[_t]=n[Et]?n[Et]:n[_t]})),i.initialize(n,[new pt])}(0,S.vF1)(e,"config",{g:function(){return n}}),r(),e.initialize=r,e.track=a,(0,w.o$)(e,i,["flush","pollInternalLogs","stopPollingInternalLogs","unload","getPlugin","addPlugin","evtNamespace","addUnloadCb","onCfgChange","getTraceCtx","updateCfg","addTelemetryInitializer"])})}return e.__ieDyn=1,e}()},6149:(e,t,n)=>{n.d(t,{Ds:()=>X,El:()=>C,Fc:()=>V,Hm:()=>A,ML:()=>k,Q3:()=>N,So:()=>L,Wg:()=>j,Ym:()=>M,ee:()=>z,lQ:()=>F,mB:()=>D,oS:()=>B,sq:()=>$,vF:()=>q,zh:()=>U});var r=n(269),i=n(6182),o=n(4276),a=n(6492),s="on",u="attachEvent",c="addEventListener",l="detachEvent",f="removeEventListener",d="events",v="visibilitychange",h="pagehide",p="pageshow",g="unload",m="beforeunload",y=(0,o.Z)("aiEvtPageHide"),b=(0,o.Z)("aiEvtPageShow"),w=/\.[\.]+/g,S=/[\.]+$/,_=1,I=(0,o.T)("events"),E=/^([^.]*)(?:\.(.+)|)/;function P(e){return e&&e[i.W7]?e[i.W7](/^[\s\.]+|(?=[\s\.])[\.\s]+$/g,a.m5):e}function T(e,t){var n;if(t){var o=a.m5;(0,r.cyL)(t)?(o=a.m5,(0,r.Iuo)(t,function(e){(e=P(e))&&("."!==e[0]&&(e="."+e),o+=e)})):o=P(t),o&&("."!==o[0]&&(o="."+o),e=(e||a.m5)+o)}var s=E.exec(e||a.m5)||[];return(n={})[i.QM]=s[1],n.ns=(s[2]||a.m5).replace(w,".").replace(S,a.m5)[i.sY](".").sort().join("."),n}function C(e,t,n){var o=[],s=I.get(e,d,{},!1),u=T(t,n);return(0,r.zav)(s,function(e,t){(0,r.Iuo)(t,function(e){var t;u[i.QM]&&u[i.QM]!==e.evtName[i.QM]||u.ns&&u.ns!=u.ns||o[i.y5](((t={})[i.RS]=e.evtName[i.QM]+(e.evtName.ns?"."+e.evtName.ns:a.m5),t.handler=e[i.Yo],t))})}),o}function x(e,t,n){void 0===n&&(n=!0);var r=I.get(e,d,{},n),i=r[t];return i||(i=r[t]=[]),i}function O(e,t,n,r){e&&t&&t[i.QM]&&(e[f]?e[f](t[i.QM],n,r):e[l]&&e[l](s+t[i.QM],n))}function R(e,t,n,r){for(var o=t[i.oI];o--;){var a=t[o];a&&(n.ns&&n.ns!==a.evtName.ns||r&&!r(a)||(O(e,a.evtName,a[i.Yo],a.capture),t[i.Ic](o,1)))}}function A(e,t){return t?T("xx",(0,r.cyL)(t)?[e].concat(t):[e,t]).ns[i.sY]("."):e}function D(e,t,n,r,o){var a;void 0===o&&(o=!1);var l=!1;if(e)try{var f=T(t,r);if(l=function(e,t,n,r){var o=!1;return e&&t&&t[i.QM]&&n&&(e[c]?(e[c](t[i.QM],n,r),o=!0):e[u]&&(e[u](s+t[i.QM],n),o=!0)),o}(e,f,n,o),l&&I.accept(e)){var d=((a={guid:_++,evtName:f})[i.Yo]=n,a.capture=o,a);x(e,f.type)[i.y5](d)}}catch(e){}return l}function k(e,t,n,o,a){if(void 0===a&&(a=!1),e)try{var s=T(t,o),u=!1;!function(e,t,n){if(t[i.QM])R(e,x(e,t[i.QM]),t,n);else{var o=I.get(e,d,{});(0,r.zav)(o,function(r,i){R(e,i,t,n)}),0===(0,r.cGk)(o)[i.oI]&&I.kill(e,d)}}(e,s,function(e){return!((!s.ns||n)&&e[i.Yo]!==n||(u=!0,0))}),u||O(e,s,n,a)}catch(e){}}function N(e,t,n,r){return void 0===r&&(r=!1),D(e,t,n,null,r)}function M(e,t,n,r){void 0===r&&(r=!1),k(e,t,n,null,r)}function L(e,t,n){var i=!1,o=(0,r.zkX)();o&&(i=D(o,e,t,n),i=D(o.body,e,t,n)||i);var a=(0,r.YEm)();return a&&(i=D(a,e,t,n)||i),i}function U(e,t,n){var i=(0,r.zkX)();i&&(k(i,e,t,n),k(i.body,e,t,n));var o=(0,r.YEm)();o&&k(o,e,t,n)}function H(e,t,n,o){var a=!1;return t&&e&&e[i.oI]>0&&(0,r.Iuo)(e,function(e){e&&(n&&-1!==(0,r.rDm)(n,e)||(a=L(e,t,o)||a))}),a}function F(e,t,n,o){var a=!1;return t&&e&&(0,r.cyL)(e)&&!(a=H(e,t,n,o))&&n&&n[i.oI]>0&&(a=H(e,t,null,o)),a}function j(e,t,n){e&&(0,r.cyL)(e)&&(0,r.Iuo)(e,function(e){e&&U(e,t,n)})}function z(e,t,n){return F([m,g,h],e,t,n)}function X(e,t){j([m,g,h],e,t)}function V(e,t,n){var i=A(y,n),o=H([h],e,t,i);return t&&-1!==(0,r.rDm)(t,v)||(o=H([v],function(t){var n=(0,r.YEm)();e&&n&&"hidden"===n.visibilityState&&e(t)},t,i)||o),!o&&t&&(o=V(e,null,n)),o}function $(e,t){var n=A(y,t);j([h],e,n),j([v],null,n)}function B(e,t,n){var i=A(b,n),o=H([p],e,t,i);return!(o=H([v],function(t){var n=(0,r.YEm)();e&&n&&"visible"===n.visibilityState&&e(t)},t,i)||o)&&t&&(o=B(e,null,n)),o}function q(e,t){var n=A(b,t);j([p],e,n),j([v],null,n)}},6182:(e,t,n)=>{n.d(t,{$5:()=>A,$o:()=>j,AP:()=>C,Az:()=>oe,Bl:()=>F,Cd:()=>ue,DI:()=>D,Di:()=>M,FI:()=>O,HC:()=>K,Ic:()=>p,Ik:()=>z,JQ:()=>i,JR:()=>X,Ju:()=>b,K0:()=>g,L:()=>he,M5:()=>L,NA:()=>Se,OL:()=>r,O_:()=>ce,P5:()=>ye,QM:()=>ne,Qg:()=>W,RF:()=>T,RS:()=>x,Rr:()=>we,Ru:()=>fe,Uw:()=>d,Vq:()=>ve,W7:()=>ee,XM:()=>N,XW:()=>a,YH:()=>Z,Yo:()=>re,Yq:()=>E,Zu:()=>se,_w:()=>m,by:()=>U,c1:()=>pe,cV:()=>ie,cp:()=>de,do:()=>ge,e4:()=>B,e_:()=>le,fA:()=>R,h0:()=>l,h3:()=>w,h4:()=>k,ih:()=>c,mE:()=>y,oI:()=>o,on:()=>u,pF:()=>P,pM:()=>V,re:()=>Y,s:()=>H,sY:()=>Q,sl:()=>I,sx:()=>te,tI:()=>$,tX:()=>J,tZ:()=>_,tn:()=>q,uR:()=>G,vR:()=>S,wJ:()=>ae,wi:()=>be,x6:()=>f,y5:()=>h,y9:()=>v,yy:()=>me,zs:()=>s});var r="toLowerCase",i="blkVal",o="length",a="rdOnly",s="notify",u="warnToConsole",c="throwInternal",l="setDf",f="watch",d="logger",v="apply",h="push",p="splice",g="hdlr",m="cancel",y="initialize",b="identifier",w="removeNotificationListener",S="addNotificationListener",_="isInitialized",I="instrumentationKey",E="INACTIVE",P="value",T="getNotifyMgr",C="getPlugin",x="name",O="iKey",R="time",A="processNext",D="getProcessTelContext",k="pollInternalLogs",N="enabled",M="stopPollingInternalLogs",L="unload",U="onComplete",H="version",F="loggingLevelConsole",j="createNew",z="teardown",X="messageId",V="message",$="isAsync",B="diagLog",q="_doTeardown",K="update",G="getNext",Z="setNextPlugin",W="protocol",J="userAgent",Q="split",Y="nodeType",ee="replace",te="logInternalMessage",ne="type",re="handler",ie="status",oe="getResponseHeader",ae="getAllResponseHeaders",se="isChildEvt",ue="data",ce="getCtx",le="setCtx",fe="complete",de="itemsReceived",ve="urlString",he="sendPOST",pe="headers",ge="timeout",me="setRequestHeader",ye="traceId",be="spanId",we="traceFlags",Se="getAttribute"},6208:function(e,t,n){var r,i=this&&this.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n);var i=Object.getOwnPropertyDescriptor(t,n);i&&!("get"in i?!t.__esModule:i.writable||i.configurable)||(i={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,i)}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),o=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),a=this&&this.__importStar||(r=function(e){return r=Object.getOwnPropertyNames||function(e){var t=[];for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[t.length]=n);return t},r(e)},function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n=r(e),a=0;a<n.length;a++)"default"!==n[a]&&i(t,e,n[a]);return o(t,e),t});Object.defineProperty(t,"__esModule",{value:!0}),t.ExperimentationTelemetry=void 0;const s=a(n(1398)),u=n(1566);t.ExperimentationTelemetry=class{constructor(e,t){this.context=e,this.baseReporter=t,this.sharedProperties={}}async createExperimentationService(){let e;switch(s.env.uriScheme){case"vscode":default:e=u.TargetPopulation.Public;break;case"vscode-insiders":e=u.TargetPopulation.Insiders;break;case"vscode-exploration":e=u.TargetPopulation.Internal;break;case"code-oss":e=u.TargetPopulation.Team}const t=this.context.extension.id,n=this.context.extension.packageJSON.version,r=(0,u.getExperimentationService)(t,n,e,this,this.context.globalState);return await r.initialFetch,r}async sendTelemetryEvent(e,t,n){this.experimentationServicePromise||(this.experimentationServicePromise=this.createExperimentationService()),await this.experimentationServicePromise,this.baseReporter.sendTelemetryEvent(e,{...this.sharedProperties,...t},n)}async sendTelemetryErrorEvent(e,t,n){this.experimentationServicePromise||(this.experimentationServicePromise=this.createExperimentationService()),await this.experimentationServicePromise,this.baseReporter.sendTelemetryErrorEvent(e,{...this.sharedProperties,...t})}setSharedProperty(e,t){this.sharedProperties[e]=t}postEvent(e,t){const n={};for(const[e,r]of t)n[e]=r;this.sendTelemetryEvent(e,n)}dispose(){return this.baseReporter.dispose()}}},6492:(e,t,n)=>{n.d(t,{Bw:()=>c,Ev:()=>b,Fk:()=>S,HP:()=>r,Hr:()=>u,LZ:()=>o,QW:()=>_,Vj:()=>y,Vo:()=>d,Yd:()=>s,Yp:()=>h,dI:()=>p,eT:()=>a,fc:()=>v,jy:()=>l,kI:()=>w,l0:()=>g,m5:()=>i,qT:()=>f,s4:()=>m,xW:()=>I});var r=void 0,i="",o="channels",a="core",s="createPerfMgr",u="disabled",c="extensionConfig",l="extensions",f="processTelemetry",d="priority",v="eventsSent",h="eventsDiscarded",p="eventsSendRequest",g="perfEvent",m="offlineEventsStored",y="offlineBatchSent",b="offlineBatchDrop",w="getPerfMgr",S="domain",_="path",I="Not dynamic - "},6535:(e,t,n)=>{n.d(t,{Si:()=>m,VN:()=>g,Z1:()=>p});var r=n(269),i=n(6182),o=n(7292),a=n(6492),s=4294967296,u=4294967295,c=123456789,l=987654321,f=!1,d=c,v=l;function h(){try{var e=2147483647&(0,r.f0d)();(t=(Math.random()*s^e)+e)<0&&(t>>>=0),d=c+t&u,v=l-t&u,f=!0}catch(e){}var t}function p(e){return e>0?Math.floor(g()/u*(e+1))>>>0:0}function g(e){var t=0,n=(0,o.MY)()||(0,o.iN)();return n&&n.getRandomValues&&(t=n.getRandomValues(new Uint32Array(1))[0]&u),0===t&&(0,o.lT)()&&(f||h(),t=function(){var e=((v=36969*(65535&v)+(v>>16)&u)<<16)+(65535&(d=18e3*(65535&d)+(d>>16)&u))>>>0&u;return e>>>=0}()&u),0===t&&(t=Math.floor(s*Math.random()|0)),e||(t>>>=0),t}function m(e){void 0===e&&(e=22);for(var t=g()>>>0,n=0,r=a.m5;r[i.oI]<e;)n++,r+="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".charAt(63&t),t>>>=6,5===n&&(t=(g()<<2&4294967295|3&t)>>>0,n=0);return r}},6548:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.BaseTelemetryReporter=void 0,t.BaseTelemetryReporter=class{constructor(e,t,n){this.telemetrySender=e,this.vscodeAPI=t,this.userOptIn=!1,this.errorOptIn=!1,this.disposables=[],this._onDidChangeTelemetryLevel=new this.vscodeAPI.EventEmitter,this.onDidChangeTelemetryLevel=this._onDidChangeTelemetryLevel.event,this.telemetryLogger=this.vscodeAPI.env.createTelemetryLogger(this.telemetrySender,n),this.updateUserOptIn(),this.telemetryLogger.onDidChangeEnableStates(()=>{this.updateUserOptIn()})}updateUserOptIn(){this.errorOptIn=this.telemetryLogger.isErrorsEnabled,this.userOptIn=this.telemetryLogger.isUsageEnabled,(this.telemetryLogger.isErrorsEnabled||this.telemetryLogger.isUsageEnabled)&&this.telemetrySender.instantiateSender(),this._onDidChangeTelemetryLevel.fire(this.telemetryLevel)}get telemetryLevel(){return this.errorOptIn&&this.userOptIn?"all":this.errorOptIn?"error":"off"}internalSendTelemetryEvent(e,t,n,r){r?this.telemetrySender.sendEventData(e,{properties:t,measurements:n}):this.telemetryLogger.logUsage(e,{properties:t,measurements:n})}sendTelemetryEvent(e,t,n){this.internalSendTelemetryEvent(e,t,n,!1)}sendRawTelemetryEvent(e,t,n){const r={...t};for(const e of Object.keys(r??{})){const t=r[e];"string"==typeof e&&void 0!==t&&(r[e]=new this.vscodeAPI.TelemetryTrustedValue("string"==typeof t?t:t.value))}this.sendTelemetryEvent(e,r,n)}sendDangerousTelemetryEvent(e,t,n){this.telemetrySender.instantiateSender(),this.internalSendTelemetryEvent(e,t,n,!0)}internalSendTelemetryErrorEvent(e,t,n,r){r?this.telemetrySender.sendEventData(e,{properties:t,measurements:n}):this.telemetryLogger.logError(e,{properties:t,measurements:n})}sendTelemetryErrorEvent(e,t,n){this.internalSendTelemetryErrorEvent(e,t,n,!1)}sendDangerousTelemetryErrorEvent(e,t,n){this.telemetrySender.instantiateSender(),this.internalSendTelemetryErrorEvent(e,t,n,!0)}async dispose(){return await this.telemetrySender.dispose(),this.telemetryLogger.dispose(),Promise.all(this.disposables.map(e=>e.dispose()))}}},6790:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.BaseFeatureProvider=void 0,t.BaseFeatureProvider=class{constructor(e){this.telemetry=e,this.isFetching=!1}async getFeatures(){if(this.isFetching&&this.fetchPromise)return this.fetchPromise;this.fetchPromise=this.fetch();let e=await this.fetchPromise;return this.isFetching=!1,this.fetchPromise=void 0,e}}},6928:e=>{e.exports=require("path")},6982:e=>{e.exports=require("crypto")},7016:e=>{e.exports=require("url")},7066:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.StopWatch=void 0,t.filterEvent=function(e,t){return(n,r=null,i)=>e(e=>t(e)&&n.call(r,e),null,i)},t.onceEvent=function(e){return(t,n=null,r)=>{const i=e(e=>(i.dispose(),t.call(n,e)),null,r);return i}},t.promiseFromEvent=function(e,t=i){let n;const o=new r.EventEmitter;return{promise:new Promise((r,i)=>{o.event(e=>i("Cancelled")),n=e(e=>{try{Promise.resolve(t(e,r,i)).catch(i)}catch(e){i(e)}})}).then(e=>(n.dispose(),e),e=>{throw n.dispose(),e}),cancel:o}},t.arrayEquals=function(e,t,n=(e,t)=>e===t){if(e===t)return!0;if(!e||!t)return!1;if(e.length!==t.length)return!1;for(let r=0,i=e.length;r<i;r++)if(!n(e[r],t[r]))return!1;return!0};const r=n(1398),i=(e,t)=>t(e);t.StopWatch=class{constructor(){this._startTime=Date.now(),this._stopTime=-1}stop(){this._stopTime=Date.now()}elapsed(){return-1!==this._stopTime?this._stopTime-this._startTime:Date.now()-this._startTime}}},7291:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ExperimentationService=void 0;const r=n(2845),i=n(1924),o=n(5761);class a extends o.ExperimentationServiceAutoPolling{constructor(e){super(e.telemetry,e.filterProviders||[],null!=e.refetchInterval?e.refetchInterval:0,e.assignmentContextTelemetryPropertyName,e.telemetryEventName,e.storageKey,e.keyValueStorage),this.options=e,this.invokeInit()}init(){this.featureProviders=[],this.addFeatureProvider(new r.TasApiFeatureProvider(new i.HttpClient(this.options.endpoint),this.telemetry,this.filterProviders)),super.init()}}t.ExperimentationService=a,a.REFRESH_RATE_IN_MINUTES=30},7292:(e,t,n)=>{n.d(t,{$Z:()=>F,Iu:()=>j,L0:()=>k,MY:()=>O,PV:()=>L,R7:()=>M,U5:()=>T,Uf:()=>N,Z:()=>C,cU:()=>E,g$:()=>P,hm:()=>x,iN:()=>R,lT:()=>D,lV:()=>A,xk:()=>U});var r=n(5664),i=n(269),o=n(6182),a=n(3673),s=n(6492),u="documentMode",c="location",l="console",f="JSON",d="crypto",v="msCrypto",h="ReactNative",p="msie",g="trident/",m="XMLHttpRequest",y=null,b=null,w=!1,S=null,_=null;function I(e,t){var n=!1;if(e){try{if(!(n=t in e)){var o=e[r.vR];o&&(n=t in o)}}catch(e){}if(!n)try{var a=new e;n=!(0,i.b07)(a[t])}catch(e){}}return n}function E(e){w=e}function P(e){if(e&&w){var t=(0,i.zS2)("__mockLocation");if(t)return t}return typeof location===r._1&&location?location:(0,i.zS2)(c)}function T(){return typeof console!==r.bA?console:(0,i.zS2)(l)}function C(){return Boolean(typeof JSON===r._1&&JSON||null!==(0,i.zS2)(f))}function x(){return C()?JSON||(0,i.zS2)(f):null}function O(){return(0,i.zS2)(d)}function R(){return(0,i.zS2)(v)}function A(){var e=(0,i.w3n)();return!(!e||!e.product)&&e.product===h}function D(){var e=(0,i.w3n)();if(e&&(e[o.tX]!==b||null===y)){var t=((b=e[o.tX])||s.m5)[o.OL]();y=(0,a.Ju)(t,p)||(0,a.Ju)(t,g)}return y}function k(e){if(void 0===e&&(e=null),!e){var t=(0,i.w3n)()||{};e=t?(t.userAgent||s.m5)[o.OL]():s.m5}var n=(e||s.m5)[o.OL]();if((0,a.Ju)(n,p)){var r=(0,i.YEm)()||{};return Math.max(parseInt(n[o.sY](p)[1]),r[u]||0)}if((0,a.Ju)(n,g)){var c=parseInt(n[o.sY](g)[1]);if(c)return c+4}return null}function N(e){return null!==_&&!1!==e||(_=(0,i.w9M)()&&Boolean((0,i.w3n)().sendBeacon)),_}function M(e){var t=!1;try{t=!!(0,i.zS2)("fetch");var n=(0,i.zS2)("Request");t&&e&&n&&(t=I(n,"keepalive"))}catch(e){}return t}function L(){return null===S&&(S=typeof XDomainRequest!==r.bA)&&U()&&(S=S&&!I((0,i.zS2)(m),"withCredentials")),S}function U(){var e=!1;try{e=!!(0,i.zS2)(m)}catch(e){}return e}function H(e,t){if(e)for(var n=0;n<e[o.oI];n++){var r=e[n];if(r[o.RS]&&r[o.RS]===t)return r}return{}}function F(e){var t=(0,i.YEm)();return t&&e?H(t.querySelectorAll("meta"),e).content:null}function j(e){var t,n=(0,i.FJj)();if(n){var r=n.getEntriesByType("navigation")||[];t=H((r[o.oI]>0?r[0]:{}).serverTiming,e).description}return t}},7358:(e,t,n)=>{n.d(t,{B:()=>r});var r=function(e,t){this.aiDataContract={baseType:1,baseData:1},this.baseType=e,this.baseData=t}},7374:(e,t,n)=>{n.d(t,{eL:()=>i,iD:()=>a,uG:()=>o});var r=n(4282),i=(0,r.H)({LocalStorage:0,SessionStorage:1}),o=(0,r.H)({AI:0,AI_AND_W3C:1,W3C:2}),a=(0,r.H)({Normal:1,Critical:2})},7847:(e,t,n)=>{n.d(t,{i:()=>r,x:()=>i});var r=500,i="Microsoft_ApplicationInsights_BypassAjaxInstrumentation"},7867:(e,t,n)=>{n.d(t,{$:()=>l,M:()=>f});var r,i=n(269),o=n(6182),a=n(6492),s=[a.fc,a.Yp,a.dI,a.l0],u=null;function c(e,t){return function(){var n=arguments,r=l(t);if(r){var i=r.listener;i&&i[e]&&i[e][o.y9](i,n)}}}function l(e){var t,n=u;return n||!0===e.disableDbgExt||(n=u||((t=(0,i.zS2)("Microsoft"))&&(u=t.ApplicationInsights),u)),n?n.ChromeDbgExt:null}function f(e){if(!r){r={};for(var t=0;t<s[o.oI];t++)r[s[t]]=c(s[t],e)}return r}},7921:function(e,t,n){var r,i=this&&this.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n);var i=Object.getOwnPropertyDescriptor(t,n);i&&!("get"in i?!t.__esModule:i.writable||i.configurable)||(i={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,i)}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),o=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),a=this&&this.__importStar||(r=function(e){return r=Object.getOwnPropertyNames||function(e){var t=[];for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[t.length]=n);return t},r(e)},function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n=r(e),a=0;a<n.length;a++)"default"!==n[a]&&i(t,e,n[a]);return o(t,e),t});Object.defineProperty(t,"__esModule",{value:!0}),t.GitHubServer=void 0;const s=a(n(1398)),u=n(1354),c=n(8016),l=n(4577),f=n(3034),d=n(3772),v=n(928),h=n(8547),p=n(2866);t.GitHubServer=class{constructor(e,t,n,r,i){this._logger=e,this._telemetryReporter=t,this._uriHandler=n,this._extensionKind=r,this._ghesUri=i,this._type=i?u.AuthProviderType.githubEnterprise:u.AuthProviderType.github,this.friendlyName=this._type===u.AuthProviderType.github?"GitHub":i?.authority}get baseUri(){return this._type===u.AuthProviderType.github?s.Uri.parse("https://github.com/"):this._ghesUri}async getRedirectEndpoint(){if(this._redirectEndpoint)return this._redirectEndpoint;if(this._type===u.AuthProviderType.github){const e=await s.commands.executeCommand("workbench.getCodeExchangeProxyEndpoints");this._redirectEndpoint="https://vscode.dev/redirect",e?.github&&"insiders.vscode.dev"===new URL(e.github).hostname&&(this._redirectEndpoint="https://insiders.vscode.dev/redirect")}else this._redirectEndpoint="https://vscode.dev/redirect";return this._redirectEndpoint}async isNoCorsEnvironment(){if(void 0!==this._isNoCorsEnvironment)return this._isNoCorsEnvironment;const e=await s.env.asExternalUri(s.Uri.parse(`${s.env.uriScheme}://vscode.github-authentication/dummy`));return this._isNoCorsEnvironment="https"===e.scheme&&/^((insiders\.)?vscode|github)\./.test(e.authority)||"http"===e.scheme&&/^localhost/.test(e.authority),this._isNoCorsEnvironment}async login(e,t,n){let r;this._logger.info(`Logging in for the following scopes: ${e}`);const i=s.l10n.t("Yes"),o=s.l10n.t("No"),a=async e=>{if(void 0===r)return;const t=r?s.l10n.t("Having trouble logging in? Would you like to try a different way? ({0})",e):s.l10n.t("You have not yet finished authorizing this extension to use GitHub. Would you like to try a different way? ({0})",e);if(await s.window.showWarningMessage(t,i,o)!==i)throw new Error(v.CANCELLATION_ERROR)},f=l.crypto.getRandomValues(new Uint32Array(2)).reduce((e,t)=>e+t.toString(16),""),h=await s.env.asExternalUri(s.Uri.parse(`${s.env.uriScheme}://vscode.github-authentication/did-authenticate?nonce=${encodeURIComponent(f)}`)),p=(0,c.isSupportedClient)(h),g=(0,c.isSupportedTarget)(this._type,this._ghesUri),m="undefined"!=typeof process&&"string"==typeof process?.versions?.node,y=(0,d.getFlows)({target:this._type===u.AuthProviderType.github?0:g?2:1,extensionHost:m?this._extensionKind===s.ExtensionKind.UI?2:1:0,isSupportedClient:p});for(const i of y)try{return i!==y[0]&&await a(i.label),await i.trigger({scopes:e,callbackUri:h,nonce:f,signInProvider:t,baseUri:this.baseUri,logger:this._logger,uriHandler:this._uriHandler,enterpriseUri:this._ghesUri,redirectUri:s.Uri.parse(await this.getRedirectEndpoint()),existingLogin:n})}catch(e){r=this.processLoginError(e)}throw new Error(r?v.CANCELLATION_ERROR:"No auth flow succeeded.")}async logout(e){if(this._logger.trace(`Deleting session (${e.id}) from server...`),!h.Config.gitHubClientSecret)return void this._logger.warn("No client secret configured for GitHub authentication. The token has been deleted with best effort on this system, but we are unable to delete the token on server without the client secret.");if(!e.accessToken.startsWith("gho_"))return void this._logger.warn("The token being deleted is not an OAuth token. It has been deleted locally, but we cannot delete it on server.");if(!(0,c.isSupportedTarget)(this._type,this._ghesUri))return void this._logger.trace("GitHub.com and GitHub hosted GitHub Enterprise are the only options that support deleting tokens on the server. Skipping.");const t="Basic "+(0,p.base64Encode)(`${h.Config.gitHubClientId}:${h.Config.gitHubClientSecret}`),n=this.getServerUri(`/applications/${h.Config.gitHubClientId}/token`);try{const r=await(0,f.fetching)(n.toString(!0),{method:"DELETE",headers:{Accept:"application/vnd.github+json",Authorization:t,"X-GitHub-Api-Version":"2022-11-28","User-Agent":`${s.env.appName} (${s.env.appHost})`},body:JSON.stringify({access_token:e.accessToken})});if(204===r.status)return void this._logger.trace(`Successfully deleted token from session (${e.id}) from server.`);try{const e=await r.text();throw new Error(e)}catch(e){throw new Error(`${r.status} ${r.statusText}`)}}catch(e){this._logger.warn("Failed to delete token from server."+(e.message??e))}}getServerUri(e=""){const t=this.baseUri;return(0,c.isSupportedTarget)(this._type,this._ghesUri)?s.Uri.parse(`${t.scheme}://api.${t.authority}`).with({path:e}):s.Uri.parse(`${t.scheme}://${t.authority}/api/v3${e}`)}async getUserInfo(e){let t;try{this._logger.info("Getting user info..."),t=await(0,f.fetching)(this.getServerUri("/user").toString(),{headers:{Authorization:`token ${e}`,"User-Agent":`${s.env.appName} (${s.env.appHost})`}})}catch(e){throw this._logger.error(e.message),new Error(v.NETWORK_ERROR)}if(!t.ok){let e=t.statusText;try{const n=await t.json();n.message&&(e=n.message)}catch(e){}throw this._logger.error(`Getting account info failed: ${e}`),new Error(e)}try{const e=await t.json();return this._logger.info("Got account info!"),{id:`${e.id}`,accountName:e.login}}catch(e){throw this._logger.error(`Unexpected error parsing response from GitHub: ${e.message??e}`),e}}async sendAdditionalTelemetryInfo(e){if(s.env.isTelemetryEnabled)return await this.isNoCorsEnvironment()?void 0:this._type===u.AuthProviderType.github?await this.checkUserDetails(e):void await this.checkEnterpriseVersion(e.accessToken)}async checkUserDetails(e){let t;try{const n=await(0,f.fetching)("https://education.github.com/api/user",{headers:{Authorization:`token ${e.accessToken}`,"faculty-check-preview":"true","User-Agent":`${s.env.appName} (${s.env.appHost})`}});if(n.ok){const e=await n.json();t=e.student?"student":e.faculty?"faculty":"none"}else t="unknown"}catch(e){t="unknown"}this._telemetryReporter.sendTelemetryEvent("session",{isEdu:t,isManaged:e.account.label.includes("_")?"true":"false"})}async checkEnterpriseVersion(e){try{let t;if((0,c.isSupportedTarget)(this._type,this._ghesUri))t="hosted";else{const n=await(0,f.fetching)(this.getServerUri("/meta").toString(),{headers:{Authorization:`token ${e}`,"User-Agent":`${s.env.appName} (${s.env.appHost})`}});if(!n.ok)return;t=(await n.json()).installed_version}this._telemetryReporter.sendTelemetryEvent("ghe-session",{version:t})}catch{}}processLoginError(e){if(e.message===v.CANCELLATION_ERROR)throw e;return this._logger.error(e.message??e),e.message===v.USER_CANCELLATION_ERROR}}},7975:(e,t,n)=>{n.d(t,{HQ:()=>p,Rr:()=>c,Vj:()=>h,Vk:()=>f,Vt:()=>d,_T:()=>g,lq:()=>u,pJ:()=>l,qW:()=>m,xP:()=>v,zx:()=>s});var r=n(269),i=n(3775),o=n(7292),a=n(5130);function s(e,t,n){var i=t[a.oI],o=u(e,t);if(o[a.oI]!==i){for(var s=0,c=o;void 0!==n[c];)s++,c=(0,r.P0f)(o,0,147)+m(s);o=c}return o}function u(e,t){var n;return t&&(t=(0,r.EHq)((0,r.oJg)(t)))[a.oI]>150&&(n=(0,r.P0f)(t,0,150),(0,i.ZP)(e,2,57,"name is too long.  It has been truncated to 150 characters.",{name:t},!0)),n||t}function c(e,t,n){var o;return void 0===n&&(n=1024),t&&(n=n||1024,(t=(0,r.EHq)((0,r.oJg)(t)))[a.oI]>n&&(o=(0,r.P0f)(t,0,n),(0,i.ZP)(e,2,61,"string value is too long. It has been truncated to "+n+" characters.",{value:t},!0))),o||t}function l(e,t){return g(e,t,2048,66)}function f(e,t){var n;return t&&t[a.oI]>32768&&(n=(0,r.P0f)(t,0,32768),(0,i.ZP)(e,2,56,"message is too long, it has been truncated to 32768 characters.",{message:t},!0)),n||t}function d(e,t){var n;if(t){var o=""+t;o[a.oI]>32768&&(n=(0,r.P0f)(o,0,32768),(0,i.ZP)(e,2,52,"exception is too long, it has been truncated to 32768 characters.",{exception:t},!0))}return n||t}function v(e,t){if(t){var n={};(0,r.zav)(t,function(t,u){if((0,r.Gvm)(u)&&(0,o.Z)())try{u=(0,o.hm)()[a.Jj](u)}catch(t){(0,i.ZP)(e,2,49,"custom property is not valid",{exception:t},!0)}u=c(e,u,8192),t=s(e,t,n),n[t]=u}),t=n}return t}function h(e,t){if(t){var n={};(0,r.zav)(t,function(t,r){t=s(e,t,n),n[t]=r}),t=n}return t}function p(e,t){return t?g(e,t,128,69)[a.xE]():t}function g(e,t,n,o){var s;return t&&(t=(0,r.EHq)((0,r.oJg)(t)))[a.oI]>n&&(s=(0,r.P0f)(t,0,n),(0,i.ZP)(e,2,o,"input is too long, it has been truncated to "+n+" characters.",{data:t},!0)),s||t}function m(e){var t="00"+e;return(0,r.hKY)(t,t[a.oI]-3)}},8016:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.isSupportedClient=function(e){return i.includes(e.scheme)||/(?:^|\.)vscode\.dev$/.test(e.authority)||/(?:^|\.)github\.dev$/.test(e.authority)},t.isSupportedTarget=function(e,t){return e===r.AuthProviderType.github||o(t)},t.isHostedGitHubEnterprise=o;const r=n(1354),i=["vscode","vscode-insiders","vscode-wsl","vscode-exploration"];function o(e){return/\.ghe\.com$/.test(e.authority)}},8142:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.MementoKeyValueStorage=void 0,t.MementoKeyValueStorage=class{constructor(e){this.mementoGlobalStorage=e}async getValue(e,t){return await this.mementoGlobalStorage.get(e)||t}setValue(e,t){this.mementoGlobalStorage.update(e,t)}}},8156:(e,t,n)=>{n.d(t,{NS:()=>d,Q6:()=>f,Z4:()=>p,r2:()=>h});var r=n(8279),i=n(269),o=n(6182),a=n(6492),s="ctx",u="ParentContextKey",c="ChildrenContextKey",l=null,f=function(){function e(t,n,r){var a,l=this;l.start=(0,i.f0d)(),l[o.RS]=t,l[o.tI]=r,l[o.Zu]=function(){return!1},(0,i.Tnt)(n)&&(0,i.vF1)(l,"payload",{g:function(){return!a&&(0,i.Tnt)(n)&&(a=n(),n=null),a}}),l[o.O_]=function(t){return t?t===e[u]||t===e[c]?l[t]:(l[s]||{})[t]:null},l[o.e_]=function(t,n){t&&(t===e[u]?(l[t]||(l[o.Zu]=function(){return!0}),l[t]=n):t===e[c]?l[t]=n:(l[s]=l[s]||{})[t]=n)},l[o.Ru]=function(){var t=0,n=l[o.O_](e[c]);if((0,i.cyL)(n))for(var r=0;r<n[o.oI];r++){var a=n[r];a&&(t+=a[o.fA])}l[o.fA]=(0,i.f0d)()-l.start,l.exTime=l[o.fA]-t,l[o.Ru]=function(){}}}return e.ParentContextKey="parent",e.ChildrenContextKey="childEvts",e}(),d=function(){function e(t){this.ctx={},(0,r.A)(e,this,function(e){e.create=function(e,t,n){return new f(e,t,n)},e.fire=function(e){e&&(e[o.Ru](),t&&(0,i.Tnt)(t[a.l0])&&t[a.l0](e))},e[o.e_]=function(t,n){t&&((e[s]=e[s]||{})[t]=n)},e[o.O_]=function(t){return(e[s]||{})[t]}})}return e.__ieDyn=1,e}(),v="CoreUtils.doPerf";function h(e,t,n,r,i){if(e){var s=e;if(s[a.kI]&&(s=s[a.kI]()),s){var l=void 0,d=s[o.O_](v);try{if(l=s.create(t(),r,i)){if(d&&l[o.e_]&&(l[o.e_](f[u],d),d[o.O_]&&d[o.e_])){var h=d[o.O_](f[c]);h||(h=[],d[o.e_](f[c],h)),h[o.y5](l)}return s[o.e_](v,l),n(l)}}catch(e){l&&l[o.e_]&&l[o.e_]("exception",e)}finally{l&&s.fire(l),s[o.e_](v,d)}}}return n()}function p(){return l}},8205:(e,t,n)=>{n.d(t,{Dv:()=>c,Qo:()=>k,Rf:()=>A,Xf:()=>N,lh:()=>D});var r,i,o,a=n(269),s="Promise",u="rejected";function c(e,t){return l(e,function(e){return t?t({status:"fulfilled",rejected:!1,value:e}):e},function(e){return t?t({status:u,rejected:!0,reason:e}):e})}function l(e,t,n,r){var i=e;try{if((0,a.$XS)(e))(t||n)&&(i=e.then(t,n));else try{t&&(i=t(e))}catch(e){if(!n)throw e;i=n(e)}}finally{r&&function(e,t){var n=e;t&&((0,a.$XS)(e)?n=e.finally?e.finally(t):e.then(function(e){return t(),e},function(e){throw t(),e}):t())}(i,r)}return i}var f,d=!1,v=["pending","resolving","resolved",u],h="dispatchEvent";function p(e){var t;return e&&e.createEvent&&(t=e.createEvent("Event")),!!t&&t.initEvent}var g,m,y,b,w="unhandledRejection",S=w.toLowerCase(),_=[],I=0,E=10;function P(e){return(0,a.Tnt)(e)?e.toString():(0,a.mmD)(e)}function T(e,t,n){var u,c,l=(0,a.KVm)(arguments,3),m=0,y=!1,b=[],T=I++,C=_.length>0?_[_.length-1]:void 0,x=!1,O=null;function R(t,n){try{return _.push(T),x=!0,O&&O.cancel(),O=null,e(function(e,r){b.push(function(){try{var i=2===m?t:n,o=(0,a.b07)(i)?u:(0,a.Tnt)(i)?i(u):i;(0,a.$XS)(o)?o.then(e,r):i?e(o):3===m?r(o):e(o)}catch(e){r(e)}}),y&&D()},l)}finally{_.pop()}}function A(){return v[m]}function D(){if(b.length>0){var e=b.slice();b=[],x=!0,O&&O.cancel(),O=null,t(e)}}function k(e,t){return function(n){if(m===t){if(2===e&&(0,a.$XS)(n))return m=1,void n.then(k(2,1),k(3,1));m=e,y=!0,u=n,D(),x||3!==e||O||(O=(0,a.dRz)(N,E))}}}function N(){if(!x)if(x=!0,(0,a.Lln)())process.emit(w,u,c);else{var e=(0,a.zkX)()||(0,a.mS$)();!g&&(g=(0,a.GuU)((0,a.gBW)(a.zS2,[s+"RejectionEvent"]).v)),function(e,t,n,r){var i=(0,a.YEm)();!f&&(f=(0,a.GuU)(!!(0,a.gBW)(p,[i]).v));var o=f.v?i.createEvent("Event"):r?new Event(t):{};if(n&&n(o),f.v&&o.initEvent(t,!1,!0),o&&e[h])e[h](o);else{var s=e["on"+t];if(s)s(o);else{var u=(0,a.zS2)("console");u&&(u.error||u.log)(t,(0,a.mmD)(o))}}}(e,S,function(e){return(0,a.vF1)(e,"promise",{g:function(){return c}}),e.reason=u,e},!!g.v)}}return c={then:R,catch:function(e){return R(void 0,e)},finally:function(e){var t=e,n=e;return(0,a.Tnt)(e)&&(t=function(t){return e&&e(),t},n=function(t){throw e&&e(),t}),R(t,n)}},(0,a.UxO)(c,"state",{get:A}),d&&function(e,t){i=i||{toString:function(){return"[[PromiseResult]]"}},o=o||{toString:function(){return"[[PromiseIsHandled]]"}};var n={};n[r=r||{toString:function(){return"[[PromiseState]]"}}]={get:t},n[i]={get:function(){return(0,a.SZ2)(u)}},n[o]={get:function(){return x}},(0,a.isD)(e,n)}(c,A),(0,a.Lok)()&&(c[(0,a.Y0g)(11)]="IPromise"),c.toString=function(){return"IPromise"+(d?"["+T+((0,a.b07)(C)?"":":"+C)+"]":"")+" "+A()+(y?" - "+P(u):"")},function(){(0,a.Tnt)(n)||(0,a.zkd)(s+": executor is not a function - "+P(n));var e=k(3,0);try{n.call(c,k(2,0),e)}catch(t){e(t)}}(),c}function C(e){return function(t){var n=(0,a.KVm)(arguments,1);return e(function(e,n){try{var r=[],i=1;(0,a.DA8)(t,function(t,o){t&&(i++,l(t,function(t){r[o]=t,0===--i&&e(r)},n))}),0===--i&&e(r)}catch(e){n(e)}},n)}}function x(e){(0,a.Iuo)(e,function(e){try{e()}catch(e){}})}function O(e,t){return T(O,function(e){var t=(0,a.EtT)(e)?e:0;return function(e){(0,a.dRz)(function(){x(e)},t)}}(t),e,t)}function R(e,t){!m&&(m=(0,a.GuU)((0,a.gBW)(a.zS2,[s]).v||null));var n=m.v;if(!n)return O(e);(0,a.Tnt)(e)||(0,a.zkd)(s+": executor is not a function - "+(0,a.mmD)(e));var r=0,i=new n(function(t,n){e(function(e){r=2,t(e)},function(e){r=3,n(e)})});return(0,a.UxO)(i,"state",{get:function(){return v[r]}}),i}function A(e){return T(A,x,e)}function D(e,t){return!y&&(n=A,y=(0,a.GuU)(function(e){var t=(0,a.KVm)(arguments,1);return n(function(t,n){var r=[],i=1;function o(e,n){i++,c(e,function(e){e.rejected?r[n]={status:u,reason:e.reason}:r[n]={status:"fulfilled",value:e.value},0===--i&&t(r)})}try{(0,a.cyL)(e)?(0,a.Iuo)(e,o):(0,a.xZI)(e)?(0,a.DA8)(e,o):(0,a.zkd)("Input is not an iterable"),0===--i&&t(r)}catch(e){n(e)}},t)})),y.v(e,t);var n}function k(e,t){return!b&&(b=(0,a.GuU)(R)),b.v.call(this,e,t)}var N=C(k);(0,a.Y0g)(11)},8257:(e,t,n)=>{n.d(t,{s:()=>g});var r,i=n(8279),o=n(269),a=n(9749),s=n(6182),u=n(3775),c=n(3673),l=n(6492),f=n(2317),d=n(836),v=n(8969),h="getPlugin",p=((r={})[l.Bw]={isVal:c.Gh,v:{}},r),g=function(){function e(){var t,n,r,g,m,y=this;function b(e){void 0===e&&(e=null);var t=e;if(!t){var i=n||(0,f.i8)(null,{},y[l.eT]);t=r&&r[h]?i[s.$o](null,r[h]):i[s.$o](null,r)}return t}function w(e,t,i){(0,a.e)(e,p,(0,u.y0)(t)),!i&&t&&(i=t[s.DI]()[s.uR]());var o=r;r&&r[h]&&(o=r[h]()),y[l.eT]=t,n=(0,f.i8)(i,e,t,o)}function S(){t=!1,y[l.eT]=null,n=null,r=null,m=(0,v.w)(),g=(0,d.P)()}S(),(0,i.A)(e,y,function(e){e[s.mE]=function(e,n,r,i){w(e,n,i),t=!0},e[s.Ik]=function(t,n){var i,o=e[l.eT];if(o&&(!t||o===t[l.eT]())){var a,u=!1,c=t||(0,f.tS)(null,o,r&&r[h]?r[h]():r),d=n||((i={reason:0})[s.tI]=!1,i);return e[s.tn]&&!0===e[s.tn](c,d,v)?a=!0:v(),a}function v(){u||(u=!0,g.run(c,n),m.run(c[s.e4]()),!0===a&&c[s.$5](d),S())}},e[s.HC]=function(t,n){var i=e[l.eT];if(i&&(!t||i===t[l.eT]())){var o,a=!1,u=t||(0,f.nU)(null,i,r&&r[h]?r[h]():r),c=n||{reason:0};return e._doUpdate&&!0===e._doUpdate(u,c,d)?o=!0:d(),o}function d(){a||(a=!0,w(u.getCfg(),u.core(),u[s.uR]()))}},(0,c.RF)(e,"_addUnloadCb",function(){return g},"add"),(0,c.RF)(e,"_addHook",function(){return m},"add"),(0,o.vF1)(e,"_unloadHooks",{g:function(){return m}})}),y[s.e4]=function(e){return b(e)[s.e4]()},y[s.tZ]=function(){return t},y.setInitialized=function(e){t=e},y[s.YH]=function(e){r=e},y[s.$5]=function(e,t){t?t[s.$5](e):r&&(0,o.Tnt)(r[l.qT])&&r[l.qT](e,null)},y._getTelCtx=b}return e.__ieDyn=1,e}()},8279:(e,t,n)=>{n.d(t,{A:()=>L});var r,i=n(269),o="constructor",a="prototype",s="function",u="_dynInstFuncs",c="_isDynProxy",l="_dynClass",f="_dynCls$",d="_dynInstChk",v=d,h="_dfOpts",p="_unknown_",g="__proto__",m="_dyn"+g,y="__dynProto$Gbl",b="_dynInstProto",w="useBaseInst",S="setInstFuncs",_=Object,I=_.getPrototypeOf,E=_.getOwnPropertyNames,P=(0,i.mS$)(),T=P[y]||(P[y]={o:(r={},r[S]=!0,r[w]=!0,r),n:1e3});function C(e){return e&&(e===_[a]||e===Array[a])}function x(e){return C(e)||e===Function[a]}function O(e){var t;if(e){if(I)return I(e);var n=e[g]||e[a]||(e[o]?e[o][a]:null);t=e[m]||n,(0,i.v0u)(e,m)||(delete e[b],t=e[m]=e[b]||e[m],e[b]=n)}return t}function R(e,t){var n=[];if(E)n=E(e);else for(var r in e)"string"==typeof r&&(0,i.v0u)(e,r)&&n.push(r);if(n&&n.length>0)for(var o=0;o<n.length;o++)t(n[o])}function A(e,t,n){return t!==o&&typeof e[t]===s&&(n||(0,i.v0u)(e,t))&&t!==g&&t!==a}function D(e){(0,i.zkd)("DynamicProto: "+e)}function k(e,t){for(var n=e.length-1;n>=0;n--)if(e[n]===t)return!0;return!1}function N(e,t,n,r,o){if(!C(e)){var a=n[u]=n[u]||(0,i.sSX)(null);if(!C(a)){var f=a[t]=a[t]||(0,i.sSX)(null);!1!==a[v]&&(a[v]=!!o),C(f)||R(n,function(t){A(n,t,!1)&&n[t]!==r[t]&&(f[t]=n[t],delete n[t],(!(0,i.v0u)(e,t)||e[t]&&!e[t][c])&&(e[t]=function(e,t){var n=function(){var r=function(e,t,n,r){var o=null;if(e&&(0,i.v0u)(n,l)){var a=e[u]||(0,i.sSX)(null);if((o=(a[n[l]]||(0,i.sSX)(null))[t])||D("Missing ["+t+"] "+s),!o[d]&&!1!==a[v]){for(var c=!(0,i.v0u)(e,t),f=O(e),h=[];c&&f&&!x(f)&&!k(h,f);){var p=f[t];if(p){c=p===r;break}h.push(f),f=O(f)}try{c&&(e[t]=o),o[d]=1}catch(e){a[v]=!1}}}return o}(this,t,e,n)||function(e,t,n){var r=t[e];return r===n&&(r=O(t)[e]),typeof r!==s&&D("["+e+"] is not a "+s),r}(t,e,n);return r.apply(this,arguments)};return n[c]=1,n}(e,t)))})}}}function M(e,t){return(0,i.v0u)(e,a)?e.name||t||p:((e||{})[o]||{}).name||t||p}function L(e,t,n,r){(0,i.v0u)(e,a)||D("theClass is an invalid class definition.");var o=e[a];(function(e,t){if(I){for(var n=[],r=O(t);r&&!x(r)&&!k(n,r);){if(r===e)return!0;n.push(r),r=O(r)}return!1}return!0})(o,t)||D("["+M(e)+"] not in hierarchy of ["+M(t)+"]");var s=null;(0,i.v0u)(o,l)?s=o[l]:(s=f+M(e,"_")+"$"+T.n,T.n++,o[l]=s);var d=L[h],p=!!d[w];p&&r&&void 0!==r[w]&&(p=!!r[w]);var g=function(e){var t=(0,i.sSX)(null);return R(e,function(n){!t[n]&&A(e,n,!1)&&(t[n]=e[n])}),t}(t),m=function(e,t,n,r){function o(e,t,n){var i=t[n];if(i[c]&&r){var o=e[u]||{};!1!==o[v]&&(i=(o[t[l]]||{})[n]||i)}return function(){return i.apply(e,arguments)}}var a=(0,i.sSX)(null);R(n,function(e){a[e]=o(t,n,e)});for(var s=O(e),f=[];s&&!x(s)&&!k(f,s);)R(s,function(e){!a[e]&&A(s,e,!I)&&(a[e]=o(t,s,e))}),f.push(s),s=O(s);return a}(o,t,g,p);n(t,m);var y=!!I&&!!d[S];y&&r&&(y=!!r[S]),N(o,s,t,g,!1!==y)}L[h]=T.o},8393:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.TelemetryUtil=void 0;class n{static applyReplacements(e,t){for(const n of Object.keys(e))for(const r of t)r.lookup.test(n)&&(void 0!==r.replacementString?e[n]=r.replacementString:delete e[n])}static shouldUseOneDataSystemSDK(e){return 74===e.length&&"-"===e[32]&&"-"===e[41]&&"-"===e[46]&&"-"===e[51]&&"-"===e[56]&&"-"===e[69]}static getAdditionalCommonProperties(e){return{"common.os":e.platform,"common.nodeArch":e.architecture,"common.platformversion":(e.release||"").replace(/^(\d+)(\.\d+)?(\.\d+)?(.*)/,"$1$2$3"),"common.telemetryclientversion":"0.9.8"}}static getInstance(){return n._instance||(n._instance=new n),n._instance}}t.TelemetryUtil=n},8547:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.Config=void 0,t.Config={gitHubClientId:"********************",gitHubClientSecret:"2af589bb2ffd03a29cc0df83f767e3f6693f14cd"}},8596:(e,t,n)=>{n.d(t,{o:()=>h});var r=n(659),i=n(3673);function o(e){var t="ai."+e+".";return function(e){return t+e}}var a=o("application"),s=o("device"),u=o("location"),c=o("operation"),l=o("session"),f=o("user"),d=o("cloud"),v=o("internal"),h=function(e){function t(){return e.call(this)||this}return(0,r.qU)(t,e),t}((0,i.SZ)({applicationVersion:a("ver"),applicationBuild:a("build"),applicationTypeId:a("typeId"),applicationId:a("applicationId"),applicationLayer:a("layer"),deviceId:s("id"),deviceIp:s("ip"),deviceLanguage:s("language"),deviceLocale:s("locale"),deviceModel:s("model"),deviceFriendlyName:s("friendlyName"),deviceNetwork:s("network"),deviceNetworkName:s("networkName"),deviceOEMName:s("oemName"),deviceOS:s("os"),deviceOSVersion:s("osVersion"),deviceRoleInstance:s("roleInstance"),deviceRoleName:s("roleName"),deviceScreenResolution:s("screenResolution"),deviceType:s("type"),deviceMachineName:s("machineName"),deviceVMName:s("vmName"),deviceBrowser:s("browser"),deviceBrowserVersion:s("browserVersion"),locationIp:u("ip"),locationCountry:u("country"),locationProvince:u("province"),locationCity:u("city"),operationId:c("id"),operationName:c("name"),operationParentId:c("parentId"),operationRootId:c("rootId"),operationSyntheticSource:c("syntheticSource"),operationCorrelationVector:c("correlationVector"),sessionId:l("id"),sessionIsFirst:l("isFirst"),sessionIsNew:l("isNew"),userAccountAcquisitionDate:f("accountAcquisitionDate"),userAccountId:f("accountId"),userAgent:f("userAgent"),userId:f("id"),userStoreRegion:f("storeRegion"),userAuthUserId:f("authUserId"),userAnonymousUserAcquisitionDate:f("anonUserAcquisitionDate"),userAuthenticatedUserAcquisitionDate:f("authUserAcquisitionDate"),cloudName:d("name"),cloudRole:d("role"),cloudRoleVer:d("roleVer"),cloudRoleInstance:d("roleInstance"),cloudEnvironment:d("environment"),cloudLocation:d("location"),cloudDeploymentUnit:d("deploymentUnit"),internalNodeName:v("nodeName"),internalSdkVersion:v("sdkVersion"),internalAgentVersion:v("agentVersion"),internalSnippet:v("snippet"),internalSdkSrc:v("sdkSrc")}))},8611:e=>{e.exports=require("http")},8823:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.MemoryKeyValueStorage=void 0,t.MemoryKeyValueStorage=class{constructor(){this.storage=new Map}async getValue(e,t){return this.storage.has(e)?await Promise.resolve(this.storage.get(e)):await Promise.resolve(t||void 0)}setValue(e,t){this.storage.set(e,t)}}},8916:(e,t,n)=>{n.r(t),n.d(t,{BE_PROFILE:()=>o,NRT_PROFILE:()=>i,PostChannel:()=>St,RT_PROFILE:()=>r});var r="REAL_TIME",i="NEAR_REAL_TIME",o="BEST_EFFORT",a=n(659),s=n(8279),u=n(4822),c=n(269),l=n(8156),f=n(6149),d=n(4276),v=n(9749),h=n(2317),p=n(3673),g=n(3662),m=n(3775),y=n(8257),b=n(8205),w="",S="drop",_="requeue",I="no-cache, no-store",E="application/x-json-stream",P="cache-control",T="content-type",C="client-version",x="client-id",O="time-delta-to-apply-millis",R="upload-time",A="apikey",D="AuthMsaDeviceTicket",k="WebAuthToken",N="AuthXToken",M="msfpc",L="trace",U="user",H="allowRequestSending",F="firstRequestSent",j="shouldAddClockSkewHeaders",z="getClockSkewHeaderValue",X="setClockSkew",V="length",$="concat",B="iKey",q="count",K="events",G="push",Z="split",W="splice",J="toLowerCase",Q="hdrs",Y="useHdrs",ee="initialize",te="setTimeoutOverride",ne="clearTimeoutOverride",re="overrideEndpointUrl",ie="avoidOptions",oe="enableCompoundKey",ae="disableXhrSync",se="disableFetchKeepAlive",ue="useSendBeacon",ce="fetchCredentials",le="alwaysUseXhrOverride",fe="serializeOfflineEvt",de="getOfflineRequestDetails",ve="createPayload",he="createOneDSPayload",pe="payloadBlob",ge="headers",me="_thePayload",ye="urlString",be="batches",we="sendType",Se="addHeader",_e="canSendRequest",Ie="sendQueuedRequests",Ee="isCompletelyIdle",Pe="setUnloading",Te="resume",Ce="sendSynchronousBatch",xe="_transport",Oe="getWParam",Re="isBeacon",Ae="timings",De="isTeardown",ke="isSync",Ne="data",Me="_sendReason",Le="setKillSwitchTenants",Ue="_backOffTransmission",He="identifier",Fe="eventsLimitInMem",je="autoFlushEventsLimit",ze="baseData",Xe="sendAttempt",Ve="latency",$e="sync";function Be(e){var t=(e.ext||{}).intweb;return t&&(0,u.yD)(t[M])?t[M]:null}function qe(e){for(var t=null,n=0;null===t&&n<e[V];n++)t=Be(e[n]);return t}var Ke=function(){function e(t,n){var r=n?[][$](n):[],i=this,o=qe(r);i[B]=function(){return t},i.Msfpc=function(){return o||w},i[q]=function(){return r[V]},i[K]=function(){return r},i.addEvent=function(e){return!!e&&(r[G](e),o||(o=Be(e)),!0)},i[Z]=function(n,i){var a;if(n<r[V]){var s=r[V]-n;(0,c.hXl)(i)||(s=i<s?i:s),a=r[W](n,s),o=qe(r)}return new e(t,a)}}return e.create=function(t,n){return new e(t,n)},e}(),Ge=n(7292),Ze=n(856),We=n(5664),Je=function(){function e(){var t=!0,n=!0,r=!0,i="use-collector-delta",o=!1;(0,s.A)(e,this,function(e){e[H]=function(){return t},e[F]=function(){r&&(r=!1,o||(t=!1))},e[j]=function(){return n},e[z]=function(){return i},e[X]=function(e){o||(e?(i=e,n=!0,o=!0):n=!1,t=!0)}})}return e.__ieDyn=1,e}(),Qe=function(){function e(){var t={};(0,s.A)(e,this,function(e){e[Le]=function(e,n){if(e&&n)try{var r=(a=e[Z](","),s=[],a&&(0,c.Iuo)(a,function(e){s[G]((0,c.EHq)(e))}),s);if("this-request-only"===n)return r;for(var i=1e3*parseInt(n,10),o=0;o<r[V];++o)t[r[o]]=(0,c.f0d)()+i}catch(e){return[]}var a,s;return[]},e.isTenantKilled=function(e){var n=t,r=(0,c.EHq)(e);return void 0!==n[r]&&n[r]>(0,c.f0d)()||(delete n[r],!1)}})}return e.__ieDyn=1,e}();function Ye(e){var t,n=Math.floor(1200*Math.random())+2400;return t=Math.pow(2,e)*n,Math.min(t,6e5)}var et,tt=2e6,nt=Math.min(tt,65e3),rt="metadata",it="f",ot=/\./,at=function(){function e(t,n,r,i,o,a){var f="data",d="baseData",v=!!i,h=!0,p=n,g={},m=!!a,y=o||u.Go;(0,s.A)(e,this,function(e){function n(e,t,i,o,a,s,l){(0,c.zav)(e,function(e,f){var d=null;if(f||(0,u.yD)(f)){var h=i,m=e,y=a,b=t;if(v&&!o&&ot.test(e)){var w=e.split("."),S=w.length;if(S>1){y&&(y=y.slice());for(var _=0;_<S-1;_++){var I=w[_];b=b[I]=b[I]||{},h+="."+I,y&&y.push(I)}m=w[S-1]}}var E=o&&function(e){var t=g[e];return void 0===t&&(e.length>=7&&(t=(0,c.tGl)(e,"ext.metadata")||(0,c.tGl)(e,"ext.web")),g[e]=t),t}(h);if(d=!E&&p&&p.handleField(h,m)?p.value(h,m,f,r):(0,u.TC)(m,f,r)){var P=d.value;if(b[m]=P,s&&s(y,m,d),l&&"object"==typeof P&&!(0,c.cyL)(P)){var T=y;T&&(T=T.slice()).push(m),n(f,P,h+"."+m,o,T,s,l)}}}})}e.createPayload=function(e,t,n,r,i,o){return{apiKeys:[],payloadBlob:w,overflow:null,sizeExceed:[],failedEvts:[],batches:[],numEvents:0,retryCnt:e,isTeardown:t,isSync:n,isBeacon:r,sendType:o,sendReason:i}},e.appendPayload=function(n,r,i){var o=n&&r&&!n.overflow;return o&&(0,l.r2)(t,function(){return"Serializer:appendPayload"},function(){for(var t=r.events(),o=n.payloadBlob,a=n.numEvents,s=!1,u=[],l=[],f=n.isBeacon,d=f?65e3:3984588,v=f?nt:tt,h=0,p=0;h<t.length;){var g=t[h];if(g){if(a>=i){n.overflow=r.split(h);break}var m=e.getEventBlob(g);if(m&&m.length<=v){var y=m.length;if(o.length+y>d){n.overflow=r.split(h);break}o&&(o+="\n"),o+=m,++p>20&&((0,c.hKY)(o,0,1),p=0),s=!0,a++}else m?u.push(g):l.push(g),t.splice(h,1),h--}h++}if(u.length>0&&n.sizeExceed.push(Ke.create(r.iKey(),u)),l.length>0&&n.failedEvts.push(Ke.create(r.iKey(),l)),s){n.batches.push(r),n.payloadBlob=o,n.numEvents=a;var b=r.iKey();-1===(0,c.rDm)(n.apiKeys,b)&&n.apiKeys.push(b)}},function(){return{payload:n,theBatch:{iKey:r.iKey(),evts:r.events()},max:i}}),o},e.getEventBlob=function(e){try{return(0,l.r2)(t,function(){return"Serializer.getEventBlob"},function(){var t={};t.name=e.name,t.time=e.time,t.ver=e.ver,t.iKey="o:"+(0,u.EO)(e.iKey);var r,i={};m||(r=function(e,t,n){!function(e,t,n,r,i){if(i&&t){var o=e(i.value,i.kind,i.propertyType);if(o>-1){var a=t[rt];a||(a=t[rt]={f:{}});var s=a[it];if(s||(s=a[it]={}),n)for(var u=0;u<n.length;u++){var l=n[u];s[l]||(s[l]={f:{}});var f=s[l][it];f||(f=s[l][it]={}),s=f}s=s[r]={},(0,c.cyL)(i.value)?s.a={t:o}:s.t=o}}}(y,i,e,t,n)});var o=e.ext;o&&(t.ext=i,(0,c.zav)(o,function(e,t){n(t,i[e]={},"ext."+e,!0,null,null,!0)}));var a=t[f]={};a.baseType=e.baseType;var s=a[d]={};return n(e.baseData,s,d,!1,[d],r,h),n(e.data,a,f,!1,[],r,h),JSON.stringify(t)},function(){return{item:e}})}catch(e){return null}}})}return e.__ieDyn=1,e}();function st(e,t){return{set:function(n,r){for(var i=[],o=2;o<arguments.length;o++)i[o-2]=arguments[o];return(0,c.vKV)([e,t],n,r,i)}}}var ut="sendAttempt",ct="?cors=true&"+T[J]()+"="+E,lt=((et={})[1]=_,et[100]=_,et[200]="sent",et[8004]=S,et[8003]=S,et),ft={},dt={};function vt(e,t,n){ft[e]=t,!1!==n&&(dt[t]=e)}function ht(e,t){var n=!1;if(e&&t){var r=(0,c.cGk)(e);if(r&&r[V]>0)for(var i=t[J](),o=0;o<r[V];o++){var a=r[o];if(a&&(0,c.v0u)(t,a)&&a[J]()===i){n=!0;break}}}return n}function pt(e,t,n,r){t&&n&&n[V]>0&&(r&&ft[t]?(e[Q][ft[t]]=n,e[Y]=!0):e.url+="&"+t+"="+n)}vt(D,D,!1),vt(C,C),vt(x,"Client-Id"),vt(A,A),vt(O,O),vt(R,R),vt(N,N);var gt=function(){function e(t,n,r,i){var o,a,f,d,h,g,y,b,_,D,k,N,L,U,J,He,Fe,je,ze,Xe,Ve,$e,Be,qe,et,tt,nt,rt,it,ot,ft,vt,gt=!1;(0,s.A)(e,this,function(e){function s(e,t){try{return ft&&ft.getSenderInst(e,t)}catch(e){}return null}function mt(){try{return{enableSendPromise:!1,isOneDs:!0,disableCredentials:!1,fetchCredentials:vt,disableXhr:!1,disableBeacon:!gt,disableBeaconSync:!gt,disableFetchKeepAlive:Ve,timeWrapper:it,addNoResponse:Be,senderOnCompleteCallBack:{xdrOnComplete:yt,fetchOnComplete:bt,xhrOnComplete:wt,beaconOnRetry:_t}}}catch(e){}return null}function yt(e,t,n){var r=(0,p.Lo)(e);St(t,200,{},r),Nt(r)}function bt(e,t,n,r){var i={},o=e[ge];o&&o.forEach(function(e,t){i[t]=e}),function(e,n,r){St(t,e,n,r),Nt(r)}(e.status,i,n||w)}function wt(e,t,n){var r=(0,p.Lo)(e);St(t,e.status,(0,p.w3)(e,!0),r),Nt(r)}function St(e,t,n,r){try{e(t,n,r)}catch(e){(0,m.ZP)(y,2,518,(0,c.mmD)(e))}}function _t(e,t,n){var r=200,i=e[me],o=e[ye]+(Be?"&NoResponseBody=true":w);try{var a=(0,c.w3n)();if(i){var s=!!_.getPlugin("LocalStorage"),u=[],l=[];(0,c.Iuo)(i[be],function(e){if(u&&e&&e[q]()>0)for(var t=e[K](),n=0;n<t[V];n++){if(!a.sendBeacon(o,U.getEventBlob(t[n]))){u[G](e[Z](n));break}l[G](e[n])}else u[G](e[Z](0))}),l[V]>0&&(i.sentEvts=l),s||Mt(u,8003,i[we],!0)}else r=0}catch(e){(0,m.OG)(y,"Failed to send telemetry using sendBeacon API. Ex:"+(0,c.mmD)(e)),r=0}finally{St(t,r,{},w)}}function It(e){return 2===e||3===e}function Et(e){return Fe&&It(e)&&(e=2),e}function Pt(){return!f&&h<n}function Tt(){var e=L;return L=[],e}function Ct(e,t,n){var r=!1;return e&&e[V]>0&&!f&&b[t]&&U&&(r=0!==t||Pt()&&(n>0||d[H]())),r}function xt(e){var t={};return e&&(0,c.Iuo)(e,function(e,n){t[n]={iKey:e[B](),evts:e[K]()}}),t}function Ot(e,n,r,i,o){if(e&&0!==e[V])if(f)Mt(e,1,i);else{i=Et(i);try{var s=e,d=0!==i;(0,l.r2)(_,function(){return"HttpManager:_sendBatches"},function(s){s&&(e=e.slice(0));for(var c=[],l=null,f=(0,u.WB)(),v=b[i]||(d?b[1]:b[0]),h=v&&v[xe],p=$e&&(Fe||It(i)||3===h||v._isSync&&2===h);Ct(e,i,n);){var g=e.shift();g&&g[q]()>0&&(a.isTenantKilled(g[B]())?c[G](g):(l=l||U[ve](n,r,d,p,o,i),U.appendPayload(l,g,t)?null!==l.overflow&&(e=[l.overflow][$](e),l.overflow=null,Dt(l,f,(0,u.WB)(),o),f=(0,u.WB)(),l=null):(Dt(l,f,(0,u.WB)(),o),f=(0,u.WB)(),e=[g][$](e),l=null)))}l&&Dt(l,f,(0,u.WB)(),o),e[V]>0&&(L=e[$](L)),Mt(c,8004,i)},function(){return{batches:xt(s),retryCount:n,isTeardown:r,isSynchronous:d,sendReason:o,useSendBeacon:It(i),sendType:i}},!d)}catch(e){(0,m.ZP)(y,2,48,"Unexpected Exception sending batch: "+(0,c.mmD)(e))}}}function Rt(e,t){var n={url:o,hdrs:{},useHdrs:!1};t?(n[Q]=(0,u.X$)(n[Q],N),n.useHdrs=(0,c.cGk)(n.hdrs)[V]>0):(0,c.zav)(N,function(e,t){dt[e]?pt(n,dt[e],t,!1):(n[Q][e]=t,n[Y]=!0)}),pt(n,x,"NO_AUTH",t),pt(n,C,u.xE,t);var r=w;(0,c.Iuo)(e.apiKeys,function(e){r[V]>0&&(r+=","),r+=e}),pt(n,A,r,t),pt(n,R,(0,c.f0d)().toString(),t);var i=function(e){for(var t=0;t<e.batches[V];t++){var n=e[be][t].Msfpc();if(n)return encodeURIComponent(n)}return w}(e);if((0,u.yD)(i)&&(n.url+="&ext.intweb.msfpc="+i),d[j]()&&pt(n,O,d[z](),t),_[Oe]){var a=_[Oe]();a>=0&&(n.url+="&w="+a)}for(var s=0;s<k[V];s++)n.url+="&"+k[s].name+"="+k[s].value;return n}function At(e,t,n){e[t]=e[t]||{},e[t][g.identifier]=n}function Dt(t,n,i,o){if(t&&t.payloadBlob&&t.payloadBlob[V]>0){var s=!!et,f=b[t.sendType];!It(t[we])&&t[Re]&&2===t.sendReason&&(f=b[2]||b[3]||f);var v=je;(t.isBeacon||3===f[xe])&&(v=!1);var p=Rt(t,v);v=v||p[Y];var w=(0,u.WB)();(0,l.r2)(_,function(){return"HttpManager:_doPayloadSend"},function(){for(var b=0;b<t.batches[V];b++)for(var S=t[be][b][K](),C=0;C<S[V];C++){var x=S[C];if(J){var O=x[Ae]=x[Ae]||{};At(O,"sendEventStart",w),At(O,"serializationStart",n),At(O,"serializationCompleted",i)}x[ut]>0?x[ut]++:x[ut]=1}Mt(t[be],1e3+(o||0),t[we],!0);var R={data:t[pe],urlString:p.url,headers:p[Q],_thePayload:t,_sendReason:o,timeout:ze,disableXhrSync:Xe,disableFetchKeepAlive:Ve};v&&(ht(R[ge],P)||(R[ge][P]=I),ht(R[ge],T)||(R[ge][T]=E));var A=null;f&&(A=function(n){d[F]();var i=function(n,i){!function(t,n,i,o){var s,l=9e3,f=null,v=!1,p=!1;try{var m=!0;if(typeof t!==We.bA){if(n){d[X](n["time-delta-millis"]);var y=n["kill-duration"]||n["kill-duration-seconds"];(0,c.Iuo)(a[Le](n["kill-tokens"],y),function(e){(0,c.Iuo)(i[be],function(t){if(t[B]()===e){f=f||[];var n=t[Z](0);i.numEvents-=n[q](),f[G](n)}})})}if(200==t||204==t)return void(l=200);((s=t)>=300&&s<500&&429!=s||501==s||505==s||i.numEvents<=0)&&(m=!1),l=9e3+t%1e3}if(m){l=100;var b=i.retryCnt;0===i[we]&&(b<r?(v=!0,kt(function(){0===i[we]&&h--,Ot(i[be],b+1,i[De],Fe?2:i[we],5)},Fe,Ye(b))):(p=!0,Fe&&(l=8001)))}}finally{v||(d[X](),function(t,n,r,i){try{i&&g[Ue]();var o=t[be];200===n&&(o=t.sentEvts||t[be],i||t[ke]||g._clearBackOff(),function(e){if(J){var t=(0,u.WB)();(0,c.Iuo)(e,function(e){e&&e[q]()>0&&function(e,t){J&&(0,c.Iuo)(e,function(e){At(e[Ae]=e[Ae]||{},"sendEventCompleted",t)})}(e[K](),t)})}}(o)),Mt(o,n,t[we],!0)}finally{0===t[we]&&(h--,5!==r&&e.sendQueuedRequests(t[we],r))}}(i,l,o,p)),Mt(f,8004,i[we])}}(n,i,t,o)},s=t[De]||t[ke];try{f.sendPOST(n,i,s),tt&&tt(R,n,s,t[Re])}catch(e){(0,m.OG)(y,"Unexpected exception sending payload. Ex:"+(0,c.mmD)(e)),St(i,0,{})}}),(0,l.r2)(_,function(){return"HttpManager:_doPayloadSend.sender"},function(){if(A)if(0===t[we]&&h++,s&&!t.isBeacon&&3!==f[xe]){var e={data:R[Ne],urlString:R[ye],headers:(0,u.X$)({},R[ge]),timeout:R.timeout,disableXhrSync:R[ae],disableFetchKeepAlive:R[se]},n=!1;(0,l.r2)(_,function(){return"HttpManager:_doPayloadSend.sendHook"},function(){try{et(e,function(e){n=!0,D||e[me]||(e[me]=e[me]||R[me],e[Me]=e[Me]||R[Me]),A(e)},t.isSync||t[De])}catch(e){n||A(R)}})}else A(R)})},function(){return{thePayload:t,serializationStart:n,serializationCompleted:i,sendReason:o}},t[ke])}t.sizeExceed&&t.sizeExceed[V]>0&&Mt(t.sizeExceed,8003,t[we]),t.failedEvts&&t.failedEvts[V]>0&&Mt(t.failedEvts,8002,t[we])}function kt(e,t,n){t?e():it.set(e,n)}function Nt(e){var t=nt;try{for(var n=0;n<t[V];n++)try{t[n](e)}catch(e){(0,m.ZP)(y,1,519,"Response handler failed: "+e)}if(e){var r=JSON.parse(e);(0,u.yD)(r.webResult)&&(0,u.yD)(r.webResult[M])&&He.set("MSFPC",r.webResult[M],31536e3)}}catch(e){}}function Mt(e,t,n,r){if(e&&e[V]>0&&i){var o=i[(s=t,c=lt[s],(0,u.yD)(c)||(c="oth",s>=9e3&&s<=9999?c="rspFail":s>=8e3&&s<=8999?c=S:s>=1e3&&s<=1999&&(c="send")),c)];if(o){var a=0!==n;(0,l.r2)(_,function(){return"HttpManager:_sendBatchesNotification"},function(){kt(function(){try{o.call(i,e,t,a,n)}catch(e){(0,m.ZP)(y,1,74,"send request notification failed: "+e)}},r||a,0)},function(){return{batches:xt(e),reason:t,isSync:a,sendSync:r,sendType:n}},!a)}}var s,c}!function(){var e;o=null,a=new Qe,f=!1,d=new Je,gt=!1,h=0,g=null,y=null,b=null,_=null,D=!0,k=[],N={},L=[],U=null,J=!1,He=null,Fe=!1,je=!1,ze=e,Xe=e,Ve=e,$e=e,Be=e,qe=[],et=e,tt=e,nt=[],rt=!1,it=st(),ot=!1,ft=null}(),e[ee]=function(e,t,n){rt||(_=t,He=t.getCookieMgr(),y=(g=n).diagLog(),(0,c.Yny)(qe,(0,v.a)(e,function(e){var r,i=e.cfg,a=e.cfg.extensionConfig[n.identifier];it=st(a[te],a[ne]),(0,u.yD)(i.anonCookieName)?function(e,t,n){for(var r=0;r<e[V];r++)if(e[r].name===t)return void(e[r].value=n);e[G]({name:t,value:n})}(k,"anoncknm",i.anonCookieName):function(e){for(var t=0;t<e[V];t++)if("anoncknm"===e[t].name)return void e[W](t,1)}(k),et=a.payloadPreprocessor,tt=a.payloadListener;var l=a.httpXHROverride,f=a[re]?a[re]:i.endpointUrl;o=f+ct,je=!!(0,c.b07)(a[ie])||!a[ie],J=!a.disableEventTimings;var d=a.valueSanitizer,v=a.stringifyObjects,h=!!i[oe];(0,c.b07)(a[oe])||(h=!!a[oe]),ze=a.xhrTimeout,Xe=!!a[ae],Ve=!!a[se],Be=!1!==a.addNoResponse,ot=!!a.excludeCsMetaData,t.getPlugin("LocalStorage")&&(Ve=!0),gt=!(0,Ge.lV)(),U=new at(_,d,v,h,u.Go,ot),(0,c.hXl)(a[ue])||(gt=!!a[ue]),a[ce]&&(vt=a[ce]);var g=mt();ft?ft.SetConfig(g):(ft=new Ze.v)[ee](g,y);var w=l,S=a[le]?l:null,I=a[le]?l:null,E=[3,2];if(!l){D=!1;var P=[];(0,Ge.lV)()?(P=[2,1],E=[2,1,3]):P=[1,2,3],(l=s(P=(0,p.jL)(P,a.transports),!1))||(0,m.OG)(y,"No available transport to send events"),w=s(P,!0)}S||(S=s(E=(0,p.jL)(E,a.unloadTransports),!0)),$e=!D&&(gt&&(0,Ge.Uf)()||!Ve&&(0,Ge.R7)(!0)),(r={})[0]=l,r[1]=w||s([1,2,3],!0),r[2]=S||w||s([1],!0),r[3]=I||s([2,3],!0)||w||s([1],!0),b=r})),rt=!0)},e.addResponseHandler=function(e){return nt[G](e),{rm:function(){var t=nt.indexOf(e);t>=0&&nt[W](t,1)}}},e[fe]=function(e){try{if(U)return U.getEventBlob(e)}catch(e){}return w},e[de]=function(){try{return Rt(U&&U[ve](0,!1,!1,!1,1,0),je)}catch(e){}return null},e[he]=function(e,n){try{var r=[];(0,c.Iuo)(e,function(e){n&&(e=(0,p.hW)(e));var t=Ke.create(e[B],[e]);r[G](t)});for(var i=null;r[V]>0&&U;){var o=r.shift();o&&o[q]()>0&&(i=i||U[ve](0,!1,!1,!1,1,0),U.appendPayload(i,o,t))}var a=Rt(i,je),s={data:i[pe],urlString:a.url,headers:a[Q],timeout:ze,disableXhrSync:Xe,disableFetchKeepAlive:Ve};return je&&(ht(s[ge],P)||(s[ge][P]=I),ht(s[ge],T)||(s[ge][T]=E)),s}catch(e){}return null},e._getDbgPlgTargets=function(){return[b[0],a,U,b,mt(),o]},e[Se]=function(e,t){N[e]=t},e.removeHeader=function(e){delete N[e]},e[_e]=function(){return Pt()&&d[H]()},e[Ie]=function(e,t){(0,c.b07)(e)&&(e=0),Fe&&(e=Et(e),t=2),Ct(L,e,0)&&Ot(Tt(),0,!1,e,t||0)},e[Ee]=function(){return!f&&0===h&&0===L[V]},e[Pe]=function(e){Fe=e},e.addBatch=function(e){if(e&&e[q]()>0){if(a.isTenantKilled(e[B]()))return!1;L[G](e)}return!0},e.teardown=function(){L[V]>0&&Ot(Tt(),0,!0,2,2),(0,c.Iuo)(qe,function(e){e&&e.rm&&e.rm()}),qe=[]},e.pause=function(){f=!0},e[Te]=function(){f=!1,e[Ie](0,4)},e[Ce]=function(e,t,n){e&&e[q]()>0&&((0,c.hXl)(t)&&(t=1),Fe&&(t=Et(t),n=2),Ot([e],0,!1,t,n||0))}})}return e.__ieDyn=1,e}(),mt=1e4,yt="eventsDiscarded",bt=void 0,wt=(0,c.ZHX)({eventsLimitInMem:{isVal:u.ei,v:mt},immediateEventLimit:{isVal:u.ei,v:500},autoFlushEventsLimit:{isVal:u.ei,v:0},disableAutoBatchFlushLimit:!1,httpXHROverride:{isVal:function(e){return e&&e.sendPOST},v:bt},overrideInstrumentationKey:bt,overrideEndpointUrl:bt,disableTelemetry:!1,ignoreMc1Ms0CookieProcessing:!1,setTimeoutOverride:bt,clearTimeoutOverride:bt,payloadPreprocessor:bt,payloadListener:bt,disableEventTimings:bt,valueSanitizer:bt,stringifyObjects:bt,enableCompoundKey:bt,disableOptimizeObj:!1,fetchCredentials:bt,transports:bt,unloadTransports:bt,useSendBeacon:bt,disableFetchKeepAlive:bt,avoidOptions:!1,xhrTimeout:bt,disableXhrSync:bt,alwaysUseXhrOverride:!1,maxEventRetryAttempts:{isVal:c.EtT,v:6},maxUnloadEventRetryAttempts:{isVal:c.EtT,v:2},addNoResponse:bt,excludeCsMetaData:bt}),St=function(e){function t(){var n,a=e.call(this)||this;a.identifier="PostChannel",a.priority=1011,a.version="4.3.4";var y,w,S,_,I,E,P,T,C,x,O,R,A,N,M,H,F,j,z,X,J,Q,Y,re,ie,oe=!1,ae=[],se=!1,ue=0,ce=0,le={},ve=r;return(0,s.A)(t,a,function(e,t){function a(){(0,f.Ds)(null,j),(0,f.sq)(null,j),(0,f.vF)(null,j)}function s(e){var t="";return e&&e[V]&&(0,c.Iuo)(e,function(e){t&&(t+="\n"),t+=e}),t}function pe(e){var t="";try{ye(e),t=T[fe](e)}catch(e){}return t}function ge(e){"beforeunload"!==(e||(0,c.zkX)().event).type&&(M=!0,T[Pe](M)),Le(2,2)}function me(e){M=!1,T[Pe](M)}function ye(e){e.ext&&e.ext[L]&&delete e.ext[L],e.ext&&e.ext[U]&&e.ext[U].id&&delete e.ext[U].id,N&&(e.ext=(0,p.hW)(e.ext),e[ze]&&(e[ze]=(0,p.hW)(e[ze])),e[Ne]&&(e[Ne]=(0,p.hW)(e[Ne])))}function we(e,t){if(e[Xe]||(e[Xe]=0),e[Ve]||(e[Ve]=1),ye(e),e[$e])if(E||se)e[Ve]=3,e[$e]=!1;else if(T)return N&&(e=(0,p.hW)(e)),void T[Ce](Ke.create(e[B],[e]),!0===e[$e]?1:e[$e],3);var n=e[Ve],r=ce,i=S;4===n&&(r=ue,i=w);var o=!1;if(r<i)o=!Ge(e,t);else{var a=1,s=20;4===n&&(a=4,s=1),o=!0,function(e,t,n,r){for(;n<=t;){var i=Be(e,t,!0);if(i&&i[q]()>0){var o=i[Z](0,r),a=o[q]();if(a>0)return 4===n?ue-=a:ce-=a,it(yt,[o],g.x.QueueFull),!0}n++}return Ze(),!1}(e[B],e[Ve],a,s)&&(o=!Ge(e,t))}o&&rt(yt,[e],g.x.QueueFull)}function xe(e,t,n){var r=We(e,t,n);return T[Ie](t,n),r}function Re(){return ce>0}function Ae(){if(R>=0&&We(R,0,A)&&T[Ie](0,A),ue>0&&!I&&!se){var e=le[ve][2];e>=0&&(I=ke(function(){I=null,xe(4,0,1),Ae()},e))}var t=le[ve][1];!_&&!y&&t>=0&&!se&&(Re()?_=ke(function(){_=null,xe(0===P?3:1,0,1),P++,P%=2,Ae()},t):P=0)}function De(){n=null,oe=!1,ae=[],y=null,se=!1,ue=0,w=500,ce=0,S=mt,le={},ve=r,_=null,I=null,E=0,P=0,C={},x=0,Y=!1,O=0,R=-1,A=null,N=!0,M=!1,H=6,F=2,j=null,re=null,ie=!1,z=st(),T=new gt(500,2,1,{requeue:tt,send:ot,sent:at,drop:ut,rspFail:ct,oth:lt}),et(),C[4]={batches:[],iKeyMap:{}},C[3]={batches:[],iKeyMap:{}},C[2]={batches:[],iKeyMap:{}},C[1]={batches:[],iKeyMap:{}},ft()}function ke(e,t){0===t&&E&&(t=1);var n=1e3;return E&&(n=Ye(E-1)),z.set(e,t*n)}function Me(){return null!==_&&(_.cancel(),_=null,P=0,!0)}function Le(e,t){Me(),y&&(y.cancel(),y=null),se||xe(1,e,t)}function Be(e,t,n){var r=C[t];r||(r=C[t=1]);var i=r.iKeyMap[e];return!i&&n&&(i=Ke.create(e),r.batches[G](i),r.iKeyMap[e]=i),i}function qe(t,n){T[_e]()&&!E&&(x>0&&ce>x&&(n=!0),n&&null==y&&e.flush(t,function(){},20))}function Ge(e,t){N&&(e=(0,p.hW)(e));var n=e[Ve],r=Be(e[B],n,!0);return!!r.addEvent(e)&&(4!==n?(ce++,t&&0===e[Xe]&&qe(!e.sync,O>0&&r[q]()>=O)):ue++,!0)}function Ze(){for(var e=0,t=0,n=function(n){var r=C[n];r&&r[be]&&(0,c.Iuo)(r[be],function(r){4===n?e+=r[q]():t+=r[q]()})},r=1;r<=4;r++)n(r);ce=t,ue=e}function We(t,n,r){var i=!1,o=0===n;return!o||T[_e]()?(0,l.r2)(e.core,function(){return"PostChannel._queueBatches"},function(){for(var e=[],n=4;n>=t;){var r=C[n];r&&r.batches&&r.batches[V]>0&&((0,c.Iuo)(r[be],function(t){T.addBatch(t)?i=i||t&&t[q]()>0:e=e[$](t[K]()),4===n?ue-=t[q]():ce-=t[q]()}),r[be]=[],r.iKeyMap={}),n--}e[V]>0&&rt(yt,e,g.x.KillSwitch),i&&R>=t&&(R=-1,A=0)},function(){return{latency:t,sendType:n,sendReason:r}},!o):(R=R>=0?Math.min(R,t):t,A=Math.max(A,r)),i}function Je(e,t){xe(1,0,t),Ze(),Qe(function(){e&&e(),ae[V]>0?y=ke(function(){y=null,Je(ae.shift(),t)},0):(y=null,Ae())})}function Qe(e){T[Ee]()?e():y=ke(function(){y=null,Qe(e)},.25)}function et(){(le={})[r]=[2,1,0],le[i]=[6,3,0],le[o]=[18,9,0]}function tt(t,n){var r=[],i=H;M&&(i=F),(0,c.Iuo)(t,function(t){t&&t[q]()>0&&(0,c.Iuo)(t[K](),function(t){t&&(t[$e]&&(t[Ve]=4,t[$e]=!1),t[Xe]<i?((0,u.u9)(t,e[He]),we(t,!1)):r[G](t))})}),r[V]>0&&rt(yt,r,g.x.NonRetryableStatus),M&&Le(2,2)}function nt(t,n){var r=Q||{},i=r[t];if(i)try{i.apply(r,n)}catch(n){(0,m.ZP)(e.diagLog(),1,74,t+" notification failed: "+n)}}function rt(e,t){for(var n=[],r=2;r<arguments.length;r++)n[r-2]=arguments[r];t&&t[V]>0&&nt(e,[t][$](n))}function it(e,t){for(var n=[],r=2;r<arguments.length;r++)n[r-2]=arguments[r];t&&t[V]>0&&(0,c.Iuo)(t,function(t){t&&t[q]()>0&&nt(e,[t.events()][$](n))})}function ot(e,t,n){e&&e[V]>0&&nt("eventsSendRequest",[t>=1e3&&t<=1999?t-1e3:0,!0!==n])}function at(e,t){it("eventsSent",e,t),Ae()}function ut(e,t){it(yt,e,t>=8e3&&t<=8999?t-8e3:g.x.Unknown)}function ct(e){it(yt,e,g.x.NonRetryableStatus),Ae()}function lt(e,t){it(yt,e,g.x.Unknown),Ae()}function ft(){O=J?0:Math.max(1500,S/6)}De(),e._getDbgPlgTargets=function(){return[T,n]},e[ee]=function(r,i,o){(0,l.r2)(i,function(){return"PostChannel:initialize"},function(){t[ee](r,i,o),Q=i.getNotifyMgr();try{j=(0,f.Hm)((0,d.Z)(e[He]),i.evtNamespace&&i.evtNamespace()),e._addHook((0,v.a)(r,function(t){var r=t.cfg,o=(0,h.i8)(null,r,i);n=o.getExtCfg(e[He],wt),z=st(n[te],n[ne]),N=!n.disableOptimizeObj&&(0,u.F2)(),X=n.ignoreMc1Ms0CookieProcessing,function(e){var t=e[Oe];e[Oe]=function(){var n=0;return X&&(n|=2),n|t.call(e)}}(i),S=n[Fe],w=n.immediateEventLimit,x=n[je],H=n.maxEventRetryAttempts,F=n.maxUnloadEventRetryAttempts,J=n.disableAutoBatchFlushLimit,(0,c.$XS)(r.endpointUrl)?e.pause():se&&e[Te](),ft(),re=n.overrideInstrumentationKey,ie=!!n.disableTelemetry,Y&&a();var s=r.disablePageUnloadEvents||[];Y=(0,f.ee)(ge,s,j),Y=(0,f.Fc)(ge,s,j)||Y,Y=(0,f.oS)(me,r.disablePageShowEvents,j)||Y})),T[ee](r,e.core,e)}catch(t){throw e.setInitialized(!1),t}},function(){return{theConfig:r,core:i,extensions:o}})},e.processTelemetry=function(t,n){(0,u.u9)(t,e[He]),n=n||e._getTelCtx(n);var r=t;ie||oe||(re&&(r[B]=re),we(r,!0),M?Le(2,2):Ae()),e.processNext(r,n)},e.getOfflineSupport=function(){try{var e=T&&T[de]();if(T)return{getUrl:function(){return e?e.url:null},serialize:pe,batch:s,shouldProcess:function(e){return!ie},createPayload:function(e){return null},createOneDSPayload:function(e){if(T[he])return T[he](e,N)}}}catch(e){}return null},e._doTeardown=function(e,t){Le(2,2),oe=!0,T.teardown(),a(),De()},e.setEventQueueLimits=function(e,t){n[Fe]=S=(0,u.ei)(e)?e:mt,n[je]=x=(0,u.ei)(t)?t:0,ft();var r=ce>e;if(!r&&O>0)for(var i=1;!r&&i<=3;i++){var o=C[i];o&&o[be]&&(0,c.Iuo)(o[be],function(e){e&&e[q]()>=O&&(r=!0)})}qe(!0,r)},e.pause=function(){Me(),se=!0,T&&T.pause()},e[Te]=function(){se=!1,T&&T[Te](),Ae()},e._loadTransmitProfiles=function(e){Me(),et(),ve=r,Ae(),(0,c.zav)(e,function(e,t){var n=t[V];if(n>=2){var r=n>2?t[2]:0;if(t[W](0,n-2),t[1]<0&&(t[0]=-1),t[1]>0&&t[0]>0){var i=t[0]/t[1];t[0]=Math.ceil(i)*t[1]}r>=0&&t[1]>=0&&r>t[1]&&(r=t[1]),t[G](r),le[e]=t}})},e.flush=function(e,t,n){var r;if(void 0===e&&(e=!0),!se)if(n=n||1,e)t||(r=(0,b.Qo)(function(e){t=e})),null==y?(Me(),We(1,0,n),y=ke(function(){y=null,Je(t,n)},0)):ae[G](t);else{var i=Me();xe(1,1,n),t&&t(),i&&Ae()}return r},e.setMsaAuthTicket=function(e){T[Se](D,e)},e.setAuthPluginHeader=function(e){T[Se](k,e)},e.removeAuthPluginHeader=function(){T.removeHeader(k)},e.hasEvents=Re,e._setTransmitProfile=function(e){ve!==e&&void 0!==le[e]&&(Me(),ve=e,Ae())},(0,p.o$)(e,function(){return T},["addResponseHandler"]),e[Ue]=function(){E<4&&(E++,Me(),Ae())},e._clearBackOff=function(){E&&(E=0,Me(),Ae())}}),a}return(0,a.qU)(t,e),t.__ieDyn=1,t}(y.s)},8967:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.TargetPopulation=t.Filters=t.VSCodeFilterProvider=void 0;const r=n(1398);class i{constructor(e,t,n){this.extensionName=e,this.extensionVersion=t,this.targetPopulation=n}static trimVersionSuffix(e){return e.split(/\-[a-zA-Z0-9]+$/)[0]}getFilterValue(e){switch(e){case o.ApplicationVersion:return i.trimVersionSuffix(r.version);case o.Build:return r.env.appName;case o.ClientId:return r.env.machineId;case o.ExtensionName:return this.extensionName;case o.ExtensionVersion:return i.trimVersionSuffix(this.extensionVersion);case o.Language:return r.env.language;case o.TargetPopulation:return this.targetPopulation;default:return""}}getFilters(){let e=new Map,t=Object.values(o);for(let n of t)e.set(n,this.getFilterValue(n));return e}}var o,a;t.VSCodeFilterProvider=i,function(e){e.Market="X-MSEdge-Market",e.CorpNet="X-FD-Corpnet",e.ApplicationVersion="X-VSCode-AppVersion",e.Build="X-VSCode-Build",e.ClientId="X-MSEdge-ClientId",e.ExtensionName="X-VSCode-ExtensionName",e.ExtensionVersion="X-VSCode-ExtensionVersion",e.Language="X-VSCode-Language",e.TargetPopulation="X-VSCode-TargetPopulation"}(o||(t.Filters=o={})),function(e){e.Team="team",e.Internal="internal",e.Insiders="insider",e.Public="public"}(a||(t.TargetPopulation=a={}))},8969:(e,t,n)=>{n.d(t,{d:()=>u,w:()=>c});var r,i,o=n(269),a=n(6182),s=n(3775);function u(e,t){r=e,i=t}function c(){var e=[];return{run:function(t){var n=e;e=[],(0,o.Iuo)(n,function(e){try{(e.rm||e.remove).call(e)}catch(e){(0,s.ZP)(t,2,73,"Unloading:"+(0,o.mmD)(e))}}),r&&n[a.oI]>r&&(i?i("doUnload",n):(0,s.ZP)(null,1,48,"Max unload hooks exceeded. An excessive number of unload hooks has been detected."))},add:function(t){t&&((0,o.Yny)(e,t),r&&e[a.oI]>r&&(i?i("Add",e):(0,s.ZP)(null,1,48,"Max unload hooks exceeded. An excessive number of unload hooks has been detected.")))}}}},9052:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.default=class{constructor(){this.initializePromise=Promise.resolve(),this.initialFetch=Promise.resolve()}isFlightEnabled(e){return!1}isCachedFlightEnabled(e){return Promise.resolve(!1)}isFlightEnabledAsync(e){return Promise.resolve(!1)}getTreatmentVariable(e,t){}getTreatmentVariableAsync(e,t){return Promise.resolve(void 0)}}},9147:(e,t,n)=>{n.d(t,{Dy:()=>u,Hf:()=>f,If:()=>v,QA:()=>c,V9:()=>l,hF:()=>d,nM:()=>o});var r=n(269),i=n(6182),o=(0,r.eCG)("[[ai_dynCfg_1]]"),a=(0,r.eCG)("[[ai_blkDynCfg_1]]"),s=(0,r.eCG)("[[ai_frcDynCfg_1]]");function u(e){var t;return e&&((0,r.cyL)(e)?(t=[])[i.oI]=e[i.oI]:(0,r.QdQ)(e)&&(t={}),t)?((0,r.zav)(e,function(e,n){t[e]=u(n)}),t):e}function c(e){if(e){var t=e[o]||e;if(t.cfg&&(t.cfg===e||t.cfg[o]===t))return t}return null}function l(e){if(e&&((0,r.QdQ)(e)||(0,r.cyL)(e)))try{e[a]=!0}catch(e){}return e}function f(e){if(e)try{e[s]=!0}catch(e){}return e}function d(e,t,n){var i=!1;return n&&!e[t.blkVal]&&((i=n[s])||n[a]||(i=(0,r.QdQ)(n)||(0,r.cyL)(n))),i}function v(e){(0,r.zkd)("InvalidAccess:"+e)}},9354:(e,t,n)=>{n.d(t,{Gz:()=>l,M0:()=>v,PS:()=>d,cM:()=>u,k6:()=>f,wX:()=>c});var r=n(269),i=n(5130),o=(0,r.YEm)()||{},a=0,s=[null,null,null,null,null];function u(e){var t=a,n=s,r=n[t];return o.createElement?n[t]||(r=n[t]=o.createElement("a")):r={host:d(e,!0)},r.href=e,++t>=n[i.oI]&&(t=0),a=t,r}function c(e){var t,n=u(e);return n&&(t=n.href),t}function l(e){var t,n=u(e);return n&&(t=n[i.Ue]),t}function f(e,t){return e?e.toUpperCase()+" "+t:t}function d(e,t){var n=v(e,t)||"";if(n){var o=n.match(/(www\d{0,5}\.)?([^\/:]{1,256})(:\d{1,20})?/i);if(null!=o&&o[i.oI]>3&&(0,r.KgX)(o[2])&&o[2][i.oI]>0)return o[2]+(o[3]||"")}return n}function v(e,t){var n=null;if(e){var o=e.match(/(\w{1,150}):\/\/([^\/:]{1,256})(:\d{1,20})?/i);if(null!=o&&o[i.oI]>2&&(0,r.KgX)(o[2])&&o[2][i.oI]>0&&(n=o[2]||"",t&&o[i.oI]>2)){var a=(o[1]||"")[i.OL](),s=o[3]||"";("http"===a&&":80"===s||"https"===a&&":443"===s)&&(s=""),n+=s}}return n}},9749:(e,t,n)=>{n.d(t,{e:()=>b,a:()=>w});var r,i=n(269),o=n(4276),a=n(6492),s=n(6182),u=n(991),c=n(9147),l=["push","pop","shift","unshift","splice"],f=function(e,t,n,r){e&&e[s.ih](3,108,"".concat(n," [").concat(t,"] failed - ")+(0,i.mmD)(r))};function d(e,t){var n=(0,i.kgX)(e,t);return n&&n.get}function v(e,t,n,r){if(t){var o=d(t,n);o&&o[e.prop]?t[n]=r:function(e,t,n,r){var o={n,h:[],trk:function(t){t&&t.fn&&(-1===(0,i.rDm)(o.h,t)&&o.h[s.y5](t),e.trk(t,o))},clr:function(e){var t=(0,i.rDm)(o.h,e);-1!==t&&o.h[s.Ic](t,1)}},u=!0,l=!1;function h(){u&&(l=l||(0,c.hF)(h,e,r),r&&!r[c.nM]&&l&&(r=p(e,r,n,"Converting")),u=!1);var t=e.act;return t&&o.trk(t),r}h[e.prop]={chng:function(){e.add(o)}},(0,i.vF1)(t,o.n,{g:h,s:function(g){if(r!==g){h[e.ro]&&!e.upd&&(0,c.If)("["+n+"] is read-only:"+(0,i.mmD)(t)),u&&(l=l||(0,c.hF)(h,e,r),u=!1);var m=l&&h[e.rf];if(l)if(m){(0,i.zav)(r,function(e){r[e]=g?g[e]:a.HP});try{(0,i.zav)(g,function(t,n){v(e,r,t,n)}),g=r}catch(t){f((e.hdlr||{})[s.Uw],n,"Assigning",t),l=!1}}else r&&r[c.nM]&&(0,i.zav)(r,function(t){var n=d(r,t);if(n){var i=n[e.prop];i&&i.chng()}});if(g!==r){var y=g&&(0,c.hF)(h,e,g);!m&&y&&(g=p(e,g,n,"Converting")),r=g,l=y}e.add(o)}}})}(e,t,n,r)}return t}function h(e,t,n,r){if(t){var i=d(t,n),o=i&&!!i[e.prop],a=r&&r[0],u=r&&r[1],l=r&&r[2];if(!o){if(l)try{(0,c.V9)(t)}catch(t){f((e.hdlr||{})[s.Uw],n,"Blocking",t)}try{v(e,t,n,t[n]),i=d(t,n)}catch(t){f((e.hdlr||{})[s.Uw],n,"State",t)}}a&&(i[e.rf]=a),u&&(i[e.ro]=u),l&&(i[e.blkVal]=!0)}return t}function p(e,t,n,r){try{(0,i.zav)(t,function(n,r){v(e,t,n,r)}),t[c.nM]||((0,i.UxO)(t,c.nM,{get:function(){return e[s.K0]}}),function(e,t,n){(0,i.cyL)(t)&&(0,i.Iuo)(l,function(r){var i=t[r];t[r]=function(){for(var r=[],o=0;o<arguments.length;o++)r[o]=arguments[o];var a=i[s.y9](this,r);return p(e,t,n,"Patching"),a}})}(e,t,n))}catch(t){f((e.hdlr||{})[s.Uw],n,r,t)}return t}var g="[[ai_",m="]]";function y(e,t,n){var a,l=(0,c.QA)(t);if(l)return l;var d,y=(0,o.Z)("dyncfg",!0),b=t&&!1!==n?t:(0,c.Dy)(t),w=((a={uid:null,cfg:b})[s.Uw]=e,a[s.zs]=function(){d[s.zs]()},a.set=function(t,n,r){try{t=v(d,t,n,r)}catch(t){f(e,n,"Setting value",t)}return t[n]},a[s.h0]=function(e,t){return t&&(0,i.zav)(t,function(t,n){(0,u.q)(w,e,t,n)}),e},a[s.x6]=function(e){return function(e,t){var n={fn:t,rm:function(){n.fn=null,e=null,t=null}};return(0,i.vF1)(n,"toJSON",{v:function(){return"WatcherHandler"+(n.fn?"":"[X]")}}),e.use(n,t),n}(d,e)},a.ref=function(e,t){var n;return h(d,e,t,(n={},n[0]=!0,n))[t]},a[s.XW]=function(e,t){var n;return h(d,e,t,(n={},n[1]=!0,n))[t]},a[s.JQ]=function(e,t){var n;return h(d,e,t,(n={},n[2]=!0,n))[t]},a._block=function(e,t){d.use(null,function(n){var r=d.upd;try{(0,i.b07)(t)||(d.upd=t),e(n)}finally{d.upd=r}})},a);return(0,i.vF1)(w,"uid",{c:!1,e:!1,w:!1,v:y}),p(d=function(e){var t,n,o=(0,i.jjc)(g+"get"+e.uid+m),a=(0,i.jjc)(g+"ro"+e.uid+m),u=(0,i.jjc)(g+"rf"+e.uid+m),c=(0,i.jjc)(g+"blkVal"+e.uid+m),l=(0,i.jjc)(g+"dtl"+e.uid+m),f=null,d=null;function v(t,r){var o=n.act;try{n.act=t,t&&t[l]&&((0,i.Iuo)(t[l],function(e){e.clr(t)}),t[l]=[]),r({cfg:e.cfg,set:e.set.bind(e),setDf:e[s.h0].bind(e),ref:e.ref.bind(e),rdOnly:e[s.XW].bind(e)})}catch(t){var a=e[s.Uw];throw a&&a[s.ih](1,107,(0,i.mmD)(t)),t}finally{n.act=o||null}}function h(){if(f){var e=f;f=null,d&&d[s._w](),d=null;var t=[];if((0,i.Iuo)(e,function(e){if(e&&(e[l]&&((0,i.Iuo)(e[l],function(t){t.clr(e)}),e[l]=null),e.fn))try{v(e,e.fn)}catch(e){t[s.y5](e)}}),f)try{h()}catch(e){t[s.y5](e)}t[s.oI]>0&&function(e,t){r||(r=(0,i.aqQ)("AggregationError",function(e,t){t[s.oI]>1&&(e.errors=t[1])}));var n="Watcher error(s): ";throw(0,i.Iuo)(t,function(e,t){n+="\n".concat(t," > ").concat((0,i.mmD)(e))}),new r(n,t||[])}(0,t)}}return(t={prop:o,ro:a,rf:u})[s.JQ]=c,t[s.K0]=e,t.add=function(e){if(e&&e.h[s.oI]>0){f||(f=[]),d||(d=(0,i.dRz)(function(){d=null,h()},0));for(var t=0;t<e.h[s.oI];t++){var n=e.h[t];n&&-1===(0,i.rDm)(f,n)&&f[s.y5](n)}}},t[s.zs]=h,t.use=v,t.trk=function(e,t){if(e){var n=e[l]=e[l]||[];-1===(0,i.rDm)(n,t)&&n[s.y5](t)}},n=t}(w),b,"config","Creating"),w}function b(e,t,n,r){var i=y(n,e||{},r);return t&&i[s.h0](i.cfg,t),i}function w(e,t,n){var r=e[c.nM]||e;return!r.cfg||r.cfg!==e&&r.cfg[c.nM]!==r?(function(e,t){e?(e[s.on](t),e[s.ih](2,108,t)):(0,c.If)(t)}(n,a.xW+(0,i.mmD)(e)),b(e,null,n)[s.x6](t)):r[s.x6](t)}},9762:(e,t,n)=>{n.d(t,{O:()=>r});var r=(0,n(4282).H)({Verbose:0,Information:1,Warning:2,Error:3,Critical:4})},9882:(e,t,n)=>{n.d(t,{aq:()=>a,cL:()=>s});var r=n(269),i=n(6492),o=n(6535);function a(){var e=s();return(0,r.P0f)(e,0,8)+"-"+(0,r.P0f)(e,8,12)+"-"+(0,r.P0f)(e,12,16)+"-"+(0,r.P0f)(e,16,20)+"-"+(0,r.P0f)(e,20)}function s(){for(var e,t=["0","1","2","3","4","5","6","7","8","9","a","b","c","d","e","f"],n=i.m5,a=0;a<4;a++)n+=t[15&(e=(0,o.VN)())]+t[e>>4&15]+t[e>>8&15]+t[e>>12&15]+t[e>>16&15]+t[e>>20&15]+t[e>>24&15]+t[e>>28&15];var s=t[8+(3&(0,o.VN)())|0];return(0,r.hKY)(n,0,8)+(0,r.hKY)(n,9,4)+"4"+(0,r.hKY)(n,13,3)+s+(0,r.hKY)(n,16,3)+(0,r.hKY)(n,19,12)}},9896:e=>{e.exports=require("fs")}},t={};function n(r){var i=t[r];if(void 0!==i)return i.exports;var o=t[r]={exports:{}};return e[r].call(o.exports,o,o.exports,n),o.exports}n.d=(e,t)=>{for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),n.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var r=n(5256),i=exports;for(var o in r)i[o]=r[o];r.__esModule&&Object.defineProperty(i,"__esModule",{value:!0})})();
//# sourceMappingURL=https://main.vscode-cdn.net/sourcemaps/360a4e4fd251bfce169a4ddf857c7d25d1ad40da/extensions/github-authentication/dist/extension.js.map