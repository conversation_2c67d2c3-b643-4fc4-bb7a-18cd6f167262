{"name": "html-language-features", "displayName": "%displayName%", "description": "%description%", "version": "1.0.0", "publisher": "vscode", "license": "MIT", "aiKey": "0c6ae279ed8443289764825290e4f9e2-1a736e7c-1324-4338-be46-fc2a58ae4d14-7255", "engines": {"vscode": "^1.77.0"}, "icon": "icons/html.png", "activationEvents": ["onLanguage:html", "onLanguage:handlebars"], "enabledApiProposals": ["extensionsAny"], "main": "./client/dist/node/htmlClientMain", "browser": "./client/dist/browser/htmlClientMain", "capabilities": {"virtualWorkspaces": true, "untrustedWorkspaces": {"supported": true}}, "categories": ["Programming Languages"], "contributes": {"configuration": {"id": "html", "order": 20, "type": "object", "title": "HTML", "properties": {"html.completion.attributeDefaultValue": {"type": "string", "scope": "resource", "enum": ["doublequotes", "singlequotes", "empty"], "enumDescriptions": ["%html.completion.attributeDefaultValue.doublequotes%", "%html.completion.attributeDefaultValue.singlequotes%", "%html.completion.attributeDefaultValue.empty%"], "default": "doublequotes", "markdownDescription": "%html.completion.attributeDefaultValue%"}, "html.customData": {"type": "array", "markdownDescription": "%html.customData.desc%", "default": [], "items": {"type": "string"}, "scope": "resource"}, "html.format.enable": {"type": "boolean", "scope": "window", "default": true, "description": "%html.format.enable.desc%"}, "html.format.wrapLineLength": {"type": "integer", "scope": "resource", "default": 120, "description": "%html.format.wrapLineLength.desc%"}, "html.format.unformatted": {"type": ["string", "null"], "scope": "resource", "default": "wbr", "markdownDescription": "%html.format.unformatted.desc%"}, "html.format.contentUnformatted": {"type": ["string", "null"], "scope": "resource", "default": "pre,code,textarea", "markdownDescription": "%html.format.contentUnformatted.desc%"}, "html.format.indentInnerHtml": {"type": "boolean", "scope": "resource", "default": false, "markdownDescription": "%html.format.indentInnerHtml.desc%"}, "html.format.preserveNewLines": {"type": "boolean", "scope": "resource", "default": true, "description": "%html.format.preserveNewLines.desc%"}, "html.format.maxPreserveNewLines": {"type": ["number", "null"], "scope": "resource", "default": null, "markdownDescription": "%html.format.maxPreserveNewLines.desc%"}, "html.format.indentHandlebars": {"type": "boolean", "scope": "resource", "default": false, "markdownDescription": "%html.format.indentHandlebars.desc%"}, "html.format.extraLiners": {"type": ["string", "null"], "scope": "resource", "default": "head, body, /html", "markdownDescription": "%html.format.extraLiners.desc%"}, "html.format.wrapAttributes": {"type": "string", "scope": "resource", "default": "auto", "enum": ["auto", "force", "force-aligned", "force-expand-multiline", "aligned-multiple", "preserve", "preserve-aligned"], "enumDescriptions": ["%html.format.wrapAttributes.auto%", "%html.format.wrapAttributes.force%", "%html.format.wrapAttributes.forcealign%", "%html.format.wrapAttributes.forcemultiline%", "%html.format.wrapAttributes.alignedmultiple%", "%html.format.wrapAttributes.preserve%", "%html.format.wrapAttributes.preservealigned%"], "description": "%html.format.wrapAttributes.desc%"}, "html.format.wrapAttributesIndentSize": {"type": ["number", "null"], "scope": "resource", "default": null, "markdownDescription": "%html.format.wrapAttributesIndentSize.desc%"}, "html.format.templating": {"type": "boolean", "scope": "resource", "default": false, "description": "%html.format.templating.desc%"}, "html.format.unformattedContentDelimiter": {"type": "string", "scope": "resource", "default": "", "markdownDescription": "%html.format.unformattedContentDelimiter.desc%"}, "html.suggest.html5": {"type": "boolean", "scope": "resource", "default": true, "description": "%html.suggest.html5.desc%"}, "html.validate.scripts": {"type": "boolean", "scope": "resource", "default": true, "description": "%html.validate.scripts%"}, "html.validate.styles": {"type": "boolean", "scope": "resource", "default": true, "description": "%html.validate.styles%"}, "html.autoCreateQuotes": {"type": "boolean", "scope": "resource", "default": true, "markdownDescription": "%html.autoCreateQuotes%"}, "html.autoClosingTags": {"type": "boolean", "scope": "resource", "default": true, "description": "%html.autoClosingTags%"}, "html.hover.documentation": {"type": "boolean", "scope": "resource", "default": true, "description": "%html.hover.documentation%"}, "html.hover.references": {"type": "boolean", "scope": "resource", "default": true, "description": "%html.hover.references%"}, "html.mirrorCursorOnMatchingTag": {"type": "boolean", "scope": "resource", "default": false, "description": "%html.mirrorCursorOnMatchingTag%", "deprecationMessage": "%html.mirrorCursorOnMatchingTagDeprecationMessage%"}, "html.trace.server": {"type": "string", "scope": "window", "enum": ["off", "messages", "verbose"], "default": "off", "description": "%html.trace.server.desc%"}}}, "configurationDefaults": {"[html]": {"editor.suggest.insertMode": "replace"}, "[handlebars]": {"editor.suggest.insertMode": "replace"}}, "jsonValidation": [{"fileMatch": "*.html-data.json", "url": "https://raw.githubusercontent.com/microsoft/vscode-html-languageservice/master/docs/customData.schema.json"}, {"fileMatch": "package.json", "url": "./schemas/package.schema.json"}]}, "repository": {"type": "git", "url": "https://github.com/microsoft/vscode.git"}}