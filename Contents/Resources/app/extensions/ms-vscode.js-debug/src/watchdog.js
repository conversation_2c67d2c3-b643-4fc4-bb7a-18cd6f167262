"use strict";(()=>{var Kf=Object.create;var Qr=Object.defineProperty;var Qo=Object.getOwnPropertyDescriptor;var Yf=Object.getOwnPropertyNames;var zf=Object.getPrototypeOf,Jf=Object.prototype.hasOwnProperty;var I=(t=>typeof require<"u"?require:typeof Proxy<"u"?new Proxy(t,{get:(e,n)=>(typeof require<"u"?require:e)[n]}):t)(function(t){if(typeof require<"u")return require.apply(this,arguments);throw Error('Dynamic require of "'+t+'" is not supported')});var g=(t,e)=>()=>(e||t((e={exports:{}}).exports,e),e.exports);var Xf=(t,e,n,r)=>{if(e&&typeof e=="object"||typeof e=="function")for(let i of Yf(e))!Jf.call(t,i)&&i!==n&&Qr(t,i,{get:()=>e[i],enumerable:!(r=Qo(e,i))||r.enumerable});return t};var Z=(t,e,n)=>(n=t!=null?Kf(zf(t)):{},Xf(e||!t||!t.__esModule?Qr(n,"default",{value:t,enumerable:!0}):n,t));var Sn=(t,e,n,r)=>{for(var i=r>1?void 0:r?Qo(e,n):e,o=t.length-1,s;o>=0;o--)(s=t[o])&&(i=(r?s(e,n,i):s(i))||i);return r&&i&&Qr(e,n,i),i};var es=g(()=>{var Zo;(function(t){(function(e){var n=typeof globalThis=="object"?globalThis:typeof global=="object"?global:typeof self=="object"?self:typeof this=="object"?this:u(),r=i(t);typeof n.Reflect<"u"&&(r=i(n.Reflect,r)),e(r,n),typeof n.Reflect>"u"&&(n.Reflect=t);function i(c,a){return function(l,p){Object.defineProperty(c,l,{configurable:!0,writable:!0,value:p}),a&&a(l,p)}}function o(){try{return Function("return this;")()}catch{}}function s(){try{return(0,eval)("(function() { return this; })()")}catch{}}function u(){return o()||s()}})(function(e,n){var r=Object.prototype.hasOwnProperty,i=typeof Symbol=="function",o=i&&typeof Symbol.toPrimitive<"u"?Symbol.toPrimitive:"@@toPrimitive",s=i&&typeof Symbol.iterator<"u"?Symbol.iterator:"@@iterator",u=typeof Object.create=="function",c={__proto__:[]}instanceof Array,a=!u&&!c,l={create:u?function(){return Xr(Object.create(null))}:c?function(){return Xr({__proto__:null})}:function(){return Xr({})},has:a?function(f,d){return r.call(f,d)}:function(f,d){return d in f},get:a?function(f,d){return r.call(f,d)?f[d]:void 0}:function(f,d){return f[d]}},p=Object.getPrototypeOf(Function),m=typeof Map=="function"&&typeof Map.prototype.entries=="function"?Map:Gf(),b=typeof Set=="function"&&typeof Set.prototype.entries=="function"?Set:Wf(),T=typeof WeakMap=="function"?WeakMap:Vf(),w=i?Symbol.for("@reflect-metadata:registry"):void 0,R=Ff(),K=Uf(R);function ue(f,d,h,y){if(O(h)){if(!Vo(f))throw new TypeError;if(!$o(d))throw new TypeError;return Pf(f,d)}else{if(!Vo(f))throw new TypeError;if(!k(d))throw new TypeError;if(!k(y)&&!O(y)&&!mt(y))throw new TypeError;return mt(y)&&(y=void 0),h=we(h),Rf(f,d,h,y)}}e("decorate",ue);function gt(f,d){function h(y,C){if(!k(y))throw new TypeError;if(!O(C)&&!Lf(C))throw new TypeError;Fo(f,d,y,C)}return h}e("metadata",gt);function ge(f,d,h,y){if(!k(h))throw new TypeError;return O(y)||(y=we(y)),Fo(f,d,h,y)}e("defineMetadata",ge);function be(f,d,h){if(!k(d))throw new TypeError;return O(h)||(h=we(h)),Bo(f,d,h)}e("hasMetadata",be);function et(f,d,h){if(!k(d))throw new TypeError;return O(h)||(h=we(h)),Yr(f,d,h)}e("hasOwnMetadata",et);function Ye(f,d,h){if(!k(d))throw new TypeError;return O(h)||(h=we(h)),Lo(f,d,h)}e("getMetadata",Ye);function wn(f,d,h){if(!k(d))throw new TypeError;return O(h)||(h=we(h)),jo(f,d,h)}e("getOwnMetadata",wn);function Ut(f,d){if(!k(f))throw new TypeError;return O(d)||(d=we(d)),Uo(f,d)}e("getMetadataKeys",Ut);function Kr(f,d){if(!k(f))throw new TypeError;return O(d)||(d=we(d)),qo(f,d)}e("getOwnMetadataKeys",Kr);function Nf(f,d,h){if(!k(d))throw new TypeError;if(O(h)||(h=we(h)),!k(d))throw new TypeError;O(h)||(h=we(h));var y=qt(d,h,!1);return O(y)?!1:y.OrdinaryDeleteMetadata(f,d,h)}e("deleteMetadata",Nf);function Pf(f,d){for(var h=f.length-1;h>=0;--h){var y=f[h],C=y(d);if(!O(C)&&!mt(C)){if(!$o(C))throw new TypeError;d=C}}return d}function Rf(f,d,h,y){for(var C=f.length-1;C>=0;--C){var B=f[C],G=B(d,h,y);if(!O(G)&&!mt(G)){if(!k(G))throw new TypeError;y=G}}return y}function Bo(f,d,h){var y=Yr(f,d,h);if(y)return!0;var C=Jr(d);return mt(C)?!1:Bo(f,C,h)}function Yr(f,d,h){var y=qt(d,h,!1);return O(y)?!1:Wo(y.OrdinaryHasOwnMetadata(f,d,h))}function Lo(f,d,h){var y=Yr(f,d,h);if(y)return jo(f,d,h);var C=Jr(d);if(!mt(C))return Lo(f,C,h)}function jo(f,d,h){var y=qt(d,h,!1);if(!O(y))return y.OrdinaryGetOwnMetadata(f,d,h)}function Fo(f,d,h,y){var C=qt(h,y,!0);C.OrdinaryDefineOwnMetadata(f,d,h,y)}function Uo(f,d){var h=qo(f,d),y=Jr(f);if(y===null)return h;var C=Uo(y,d);if(C.length<=0)return h;if(h.length<=0)return C;for(var B=new b,G=[],N=0,_=h;N<_.length;N++){var x=_[N],E=B.has(x);E||(B.add(x),G.push(x))}for(var A=0,P=C;A<P.length;A++){var x=P[A],E=B.has(x);E||(B.add(x),G.push(x))}return G}function qo(f,d){var h=qt(f,d,!1);return h?h.OrdinaryOwnMetadataKeys(f,d):[]}function Go(f){if(f===null)return 1;switch(typeof f){case"undefined":return 0;case"boolean":return 2;case"string":return 3;case"symbol":return 4;case"number":return 5;case"object":return f===null?1:6;default:return 6}}function O(f){return f===void 0}function mt(f){return f===null}function Mf(f){return typeof f=="symbol"}function k(f){return typeof f=="object"?f!==null:typeof f=="function"}function Df(f,d){switch(Go(f)){case 0:return f;case 1:return f;case 2:return f;case 3:return f;case 4:return f;case 5:return f}var h=d===3?"string":d===5?"number":"default",y=Ho(f,o);if(y!==void 0){var C=y.call(f,h);if(k(C))throw new TypeError;return C}return kf(f,h==="default"?"number":h)}function kf(f,d){if(d==="string"){var h=f.toString;if(yt(h)){var y=h.call(f);if(!k(y))return y}var C=f.valueOf;if(yt(C)){var y=C.call(f);if(!k(y))return y}}else{var C=f.valueOf;if(yt(C)){var y=C.call(f);if(!k(y))return y}var B=f.toString;if(yt(B)){var y=B.call(f);if(!k(y))return y}}throw new TypeError}function Wo(f){return!!f}function Bf(f){return""+f}function we(f){var d=Df(f,3);return Mf(d)?d:Bf(d)}function Vo(f){return Array.isArray?Array.isArray(f):f instanceof Object?f instanceof Array:Object.prototype.toString.call(f)==="[object Array]"}function yt(f){return typeof f=="function"}function $o(f){return typeof f=="function"}function Lf(f){switch(Go(f)){case 3:return!0;case 4:return!0;default:return!1}}function zr(f,d){return f===d||f!==f&&d!==d}function Ho(f,d){var h=f[d];if(h!=null){if(!yt(h))throw new TypeError;return h}}function Ko(f){var d=Ho(f,s);if(!yt(d))throw new TypeError;var h=d.call(f);if(!k(h))throw new TypeError;return h}function Yo(f){return f.value}function zo(f){var d=f.next();return d.done?!1:d}function Jo(f){var d=f.return;d&&d.call(f)}function Jr(f){var d=Object.getPrototypeOf(f);if(typeof f!="function"||f===p||d!==p)return d;var h=f.prototype,y=h&&Object.getPrototypeOf(h);if(y==null||y===Object.prototype)return d;var C=y.constructor;return typeof C!="function"||C===f?d:C}function jf(){var f;!O(w)&&typeof n.Reflect<"u"&&!(w in n.Reflect)&&typeof n.Reflect.defineMetadata=="function"&&(f=qf(n.Reflect));var d,h,y,C=new T,B={registerProvider:G,getProvider:_,setProvider:E};return B;function G(A){if(!Object.isExtensible(B))throw new Error("Cannot add provider to a frozen registry.");switch(!0){case f===A:break;case O(d):d=A;break;case d===A:break;case O(h):h=A;break;case h===A:break;default:y===void 0&&(y=new b),y.add(A);break}}function N(A,P){if(!O(d)){if(d.isProviderFor(A,P))return d;if(!O(h)){if(h.isProviderFor(A,P))return d;if(!O(y))for(var D=Ko(y);;){var L=zo(D);if(!L)return;var me=Yo(L);if(me.isProviderFor(A,P))return Jo(D),me}}}if(!O(f)&&f.isProviderFor(A,P))return f}function _(A,P){var D=C.get(A),L;return O(D)||(L=D.get(P)),O(L)&&(L=N(A,P),O(L)||(O(D)&&(D=new m,C.set(A,D)),D.set(P,L))),L}function x(A){if(O(A))throw new TypeError;return d===A||h===A||!O(y)&&y.has(A)}function E(A,P,D){if(!x(D))throw new Error("Metadata provider not registered.");var L=_(A,P);if(L!==D){if(!O(L))return!1;var me=C.get(A);O(me)&&(me=new m,C.set(A,me)),me.set(P,D)}return!0}}function Ff(){var f;return!O(w)&&k(n.Reflect)&&Object.isExtensible(n.Reflect)&&(f=n.Reflect[w]),O(f)&&(f=jf()),!O(w)&&k(n.Reflect)&&Object.isExtensible(n.Reflect)&&Object.defineProperty(n.Reflect,w,{enumerable:!1,configurable:!1,writable:!1,value:f}),f}function Uf(f){var d=new T,h={isProviderFor:function(x,E){var A=d.get(x);return O(A)?!1:A.has(E)},OrdinaryDefineOwnMetadata:G,OrdinaryHasOwnMetadata:C,OrdinaryGetOwnMetadata:B,OrdinaryOwnMetadataKeys:N,OrdinaryDeleteMetadata:_};return R.registerProvider(h),h;function y(x,E,A){var P=d.get(x),D=!1;if(O(P)){if(!A)return;P=new m,d.set(x,P),D=!0}var L=P.get(E);if(O(L)){if(!A)return;if(L=new m,P.set(E,L),!f.setProvider(x,E,h))throw P.delete(E),D&&d.delete(x),new Error("Wrong provider for target.")}return L}function C(x,E,A){var P=y(E,A,!1);return O(P)?!1:Wo(P.has(x))}function B(x,E,A){var P=y(E,A,!1);if(!O(P))return P.get(x)}function G(x,E,A,P){var D=y(A,P,!0);D.set(x,E)}function N(x,E){var A=[],P=y(x,E,!1);if(O(P))return A;for(var D=P.keys(),L=Ko(D),me=0;;){var Xo=zo(L);if(!Xo)return A.length=me,A;var $f=Yo(Xo);try{A[me]=$f}catch(Hf){try{Jo(L)}finally{throw Hf}}me++}}function _(x,E,A){var P=y(E,A,!1);if(O(P)||!P.delete(x))return!1;if(P.size===0){var D=d.get(E);O(D)||(D.delete(A),D.size===0&&d.delete(D))}return!0}}function qf(f){var d=f.defineMetadata,h=f.hasOwnMetadata,y=f.getOwnMetadata,C=f.getOwnMetadataKeys,B=f.deleteMetadata,G=new T,N={isProviderFor:function(_,x){var E=G.get(_);return O(E)?C(_,x).length?(O(E)&&(E=new b,G.set(_,E)),E.add(x),!0):!1:E.has(x)},OrdinaryDefineOwnMetadata:d,OrdinaryHasOwnMetadata:h,OrdinaryGetOwnMetadata:y,OrdinaryOwnMetadataKeys:C,OrdinaryDeleteMetadata:B};return N}function qt(f,d,h){var y=R.getProvider(f,d);if(!O(y))return y;if(h){if(R.setProvider(f,d,K))return K;throw new Error("Illegal state.")}}function Gf(){var f={},d=[],h=function(){function N(_,x,E){this._index=0,this._keys=_,this._values=x,this._selector=E}return N.prototype["@@iterator"]=function(){return this},N.prototype[s]=function(){return this},N.prototype.next=function(){var _=this._index;if(_>=0&&_<this._keys.length){var x=this._selector(this._keys[_],this._values[_]);return _+1>=this._keys.length?(this._index=-1,this._keys=d,this._values=d):this._index++,{value:x,done:!1}}return{value:void 0,done:!0}},N.prototype.throw=function(_){throw this._index>=0&&(this._index=-1,this._keys=d,this._values=d),_},N.prototype.return=function(_){return this._index>=0&&(this._index=-1,this._keys=d,this._values=d),{value:_,done:!0}},N}(),y=function(){function N(){this._keys=[],this._values=[],this._cacheKey=f,this._cacheIndex=-2}return Object.defineProperty(N.prototype,"size",{get:function(){return this._keys.length},enumerable:!0,configurable:!0}),N.prototype.has=function(_){return this._find(_,!1)>=0},N.prototype.get=function(_){var x=this._find(_,!1);return x>=0?this._values[x]:void 0},N.prototype.set=function(_,x){var E=this._find(_,!0);return this._values[E]=x,this},N.prototype.delete=function(_){var x=this._find(_,!1);if(x>=0){for(var E=this._keys.length,A=x+1;A<E;A++)this._keys[A-1]=this._keys[A],this._values[A-1]=this._values[A];return this._keys.length--,this._values.length--,zr(_,this._cacheKey)&&(this._cacheKey=f,this._cacheIndex=-2),!0}return!1},N.prototype.clear=function(){this._keys.length=0,this._values.length=0,this._cacheKey=f,this._cacheIndex=-2},N.prototype.keys=function(){return new h(this._keys,this._values,C)},N.prototype.values=function(){return new h(this._keys,this._values,B)},N.prototype.entries=function(){return new h(this._keys,this._values,G)},N.prototype["@@iterator"]=function(){return this.entries()},N.prototype[s]=function(){return this.entries()},N.prototype._find=function(_,x){if(!zr(this._cacheKey,_)){this._cacheIndex=-1;for(var E=0;E<this._keys.length;E++)if(zr(this._keys[E],_)){this._cacheIndex=E;break}}return this._cacheIndex<0&&x&&(this._cacheIndex=this._keys.length,this._keys.push(_),this._values.push(void 0)),this._cacheIndex},N}();return y;function C(N,_){return N}function B(N,_){return _}function G(N,_){return[N,_]}}function Wf(){var f=function(){function d(){this._map=new m}return Object.defineProperty(d.prototype,"size",{get:function(){return this._map.size},enumerable:!0,configurable:!0}),d.prototype.has=function(h){return this._map.has(h)},d.prototype.add=function(h){return this._map.set(h,h),this},d.prototype.delete=function(h){return this._map.delete(h)},d.prototype.clear=function(){this._map.clear()},d.prototype.keys=function(){return this._map.keys()},d.prototype.values=function(){return this._map.keys()},d.prototype.entries=function(){return this._map.entries()},d.prototype["@@iterator"]=function(){return this.keys()},d.prototype[s]=function(){return this.keys()},d}();return f}function Vf(){var f=16,d=l.create(),h=y();return function(){function _(){this._key=y()}return _.prototype.has=function(x){var E=C(x,!1);return E!==void 0?l.has(E,this._key):!1},_.prototype.get=function(x){var E=C(x,!1);return E!==void 0?l.get(E,this._key):void 0},_.prototype.set=function(x,E){var A=C(x,!0);return A[this._key]=E,this},_.prototype.delete=function(x){var E=C(x,!1);return E!==void 0?delete E[this._key]:!1},_.prototype.clear=function(){this._key=y()},_}();function y(){var _;do _="@@WeakMap@@"+N();while(l.has(d,_));return d[_]=!0,_}function C(_,x){if(!r.call(_,h)){if(!x)return;Object.defineProperty(_,h,{value:l.create()})}return _[h]}function B(_,x){for(var E=0;E<x;++E)_[E]=Math.random()*255|0;return _}function G(_){return typeof Uint8Array=="function"?typeof crypto<"u"?crypto.getRandomValues(new Uint8Array(_)):typeof msCrypto<"u"?msCrypto.getRandomValues(new Uint8Array(_)):B(new Uint8Array(_),_):B(new Array(_),_)}function N(){var _=G(f);_[6]=_[6]&79|64,_[8]=_[8]&191|128;for(var x="",E=0;E<f;++E){var A=_[E];(E===4||E===6||E===8)&&(x+="-"),A<16&&(x+="0"),x+=A.toString(16).toLowerCase()}return x}}function Xr(f){return f.__=void 0,delete f.__,f}})})(Zo||(Zo={}))});var U=g(M=>{"use strict";Object.defineProperty(M,"__esModule",{value:!0});M.NON_CUSTOM_TAG_KEYS=M.PRE_DESTROY=M.POST_CONSTRUCT=M.DESIGN_PARAM_TYPES=M.PARAM_TYPES=M.TAGGED_PROP=M.TAGGED=M.MULTI_INJECT_TAG=M.INJECT_TAG=M.OPTIONAL_TAG=M.UNMANAGED_TAG=M.NAME_TAG=M.NAMED_TAG=void 0;M.NAMED_TAG="named";M.NAME_TAG="name";M.UNMANAGED_TAG="unmanaged";M.OPTIONAL_TAG="optional";M.INJECT_TAG="inject";M.MULTI_INJECT_TAG="multi_inject";M.TAGGED="inversify:tagged";M.TAGGED_PROP="inversify:tagged_props";M.PARAM_TYPES="inversify:paramtypes";M.DESIGN_PARAM_TYPES="design:paramtypes";M.POST_CONSTRUCT="post_construct";M.PRE_DESTROY="pre_destroy";function Zf(){return[M.INJECT_TAG,M.MULTI_INJECT_TAG,M.NAME_TAG,M.UNMANAGED_TAG,M.NAMED_TAG,M.OPTIONAL_TAG]}M.NON_CUSTOM_TAG_KEYS=Zf()});var ce=g(ze=>{"use strict";Object.defineProperty(ze,"__esModule",{value:!0});ze.TargetTypeEnum=ze.BindingTypeEnum=ze.BindingScopeEnum=void 0;var ed={Request:"Request",Singleton:"Singleton",Transient:"Transient"};ze.BindingScopeEnum=ed;var td={ConstantValue:"ConstantValue",Constructor:"Constructor",DynamicValue:"DynamicValue",Factory:"Factory",Function:"Function",Instance:"Instance",Invalid:"Invalid",Provider:"Provider"};ze.BindingTypeEnum=td;var nd={ClassProperty:"ClassProperty",ConstructorArgument:"ConstructorArgument",Variable:"Variable"};ze.TargetTypeEnum=nd});var Je=g(Tn=>{"use strict";Object.defineProperty(Tn,"__esModule",{value:!0});Tn.id=void 0;var rd=0;function id(){return rd++}Tn.id=id});var ns=g(En=>{"use strict";Object.defineProperty(En,"__esModule",{value:!0});En.Binding=void 0;var ts=ce(),od=Je(),sd=function(){function t(e,n){this.id=(0,od.id)(),this.activated=!1,this.serviceIdentifier=e,this.scope=n,this.type=ts.BindingTypeEnum.Invalid,this.constraint=function(r){return!0},this.implementationType=null,this.cache=null,this.factory=null,this.provider=null,this.onActivation=null,this.onDeactivation=null,this.dynamicValue=null}return t.prototype.clone=function(){var e=new t(this.serviceIdentifier,this.scope);return e.activated=e.scope===ts.BindingScopeEnum.Singleton?this.activated:!1,e.implementationType=this.implementationType,e.dynamicValue=this.dynamicValue,e.scope=this.scope,e.type=this.type,e.factory=this.factory,e.provider=this.provider,e.constraint=this.constraint,e.onActivation=this.onActivation,e.onDeactivation=this.onDeactivation,e.cache=this.cache,e},t}();En.Binding=sd});var Y=g(S=>{"use strict";Object.defineProperty(S,"__esModule",{value:!0});S.STACK_OVERFLOW=S.CIRCULAR_DEPENDENCY_IN_FACTORY=S.ON_DEACTIVATION_ERROR=S.PRE_DESTROY_ERROR=S.POST_CONSTRUCT_ERROR=S.ASYNC_UNBIND_REQUIRED=S.MULTIPLE_POST_CONSTRUCT_METHODS=S.MULTIPLE_PRE_DESTROY_METHODS=S.CONTAINER_OPTIONS_INVALID_SKIP_BASE_CHECK=S.CONTAINER_OPTIONS_INVALID_AUTO_BIND_INJECTABLE=S.CONTAINER_OPTIONS_INVALID_DEFAULT_SCOPE=S.CONTAINER_OPTIONS_MUST_BE_AN_OBJECT=S.ARGUMENTS_LENGTH_MISMATCH=S.INVALID_DECORATOR_OPERATION=S.INVALID_TO_SELF_VALUE=S.LAZY_IN_SYNC=S.INVALID_FUNCTION_BINDING=S.INVALID_MIDDLEWARE_RETURN=S.NO_MORE_SNAPSHOTS_AVAILABLE=S.INVALID_BINDING_TYPE=S.NOT_IMPLEMENTED=S.CIRCULAR_DEPENDENCY=S.UNDEFINED_INJECT_ANNOTATION=S.MISSING_INJECT_ANNOTATION=S.MISSING_INJECTABLE_ANNOTATION=S.NOT_REGISTERED=S.CANNOT_UNBIND=S.AMBIGUOUS_MATCH=S.KEY_NOT_FOUND=S.NULL_ARGUMENT=S.DUPLICATED_METADATA=S.DUPLICATED_INJECTABLE_DECORATOR=void 0;S.DUPLICATED_INJECTABLE_DECORATOR="Cannot apply @injectable decorator multiple times.";S.DUPLICATED_METADATA="Metadata key was used more than once in a parameter:";S.NULL_ARGUMENT="NULL argument";S.KEY_NOT_FOUND="Key Not Found";S.AMBIGUOUS_MATCH="Ambiguous match found for serviceIdentifier:";S.CANNOT_UNBIND="Could not unbind serviceIdentifier:";S.NOT_REGISTERED="No matching bindings found for serviceIdentifier:";S.MISSING_INJECTABLE_ANNOTATION="Missing required @injectable annotation in:";S.MISSING_INJECT_ANNOTATION="Missing required @inject or @multiInject annotation in:";var ad=function(t){return"@inject called with undefined this could mean that the class "+t+" has a circular dependency problem. You can use a LazyServiceIdentifier to  overcome this limitation."};S.UNDEFINED_INJECT_ANNOTATION=ad;S.CIRCULAR_DEPENDENCY="Circular dependency found:";S.NOT_IMPLEMENTED="Sorry, this feature is not fully implemented yet.";S.INVALID_BINDING_TYPE="Invalid binding type:";S.NO_MORE_SNAPSHOTS_AVAILABLE="No snapshot available to restore.";S.INVALID_MIDDLEWARE_RETURN="Invalid return type in middleware. Middleware must return!";S.INVALID_FUNCTION_BINDING="Value provided to function binding must be a function!";var ud=function(t){return"You are attempting to construct '"+t+`' in a synchronous way
 but it has asynchronous dependencies.`};S.LAZY_IN_SYNC=ud;S.INVALID_TO_SELF_VALUE="The toSelf function can only be applied when a constructor is used as service identifier";S.INVALID_DECORATOR_OPERATION="The @inject @multiInject @tagged and @named decorators must be applied to the parameters of a class constructor or a class property.";var cd=function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];return"The number of constructor arguments in the derived class "+(t[0]+" must be >= than the number of constructor arguments of its base class.")};S.ARGUMENTS_LENGTH_MISMATCH=cd;S.CONTAINER_OPTIONS_MUST_BE_AN_OBJECT="Invalid Container constructor argument. Container options must be an object.";S.CONTAINER_OPTIONS_INVALID_DEFAULT_SCOPE='Invalid Container option. Default scope must be a string ("singleton" or "transient").';S.CONTAINER_OPTIONS_INVALID_AUTO_BIND_INJECTABLE="Invalid Container option. Auto bind injectable must be a boolean";S.CONTAINER_OPTIONS_INVALID_SKIP_BASE_CHECK="Invalid Container option. Skip base check must be a boolean";S.MULTIPLE_PRE_DESTROY_METHODS="Cannot apply @preDestroy decorator multiple times in the same class";S.MULTIPLE_POST_CONSTRUCT_METHODS="Cannot apply @postConstruct decorator multiple times in the same class";S.ASYNC_UNBIND_REQUIRED="Attempting to unbind dependency with asynchronous destruction (@preDestroy or onDeactivation)";var ld=function(t,e){return"@postConstruct error in class "+t+": "+e};S.POST_CONSTRUCT_ERROR=ld;var fd=function(t,e){return"@preDestroy error in class "+t+": "+e};S.PRE_DESTROY_ERROR=fd;var dd=function(t,e){return"onDeactivation() error in class "+t+": "+e};S.ON_DEACTIVATION_ERROR=dd;var pd=function(t,e){return"It looks like there is a circular dependency in one of the '"+t+"' bindings. Please investigate bindings with "+("service identifier '"+e+"'.")};S.CIRCULAR_DEPENDENCY_IN_FACTORY=pd;S.STACK_OVERFLOW="Maximum call stack size exceeded"});var ei=g(Se=>{"use strict";var hd=Se&&Se.__createBinding||(Object.create?function(t,e,n,r){r===void 0&&(r=n),Object.defineProperty(t,r,{enumerable:!0,get:function(){return e[n]}})}:function(t,e,n,r){r===void 0&&(r=n),t[r]=e[n]}),gd=Se&&Se.__setModuleDefault||(Object.create?function(t,e){Object.defineProperty(t,"default",{enumerable:!0,value:e})}:function(t,e){t.default=e}),md=Se&&Se.__importStar||function(t){if(t&&t.__esModule)return t;var e={};if(t!=null)for(var n in t)n!=="default"&&Object.prototype.hasOwnProperty.call(t,n)&&hd(e,t,n);return gd(e,t),e};Object.defineProperty(Se,"__esModule",{value:!0});Se.MetadataReader=void 0;var Zr=md(U()),yd=function(){function t(){}return t.prototype.getConstructorMetadata=function(e){var n=Reflect.getMetadata(Zr.PARAM_TYPES,e),r=Reflect.getMetadata(Zr.TAGGED,e);return{compilerGeneratedMetadata:n,userGeneratedMetadata:r||{}}},t.prototype.getPropertiesMetadata=function(e){var n=Reflect.getMetadata(Zr.TAGGED_PROP,e)||[];return n},t}();Se.MetadataReader=yd});var rs=g(xn=>{"use strict";Object.defineProperty(xn,"__esModule",{value:!0});xn.BindingCount=void 0;xn.BindingCount={MultipleBindingsAvailable:2,NoBindingsAvailable:0,OnlyOneBindingAvailable:1}});var ti=g(le=>{"use strict";var _d=le&&le.__createBinding||(Object.create?function(t,e,n,r){r===void 0&&(r=n),Object.defineProperty(t,r,{enumerable:!0,get:function(){return e[n]}})}:function(t,e,n,r){r===void 0&&(r=n),t[r]=e[n]}),vd=le&&le.__setModuleDefault||(Object.create?function(t,e){Object.defineProperty(t,"default",{enumerable:!0,value:e})}:function(t,e){t.default=e}),bd=le&&le.__importStar||function(t){if(t&&t.__esModule)return t;var e={};if(t!=null)for(var n in t)n!=="default"&&Object.prototype.hasOwnProperty.call(t,n)&&_d(e,t,n);return vd(e,t),e};Object.defineProperty(le,"__esModule",{value:!0});le.tryAndThrowErrorIfStackOverflow=le.isStackOverflowExeption=void 0;var wd=bd(Y());function is(t){return t instanceof RangeError||t.message===wd.STACK_OVERFLOW}le.isStackOverflowExeption=is;var Sd=function(t,e){try{return t()}catch(n){throw is(n)&&(n=e()),n}};le.tryAndThrowErrorIfStackOverflow=Sd});var tt=g(q=>{"use strict";var Td=q&&q.__createBinding||(Object.create?function(t,e,n,r){r===void 0&&(r=n),Object.defineProperty(t,r,{enumerable:!0,get:function(){return e[n]}})}:function(t,e,n,r){r===void 0&&(r=n),t[r]=e[n]}),Ed=q&&q.__setModuleDefault||(Object.create?function(t,e){Object.defineProperty(t,"default",{enumerable:!0,value:e})}:function(t,e){t.default=e}),xd=q&&q.__importStar||function(t){if(t&&t.__esModule)return t;var e={};if(t!=null)for(var n in t)n!=="default"&&Object.prototype.hasOwnProperty.call(t,n)&&Td(e,t,n);return Ed(e,t),e};Object.defineProperty(q,"__esModule",{value:!0});q.getSymbolDescription=q.circularDependencyToException=q.listMetadataForTarget=q.listRegisteredBindingsForServiceIdentifier=q.getServiceIdentifierAsString=q.getFunctionName=void 0;var Ad=xd(Y());function os(t){if(typeof t=="function"){var e=t;return e.name}else{if(typeof t=="symbol")return t.toString();var e=t;return e}}q.getServiceIdentifierAsString=os;function Cd(t,e,n){var r="",i=n(t,e);return i.length!==0&&(r=`
Registered bindings:`,i.forEach(function(o){var s="Object";o.implementationType!==null&&(s=us(o.implementationType)),r=r+`
 `+s,o.constraint.metaData&&(r=r+" - "+o.constraint.metaData)})),r}q.listRegisteredBindingsForServiceIdentifier=Cd;function ss(t,e){return t.parentRequest===null?!1:t.parentRequest.serviceIdentifier===e?!0:ss(t.parentRequest,e)}function Id(t){function e(r,i){i===void 0&&(i=[]);var o=os(r.serviceIdentifier);return i.push(o),r.parentRequest!==null?e(r.parentRequest,i):i}var n=e(t);return n.reverse().join(" --> ")}function as(t){t.childRequests.forEach(function(e){if(ss(e,e.serviceIdentifier)){var n=Id(e);throw new Error(Ad.CIRCULAR_DEPENDENCY+" "+n)}else as(e)})}q.circularDependencyToException=as;function Od(t,e){if(e.isTagged()||e.isNamed()){var n="",r=e.getNamedTag(),i=e.getCustomTags();return r!==null&&(n+=r.toString()+`
`),i!==null&&i.forEach(function(o){n+=o.toString()+`
`})," "+t+`
 `+t+" - "+n}else return" "+t}q.listMetadataForTarget=Od;function us(t){if(t.name)return t.name;var e=t.toString(),n=e.match(/^function\s*([^\s(]+)/);return n?n[1]:"Anonymous function: "+e}q.getFunctionName=us;function Nd(t){return t.toString().slice(7,-1)}q.getSymbolDescription=Nd});var cs=g(An=>{"use strict";Object.defineProperty(An,"__esModule",{value:!0});An.Context=void 0;var Pd=Je(),Rd=function(){function t(e){this.id=(0,Pd.id)(),this.container=e}return t.prototype.addPlan=function(e){this.plan=e},t.prototype.setCurrentRequest=function(e){this.currentRequest=e},t}();An.Context=Rd});var ye=g(Te=>{"use strict";var Md=Te&&Te.__createBinding||(Object.create?function(t,e,n,r){r===void 0&&(r=n),Object.defineProperty(t,r,{enumerable:!0,get:function(){return e[n]}})}:function(t,e,n,r){r===void 0&&(r=n),t[r]=e[n]}),Dd=Te&&Te.__setModuleDefault||(Object.create?function(t,e){Object.defineProperty(t,"default",{enumerable:!0,value:e})}:function(t,e){t.default=e}),kd=Te&&Te.__importStar||function(t){if(t&&t.__esModule)return t;var e={};if(t!=null)for(var n in t)n!=="default"&&Object.prototype.hasOwnProperty.call(t,n)&&Md(e,t,n);return Dd(e,t),e};Object.defineProperty(Te,"__esModule",{value:!0});Te.Metadata=void 0;var Bd=kd(U()),Ld=function(){function t(e,n){this.key=e,this.value=n}return t.prototype.toString=function(){return this.key===Bd.NAMED_TAG?"named: "+String(this.value).toString()+" ":"tagged: { key:"+this.key.toString()+", value: "+String(this.value)+" }"},t}();Te.Metadata=Ld});var ls=g(Cn=>{"use strict";Object.defineProperty(Cn,"__esModule",{value:!0});Cn.Plan=void 0;var jd=function(){function t(e,n){this.parentContext=e,this.rootRequest=n}return t}();Cn.Plan=jd});var On=g(In=>{"use strict";Object.defineProperty(In,"__esModule",{value:!0});In.LazyServiceIdentifier=void 0;var Fd=function(){function t(e){this._cb=e}return t.prototype.unwrap=function(){return this._cb()},t}();In.LazyServiceIdentifier=Fd});var fs=g(Nn=>{"use strict";Object.defineProperty(Nn,"__esModule",{value:!0});Nn.QueryableString=void 0;var Ud=function(){function t(e){this.str=e}return t.prototype.startsWith=function(e){return this.str.indexOf(e)===0},t.prototype.endsWith=function(e){var n="",r=e.split("").reverse().join("");return n=this.str.split("").reverse().join(""),this.startsWith.call({str:n},r)},t.prototype.contains=function(e){return this.str.indexOf(e)!==-1},t.prototype.equals=function(e){return this.str===e},t.prototype.value=function(){return this.str},t}();Nn.QueryableString=Ud});var ni=g(Ee=>{"use strict";var qd=Ee&&Ee.__createBinding||(Object.create?function(t,e,n,r){r===void 0&&(r=n),Object.defineProperty(t,r,{enumerable:!0,get:function(){return e[n]}})}:function(t,e,n,r){r===void 0&&(r=n),t[r]=e[n]}),Gd=Ee&&Ee.__setModuleDefault||(Object.create?function(t,e){Object.defineProperty(t,"default",{enumerable:!0,value:e})}:function(t,e){t.default=e}),Wd=Ee&&Ee.__importStar||function(t){if(t&&t.__esModule)return t;var e={};if(t!=null)for(var n in t)n!=="default"&&Object.prototype.hasOwnProperty.call(t,n)&&qd(e,t,n);return Gd(e,t),e};Object.defineProperty(Ee,"__esModule",{value:!0});Ee.Target=void 0;var Ge=Wd(U()),Vd=Je(),$d=tt(),ds=ye(),Hd=fs(),Kd=function(){function t(e,n,r,i){this.id=(0,Vd.id)(),this.type=e,this.serviceIdentifier=r;var o=typeof n=="symbol"?(0,$d.getSymbolDescription)(n):n;this.name=new Hd.QueryableString(o||""),this.identifier=n,this.metadata=new Array;var s=null;typeof i=="string"?s=new ds.Metadata(Ge.NAMED_TAG,i):i instanceof ds.Metadata&&(s=i),s!==null&&this.metadata.push(s)}return t.prototype.hasTag=function(e){for(var n=0,r=this.metadata;n<r.length;n++){var i=r[n];if(i.key===e)return!0}return!1},t.prototype.isArray=function(){return this.hasTag(Ge.MULTI_INJECT_TAG)},t.prototype.matchesArray=function(e){return this.matchesTag(Ge.MULTI_INJECT_TAG)(e)},t.prototype.isNamed=function(){return this.hasTag(Ge.NAMED_TAG)},t.prototype.isTagged=function(){return this.metadata.some(function(e){return Ge.NON_CUSTOM_TAG_KEYS.every(function(n){return e.key!==n})})},t.prototype.isOptional=function(){return this.matchesTag(Ge.OPTIONAL_TAG)(!0)},t.prototype.getNamedTag=function(){return this.isNamed()?this.metadata.filter(function(e){return e.key===Ge.NAMED_TAG})[0]:null},t.prototype.getCustomTags=function(){return this.isTagged()?this.metadata.filter(function(e){return Ge.NON_CUSTOM_TAG_KEYS.every(function(n){return e.key!==n})}):null},t.prototype.matchesNamedTag=function(e){return this.matchesTag(Ge.NAMED_TAG)(e)},t.prototype.matchesTag=function(e){var n=this;return function(r){for(var i=0,o=n.metadata;i<o.length;i++){var s=o[i];if(s.key===e&&s.value===r)return!0}return!1}},t}();Ee.Target=Kd});var bs=g(z=>{"use strict";var Yd=z&&z.__createBinding||(Object.create?function(t,e,n,r){r===void 0&&(r=n),Object.defineProperty(t,r,{enumerable:!0,get:function(){return e[n]}})}:function(t,e,n,r){r===void 0&&(r=n),t[r]=e[n]}),zd=z&&z.__setModuleDefault||(Object.create?function(t,e){Object.defineProperty(t,"default",{enumerable:!0,value:e})}:function(t,e){t.default=e}),ps=z&&z.__importStar||function(t){if(t&&t.__esModule)return t;var e={};if(t!=null)for(var n in t)n!=="default"&&Object.prototype.hasOwnProperty.call(t,n)&&Yd(e,t,n);return zd(e,t),e},Pn=z&&z.__spreadArray||function(t,e,n){if(n||arguments.length===2)for(var r=0,i=e.length,o;r<i;r++)(o||!(r in e))&&(o||(o=Array.prototype.slice.call(e,0,r)),o[r]=e[r]);return t.concat(o||Array.prototype.slice.call(e))};Object.defineProperty(z,"__esModule",{value:!0});z.getFunctionName=z.getBaseClassDependencyCount=z.getDependencies=void 0;var Jd=On(),ri=ps(Y()),hs=ce(),Gt=ps(U()),ii=tt();Object.defineProperty(z,"getFunctionName",{enumerable:!0,get:function(){return ii.getFunctionName}});var gs=ni();function Xd(t,e){var n=(0,ii.getFunctionName)(e);return ms(t,n,e,!1)}z.getDependencies=Xd;function ms(t,e,n,r){var i=t.getConstructorMetadata(n),o=i.compilerGeneratedMetadata;if(o===void 0){var s=ri.MISSING_INJECTABLE_ANNOTATION+" "+e+".";throw new Error(s)}var u=i.userGeneratedMetadata,c=Object.keys(u),a=n.length===0&&c.length>0,l=c.length>n.length,p=a||l?c.length:n.length,m=Zd(r,e,o,u,p),b=ys(t,n,e),T=Pn(Pn([],m,!0),b,!0);return T}function Qd(t,e,n,r,i){var o=i[t.toString()]||[],s=vs(o),u=s.unmanaged!==!0,c=r[t],a=s.inject||s.multiInject;if(c=a||c,c instanceof Jd.LazyServiceIdentifier&&(c=c.unwrap()),u){var l=c===Object,p=c===Function,m=c===void 0,b=l||p||m;if(!e&&b){var T=ri.MISSING_INJECT_ANNOTATION+" argument "+t+" in class "+n+".";throw new Error(T)}var w=new gs.Target(hs.TargetTypeEnum.ConstructorArgument,s.targetName,c);return w.metadata=o,w}return null}function Zd(t,e,n,r,i){for(var o=[],s=0;s<i;s++){var u=s,c=Qd(u,t,e,n,r);c!==null&&o.push(c)}return o}function ep(t,e,n,r){var i=t||e;if(i===void 0){var o=ri.MISSING_INJECTABLE_ANNOTATION+" for property "+String(n)+" in class "+r+".";throw new Error(o)}return i}function ys(t,e,n){for(var r=t.getPropertiesMetadata(e),i=[],o=Object.getOwnPropertySymbols(r),s=Object.keys(r),u=s.concat(o),c=0,a=u;c<a.length;c++){var l=a[c],p=r[l],m=vs(p),b=m.targetName||l,T=ep(m.inject,m.multiInject,l,n),w=new gs.Target(hs.TargetTypeEnum.ClassProperty,b,T);w.metadata=p,i.push(w)}var R=Object.getPrototypeOf(e.prototype).constructor;if(R!==Object){var K=ys(t,R,n);i=Pn(Pn([],i,!0),K,!0)}return i}function _s(t,e){var n=Object.getPrototypeOf(e.prototype).constructor;if(n!==Object){var r=(0,ii.getFunctionName)(n),i=ms(t,r,n,!0),o=i.map(function(c){return c.metadata.filter(function(a){return a.key===Gt.UNMANAGED_TAG})}),s=[].concat.apply([],o).length,u=i.length-s;return u>0?u:_s(t,n)}else return 0}z.getBaseClassDependencyCount=_s;function vs(t){var e={};return t.forEach(function(n){e[n.key.toString()]=n.value}),{inject:e[Gt.INJECT_TAG],multiInject:e[Gt.MULTI_INJECT_TAG],targetName:e[Gt.NAME_TAG],unmanaged:e[Gt.UNMANAGED_TAG]}}});var ws=g(Rn=>{"use strict";Object.defineProperty(Rn,"__esModule",{value:!0});Rn.Request=void 0;var tp=Je(),np=function(){function t(e,n,r,i,o){this.id=(0,tp.id)(),this.serviceIdentifier=e,this.parentContext=n,this.parentRequest=r,this.target=o,this.childRequests=[],this.bindings=Array.isArray(i)?i:[i],this.requestScope=r===null?new Map:null}return t.prototype.addChildRequest=function(e,n,r){var i=new t(e,this.parentContext,this,n,r);return this.childRequests.push(i),i},t}();Rn.Request=np});var ci=g(ie=>{"use strict";var rp=ie&&ie.__createBinding||(Object.create?function(t,e,n,r){r===void 0&&(r=n),Object.defineProperty(t,r,{enumerable:!0,get:function(){return e[n]}})}:function(t,e,n,r){r===void 0&&(r=n),t[r]=e[n]}),ip=ie&&ie.__setModuleDefault||(Object.create?function(t,e){Object.defineProperty(t,"default",{enumerable:!0,value:e})}:function(t,e){t.default=e}),Es=ie&&ie.__importStar||function(t){if(t&&t.__esModule)return t;var e={};if(t!=null)for(var n in t)n!=="default"&&Object.prototype.hasOwnProperty.call(t,n)&&rp(e,t,n);return ip(e,t),e};Object.defineProperty(ie,"__esModule",{value:!0});ie.getBindingDictionary=ie.createMockRequest=ie.plan=void 0;var Mn=rs(),si=Es(Y()),xs=ce(),Ss=Es(U()),op=ti(),_t=tt(),As=cs(),ai=ye(),sp=ls(),oi=bs(),ui=ws(),Cs=ni();function Is(t){return t._bindingDictionary}ie.getBindingDictionary=Is;function ap(t,e,n,r,i,o){var s=t?Ss.MULTI_INJECT_TAG:Ss.INJECT_TAG,u=new ai.Metadata(s,n),c=new Cs.Target(e,r,n,u);if(i!==void 0){var a=new ai.Metadata(i,o);c.metadata.push(a)}return c}function Ts(t,e,n,r,i){var o=Wt(n.container,i.serviceIdentifier),s=[];return o.length===Mn.BindingCount.NoBindingsAvailable&&n.container.options.autoBindInjectable&&typeof i.serviceIdentifier=="function"&&t.getConstructorMetadata(i.serviceIdentifier).compilerGeneratedMetadata&&(n.container.bind(i.serviceIdentifier).toSelf(),o=Wt(n.container,i.serviceIdentifier)),e?s=o:s=o.filter(function(u){var c=new ui.Request(u.serviceIdentifier,n,r,u,i);return u.constraint(c)}),up(i.serviceIdentifier,s,i,n.container),s}function up(t,e,n,r){switch(e.length){case Mn.BindingCount.NoBindingsAvailable:if(n.isOptional())return e;var i=(0,_t.getServiceIdentifierAsString)(t),o=si.NOT_REGISTERED;throw o+=(0,_t.listMetadataForTarget)(i,n),o+=(0,_t.listRegisteredBindingsForServiceIdentifier)(r,i,Wt),new Error(o);case Mn.BindingCount.OnlyOneBindingAvailable:return e;case Mn.BindingCount.MultipleBindingsAvailable:default:if(n.isArray())return e;var i=(0,_t.getServiceIdentifierAsString)(t),o=si.AMBIGUOUS_MATCH+" "+i;throw o+=(0,_t.listRegisteredBindingsForServiceIdentifier)(r,i,Wt),new Error(o)}}function Os(t,e,n,r,i,o){var s,u;if(i===null){s=Ts(t,e,r,null,o),u=new ui.Request(n,r,null,s,o);var c=new sp.Plan(r,u);r.addPlan(c)}else s=Ts(t,e,r,i,o),u=i.addChildRequest(o.serviceIdentifier,s,o);s.forEach(function(a){var l=null;if(o.isArray())l=u.addChildRequest(a.serviceIdentifier,a,o);else{if(a.cache)return;l=u}if(a.type===xs.BindingTypeEnum.Instance&&a.implementationType!==null){var p=(0,oi.getDependencies)(t,a.implementationType);if(!r.container.options.skipBaseClassChecks){var m=(0,oi.getBaseClassDependencyCount)(t,a.implementationType);if(p.length<m){var b=si.ARGUMENTS_LENGTH_MISMATCH((0,oi.getFunctionName)(a.implementationType));throw new Error(b)}}p.forEach(function(T){Os(t,!1,T.serviceIdentifier,r,l,T)})}})}function Wt(t,e){var n=[],r=Is(t);return r.hasKey(e)?n=r.get(e):t.parent!==null&&(n=Wt(t.parent,e)),n}function cp(t,e,n,r,i,o,s,u){u===void 0&&(u=!1);var c=new As.Context(e),a=ap(n,r,i,"",o,s);try{return Os(t,u,i,c,null,a),c}catch(l){throw(0,op.isStackOverflowExeption)(l)&&(0,_t.circularDependencyToException)(c.plan.rootRequest),l}}ie.plan=cp;function lp(t,e,n,r){var i=new Cs.Target(xs.TargetTypeEnum.Variable,"",e,new ai.Metadata(n,r)),o=new As.Context(t),s=new ui.Request(e,o,null,[],i);return s}ie.createMockRequest=lp});var Vt=g(vt=>{"use strict";Object.defineProperty(vt,"__esModule",{value:!0});vt.isPromiseOrContainsPromise=vt.isPromise=void 0;function li(t){var e=typeof t=="object"&&t!==null||typeof t=="function";return e&&typeof t.then=="function"}vt.isPromise=li;function fp(t){return li(t)?!0:Array.isArray(t)&&t.some(li)}vt.isPromiseOrContainsPromise=fp});var Ns=g(xe=>{"use strict";var dp=xe&&xe.__awaiter||function(t,e,n,r){function i(o){return o instanceof n?o:new n(function(s){s(o)})}return new(n||(n=Promise))(function(o,s){function u(l){try{a(r.next(l))}catch(p){s(p)}}function c(l){try{a(r.throw(l))}catch(p){s(p)}}function a(l){l.done?o(l.value):i(l.value).then(u,c)}a((r=r.apply(t,e||[])).next())})},pp=xe&&xe.__generator||function(t,e){var n={label:0,sent:function(){if(o[0]&1)throw o[1];return o[1]},trys:[],ops:[]},r,i,o,s;return s={next:u(0),throw:u(1),return:u(2)},typeof Symbol=="function"&&(s[Symbol.iterator]=function(){return this}),s;function u(a){return function(l){return c([a,l])}}function c(a){if(r)throw new TypeError("Generator is already executing.");for(;n;)try{if(r=1,i&&(o=a[0]&2?i.return:a[0]?i.throw||((o=i.return)&&o.call(i),0):i.next)&&!(o=o.call(i,a[1])).done)return o;switch(i=0,o&&(a=[a[0]&2,o.value]),a[0]){case 0:case 1:o=a;break;case 4:return n.label++,{value:a[1],done:!1};case 5:n.label++,i=a[1],a=[0];continue;case 7:a=n.ops.pop(),n.trys.pop();continue;default:if(o=n.trys,!(o=o.length>0&&o[o.length-1])&&(a[0]===6||a[0]===2)){n=0;continue}if(a[0]===3&&(!o||a[1]>o[0]&&a[1]<o[3])){n.label=a[1];break}if(a[0]===6&&n.label<o[1]){n.label=o[1],o=a;break}if(o&&n.label<o[2]){n.label=o[2],n.ops.push(a);break}o[2]&&n.ops.pop(),n.trys.pop();continue}a=e.call(t,n)}catch(l){a=[6,l],i=0}finally{r=o=0}if(a[0]&5)throw a[1];return{value:a[0]?a[1]:void 0,done:!0}}};Object.defineProperty(xe,"__esModule",{value:!0});xe.saveToScope=xe.tryGetFromScope=void 0;var Dn=ce(),hp=Vt(),gp=function(t,e){return e.scope===Dn.BindingScopeEnum.Singleton&&e.activated?e.cache:e.scope===Dn.BindingScopeEnum.Request&&t.has(e.id)?t.get(e.id):null};xe.tryGetFromScope=gp;var mp=function(t,e,n){e.scope===Dn.BindingScopeEnum.Singleton&&_p(e,n),e.scope===Dn.BindingScopeEnum.Request&&yp(t,e,n)};xe.saveToScope=mp;var yp=function(t,e,n){t.has(e.id)||t.set(e.id,n)},_p=function(t,e){t.cache=e,t.activated=!0,(0,hp.isPromise)(e)&&vp(t,e)},vp=function(t,e){return dp(void 0,void 0,void 0,function(){var n,r;return pp(this,function(i){switch(i.label){case 0:return i.trys.push([0,2,,3]),[4,e];case 1:return n=i.sent(),t.cache=n,[3,3];case 2:throw r=i.sent(),t.cache=null,t.activated=!1,r;case 3:return[2]}})})}});var Ps=g($t=>{"use strict";Object.defineProperty($t,"__esModule",{value:!0});$t.FactoryType=void 0;var bp;(function(t){t.DynamicValue="toDynamicValue",t.Factory="toFactory",t.Provider="toProvider"})(bp=$t.FactoryType||($t.FactoryType={}))});var di=g(oe=>{"use strict";var wp=oe&&oe.__createBinding||(Object.create?function(t,e,n,r){r===void 0&&(r=n),Object.defineProperty(t,r,{enumerable:!0,get:function(){return e[n]}})}:function(t,e,n,r){r===void 0&&(r=n),t[r]=e[n]}),Sp=oe&&oe.__setModuleDefault||(Object.create?function(t,e){Object.defineProperty(t,"default",{enumerable:!0,value:e})}:function(t,e){t.default=e}),Tp=oe&&oe.__importStar||function(t){if(t&&t.__esModule)return t;var e={};if(t!=null)for(var n in t)n!=="default"&&Object.prototype.hasOwnProperty.call(t,n)&&wp(e,t,n);return Sp(e,t),e};Object.defineProperty(oe,"__esModule",{value:!0});oe.getFactoryDetails=oe.ensureFullyBound=oe.multiBindToService=void 0;var Ep=tt(),xp=Tp(Y()),Ae=ce(),fi=Ps(),Ap=function(t){return function(e){return function(){for(var n=[],r=0;r<arguments.length;r++)n[r]=arguments[r];return n.forEach(function(i){return t.bind(i).toService(e)})}}};oe.multiBindToService=Ap;var Cp=function(t){var e=null;switch(t.type){case Ae.BindingTypeEnum.ConstantValue:case Ae.BindingTypeEnum.Function:e=t.cache;break;case Ae.BindingTypeEnum.Constructor:case Ae.BindingTypeEnum.Instance:e=t.implementationType;break;case Ae.BindingTypeEnum.DynamicValue:e=t.dynamicValue;break;case Ae.BindingTypeEnum.Provider:e=t.provider;break;case Ae.BindingTypeEnum.Factory:e=t.factory;break}if(e===null){var n=(0,Ep.getServiceIdentifierAsString)(t.serviceIdentifier);throw new Error(xp.INVALID_BINDING_TYPE+" "+n)}};oe.ensureFullyBound=Cp;var Ip=function(t){switch(t.type){case Ae.BindingTypeEnum.Factory:return{factory:t.factory,factoryType:fi.FactoryType.Factory};case Ae.BindingTypeEnum.Provider:return{factory:t.provider,factoryType:fi.FactoryType.Provider};case Ae.BindingTypeEnum.DynamicValue:return{factory:t.dynamicValue,factoryType:fi.FactoryType.DynamicValue};default:throw new Error("Unexpected factory type "+t.type)}};oe.getFactoryDetails=Ip});var Ls=g(V=>{"use strict";var bt=V&&V.__assign||function(){return bt=Object.assign||function(t){for(var e,n=1,r=arguments.length;n<r;n++){e=arguments[n];for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])}return t},bt.apply(this,arguments)},Op=V&&V.__createBinding||(Object.create?function(t,e,n,r){r===void 0&&(r=n),Object.defineProperty(t,r,{enumerable:!0,get:function(){return e[n]}})}:function(t,e,n,r){r===void 0&&(r=n),t[r]=e[n]}),Np=V&&V.__setModuleDefault||(Object.create?function(t,e){Object.defineProperty(t,"default",{enumerable:!0,value:e})}:function(t,e){t.default=e}),Pp=V&&V.__importStar||function(t){if(t&&t.__esModule)return t;var e={};if(t!=null)for(var n in t)n!=="default"&&Object.prototype.hasOwnProperty.call(t,n)&&Op(e,t,n);return Np(e,t),e},Ds=V&&V.__awaiter||function(t,e,n,r){function i(o){return o instanceof n?o:new n(function(s){s(o)})}return new(n||(n=Promise))(function(o,s){function u(l){try{a(r.next(l))}catch(p){s(p)}}function c(l){try{a(r.throw(l))}catch(p){s(p)}}function a(l){l.done?o(l.value):i(l.value).then(u,c)}a((r=r.apply(t,e||[])).next())})},ks=V&&V.__generator||function(t,e){var n={label:0,sent:function(){if(o[0]&1)throw o[1];return o[1]},trys:[],ops:[]},r,i,o,s;return s={next:u(0),throw:u(1),return:u(2)},typeof Symbol=="function"&&(s[Symbol.iterator]=function(){return this}),s;function u(a){return function(l){return c([a,l])}}function c(a){if(r)throw new TypeError("Generator is already executing.");for(;n;)try{if(r=1,i&&(o=a[0]&2?i.return:a[0]?i.throw||((o=i.return)&&o.call(i),0):i.next)&&!(o=o.call(i,a[1])).done)return o;switch(i=0,o&&(a=[a[0]&2,o.value]),a[0]){case 0:case 1:o=a;break;case 4:return n.label++,{value:a[1],done:!1};case 5:n.label++,i=a[1],a=[0];continue;case 7:a=n.ops.pop(),n.trys.pop();continue;default:if(o=n.trys,!(o=o.length>0&&o[o.length-1])&&(a[0]===6||a[0]===2)){n=0;continue}if(a[0]===3&&(!o||a[1]>o[0]&&a[1]<o[3])){n.label=a[1];break}if(a[0]===6&&n.label<o[1]){n.label=o[1],o=a;break}if(o&&n.label<o[2]){n.label=o[2],n.ops.push(a);break}o[2]&&n.ops.pop(),n.trys.pop();continue}a=e.call(t,n)}catch(l){a=[6,l],i=0}finally{r=o=0}if(a[0]&5)throw a[1];return{value:a[0]?a[1]:void 0,done:!0}}},Rp=V&&V.__spreadArray||function(t,e,n){if(n||arguments.length===2)for(var r=0,i=e.length,o;r<i;r++)(o||!(r in e))&&(o||(o=Array.prototype.slice.call(e,0,r)),o[r]=e[r]);return t.concat(o||Array.prototype.slice.call(e))};Object.defineProperty(V,"__esModule",{value:!0});V.resolveInstance=void 0;var pi=Y(),gi=ce(),hi=Pp(U()),mi=Vt();function Mp(t,e){return t.reduce(function(n,r){var i=e(r),o=r.target.type;return o===gi.TargetTypeEnum.ConstructorArgument?n.constructorInjections.push(i):(n.propertyRequests.push(r),n.propertyInjections.push(i)),n.isAsync||(n.isAsync=(0,mi.isPromiseOrContainsPromise)(i)),n},{constructorInjections:[],propertyInjections:[],propertyRequests:[],isAsync:!1})}function Dp(t,e,n){var r;if(e.length>0){var i=Mp(e,n),o=bt(bt({},i),{constr:t});i.isAsync?r=kp(o):r=Bs(o)}else r=new t;return r}function Bs(t){var e,n=new((e=t.constr).bind.apply(e,Rp([void 0],t.constructorInjections,!1)));return t.propertyRequests.forEach(function(r,i){var o=r.target.identifier,s=t.propertyInjections[i];(!r.target.isOptional()||s!==void 0)&&(n[o]=s)}),n}function kp(t){return Ds(this,void 0,void 0,function(){var e,n;return ks(this,function(r){switch(r.label){case 0:return[4,Rs(t.constructorInjections)];case 1:return e=r.sent(),[4,Rs(t.propertyInjections)];case 2:return n=r.sent(),[2,Bs(bt(bt({},t),{constructorInjections:e,propertyInjections:n}))]}})})}function Rs(t){return Ds(this,void 0,void 0,function(){var e,n,r,i;return ks(this,function(o){for(e=[],n=0,r=t;n<r.length;n++)i=r[n],Array.isArray(i)?e.push(Promise.all(i)):e.push(i);return[2,Promise.all(e)]})})}function Ms(t,e){var n=Bp(t,e);return(0,mi.isPromise)(n)?n.then(function(){return e}):e}function Bp(t,e){var n,r;if(Reflect.hasMetadata(hi.POST_CONSTRUCT,t)){var i=Reflect.getMetadata(hi.POST_CONSTRUCT,t);try{return(r=(n=e)[i.value])===null||r===void 0?void 0:r.call(n)}catch(o){if(o instanceof Error)throw new Error((0,pi.POST_CONSTRUCT_ERROR)(t.name,o.message))}}}function Lp(t,e){t.scope!==gi.BindingScopeEnum.Singleton&&jp(t,e)}function jp(t,e){var n="Class cannot be instantiated in "+(t.scope===gi.BindingScopeEnum.Request?"request":"transient")+" scope.";if(typeof t.onDeactivation=="function")throw new Error((0,pi.ON_DEACTIVATION_ERROR)(e.name,n));if(Reflect.hasMetadata(hi.PRE_DESTROY,e))throw new Error((0,pi.PRE_DESTROY_ERROR)(e.name,n))}function Fp(t,e,n,r){Lp(t,e);var i=Dp(e,n,r);return(0,mi.isPromise)(i)?i.then(function(o){return Ms(e,o)}):Ms(e,i)}V.resolveInstance=Fp});var Gs=g(se=>{"use strict";var Up=se&&se.__createBinding||(Object.create?function(t,e,n,r){r===void 0&&(r=n),Object.defineProperty(t,r,{enumerable:!0,get:function(){return e[n]}})}:function(t,e,n,r){r===void 0&&(r=n),t[r]=e[n]}),qp=se&&se.__setModuleDefault||(Object.create?function(t,e){Object.defineProperty(t,"default",{enumerable:!0,value:e})}:function(t,e){t.default=e}),Gp=se&&se.__importStar||function(t){if(t&&t.__esModule)return t;var e={};if(t!=null)for(var n in t)n!=="default"&&Object.prototype.hasOwnProperty.call(t,n)&&Up(e,t,n);return qp(e,t),e},Wp=se&&se.__awaiter||function(t,e,n,r){function i(o){return o instanceof n?o:new n(function(s){s(o)})}return new(n||(n=Promise))(function(o,s){function u(l){try{a(r.next(l))}catch(p){s(p)}}function c(l){try{a(r.throw(l))}catch(p){s(p)}}function a(l){l.done?o(l.value):i(l.value).then(u,c)}a((r=r.apply(t,e||[])).next())})},Vp=se&&se.__generator||function(t,e){var n={label:0,sent:function(){if(o[0]&1)throw o[1];return o[1]},trys:[],ops:[]},r,i,o,s;return s={next:u(0),throw:u(1),return:u(2)},typeof Symbol=="function"&&(s[Symbol.iterator]=function(){return this}),s;function u(a){return function(l){return c([a,l])}}function c(a){if(r)throw new TypeError("Generator is already executing.");for(;n;)try{if(r=1,i&&(o=a[0]&2?i.return:a[0]?i.throw||((o=i.return)&&o.call(i),0):i.next)&&!(o=o.call(i,a[1])).done)return o;switch(i=0,o&&(a=[a[0]&2,o.value]),a[0]){case 0:case 1:o=a;break;case 4:return n.label++,{value:a[1],done:!1};case 5:n.label++,i=a[1],a=[0];continue;case 7:a=n.ops.pop(),n.trys.pop();continue;default:if(o=n.trys,!(o=o.length>0&&o[o.length-1])&&(a[0]===6||a[0]===2)){n=0;continue}if(a[0]===3&&(!o||a[1]>o[0]&&a[1]<o[3])){n.label=a[1];break}if(a[0]===6&&n.label<o[1]){n.label=o[1],o=a;break}if(o&&n.label<o[2]){n.label=o[2],n.ops.push(a);break}o[2]&&n.ops.pop(),n.trys.pop();continue}a=e.call(t,n)}catch(l){a=[6,l],i=0}finally{r=o=0}if(a[0]&5)throw a[1];return{value:a[0]?a[1]:void 0,done:!0}}};Object.defineProperty(se,"__esModule",{value:!0});se.resolve=void 0;var $p=Gp(Y()),kn=ce(),Hp=ci(),js=Ns(),yi=Vt(),Us=di(),Kp=ti(),Yp=Ls(),_i=function(t){return function(e){e.parentContext.setCurrentRequest(e);var n=e.bindings,r=e.childRequests,i=e.target&&e.target.isArray(),o=!e.parentRequest||!e.parentRequest.target||!e.target||!e.parentRequest.target.matchesArray(e.target.serviceIdentifier);if(i&&o)return r.map(function(u){var c=_i(t);return c(u)});if(e.target.isOptional()&&n.length===0)return;var s=n[0];return Qp(t,e,s)}},zp=function(t,e){var n=(0,Us.getFactoryDetails)(t);return(0,Kp.tryAndThrowErrorIfStackOverflow)(function(){return n.factory.bind(t)(e)},function(){return new Error($p.CIRCULAR_DEPENDENCY_IN_FACTORY(n.factoryType,e.currentRequest.serviceIdentifier.toString()))})},Jp=function(t,e,n){var r,i=e.childRequests;switch((0,Us.ensureFullyBound)(n),n.type){case kn.BindingTypeEnum.ConstantValue:case kn.BindingTypeEnum.Function:r=n.cache;break;case kn.BindingTypeEnum.Constructor:r=n.implementationType;break;case kn.BindingTypeEnum.Instance:r=(0,Yp.resolveInstance)(n,n.implementationType,i,_i(t));break;default:r=zp(n,e.parentContext)}return r},Xp=function(t,e,n){var r=(0,js.tryGetFromScope)(t,e);return r!==null||(r=n(),(0,js.saveToScope)(t,e,r)),r},Qp=function(t,e,n){return Xp(t,n,function(){var r=Jp(t,e,n);return(0,yi.isPromise)(r)?r=r.then(function(i){return Fs(e,n,i)}):r=Fs(e,n,r),r})};function Fs(t,e,n){var r=Zp(t.parentContext,e,n),i=nh(t.parentContext.container),o,s=i.next();do{o=s.value;var u=t.parentContext,c=t.serviceIdentifier,a=th(o,c);(0,yi.isPromise)(r)?r=qs(a,u,r):r=eh(a,u,r),s=i.next()}while(s.done!==!0&&!(0,Hp.getBindingDictionary)(o).hasKey(t.serviceIdentifier));return r}var Zp=function(t,e,n){var r;return typeof e.onActivation=="function"?r=e.onActivation(t,n):r=n,r},eh=function(t,e,n){for(var r=t.next();!r.done;){if(n=r.value(e,n),(0,yi.isPromise)(n))return qs(t,e,n);r=t.next()}return n},qs=function(t,e,n){return Wp(void 0,void 0,void 0,function(){var r,i;return Vp(this,function(o){switch(o.label){case 0:return[4,n];case 1:r=o.sent(),i=t.next(),o.label=2;case 2:return i.done?[3,4]:[4,i.value(e,r)];case 3:return r=o.sent(),i=t.next(),[3,2];case 4:return[2,r]}})})},th=function(t,e){var n=t._activations;return n.hasKey(e)?n.get(e).values():[].values()},nh=function(t){for(var e=[t],n=t.parent;n!==null;)e.push(n),n=n.parent;var r=function(){var o=e.pop();return o!==void 0?{done:!1,value:o}:{done:!0,value:void 0}},i={next:r};return i};function rh(t){var e=_i(t.plan.rootRequest.requestScope);return e(t.plan.rootRequest)}se.resolve=rh});var vi=g(J=>{"use strict";var ih=J&&J.__createBinding||(Object.create?function(t,e,n,r){r===void 0&&(r=n),Object.defineProperty(t,r,{enumerable:!0,get:function(){return e[n]}})}:function(t,e,n,r){r===void 0&&(r=n),t[r]=e[n]}),oh=J&&J.__setModuleDefault||(Object.create?function(t,e){Object.defineProperty(t,"default",{enumerable:!0,value:e})}:function(t,e){t.default=e}),sh=J&&J.__importStar||function(t){if(t&&t.__esModule)return t;var e={};if(t!=null)for(var n in t)n!=="default"&&Object.prototype.hasOwnProperty.call(t,n)&&ih(e,t,n);return oh(e,t),e};Object.defineProperty(J,"__esModule",{value:!0});J.typeConstraint=J.namedConstraint=J.taggedConstraint=J.traverseAncerstors=void 0;var ah=sh(U()),uh=ye(),Ws=function(t,e){var n=t.parentRequest;return n!==null?e(n)?!0:Ws(n,e):!1};J.traverseAncerstors=Ws;var Vs=function(t){return function(e){var n=function(r){return r!==null&&r.target!==null&&r.target.matchesTag(t)(e)};return n.metaData=new uh.Metadata(t,e),n}};J.taggedConstraint=Vs;var ch=Vs(ah.NAMED_TAG);J.namedConstraint=ch;var lh=function(t){return function(e){var n=null;if(e!==null)if(n=e.bindings[0],typeof t=="string"){var r=n.serviceIdentifier;return r===t}else{var i=e.bindings[0].implementationType;return t===i}return!1}};J.typeConstraint=lh});var Ln=g(Bn=>{"use strict";Object.defineProperty(Bn,"__esModule",{value:!0});Bn.BindingWhenSyntax=void 0;var ee=jn(),W=vi(),fh=function(){function t(e){this._binding=e}return t.prototype.when=function(e){return this._binding.constraint=e,new ee.BindingOnSyntax(this._binding)},t.prototype.whenTargetNamed=function(e){return this._binding.constraint=(0,W.namedConstraint)(e),new ee.BindingOnSyntax(this._binding)},t.prototype.whenTargetIsDefault=function(){return this._binding.constraint=function(e){if(e===null)return!1;var n=e.target!==null&&!e.target.isNamed()&&!e.target.isTagged();return n},new ee.BindingOnSyntax(this._binding)},t.prototype.whenTargetTagged=function(e,n){return this._binding.constraint=(0,W.taggedConstraint)(e)(n),new ee.BindingOnSyntax(this._binding)},t.prototype.whenInjectedInto=function(e){return this._binding.constraint=function(n){return n!==null&&(0,W.typeConstraint)(e)(n.parentRequest)},new ee.BindingOnSyntax(this._binding)},t.prototype.whenParentNamed=function(e){return this._binding.constraint=function(n){return n!==null&&(0,W.namedConstraint)(e)(n.parentRequest)},new ee.BindingOnSyntax(this._binding)},t.prototype.whenParentTagged=function(e,n){return this._binding.constraint=function(r){return r!==null&&(0,W.taggedConstraint)(e)(n)(r.parentRequest)},new ee.BindingOnSyntax(this._binding)},t.prototype.whenAnyAncestorIs=function(e){return this._binding.constraint=function(n){return n!==null&&(0,W.traverseAncerstors)(n,(0,W.typeConstraint)(e))},new ee.BindingOnSyntax(this._binding)},t.prototype.whenNoAncestorIs=function(e){return this._binding.constraint=function(n){return n!==null&&!(0,W.traverseAncerstors)(n,(0,W.typeConstraint)(e))},new ee.BindingOnSyntax(this._binding)},t.prototype.whenAnyAncestorNamed=function(e){return this._binding.constraint=function(n){return n!==null&&(0,W.traverseAncerstors)(n,(0,W.namedConstraint)(e))},new ee.BindingOnSyntax(this._binding)},t.prototype.whenNoAncestorNamed=function(e){return this._binding.constraint=function(n){return n!==null&&!(0,W.traverseAncerstors)(n,(0,W.namedConstraint)(e))},new ee.BindingOnSyntax(this._binding)},t.prototype.whenAnyAncestorTagged=function(e,n){return this._binding.constraint=function(r){return r!==null&&(0,W.traverseAncerstors)(r,(0,W.taggedConstraint)(e)(n))},new ee.BindingOnSyntax(this._binding)},t.prototype.whenNoAncestorTagged=function(e,n){return this._binding.constraint=function(r){return r!==null&&!(0,W.traverseAncerstors)(r,(0,W.taggedConstraint)(e)(n))},new ee.BindingOnSyntax(this._binding)},t.prototype.whenAnyAncestorMatches=function(e){return this._binding.constraint=function(n){return n!==null&&(0,W.traverseAncerstors)(n,e)},new ee.BindingOnSyntax(this._binding)},t.prototype.whenNoAncestorMatches=function(e){return this._binding.constraint=function(n){return n!==null&&!(0,W.traverseAncerstors)(n,e)},new ee.BindingOnSyntax(this._binding)},t}();Bn.BindingWhenSyntax=fh});var jn=g(Fn=>{"use strict";Object.defineProperty(Fn,"__esModule",{value:!0});Fn.BindingOnSyntax=void 0;var $s=Ln(),dh=function(){function t(e){this._binding=e}return t.prototype.onActivation=function(e){return this._binding.onActivation=e,new $s.BindingWhenSyntax(this._binding)},t.prototype.onDeactivation=function(e){return this._binding.onDeactivation=e,new $s.BindingWhenSyntax(this._binding)},t}();Fn.BindingOnSyntax=dh});var bi=g(Un=>{"use strict";Object.defineProperty(Un,"__esModule",{value:!0});Un.BindingWhenOnSyntax=void 0;var ph=jn(),hh=Ln(),gh=function(){function t(e){this._binding=e,this._bindingWhenSyntax=new hh.BindingWhenSyntax(this._binding),this._bindingOnSyntax=new ph.BindingOnSyntax(this._binding)}return t.prototype.when=function(e){return this._bindingWhenSyntax.when(e)},t.prototype.whenTargetNamed=function(e){return this._bindingWhenSyntax.whenTargetNamed(e)},t.prototype.whenTargetIsDefault=function(){return this._bindingWhenSyntax.whenTargetIsDefault()},t.prototype.whenTargetTagged=function(e,n){return this._bindingWhenSyntax.whenTargetTagged(e,n)},t.prototype.whenInjectedInto=function(e){return this._bindingWhenSyntax.whenInjectedInto(e)},t.prototype.whenParentNamed=function(e){return this._bindingWhenSyntax.whenParentNamed(e)},t.prototype.whenParentTagged=function(e,n){return this._bindingWhenSyntax.whenParentTagged(e,n)},t.prototype.whenAnyAncestorIs=function(e){return this._bindingWhenSyntax.whenAnyAncestorIs(e)},t.prototype.whenNoAncestorIs=function(e){return this._bindingWhenSyntax.whenNoAncestorIs(e)},t.prototype.whenAnyAncestorNamed=function(e){return this._bindingWhenSyntax.whenAnyAncestorNamed(e)},t.prototype.whenAnyAncestorTagged=function(e,n){return this._bindingWhenSyntax.whenAnyAncestorTagged(e,n)},t.prototype.whenNoAncestorNamed=function(e){return this._bindingWhenSyntax.whenNoAncestorNamed(e)},t.prototype.whenNoAncestorTagged=function(e,n){return this._bindingWhenSyntax.whenNoAncestorTagged(e,n)},t.prototype.whenAnyAncestorMatches=function(e){return this._bindingWhenSyntax.whenAnyAncestorMatches(e)},t.prototype.whenNoAncestorMatches=function(e){return this._bindingWhenSyntax.whenNoAncestorMatches(e)},t.prototype.onActivation=function(e){return this._bindingOnSyntax.onActivation(e)},t.prototype.onDeactivation=function(e){return this._bindingOnSyntax.onDeactivation(e)},t}();Un.BindingWhenOnSyntax=gh});var Hs=g(qn=>{"use strict";Object.defineProperty(qn,"__esModule",{value:!0});qn.BindingInSyntax=void 0;var wi=ce(),Si=bi(),mh=function(){function t(e){this._binding=e}return t.prototype.inRequestScope=function(){return this._binding.scope=wi.BindingScopeEnum.Request,new Si.BindingWhenOnSyntax(this._binding)},t.prototype.inSingletonScope=function(){return this._binding.scope=wi.BindingScopeEnum.Singleton,new Si.BindingWhenOnSyntax(this._binding)},t.prototype.inTransientScope=function(){return this._binding.scope=wi.BindingScopeEnum.Transient,new Si.BindingWhenOnSyntax(this._binding)},t}();qn.BindingInSyntax=mh});var Ks=g(Gn=>{"use strict";Object.defineProperty(Gn,"__esModule",{value:!0});Gn.BindingInWhenOnSyntax=void 0;var yh=Hs(),_h=jn(),vh=Ln(),bh=function(){function t(e){this._binding=e,this._bindingWhenSyntax=new vh.BindingWhenSyntax(this._binding),this._bindingOnSyntax=new _h.BindingOnSyntax(this._binding),this._bindingInSyntax=new yh.BindingInSyntax(e)}return t.prototype.inRequestScope=function(){return this._bindingInSyntax.inRequestScope()},t.prototype.inSingletonScope=function(){return this._bindingInSyntax.inSingletonScope()},t.prototype.inTransientScope=function(){return this._bindingInSyntax.inTransientScope()},t.prototype.when=function(e){return this._bindingWhenSyntax.when(e)},t.prototype.whenTargetNamed=function(e){return this._bindingWhenSyntax.whenTargetNamed(e)},t.prototype.whenTargetIsDefault=function(){return this._bindingWhenSyntax.whenTargetIsDefault()},t.prototype.whenTargetTagged=function(e,n){return this._bindingWhenSyntax.whenTargetTagged(e,n)},t.prototype.whenInjectedInto=function(e){return this._bindingWhenSyntax.whenInjectedInto(e)},t.prototype.whenParentNamed=function(e){return this._bindingWhenSyntax.whenParentNamed(e)},t.prototype.whenParentTagged=function(e,n){return this._bindingWhenSyntax.whenParentTagged(e,n)},t.prototype.whenAnyAncestorIs=function(e){return this._bindingWhenSyntax.whenAnyAncestorIs(e)},t.prototype.whenNoAncestorIs=function(e){return this._bindingWhenSyntax.whenNoAncestorIs(e)},t.prototype.whenAnyAncestorNamed=function(e){return this._bindingWhenSyntax.whenAnyAncestorNamed(e)},t.prototype.whenAnyAncestorTagged=function(e,n){return this._bindingWhenSyntax.whenAnyAncestorTagged(e,n)},t.prototype.whenNoAncestorNamed=function(e){return this._bindingWhenSyntax.whenNoAncestorNamed(e)},t.prototype.whenNoAncestorTagged=function(e,n){return this._bindingWhenSyntax.whenNoAncestorTagged(e,n)},t.prototype.whenAnyAncestorMatches=function(e){return this._bindingWhenSyntax.whenAnyAncestorMatches(e)},t.prototype.whenNoAncestorMatches=function(e){return this._bindingWhenSyntax.whenNoAncestorMatches(e)},t.prototype.onActivation=function(e){return this._bindingOnSyntax.onActivation(e)},t.prototype.onDeactivation=function(e){return this._bindingOnSyntax.onDeactivation(e)},t}();Gn.BindingInWhenOnSyntax=bh});var Js=g(Ce=>{"use strict";var wh=Ce&&Ce.__createBinding||(Object.create?function(t,e,n,r){r===void 0&&(r=n),Object.defineProperty(t,r,{enumerable:!0,get:function(){return e[n]}})}:function(t,e,n,r){r===void 0&&(r=n),t[r]=e[n]}),Sh=Ce&&Ce.__setModuleDefault||(Object.create?function(t,e){Object.defineProperty(t,"default",{enumerable:!0,value:e})}:function(t,e){t.default=e}),Th=Ce&&Ce.__importStar||function(t){if(t&&t.__esModule)return t;var e={};if(t!=null)for(var n in t)n!=="default"&&Object.prototype.hasOwnProperty.call(t,n)&&wh(e,t,n);return Sh(e,t),e};Object.defineProperty(Ce,"__esModule",{value:!0});Ce.BindingToSyntax=void 0;var Ys=Th(Y()),te=ce(),zs=Ks(),wt=bi(),Eh=function(){function t(e){this._binding=e}return t.prototype.to=function(e){return this._binding.type=te.BindingTypeEnum.Instance,this._binding.implementationType=e,new zs.BindingInWhenOnSyntax(this._binding)},t.prototype.toSelf=function(){if(typeof this._binding.serviceIdentifier!="function")throw new Error(""+Ys.INVALID_TO_SELF_VALUE);var e=this._binding.serviceIdentifier;return this.to(e)},t.prototype.toConstantValue=function(e){return this._binding.type=te.BindingTypeEnum.ConstantValue,this._binding.cache=e,this._binding.dynamicValue=null,this._binding.implementationType=null,this._binding.scope=te.BindingScopeEnum.Singleton,new wt.BindingWhenOnSyntax(this._binding)},t.prototype.toDynamicValue=function(e){return this._binding.type=te.BindingTypeEnum.DynamicValue,this._binding.cache=null,this._binding.dynamicValue=e,this._binding.implementationType=null,new zs.BindingInWhenOnSyntax(this._binding)},t.prototype.toConstructor=function(e){return this._binding.type=te.BindingTypeEnum.Constructor,this._binding.implementationType=e,this._binding.scope=te.BindingScopeEnum.Singleton,new wt.BindingWhenOnSyntax(this._binding)},t.prototype.toFactory=function(e){return this._binding.type=te.BindingTypeEnum.Factory,this._binding.factory=e,this._binding.scope=te.BindingScopeEnum.Singleton,new wt.BindingWhenOnSyntax(this._binding)},t.prototype.toFunction=function(e){if(typeof e!="function")throw new Error(Ys.INVALID_FUNCTION_BINDING);var n=this.toConstantValue(e);return this._binding.type=te.BindingTypeEnum.Function,this._binding.scope=te.BindingScopeEnum.Singleton,n},t.prototype.toAutoFactory=function(e){return this._binding.type=te.BindingTypeEnum.Factory,this._binding.factory=function(n){var r=function(){return n.container.get(e)};return r},this._binding.scope=te.BindingScopeEnum.Singleton,new wt.BindingWhenOnSyntax(this._binding)},t.prototype.toAutoNamedFactory=function(e){return this._binding.type=te.BindingTypeEnum.Factory,this._binding.factory=function(n){return function(r){return n.container.getNamed(e,r)}},new wt.BindingWhenOnSyntax(this._binding)},t.prototype.toProvider=function(e){return this._binding.type=te.BindingTypeEnum.Provider,this._binding.provider=e,this._binding.scope=te.BindingScopeEnum.Singleton,new wt.BindingWhenOnSyntax(this._binding)},t.prototype.toService=function(e){this.toDynamicValue(function(n){return n.container.get(e)})},t}();Ce.BindingToSyntax=Eh});var Xs=g(Wn=>{"use strict";Object.defineProperty(Wn,"__esModule",{value:!0});Wn.ContainerSnapshot=void 0;var xh=function(){function t(){}return t.of=function(e,n,r,i,o){var s=new t;return s.bindings=e,s.middleware=n,s.deactivations=i,s.activations=r,s.moduleActivationStore=o,s},t}();Wn.ContainerSnapshot=xh});var Qs=g(Vn=>{"use strict";Object.defineProperty(Vn,"__esModule",{value:!0});Vn.isClonable=void 0;function Ah(t){return typeof t=="object"&&t!==null&&"clone"in t&&typeof t.clone=="function"}Vn.isClonable=Ah});var Ti=g(Ie=>{"use strict";var Ch=Ie&&Ie.__createBinding||(Object.create?function(t,e,n,r){r===void 0&&(r=n),Object.defineProperty(t,r,{enumerable:!0,get:function(){return e[n]}})}:function(t,e,n,r){r===void 0&&(r=n),t[r]=e[n]}),Ih=Ie&&Ie.__setModuleDefault||(Object.create?function(t,e){Object.defineProperty(t,"default",{enumerable:!0,value:e})}:function(t,e){t.default=e}),Oh=Ie&&Ie.__importStar||function(t){if(t&&t.__esModule)return t;var e={};if(t!=null)for(var n in t)n!=="default"&&Object.prototype.hasOwnProperty.call(t,n)&&Ch(e,t,n);return Ih(e,t),e};Object.defineProperty(Ie,"__esModule",{value:!0});Ie.Lookup=void 0;var nt=Oh(Y()),Nh=Qs(),Ph=function(){function t(){this._map=new Map}return t.prototype.getMap=function(){return this._map},t.prototype.add=function(e,n){if(e==null)throw new Error(nt.NULL_ARGUMENT);if(n==null)throw new Error(nt.NULL_ARGUMENT);var r=this._map.get(e);r!==void 0?r.push(n):this._map.set(e,[n])},t.prototype.get=function(e){if(e==null)throw new Error(nt.NULL_ARGUMENT);var n=this._map.get(e);if(n!==void 0)return n;throw new Error(nt.KEY_NOT_FOUND)},t.prototype.remove=function(e){if(e==null)throw new Error(nt.NULL_ARGUMENT);if(!this._map.delete(e))throw new Error(nt.KEY_NOT_FOUND)},t.prototype.removeIntersection=function(e){var n=this;this.traverse(function(r,i){var o=e.hasKey(r)?e.get(r):void 0;if(o!==void 0){var s=i.filter(function(u){return!o.some(function(c){return u===c})});n._setValue(r,s)}})},t.prototype.removeByCondition=function(e){var n=this,r=[];return this._map.forEach(function(i,o){for(var s=[],u=0,c=i;u<c.length;u++){var a=c[u],l=e(a);l?r.push(a):s.push(a)}n._setValue(o,s)}),r},t.prototype.hasKey=function(e){if(e==null)throw new Error(nt.NULL_ARGUMENT);return this._map.has(e)},t.prototype.clone=function(){var e=new t;return this._map.forEach(function(n,r){n.forEach(function(i){return e.add(r,(0,Nh.isClonable)(i)?i.clone():i)})}),e},t.prototype.traverse=function(e){this._map.forEach(function(n,r){e(r,n)})},t.prototype._setValue=function(e,n){n.length>0?this._map.set(e,n):this._map.delete(e)},t}();Ie.Lookup=Ph});var ea=g($n=>{"use strict";Object.defineProperty($n,"__esModule",{value:!0});$n.ModuleActivationStore=void 0;var Zs=Ti(),Rh=function(){function t(){this._map=new Map}return t.prototype.remove=function(e){if(this._map.has(e)){var n=this._map.get(e);return this._map.delete(e),n}return this._getEmptyHandlersStore()},t.prototype.addDeactivation=function(e,n,r){this._getModuleActivationHandlers(e).onDeactivations.add(n,r)},t.prototype.addActivation=function(e,n,r){this._getModuleActivationHandlers(e).onActivations.add(n,r)},t.prototype.clone=function(){var e=new t;return this._map.forEach(function(n,r){e._map.set(r,{onActivations:n.onActivations.clone(),onDeactivations:n.onDeactivations.clone()})}),e},t.prototype._getModuleActivationHandlers=function(e){var n=this._map.get(e);return n===void 0&&(n=this._getEmptyHandlersStore(),this._map.set(e,n)),n},t.prototype._getEmptyHandlersStore=function(){var e={onActivations:new Zs.Lookup,onDeactivations:new Zs.Lookup};return e},t}();$n.ModuleActivationStore=Rh});var na=g($=>{"use strict";var Kn=$&&$.__assign||function(){return Kn=Object.assign||function(t){for(var e,n=1,r=arguments.length;n<r;n++){e=arguments[n];for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])}return t},Kn.apply(this,arguments)},Mh=$&&$.__createBinding||(Object.create?function(t,e,n,r){r===void 0&&(r=n),Object.defineProperty(t,r,{enumerable:!0,get:function(){return e[n]}})}:function(t,e,n,r){r===void 0&&(r=n),t[r]=e[n]}),Dh=$&&$.__setModuleDefault||(Object.create?function(t,e){Object.defineProperty(t,"default",{enumerable:!0,value:e})}:function(t,e){t.default=e}),ta=$&&$.__importStar||function(t){if(t&&t.__esModule)return t;var e={};if(t!=null)for(var n in t)n!=="default"&&Object.prototype.hasOwnProperty.call(t,n)&&Mh(e,t,n);return Dh(e,t),e},fe=$&&$.__awaiter||function(t,e,n,r){function i(o){return o instanceof n?o:new n(function(s){s(o)})}return new(n||(n=Promise))(function(o,s){function u(l){try{a(r.next(l))}catch(p){s(p)}}function c(l){try{a(r.throw(l))}catch(p){s(p)}}function a(l){l.done?o(l.value):i(l.value).then(u,c)}a((r=r.apply(t,e||[])).next())})},de=$&&$.__generator||function(t,e){var n={label:0,sent:function(){if(o[0]&1)throw o[1];return o[1]},trys:[],ops:[]},r,i,o,s;return s={next:u(0),throw:u(1),return:u(2)},typeof Symbol=="function"&&(s[Symbol.iterator]=function(){return this}),s;function u(a){return function(l){return c([a,l])}}function c(a){if(r)throw new TypeError("Generator is already executing.");for(;n;)try{if(r=1,i&&(o=a[0]&2?i.return:a[0]?i.throw||((o=i.return)&&o.call(i),0):i.next)&&!(o=o.call(i,a[1])).done)return o;switch(i=0,o&&(a=[a[0]&2,o.value]),a[0]){case 0:case 1:o=a;break;case 4:return n.label++,{value:a[1],done:!1};case 5:n.label++,i=a[1],a=[0];continue;case 7:a=n.ops.pop(),n.trys.pop();continue;default:if(o=n.trys,!(o=o.length>0&&o[o.length-1])&&(a[0]===6||a[0]===2)){n=0;continue}if(a[0]===3&&(!o||a[1]>o[0]&&a[1]<o[3])){n.label=a[1];break}if(a[0]===6&&n.label<o[1]){n.label=o[1],o=a;break}if(o&&n.label<o[2]){n.label=o[2],n.ops.push(a);break}o[2]&&n.ops.pop(),n.trys.pop();continue}a=e.call(t,n)}catch(l){a=[6,l],i=0}finally{r=o=0}if(a[0]&5)throw a[1];return{value:a[0]?a[1]:void 0,done:!0}}},kh=$&&$.__spreadArray||function(t,e,n){if(n||arguments.length===2)for(var r=0,i=e.length,o;r<i;r++)(o||!(r in e))&&(o||(o=Array.prototype.slice.call(e,0,r)),o[r]=e[r]);return t.concat(o||Array.prototype.slice.call(e))};Object.defineProperty($,"__esModule",{value:!0});$.Container=void 0;var Bh=ns(),_e=ta(Y()),St=ce(),rt=ta(U()),Lh=ei(),Hn=ci(),jh=Gs(),Fh=Js(),it=Vt(),Uh=Je(),qh=tt(),Gh=Xs(),Ht=Ti(),Wh=ea(),Vh=function(){function t(e){var n=e||{};if(typeof n!="object")throw new Error(""+_e.CONTAINER_OPTIONS_MUST_BE_AN_OBJECT);if(n.defaultScope===void 0)n.defaultScope=St.BindingScopeEnum.Transient;else if(n.defaultScope!==St.BindingScopeEnum.Singleton&&n.defaultScope!==St.BindingScopeEnum.Transient&&n.defaultScope!==St.BindingScopeEnum.Request)throw new Error(""+_e.CONTAINER_OPTIONS_INVALID_DEFAULT_SCOPE);if(n.autoBindInjectable===void 0)n.autoBindInjectable=!1;else if(typeof n.autoBindInjectable!="boolean")throw new Error(""+_e.CONTAINER_OPTIONS_INVALID_AUTO_BIND_INJECTABLE);if(n.skipBaseClassChecks===void 0)n.skipBaseClassChecks=!1;else if(typeof n.skipBaseClassChecks!="boolean")throw new Error(""+_e.CONTAINER_OPTIONS_INVALID_SKIP_BASE_CHECK);this.options={autoBindInjectable:n.autoBindInjectable,defaultScope:n.defaultScope,skipBaseClassChecks:n.skipBaseClassChecks},this.id=(0,Uh.id)(),this._bindingDictionary=new Ht.Lookup,this._snapshots=[],this._middleware=null,this._activations=new Ht.Lookup,this._deactivations=new Ht.Lookup,this.parent=null,this._metadataReader=new Lh.MetadataReader,this._moduleActivationStore=new Wh.ModuleActivationStore}return t.merge=function(e,n){for(var r=[],i=2;i<arguments.length;i++)r[i-2]=arguments[i];var o=new t,s=kh([e,n],r,!0).map(function(a){return(0,Hn.getBindingDictionary)(a)}),u=(0,Hn.getBindingDictionary)(o);function c(a,l){a.traverse(function(p,m){m.forEach(function(b){l.add(b.serviceIdentifier,b.clone())})})}return s.forEach(function(a){c(a,u)}),o},t.prototype.load=function(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];for(var r=this._getContainerModuleHelpersFactory(),i=0,o=e;i<o.length;i++){var s=o[i],u=r(s.id);s.registry(u.bindFunction,u.unbindFunction,u.isboundFunction,u.rebindFunction,u.unbindAsyncFunction,u.onActivationFunction,u.onDeactivationFunction)}},t.prototype.loadAsync=function(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];return fe(this,void 0,void 0,function(){var r,i,o,s,u;return de(this,function(c){switch(c.label){case 0:r=this._getContainerModuleHelpersFactory(),i=0,o=e,c.label=1;case 1:return i<o.length?(s=o[i],u=r(s.id),[4,s.registry(u.bindFunction,u.unbindFunction,u.isboundFunction,u.rebindFunction,u.unbindAsyncFunction,u.onActivationFunction,u.onDeactivationFunction)]):[3,4];case 2:c.sent(),c.label=3;case 3:return i++,[3,1];case 4:return[2]}})})},t.prototype.unload=function(){for(var e=this,n=[],r=0;r<arguments.length;r++)n[r]=arguments[r];n.forEach(function(i){var o=e._removeModuleBindings(i.id);e._deactivateSingletons(o),e._removeModuleHandlers(i.id)})},t.prototype.unloadAsync=function(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];return fe(this,void 0,void 0,function(){var r,i,o,s;return de(this,function(u){switch(u.label){case 0:r=0,i=e,u.label=1;case 1:return r<i.length?(o=i[r],s=this._removeModuleBindings(o.id),[4,this._deactivateSingletonsAsync(s)]):[3,4];case 2:u.sent(),this._removeModuleHandlers(o.id),u.label=3;case 3:return r++,[3,1];case 4:return[2]}})})},t.prototype.bind=function(e){var n=this.options.defaultScope||St.BindingScopeEnum.Transient,r=new Bh.Binding(e,n);return this._bindingDictionary.add(e,r),new Fh.BindingToSyntax(r)},t.prototype.rebind=function(e){return this.unbind(e),this.bind(e)},t.prototype.rebindAsync=function(e){return fe(this,void 0,void 0,function(){return de(this,function(n){switch(n.label){case 0:return[4,this.unbindAsync(e)];case 1:return n.sent(),[2,this.bind(e)]}})})},t.prototype.unbind=function(e){if(this._bindingDictionary.hasKey(e)){var n=this._bindingDictionary.get(e);this._deactivateSingletons(n)}this._removeServiceFromDictionary(e)},t.prototype.unbindAsync=function(e){return fe(this,void 0,void 0,function(){var n;return de(this,function(r){switch(r.label){case 0:return this._bindingDictionary.hasKey(e)?(n=this._bindingDictionary.get(e),[4,this._deactivateSingletonsAsync(n)]):[3,2];case 1:r.sent(),r.label=2;case 2:return this._removeServiceFromDictionary(e),[2]}})})},t.prototype.unbindAll=function(){var e=this;this._bindingDictionary.traverse(function(n,r){e._deactivateSingletons(r)}),this._bindingDictionary=new Ht.Lookup},t.prototype.unbindAllAsync=function(){return fe(this,void 0,void 0,function(){var e,n=this;return de(this,function(r){switch(r.label){case 0:return e=[],this._bindingDictionary.traverse(function(i,o){e.push(n._deactivateSingletonsAsync(o))}),[4,Promise.all(e)];case 1:return r.sent(),this._bindingDictionary=new Ht.Lookup,[2]}})})},t.prototype.onActivation=function(e,n){this._activations.add(e,n)},t.prototype.onDeactivation=function(e,n){this._deactivations.add(e,n)},t.prototype.isBound=function(e){var n=this._bindingDictionary.hasKey(e);return!n&&this.parent&&(n=this.parent.isBound(e)),n},t.prototype.isCurrentBound=function(e){return this._bindingDictionary.hasKey(e)},t.prototype.isBoundNamed=function(e,n){return this.isBoundTagged(e,rt.NAMED_TAG,n)},t.prototype.isBoundTagged=function(e,n,r){var i=!1;if(this._bindingDictionary.hasKey(e)){var o=this._bindingDictionary.get(e),s=(0,Hn.createMockRequest)(this,e,n,r);i=o.some(function(u){return u.constraint(s)})}return!i&&this.parent&&(i=this.parent.isBoundTagged(e,n,r)),i},t.prototype.snapshot=function(){this._snapshots.push(Gh.ContainerSnapshot.of(this._bindingDictionary.clone(),this._middleware,this._activations.clone(),this._deactivations.clone(),this._moduleActivationStore.clone()))},t.prototype.restore=function(){var e=this._snapshots.pop();if(e===void 0)throw new Error(_e.NO_MORE_SNAPSHOTS_AVAILABLE);this._bindingDictionary=e.bindings,this._activations=e.activations,this._deactivations=e.deactivations,this._middleware=e.middleware,this._moduleActivationStore=e.moduleActivationStore},t.prototype.createChild=function(e){var n=new t(e||this.options);return n.parent=this,n},t.prototype.applyMiddleware=function(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];var r=this._middleware?this._middleware:this._planAndResolve();this._middleware=e.reduce(function(i,o){return o(i)},r)},t.prototype.applyCustomMetadataReader=function(e){this._metadataReader=e},t.prototype.get=function(e){var n=this._getNotAllArgs(e,!1);return this._getButThrowIfAsync(n)},t.prototype.getAsync=function(e){return fe(this,void 0,void 0,function(){var n;return de(this,function(r){return n=this._getNotAllArgs(e,!1),[2,this._get(n)]})})},t.prototype.getTagged=function(e,n,r){var i=this._getNotAllArgs(e,!1,n,r);return this._getButThrowIfAsync(i)},t.prototype.getTaggedAsync=function(e,n,r){return fe(this,void 0,void 0,function(){var i;return de(this,function(o){return i=this._getNotAllArgs(e,!1,n,r),[2,this._get(i)]})})},t.prototype.getNamed=function(e,n){return this.getTagged(e,rt.NAMED_TAG,n)},t.prototype.getNamedAsync=function(e,n){return this.getTaggedAsync(e,rt.NAMED_TAG,n)},t.prototype.getAll=function(e){var n=this._getAllArgs(e);return this._getButThrowIfAsync(n)},t.prototype.getAllAsync=function(e){var n=this._getAllArgs(e);return this._getAll(n)},t.prototype.getAllTagged=function(e,n,r){var i=this._getNotAllArgs(e,!0,n,r);return this._getButThrowIfAsync(i)},t.prototype.getAllTaggedAsync=function(e,n,r){var i=this._getNotAllArgs(e,!0,n,r);return this._getAll(i)},t.prototype.getAllNamed=function(e,n){return this.getAllTagged(e,rt.NAMED_TAG,n)},t.prototype.getAllNamedAsync=function(e,n){return this.getAllTaggedAsync(e,rt.NAMED_TAG,n)},t.prototype.resolve=function(e){var n=this.isBound(e);n||this.bind(e).toSelf();var r=this.get(e);return n||this.unbind(e),r},t.prototype._preDestroy=function(e,n){var r,i;if(Reflect.hasMetadata(rt.PRE_DESTROY,e)){var o=Reflect.getMetadata(rt.PRE_DESTROY,e);return(i=(r=n)[o.value])===null||i===void 0?void 0:i.call(r)}},t.prototype._removeModuleHandlers=function(e){var n=this._moduleActivationStore.remove(e);this._activations.removeIntersection(n.onActivations),this._deactivations.removeIntersection(n.onDeactivations)},t.prototype._removeModuleBindings=function(e){return this._bindingDictionary.removeByCondition(function(n){return n.moduleId===e})},t.prototype._deactivate=function(e,n){var r=this,i=Object.getPrototypeOf(n).constructor;try{if(this._deactivations.hasKey(e.serviceIdentifier)){var o=this._deactivateContainer(n,this._deactivations.get(e.serviceIdentifier).values());if((0,it.isPromise)(o))return this._handleDeactivationError(o.then(function(){return r._propagateContainerDeactivationThenBindingAndPreDestroyAsync(e,n,i)}),i)}var s=this._propagateContainerDeactivationThenBindingAndPreDestroy(e,n,i);if((0,it.isPromise)(s))return this._handleDeactivationError(s,i)}catch(u){if(u instanceof Error)throw new Error(_e.ON_DEACTIVATION_ERROR(i.name,u.message))}},t.prototype._handleDeactivationError=function(e,n){return fe(this,void 0,void 0,function(){var r;return de(this,function(i){switch(i.label){case 0:return i.trys.push([0,2,,3]),[4,e];case 1:return i.sent(),[3,3];case 2:if(r=i.sent(),r instanceof Error)throw new Error(_e.ON_DEACTIVATION_ERROR(n.name,r.message));return[3,3];case 3:return[2]}})})},t.prototype._deactivateContainer=function(e,n){for(var r=this,i=n.next();i.value;){var o=i.value(e);if((0,it.isPromise)(o))return o.then(function(){return r._deactivateContainerAsync(e,n)});i=n.next()}},t.prototype._deactivateContainerAsync=function(e,n){return fe(this,void 0,void 0,function(){var r;return de(this,function(i){switch(i.label){case 0:r=n.next(),i.label=1;case 1:return r.value?[4,r.value(e)]:[3,3];case 2:return i.sent(),r=n.next(),[3,1];case 3:return[2]}})})},t.prototype._getContainerModuleHelpersFactory=function(){var e=this,n=function(l,p){l._binding.moduleId=p},r=function(l){return function(p){var m=e.bind(p);return n(m,l),m}},i=function(){return function(l){return e.unbind(l)}},o=function(){return function(l){return e.unbindAsync(l)}},s=function(){return function(l){return e.isBound(l)}},u=function(l){return function(p){var m=e.rebind(p);return n(m,l),m}},c=function(l){return function(p,m){e._moduleActivationStore.addActivation(l,p,m),e.onActivation(p,m)}},a=function(l){return function(p,m){e._moduleActivationStore.addDeactivation(l,p,m),e.onDeactivation(p,m)}};return function(l){return{bindFunction:r(l),isboundFunction:s(),onActivationFunction:c(l),onDeactivationFunction:a(l),rebindFunction:u(l),unbindFunction:i(),unbindAsyncFunction:o()}}},t.prototype._getAll=function(e){return Promise.all(this._get(e))},t.prototype._get=function(e){var n=Kn(Kn({},e),{contextInterceptor:function(i){return i},targetType:St.TargetTypeEnum.Variable});if(this._middleware){var r=this._middleware(n);if(r==null)throw new Error(_e.INVALID_MIDDLEWARE_RETURN);return r}return this._planAndResolve()(n)},t.prototype._getButThrowIfAsync=function(e){var n=this._get(e);if((0,it.isPromiseOrContainsPromise)(n))throw new Error(_e.LAZY_IN_SYNC(e.serviceIdentifier));return n},t.prototype._getAllArgs=function(e){var n={avoidConstraints:!0,isMultiInject:!0,serviceIdentifier:e};return n},t.prototype._getNotAllArgs=function(e,n,r,i){var o={avoidConstraints:!1,isMultiInject:n,serviceIdentifier:e,key:r,value:i};return o},t.prototype._planAndResolve=function(){var e=this;return function(n){var r=(0,Hn.plan)(e._metadataReader,e,n.isMultiInject,n.targetType,n.serviceIdentifier,n.key,n.value,n.avoidConstraints);r=n.contextInterceptor(r);var i=(0,jh.resolve)(r);return i}},t.prototype._deactivateIfSingleton=function(e){var n=this;if(e.activated)return(0,it.isPromise)(e.cache)?e.cache.then(function(r){return n._deactivate(e,r)}):this._deactivate(e,e.cache)},t.prototype._deactivateSingletons=function(e){for(var n=0,r=e;n<r.length;n++){var i=r[n],o=this._deactivateIfSingleton(i);if((0,it.isPromise)(o))throw new Error(_e.ASYNC_UNBIND_REQUIRED)}},t.prototype._deactivateSingletonsAsync=function(e){return fe(this,void 0,void 0,function(){var n=this;return de(this,function(r){switch(r.label){case 0:return[4,Promise.all(e.map(function(i){return n._deactivateIfSingleton(i)}))];case 1:return r.sent(),[2]}})})},t.prototype._propagateContainerDeactivationThenBindingAndPreDestroy=function(e,n,r){return this.parent?this._deactivate.bind(this.parent)(e,n):this._bindingDeactivationAndPreDestroy(e,n,r)},t.prototype._propagateContainerDeactivationThenBindingAndPreDestroyAsync=function(e,n,r){return fe(this,void 0,void 0,function(){return de(this,function(i){switch(i.label){case 0:return this.parent?[4,this._deactivate.bind(this.parent)(e,n)]:[3,2];case 1:return i.sent(),[3,4];case 2:return[4,this._bindingDeactivationAndPreDestroyAsync(e,n,r)];case 3:i.sent(),i.label=4;case 4:return[2]}})})},t.prototype._removeServiceFromDictionary=function(e){try{this._bindingDictionary.remove(e)}catch{throw new Error(_e.CANNOT_UNBIND+" "+(0,qh.getServiceIdentifierAsString)(e))}},t.prototype._bindingDeactivationAndPreDestroy=function(e,n,r){var i=this;if(typeof e.onDeactivation=="function"){var o=e.onDeactivation(n);if((0,it.isPromise)(o))return o.then(function(){return i._preDestroy(r,n)})}return this._preDestroy(r,n)},t.prototype._bindingDeactivationAndPreDestroyAsync=function(e,n,r){return fe(this,void 0,void 0,function(){return de(this,function(i){switch(i.label){case 0:return typeof e.onDeactivation!="function"?[3,2]:[4,e.onDeactivation(n)];case 1:i.sent(),i.label=2;case 2:return[4,this._preDestroy(r,n)];case 3:return i.sent(),[2]}})})},t}();$.Container=Vh});var ia=g(Tt=>{"use strict";Object.defineProperty(Tt,"__esModule",{value:!0});Tt.AsyncContainerModule=Tt.ContainerModule=void 0;var ra=Je(),$h=function(){function t(e){this.id=(0,ra.id)(),this.registry=e}return t}();Tt.ContainerModule=$h;var Hh=function(){function t(e){this.id=(0,ra.id)(),this.registry=e}return t}();Tt.AsyncContainerModule=Hh});var oa=g(Yn=>{"use strict";Object.defineProperty(Yn,"__esModule",{value:!0});Yn.getFirstArrayDuplicate=void 0;function Kh(t){for(var e=new Set,n=0,r=t;n<r.length;n++){var i=r[n];if(e.has(i))return i;e.add(i)}}Yn.getFirstArrayDuplicate=Kh});var We=g(X=>{"use strict";var Yh=X&&X.__createBinding||(Object.create?function(t,e,n,r){r===void 0&&(r=n),Object.defineProperty(t,r,{enumerable:!0,get:function(){return e[n]}})}:function(t,e,n,r){r===void 0&&(r=n),t[r]=e[n]}),zh=X&&X.__setModuleDefault||(Object.create?function(t,e){Object.defineProperty(t,"default",{enumerable:!0,value:e})}:function(t,e){t.default=e}),aa=X&&X.__importStar||function(t){if(t&&t.__esModule)return t;var e={};if(t!=null)for(var n in t)n!=="default"&&Object.prototype.hasOwnProperty.call(t,n)&&Yh(e,t,n);return zh(e,t),e};Object.defineProperty(X,"__esModule",{value:!0});X.createTaggedDecorator=X.tagProperty=X.tagParameter=X.decorate=void 0;var zn=aa(Y()),ua=aa(U()),Jh=oa();function Xh(t){return t.prototype!==void 0}function Qh(t){if(t!==void 0)throw new Error(zn.INVALID_DECORATOR_OPERATION)}function ca(t,e,n,r){Qh(e),fa(ua.TAGGED,t,n.toString(),r)}X.tagParameter=ca;function la(t,e,n){if(Xh(t))throw new Error(zn.INVALID_DECORATOR_OPERATION);fa(ua.TAGGED_PROP,t.constructor,e,n)}X.tagProperty=la;function Zh(t){var e=[];if(Array.isArray(t)){e=t;var n=(0,Jh.getFirstArrayDuplicate)(e.map(function(r){return r.key}));if(n!==void 0)throw new Error(zn.DUPLICATED_METADATA+" "+n.toString())}else e=[t];return e}function fa(t,e,n,r){var i=Zh(r),o={};Reflect.hasOwnMetadata(t,e)&&(o=Reflect.getMetadata(t,e));var s=o[n];if(s===void 0)s=[];else for(var u=function(p){if(i.some(function(m){return m.key===p.key}))throw new Error(zn.DUPLICATED_METADATA+" "+p.key.toString())},c=0,a=s;c<a.length;c++){var l=a[c];u(l)}s.push.apply(s,i),o[n]=s,Reflect.defineMetadata(t,o,e)}function eg(t){return function(e,n,r){typeof r=="number"?ca(e,n,r,t):la(e,n,t)}}X.createTaggedDecorator=eg;function sa(t,e){Reflect.decorate(t,e)}function tg(t,e){return function(n,r){e(n,r,t)}}function ng(t,e,n){typeof n=="number"?sa([tg(n,t)],e):typeof n=="string"?Reflect.decorate([t],e,n):sa([t],e)}X.decorate=ng});var pa=g(Oe=>{"use strict";var rg=Oe&&Oe.__createBinding||(Object.create?function(t,e,n,r){r===void 0&&(r=n),Object.defineProperty(t,r,{enumerable:!0,get:function(){return e[n]}})}:function(t,e,n,r){r===void 0&&(r=n),t[r]=e[n]}),ig=Oe&&Oe.__setModuleDefault||(Object.create?function(t,e){Object.defineProperty(t,"default",{enumerable:!0,value:e})}:function(t,e){t.default=e}),da=Oe&&Oe.__importStar||function(t){if(t&&t.__esModule)return t;var e={};if(t!=null)for(var n in t)n!=="default"&&Object.prototype.hasOwnProperty.call(t,n)&&rg(e,t,n);return ig(e,t),e};Object.defineProperty(Oe,"__esModule",{value:!0});Oe.injectable=void 0;var og=da(Y()),Ei=da(U());function sg(){return function(t){if(Reflect.hasOwnMetadata(Ei.PARAM_TYPES,t))throw new Error(og.DUPLICATED_INJECTABLE_DECORATOR);var e=Reflect.getMetadata(Ei.DESIGN_PARAM_TYPES,t)||[];return Reflect.defineMetadata(Ei.PARAM_TYPES,e,t),t}}Oe.injectable=sg});var ha=g(Jn=>{"use strict";Object.defineProperty(Jn,"__esModule",{value:!0});Jn.tagged=void 0;var ag=ye(),ug=We();function cg(t,e){return(0,ug.createTaggedDecorator)(new ag.Metadata(t,e))}Jn.tagged=cg});var ga=g(Ne=>{"use strict";var lg=Ne&&Ne.__createBinding||(Object.create?function(t,e,n,r){r===void 0&&(r=n),Object.defineProperty(t,r,{enumerable:!0,get:function(){return e[n]}})}:function(t,e,n,r){r===void 0&&(r=n),t[r]=e[n]}),fg=Ne&&Ne.__setModuleDefault||(Object.create?function(t,e){Object.defineProperty(t,"default",{enumerable:!0,value:e})}:function(t,e){t.default=e}),dg=Ne&&Ne.__importStar||function(t){if(t&&t.__esModule)return t;var e={};if(t!=null)for(var n in t)n!=="default"&&Object.prototype.hasOwnProperty.call(t,n)&&lg(e,t,n);return fg(e,t),e};Object.defineProperty(Ne,"__esModule",{value:!0});Ne.named=void 0;var pg=dg(U()),hg=ye(),gg=We();function mg(t){return(0,gg.createTaggedDecorator)(new hg.Metadata(pg.NAMED_TAG,t))}Ne.named=mg});var xi=g(Xn=>{"use strict";Object.defineProperty(Xn,"__esModule",{value:!0});Xn.injectBase=void 0;var yg=Y(),_g=ye(),vg=We();function bg(t){return function(e){return function(n,r,i){if(e===void 0){var o=typeof n=="function"?n.name:n.constructor.name;throw new Error((0,yg.UNDEFINED_INJECT_ANNOTATION)(o))}return(0,vg.createTaggedDecorator)(new _g.Metadata(t,e))(n,r,i)}}}Xn.injectBase=bg});var ma=g(Pe=>{"use strict";var wg=Pe&&Pe.__createBinding||(Object.create?function(t,e,n,r){r===void 0&&(r=n),Object.defineProperty(t,r,{enumerable:!0,get:function(){return e[n]}})}:function(t,e,n,r){r===void 0&&(r=n),t[r]=e[n]}),Sg=Pe&&Pe.__setModuleDefault||(Object.create?function(t,e){Object.defineProperty(t,"default",{enumerable:!0,value:e})}:function(t,e){t.default=e}),Tg=Pe&&Pe.__importStar||function(t){if(t&&t.__esModule)return t;var e={};if(t!=null)for(var n in t)n!=="default"&&Object.prototype.hasOwnProperty.call(t,n)&&wg(e,t,n);return Sg(e,t),e};Object.defineProperty(Pe,"__esModule",{value:!0});Pe.inject=void 0;var Eg=Tg(U()),xg=xi(),Ag=(0,xg.injectBase)(Eg.INJECT_TAG);Pe.inject=Ag});var ya=g(Re=>{"use strict";var Cg=Re&&Re.__createBinding||(Object.create?function(t,e,n,r){r===void 0&&(r=n),Object.defineProperty(t,r,{enumerable:!0,get:function(){return e[n]}})}:function(t,e,n,r){r===void 0&&(r=n),t[r]=e[n]}),Ig=Re&&Re.__setModuleDefault||(Object.create?function(t,e){Object.defineProperty(t,"default",{enumerable:!0,value:e})}:function(t,e){t.default=e}),Og=Re&&Re.__importStar||function(t){if(t&&t.__esModule)return t;var e={};if(t!=null)for(var n in t)n!=="default"&&Object.prototype.hasOwnProperty.call(t,n)&&Cg(e,t,n);return Ig(e,t),e};Object.defineProperty(Re,"__esModule",{value:!0});Re.optional=void 0;var Ng=Og(U()),Pg=ye(),Rg=We();function Mg(){return(0,Rg.createTaggedDecorator)(new Pg.Metadata(Ng.OPTIONAL_TAG,!0))}Re.optional=Mg});var _a=g(Me=>{"use strict";var Dg=Me&&Me.__createBinding||(Object.create?function(t,e,n,r){r===void 0&&(r=n),Object.defineProperty(t,r,{enumerable:!0,get:function(){return e[n]}})}:function(t,e,n,r){r===void 0&&(r=n),t[r]=e[n]}),kg=Me&&Me.__setModuleDefault||(Object.create?function(t,e){Object.defineProperty(t,"default",{enumerable:!0,value:e})}:function(t,e){t.default=e}),Bg=Me&&Me.__importStar||function(t){if(t&&t.__esModule)return t;var e={};if(t!=null)for(var n in t)n!=="default"&&Object.prototype.hasOwnProperty.call(t,n)&&Dg(e,t,n);return kg(e,t),e};Object.defineProperty(Me,"__esModule",{value:!0});Me.unmanaged=void 0;var Lg=Bg(U()),jg=ye(),Fg=We();function Ug(){return function(t,e,n){var r=new jg.Metadata(Lg.UNMANAGED_TAG,!0);(0,Fg.tagParameter)(t,e,n,r)}}Me.unmanaged=Ug});var va=g(De=>{"use strict";var qg=De&&De.__createBinding||(Object.create?function(t,e,n,r){r===void 0&&(r=n),Object.defineProperty(t,r,{enumerable:!0,get:function(){return e[n]}})}:function(t,e,n,r){r===void 0&&(r=n),t[r]=e[n]}),Gg=De&&De.__setModuleDefault||(Object.create?function(t,e){Object.defineProperty(t,"default",{enumerable:!0,value:e})}:function(t,e){t.default=e}),Wg=De&&De.__importStar||function(t){if(t&&t.__esModule)return t;var e={};if(t!=null)for(var n in t)n!=="default"&&Object.prototype.hasOwnProperty.call(t,n)&&qg(e,t,n);return Gg(e,t),e};Object.defineProperty(De,"__esModule",{value:!0});De.multiInject=void 0;var Vg=Wg(U()),$g=xi(),Hg=(0,$g.injectBase)(Vg.MULTI_INJECT_TAG);De.multiInject=Hg});var ba=g(ke=>{"use strict";var Kg=ke&&ke.__createBinding||(Object.create?function(t,e,n,r){r===void 0&&(r=n),Object.defineProperty(t,r,{enumerable:!0,get:function(){return e[n]}})}:function(t,e,n,r){r===void 0&&(r=n),t[r]=e[n]}),Yg=ke&&ke.__setModuleDefault||(Object.create?function(t,e){Object.defineProperty(t,"default",{enumerable:!0,value:e})}:function(t,e){t.default=e}),zg=ke&&ke.__importStar||function(t){if(t&&t.__esModule)return t;var e={};if(t!=null)for(var n in t)n!=="default"&&Object.prototype.hasOwnProperty.call(t,n)&&Kg(e,t,n);return Yg(e,t),e};Object.defineProperty(ke,"__esModule",{value:!0});ke.targetName=void 0;var Jg=zg(U()),Xg=ye(),Qg=We();function Zg(t){return function(e,n,r){var i=new Xg.Metadata(Jg.NAME_TAG,t);(0,Qg.tagParameter)(e,n,r,i)}}ke.targetName=Zg});var Ai=g(Qn=>{"use strict";Object.defineProperty(Qn,"__esModule",{value:!0});Qn.propertyEventDecorator=void 0;var em=ye();function tm(t,e){return function(){return function(n,r){var i=new em.Metadata(t,r);if(Reflect.hasOwnMetadata(t,n.constructor))throw new Error(e);Reflect.defineMetadata(t,i,n.constructor)}}}Qn.propertyEventDecorator=tm});var Sa=g(Be=>{"use strict";var nm=Be&&Be.__createBinding||(Object.create?function(t,e,n,r){r===void 0&&(r=n),Object.defineProperty(t,r,{enumerable:!0,get:function(){return e[n]}})}:function(t,e,n,r){r===void 0&&(r=n),t[r]=e[n]}),rm=Be&&Be.__setModuleDefault||(Object.create?function(t,e){Object.defineProperty(t,"default",{enumerable:!0,value:e})}:function(t,e){t.default=e}),wa=Be&&Be.__importStar||function(t){if(t&&t.__esModule)return t;var e={};if(t!=null)for(var n in t)n!=="default"&&Object.prototype.hasOwnProperty.call(t,n)&&nm(e,t,n);return rm(e,t),e};Object.defineProperty(Be,"__esModule",{value:!0});Be.postConstruct=void 0;var im=wa(Y()),om=wa(U()),sm=Ai(),am=(0,sm.propertyEventDecorator)(om.POST_CONSTRUCT,im.MULTIPLE_POST_CONSTRUCT_METHODS);Be.postConstruct=am});var Ea=g(Le=>{"use strict";var um=Le&&Le.__createBinding||(Object.create?function(t,e,n,r){r===void 0&&(r=n),Object.defineProperty(t,r,{enumerable:!0,get:function(){return e[n]}})}:function(t,e,n,r){r===void 0&&(r=n),t[r]=e[n]}),cm=Le&&Le.__setModuleDefault||(Object.create?function(t,e){Object.defineProperty(t,"default",{enumerable:!0,value:e})}:function(t,e){t.default=e}),Ta=Le&&Le.__importStar||function(t){if(t&&t.__esModule)return t;var e={};if(t!=null)for(var n in t)n!=="default"&&Object.prototype.hasOwnProperty.call(t,n)&&um(e,t,n);return cm(e,t),e};Object.defineProperty(Le,"__esModule",{value:!0});Le.preDestroy=void 0;var lm=Ta(Y()),fm=Ta(U()),dm=Ai(),pm=(0,dm.propertyEventDecorator)(fm.PRE_DESTROY,lm.MULTIPLE_PRE_DESTROY_METHODS);Le.preDestroy=pm});var xa=g(Zn=>{"use strict";Object.defineProperty(Zn,"__esModule",{value:!0});Zn.interfaces=void 0;var Ci;Ci||(Ci={});Zn.interfaces=Ci});var Oi=g(v=>{"use strict";var hm=v&&v.__createBinding||(Object.create?function(t,e,n,r){r===void 0&&(r=n),Object.defineProperty(t,r,{enumerable:!0,get:function(){return e[n]}})}:function(t,e,n,r){r===void 0&&(r=n),t[r]=e[n]}),gm=v&&v.__setModuleDefault||(Object.create?function(t,e){Object.defineProperty(t,"default",{enumerable:!0,value:e})}:function(t,e){t.default=e}),mm=v&&v.__importStar||function(t){if(t&&t.__esModule)return t;var e={};if(t!=null)for(var n in t)n!=="default"&&Object.prototype.hasOwnProperty.call(t,n)&&hm(e,t,n);return gm(e,t),e};Object.defineProperty(v,"__esModule",{value:!0});v.multiBindToService=v.getServiceIdentifierAsString=v.typeConstraint=v.namedConstraint=v.taggedConstraint=v.traverseAncerstors=v.decorate=v.interfaces=v.id=v.MetadataReader=v.preDestroy=v.postConstruct=v.targetName=v.multiInject=v.unmanaged=v.optional=v.LazyServiceIdentifer=v.LazyServiceIdentifier=v.inject=v.named=v.tagged=v.injectable=v.createTaggedDecorator=v.ContainerModule=v.AsyncContainerModule=v.TargetTypeEnum=v.BindingTypeEnum=v.BindingScopeEnum=v.Container=v.METADATA_KEY=void 0;var ym=mm(U());v.METADATA_KEY=ym;var _m=na();Object.defineProperty(v,"Container",{enumerable:!0,get:function(){return _m.Container}});var Ii=ce();Object.defineProperty(v,"BindingScopeEnum",{enumerable:!0,get:function(){return Ii.BindingScopeEnum}});Object.defineProperty(v,"BindingTypeEnum",{enumerable:!0,get:function(){return Ii.BindingTypeEnum}});Object.defineProperty(v,"TargetTypeEnum",{enumerable:!0,get:function(){return Ii.TargetTypeEnum}});var Aa=ia();Object.defineProperty(v,"AsyncContainerModule",{enumerable:!0,get:function(){return Aa.AsyncContainerModule}});Object.defineProperty(v,"ContainerModule",{enumerable:!0,get:function(){return Aa.ContainerModule}});var vm=We();Object.defineProperty(v,"createTaggedDecorator",{enumerable:!0,get:function(){return vm.createTaggedDecorator}});var bm=pa();Object.defineProperty(v,"injectable",{enumerable:!0,get:function(){return bm.injectable}});var wm=ha();Object.defineProperty(v,"tagged",{enumerable:!0,get:function(){return wm.tagged}});var Sm=ga();Object.defineProperty(v,"named",{enumerable:!0,get:function(){return Sm.named}});var Tm=ma();Object.defineProperty(v,"inject",{enumerable:!0,get:function(){return Tm.inject}});var Em=On();Object.defineProperty(v,"LazyServiceIdentifier",{enumerable:!0,get:function(){return Em.LazyServiceIdentifier}});var xm=On();Object.defineProperty(v,"LazyServiceIdentifer",{enumerable:!0,get:function(){return xm.LazyServiceIdentifier}});var Am=ya();Object.defineProperty(v,"optional",{enumerable:!0,get:function(){return Am.optional}});var Cm=_a();Object.defineProperty(v,"unmanaged",{enumerable:!0,get:function(){return Cm.unmanaged}});var Im=va();Object.defineProperty(v,"multiInject",{enumerable:!0,get:function(){return Im.multiInject}});var Om=ba();Object.defineProperty(v,"targetName",{enumerable:!0,get:function(){return Om.targetName}});var Nm=Sa();Object.defineProperty(v,"postConstruct",{enumerable:!0,get:function(){return Nm.postConstruct}});var Pm=Ea();Object.defineProperty(v,"preDestroy",{enumerable:!0,get:function(){return Pm.preDestroy}});var Rm=ei();Object.defineProperty(v,"MetadataReader",{enumerable:!0,get:function(){return Rm.MetadataReader}});var Mm=Je();Object.defineProperty(v,"id",{enumerable:!0,get:function(){return Mm.id}});var Dm=xa();Object.defineProperty(v,"interfaces",{enumerable:!0,get:function(){return Dm.interfaces}});var km=We();Object.defineProperty(v,"decorate",{enumerable:!0,get:function(){return km.decorate}});var er=vi();Object.defineProperty(v,"traverseAncerstors",{enumerable:!0,get:function(){return er.traverseAncerstors}});Object.defineProperty(v,"taggedConstraint",{enumerable:!0,get:function(){return er.taggedConstraint}});Object.defineProperty(v,"namedConstraint",{enumerable:!0,get:function(){return er.namedConstraint}});Object.defineProperty(v,"typeConstraint",{enumerable:!0,get:function(){return er.typeConstraint}});var Bm=tt();Object.defineProperty(v,"getServiceIdentifierAsString",{enumerable:!0,get:function(){return Bm.getServiceIdentifierAsString}});var Lm=di();Object.defineProperty(v,"multiBindToService",{enumerable:!0,get:function(){return Lm.multiBindToService}})});var Ua=g((pT,Fa)=>{"use strict";var{Duplex:zm}=I("stream");function La(t){t.emit("close")}function Jm(){!this.destroyed&&this._writableState.finished&&this.destroy()}function ja(t){this.removeListener("error",ja),this.destroy(),this.listenerCount("error")===0&&this.emit("error",t)}function Xm(t,e){let n=!0,r=new zm({...e,autoDestroy:!1,emitClose:!1,objectMode:!1,writableObjectMode:!1});return t.on("message",function(o,s){let u=!s&&r._readableState.objectMode?o.toString():o;r.push(u)||t.pause()}),t.once("error",function(o){r.destroyed||(n=!1,r.destroy(o))}),t.once("close",function(){r.destroyed||r.push(null)}),r._destroy=function(i,o){if(t.readyState===t.CLOSED){o(i),process.nextTick(La,r);return}let s=!1;t.once("error",function(c){s=!0,o(c)}),t.once("close",function(){s||o(i),process.nextTick(La,r)}),n&&t.terminate()},r._final=function(i){if(t.readyState===t.CONNECTING){t.once("open",function(){r._final(i)});return}t._socket!==null&&(t._socket._writableState.finished?(i(),r._readableState.endEmitted&&r.destroy()):(t._socket.once("finish",function(){i()}),t.close()))},r._read=function(){t.isPaused&&t.resume()},r._write=function(i,o,s){if(t.readyState===t.CONNECTING){t.once("open",function(){r._write(i,o,s)});return}t.send(i,s)},r.on("end",Jm),r.on("error",ja),r}Fa.exports=Xm});var Xe=g((hT,qa)=>{"use strict";qa.exports={BINARY_TYPES:["nodebuffer","arraybuffer","fragments"],EMPTY_BUFFER:Buffer.alloc(0),GUID:"258EAFA5-E914-47DA-95CA-C5AB0DC85B11",kForOnEventAttribute:Symbol("kIsForOnEventAttribute"),kListener:Symbol("kListener"),kStatusCode:Symbol("status-code"),kWebSocket:Symbol("websocket"),NOOP:()=>{}}});var Yt=g((gT,ar)=>{"use strict";var{EMPTY_BUFFER:Qm}=Xe(),ki=Buffer[Symbol.species];function Zm(t,e){if(t.length===0)return Qm;if(t.length===1)return t[0];let n=Buffer.allocUnsafe(e),r=0;for(let i=0;i<t.length;i++){let o=t[i];n.set(o,r),r+=o.length}return r<e?new ki(n.buffer,n.byteOffset,r):n}function Ga(t,e,n,r,i){for(let o=0;o<i;o++)n[r+o]=t[o]^e[o&3]}function Wa(t,e){for(let n=0;n<t.length;n++)t[n]^=e[n&3]}function ey(t){return t.length===t.buffer.byteLength?t.buffer:t.buffer.slice(t.byteOffset,t.byteOffset+t.length)}function Bi(t){if(Bi.readOnly=!0,Buffer.isBuffer(t))return t;let e;return t instanceof ArrayBuffer?e=new ki(t):ArrayBuffer.isView(t)?e=new ki(t.buffer,t.byteOffset,t.byteLength):(e=Buffer.from(t),Bi.readOnly=!1),e}ar.exports={concat:Zm,mask:Ga,toArrayBuffer:ey,toBuffer:Bi,unmask:Wa};if(!process.env.WS_NO_BUFFER_UTIL)try{let t=I("bufferutil");ar.exports.mask=function(e,n,r,i,o){o<48?Ga(e,n,r,i,o):t.mask(e,n,r,i,o)},ar.exports.unmask=function(e,n){e.length<32?Wa(e,n):t.unmask(e,n)}}catch{}});var Ha=g((mT,$a)=>{"use strict";var Va=Symbol("kDone"),Li=Symbol("kRun"),ji=class{constructor(e){this[Va]=()=>{this.pending--,this[Li]()},this.concurrency=e||1/0,this.jobs=[],this.pending=0}add(e){this.jobs.push(e),this[Li]()}[Li](){if(this.pending!==this.concurrency&&this.jobs.length){let e=this.jobs.shift();this.pending++,e(this[Va])}}};$a.exports=ji});var Xt=g((yT,Ja)=>{"use strict";var zt=I("zlib"),Ka=Yt(),ty=Ha(),{kStatusCode:Ya}=Xe(),ny=Buffer[Symbol.species],ry=Buffer.from([0,0,255,255]),lr=Symbol("permessage-deflate"),Ve=Symbol("total-length"),Jt=Symbol("callback"),Qe=Symbol("buffers"),cr=Symbol("error"),ur,Fi=class{constructor(e,n,r){if(this._maxPayload=r|0,this._options=e||{},this._threshold=this._options.threshold!==void 0?this._options.threshold:1024,this._isServer=!!n,this._deflate=null,this._inflate=null,this.params=null,!ur){let i=this._options.concurrencyLimit!==void 0?this._options.concurrencyLimit:10;ur=new ty(i)}}static get extensionName(){return"permessage-deflate"}offer(){let e={};return this._options.serverNoContextTakeover&&(e.server_no_context_takeover=!0),this._options.clientNoContextTakeover&&(e.client_no_context_takeover=!0),this._options.serverMaxWindowBits&&(e.server_max_window_bits=this._options.serverMaxWindowBits),this._options.clientMaxWindowBits?e.client_max_window_bits=this._options.clientMaxWindowBits:this._options.clientMaxWindowBits==null&&(e.client_max_window_bits=!0),e}accept(e){return e=this.normalizeParams(e),this.params=this._isServer?this.acceptAsServer(e):this.acceptAsClient(e),this.params}cleanup(){if(this._inflate&&(this._inflate.close(),this._inflate=null),this._deflate){let e=this._deflate[Jt];this._deflate.close(),this._deflate=null,e&&e(new Error("The deflate stream was closed while data was being processed"))}}acceptAsServer(e){let n=this._options,r=e.find(i=>!(n.serverNoContextTakeover===!1&&i.server_no_context_takeover||i.server_max_window_bits&&(n.serverMaxWindowBits===!1||typeof n.serverMaxWindowBits=="number"&&n.serverMaxWindowBits>i.server_max_window_bits)||typeof n.clientMaxWindowBits=="number"&&!i.client_max_window_bits));if(!r)throw new Error("None of the extension offers can be accepted");return n.serverNoContextTakeover&&(r.server_no_context_takeover=!0),n.clientNoContextTakeover&&(r.client_no_context_takeover=!0),typeof n.serverMaxWindowBits=="number"&&(r.server_max_window_bits=n.serverMaxWindowBits),typeof n.clientMaxWindowBits=="number"?r.client_max_window_bits=n.clientMaxWindowBits:(r.client_max_window_bits===!0||n.clientMaxWindowBits===!1)&&delete r.client_max_window_bits,r}acceptAsClient(e){let n=e[0];if(this._options.clientNoContextTakeover===!1&&n.client_no_context_takeover)throw new Error('Unexpected parameter "client_no_context_takeover"');if(!n.client_max_window_bits)typeof this._options.clientMaxWindowBits=="number"&&(n.client_max_window_bits=this._options.clientMaxWindowBits);else if(this._options.clientMaxWindowBits===!1||typeof this._options.clientMaxWindowBits=="number"&&n.client_max_window_bits>this._options.clientMaxWindowBits)throw new Error('Unexpected or invalid parameter "client_max_window_bits"');return n}normalizeParams(e){return e.forEach(n=>{Object.keys(n).forEach(r=>{let i=n[r];if(i.length>1)throw new Error(`Parameter "${r}" must have only a single value`);if(i=i[0],r==="client_max_window_bits"){if(i!==!0){let o=+i;if(!Number.isInteger(o)||o<8||o>15)throw new TypeError(`Invalid value for parameter "${r}": ${i}`);i=o}else if(!this._isServer)throw new TypeError(`Invalid value for parameter "${r}": ${i}`)}else if(r==="server_max_window_bits"){let o=+i;if(!Number.isInteger(o)||o<8||o>15)throw new TypeError(`Invalid value for parameter "${r}": ${i}`);i=o}else if(r==="client_no_context_takeover"||r==="server_no_context_takeover"){if(i!==!0)throw new TypeError(`Invalid value for parameter "${r}": ${i}`)}else throw new Error(`Unknown parameter "${r}"`);n[r]=i})}),e}decompress(e,n,r){ur.add(i=>{this._decompress(e,n,(o,s)=>{i(),r(o,s)})})}compress(e,n,r){ur.add(i=>{this._compress(e,n,(o,s)=>{i(),r(o,s)})})}_decompress(e,n,r){let i=this._isServer?"client":"server";if(!this._inflate){let o=`${i}_max_window_bits`,s=typeof this.params[o]!="number"?zt.Z_DEFAULT_WINDOWBITS:this.params[o];this._inflate=zt.createInflateRaw({...this._options.zlibInflateOptions,windowBits:s}),this._inflate[lr]=this,this._inflate[Ve]=0,this._inflate[Qe]=[],this._inflate.on("error",oy),this._inflate.on("data",za)}this._inflate[Jt]=r,this._inflate.write(e),n&&this._inflate.write(ry),this._inflate.flush(()=>{let o=this._inflate[cr];if(o){this._inflate.close(),this._inflate=null,r(o);return}let s=Ka.concat(this._inflate[Qe],this._inflate[Ve]);this._inflate._readableState.endEmitted?(this._inflate.close(),this._inflate=null):(this._inflate[Ve]=0,this._inflate[Qe]=[],n&&this.params[`${i}_no_context_takeover`]&&this._inflate.reset()),r(null,s)})}_compress(e,n,r){let i=this._isServer?"server":"client";if(!this._deflate){let o=`${i}_max_window_bits`,s=typeof this.params[o]!="number"?zt.Z_DEFAULT_WINDOWBITS:this.params[o];this._deflate=zt.createDeflateRaw({...this._options.zlibDeflateOptions,windowBits:s}),this._deflate[Ve]=0,this._deflate[Qe]=[],this._deflate.on("data",iy)}this._deflate[Jt]=r,this._deflate.write(e),this._deflate.flush(zt.Z_SYNC_FLUSH,()=>{if(!this._deflate)return;let o=Ka.concat(this._deflate[Qe],this._deflate[Ve]);n&&(o=new ny(o.buffer,o.byteOffset,o.length-4)),this._deflate[Jt]=null,this._deflate[Ve]=0,this._deflate[Qe]=[],n&&this.params[`${i}_no_context_takeover`]&&this._deflate.reset(),r(null,o)})}};Ja.exports=Fi;function iy(t){this[Qe].push(t),this[Ve]+=t.length}function za(t){if(this[Ve]+=t.length,this[lr]._maxPayload<1||this[Ve]<=this[lr]._maxPayload){this[Qe].push(t);return}this[cr]=new RangeError("Max payload size exceeded"),this[cr].code="WS_ERR_UNSUPPORTED_MESSAGE_LENGTH",this[cr][Ya]=1009,this.removeListener("data",za),this.reset()}function oy(t){this[lr]._inflate=null,t[Ya]=1007,this[Jt](t)}});var Qt=g((_T,fr)=>{"use strict";var{isUtf8:Xa}=I("buffer"),sy=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,1,1,1,1,1,0,0,1,1,0,1,1,0,1,1,1,1,1,1,1,1,1,1,0,0,0,0,0,0,0,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,0,0,0,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,0,1,0,1,0];function ay(t){return t>=1e3&&t<=1014&&t!==1004&&t!==1005&&t!==1006||t>=3e3&&t<=4999}function Ui(t){let e=t.length,n=0;for(;n<e;)if((t[n]&128)===0)n++;else if((t[n]&224)===192){if(n+1===e||(t[n+1]&192)!==128||(t[n]&254)===192)return!1;n+=2}else if((t[n]&240)===224){if(n+2>=e||(t[n+1]&192)!==128||(t[n+2]&192)!==128||t[n]===224&&(t[n+1]&224)===128||t[n]===237&&(t[n+1]&224)===160)return!1;n+=3}else if((t[n]&248)===240){if(n+3>=e||(t[n+1]&192)!==128||(t[n+2]&192)!==128||(t[n+3]&192)!==128||t[n]===240&&(t[n+1]&240)===128||t[n]===244&&t[n+1]>143||t[n]>244)return!1;n+=4}else return!1;return!0}fr.exports={isValidStatusCode:ay,isValidUTF8:Ui,tokenChars:sy};if(Xa)fr.exports.isValidUTF8=function(t){return t.length<24?Ui(t):Xa(t)};else if(!process.env.WS_NO_UTF_8_VALIDATE)try{let t=I("utf-8-validate");fr.exports.isValidUTF8=function(e){return e.length<32?Ui(e):t(e)}}catch{}});var $i=g((vT,iu)=>{"use strict";var{Writable:uy}=I("stream"),Qa=Xt(),{BINARY_TYPES:cy,EMPTY_BUFFER:Za,kStatusCode:ly,kWebSocket:fy}=Xe(),{concat:qi,toArrayBuffer:dy,unmask:py}=Yt(),{isValidStatusCode:hy,isValidUTF8:eu}=Qt(),dr=Buffer[Symbol.species],he=0,tu=1,nu=2,ru=3,Gi=4,Wi=5,pr=6,Vi=class extends uy{constructor(e={}){super(),this._allowSynchronousEvents=e.allowSynchronousEvents!==void 0?e.allowSynchronousEvents:!0,this._binaryType=e.binaryType||cy[0],this._extensions=e.extensions||{},this._isServer=!!e.isServer,this._maxPayload=e.maxPayload|0,this._skipUTF8Validation=!!e.skipUTF8Validation,this[fy]=void 0,this._bufferedBytes=0,this._buffers=[],this._compressed=!1,this._payloadLength=0,this._mask=void 0,this._fragmented=0,this._masked=!1,this._fin=!1,this._opcode=0,this._totalPayloadLength=0,this._messageLength=0,this._fragments=[],this._errored=!1,this._loop=!1,this._state=he}_write(e,n,r){if(this._opcode===8&&this._state==he)return r();this._bufferedBytes+=e.length,this._buffers.push(e),this.startLoop(r)}consume(e){if(this._bufferedBytes-=e,e===this._buffers[0].length)return this._buffers.shift();if(e<this._buffers[0].length){let r=this._buffers[0];return this._buffers[0]=new dr(r.buffer,r.byteOffset+e,r.length-e),new dr(r.buffer,r.byteOffset,e)}let n=Buffer.allocUnsafe(e);do{let r=this._buffers[0],i=n.length-e;e>=r.length?n.set(this._buffers.shift(),i):(n.set(new Uint8Array(r.buffer,r.byteOffset,e),i),this._buffers[0]=new dr(r.buffer,r.byteOffset+e,r.length-e)),e-=r.length}while(e>0);return n}startLoop(e){this._loop=!0;do switch(this._state){case he:this.getInfo(e);break;case tu:this.getPayloadLength16(e);break;case nu:this.getPayloadLength64(e);break;case ru:this.getMask();break;case Gi:this.getData(e);break;case Wi:case pr:this._loop=!1;return}while(this._loop);this._errored||e()}getInfo(e){if(this._bufferedBytes<2){this._loop=!1;return}let n=this.consume(2);if((n[0]&48)!==0){let i=this.createError(RangeError,"RSV2 and RSV3 must be clear",!0,1002,"WS_ERR_UNEXPECTED_RSV_2_3");e(i);return}let r=(n[0]&64)===64;if(r&&!this._extensions[Qa.extensionName]){let i=this.createError(RangeError,"RSV1 must be clear",!0,1002,"WS_ERR_UNEXPECTED_RSV_1");e(i);return}if(this._fin=(n[0]&128)===128,this._opcode=n[0]&15,this._payloadLength=n[1]&127,this._opcode===0){if(r){let i=this.createError(RangeError,"RSV1 must be clear",!0,1002,"WS_ERR_UNEXPECTED_RSV_1");e(i);return}if(!this._fragmented){let i=this.createError(RangeError,"invalid opcode 0",!0,1002,"WS_ERR_INVALID_OPCODE");e(i);return}this._opcode=this._fragmented}else if(this._opcode===1||this._opcode===2){if(this._fragmented){let i=this.createError(RangeError,`invalid opcode ${this._opcode}`,!0,1002,"WS_ERR_INVALID_OPCODE");e(i);return}this._compressed=r}else if(this._opcode>7&&this._opcode<11){if(!this._fin){let i=this.createError(RangeError,"FIN must be set",!0,1002,"WS_ERR_EXPECTED_FIN");e(i);return}if(r){let i=this.createError(RangeError,"RSV1 must be clear",!0,1002,"WS_ERR_UNEXPECTED_RSV_1");e(i);return}if(this._payloadLength>125||this._opcode===8&&this._payloadLength===1){let i=this.createError(RangeError,`invalid payload length ${this._payloadLength}`,!0,1002,"WS_ERR_INVALID_CONTROL_PAYLOAD_LENGTH");e(i);return}}else{let i=this.createError(RangeError,`invalid opcode ${this._opcode}`,!0,1002,"WS_ERR_INVALID_OPCODE");e(i);return}if(!this._fin&&!this._fragmented&&(this._fragmented=this._opcode),this._masked=(n[1]&128)===128,this._isServer){if(!this._masked){let i=this.createError(RangeError,"MASK must be set",!0,1002,"WS_ERR_EXPECTED_MASK");e(i);return}}else if(this._masked){let i=this.createError(RangeError,"MASK must be clear",!0,1002,"WS_ERR_UNEXPECTED_MASK");e(i);return}this._payloadLength===126?this._state=tu:this._payloadLength===127?this._state=nu:this.haveLength(e)}getPayloadLength16(e){if(this._bufferedBytes<2){this._loop=!1;return}this._payloadLength=this.consume(2).readUInt16BE(0),this.haveLength(e)}getPayloadLength64(e){if(this._bufferedBytes<8){this._loop=!1;return}let n=this.consume(8),r=n.readUInt32BE(0);if(r>Math.pow(2,21)-1){let i=this.createError(RangeError,"Unsupported WebSocket frame: payload length > 2^53 - 1",!1,1009,"WS_ERR_UNSUPPORTED_DATA_PAYLOAD_LENGTH");e(i);return}this._payloadLength=r*Math.pow(2,32)+n.readUInt32BE(4),this.haveLength(e)}haveLength(e){if(this._payloadLength&&this._opcode<8&&(this._totalPayloadLength+=this._payloadLength,this._totalPayloadLength>this._maxPayload&&this._maxPayload>0)){let n=this.createError(RangeError,"Max payload size exceeded",!1,1009,"WS_ERR_UNSUPPORTED_MESSAGE_LENGTH");e(n);return}this._masked?this._state=ru:this._state=Gi}getMask(){if(this._bufferedBytes<4){this._loop=!1;return}this._mask=this.consume(4),this._state=Gi}getData(e){let n=Za;if(this._payloadLength){if(this._bufferedBytes<this._payloadLength){this._loop=!1;return}n=this.consume(this._payloadLength),this._masked&&(this._mask[0]|this._mask[1]|this._mask[2]|this._mask[3])!==0&&py(n,this._mask)}if(this._opcode>7){this.controlMessage(n,e);return}if(this._compressed){this._state=Wi,this.decompress(n,e);return}n.length&&(this._messageLength=this._totalPayloadLength,this._fragments.push(n)),this.dataMessage(e)}decompress(e,n){this._extensions[Qa.extensionName].decompress(e,this._fin,(i,o)=>{if(i)return n(i);if(o.length){if(this._messageLength+=o.length,this._messageLength>this._maxPayload&&this._maxPayload>0){let s=this.createError(RangeError,"Max payload size exceeded",!1,1009,"WS_ERR_UNSUPPORTED_MESSAGE_LENGTH");n(s);return}this._fragments.push(o)}this.dataMessage(n),this._state===he&&this.startLoop(n)})}dataMessage(e){if(!this._fin){this._state=he;return}let n=this._messageLength,r=this._fragments;if(this._totalPayloadLength=0,this._messageLength=0,this._fragmented=0,this._fragments=[],this._opcode===2){let i;this._binaryType==="nodebuffer"?i=qi(r,n):this._binaryType==="arraybuffer"?i=dy(qi(r,n)):i=r,this._allowSynchronousEvents?(this.emit("message",i,!0),this._state=he):(this._state=pr,setImmediate(()=>{this.emit("message",i,!0),this._state=he,this.startLoop(e)}))}else{let i=qi(r,n);if(!this._skipUTF8Validation&&!eu(i)){let o=this.createError(Error,"invalid UTF-8 sequence",!0,1007,"WS_ERR_INVALID_UTF8");e(o);return}this._state===Wi||this._allowSynchronousEvents?(this.emit("message",i,!1),this._state=he):(this._state=pr,setImmediate(()=>{this.emit("message",i,!1),this._state=he,this.startLoop(e)}))}}controlMessage(e,n){if(this._opcode===8){if(e.length===0)this._loop=!1,this.emit("conclude",1005,Za),this.end();else{let r=e.readUInt16BE(0);if(!hy(r)){let o=this.createError(RangeError,`invalid status code ${r}`,!0,1002,"WS_ERR_INVALID_CLOSE_CODE");n(o);return}let i=new dr(e.buffer,e.byteOffset+2,e.length-2);if(!this._skipUTF8Validation&&!eu(i)){let o=this.createError(Error,"invalid UTF-8 sequence",!0,1007,"WS_ERR_INVALID_UTF8");n(o);return}this._loop=!1,this.emit("conclude",r,i),this.end()}this._state=he;return}this._allowSynchronousEvents?(this.emit(this._opcode===9?"ping":"pong",e),this._state=he):(this._state=pr,setImmediate(()=>{this.emit(this._opcode===9?"ping":"pong",e),this._state=he,this.startLoop(n)}))}createError(e,n,r,i,o){this._loop=!1,this._errored=!0;let s=new e(r?`Invalid WebSocket frame: ${n}`:n);return Error.captureStackTrace(s,this.createError),s.code=o,s[ly]=i,s}};iu.exports=Vi});var Ki=g((wT,au)=>{"use strict";var{Duplex:bT}=I("stream"),{randomFillSync:gy}=I("crypto"),ou=Xt(),{EMPTY_BUFFER:my}=Xe(),{isValidStatusCode:yy}=Qt(),{mask:su,toBuffer:At}=Yt(),ve=Symbol("kByteLength"),_y=Buffer.alloc(4),hr=8*1024,ot,Ct=hr,Hi=class t{constructor(e,n,r){this._extensions=n||{},r&&(this._generateMask=r,this._maskBuffer=Buffer.alloc(4)),this._socket=e,this._firstFragment=!0,this._compress=!1,this._bufferedBytes=0,this._deflating=!1,this._queue=[]}static frame(e,n){let r,i=!1,o=2,s=!1;n.mask&&(r=n.maskBuffer||_y,n.generateMask?n.generateMask(r):(Ct===hr&&(ot===void 0&&(ot=Buffer.alloc(hr)),gy(ot,0,hr),Ct=0),r[0]=ot[Ct++],r[1]=ot[Ct++],r[2]=ot[Ct++],r[3]=ot[Ct++]),s=(r[0]|r[1]|r[2]|r[3])===0,o=6);let u;typeof e=="string"?(!n.mask||s)&&n[ve]!==void 0?u=n[ve]:(e=Buffer.from(e),u=e.length):(u=e.length,i=n.mask&&n.readOnly&&!s);let c=u;u>=65536?(o+=8,c=127):u>125&&(o+=2,c=126);let a=Buffer.allocUnsafe(i?u+o:o);return a[0]=n.fin?n.opcode|128:n.opcode,n.rsv1&&(a[0]|=64),a[1]=c,c===126?a.writeUInt16BE(u,2):c===127&&(a[2]=a[3]=0,a.writeUIntBE(u,4,6)),n.mask?(a[1]|=128,a[o-4]=r[0],a[o-3]=r[1],a[o-2]=r[2],a[o-1]=r[3],s?[a,e]:i?(su(e,r,a,o,u),[a]):(su(e,r,e,0,u),[a,e])):[a,e]}close(e,n,r,i){let o;if(e===void 0)o=my;else{if(typeof e!="number"||!yy(e))throw new TypeError("First argument must be a valid error code number");if(n===void 0||!n.length)o=Buffer.allocUnsafe(2),o.writeUInt16BE(e,0);else{let u=Buffer.byteLength(n);if(u>123)throw new RangeError("The message must not be greater than 123 bytes");o=Buffer.allocUnsafe(2+u),o.writeUInt16BE(e,0),typeof n=="string"?o.write(n,2):o.set(n,2)}}let s={[ve]:o.length,fin:!0,generateMask:this._generateMask,mask:r,maskBuffer:this._maskBuffer,opcode:8,readOnly:!1,rsv1:!1};this._deflating?this.enqueue([this.dispatch,o,!1,s,i]):this.sendFrame(t.frame(o,s),i)}ping(e,n,r){let i,o;if(typeof e=="string"?(i=Buffer.byteLength(e),o=!1):(e=At(e),i=e.length,o=At.readOnly),i>125)throw new RangeError("The data size must not be greater than 125 bytes");let s={[ve]:i,fin:!0,generateMask:this._generateMask,mask:n,maskBuffer:this._maskBuffer,opcode:9,readOnly:o,rsv1:!1};this._deflating?this.enqueue([this.dispatch,e,!1,s,r]):this.sendFrame(t.frame(e,s),r)}pong(e,n,r){let i,o;if(typeof e=="string"?(i=Buffer.byteLength(e),o=!1):(e=At(e),i=e.length,o=At.readOnly),i>125)throw new RangeError("The data size must not be greater than 125 bytes");let s={[ve]:i,fin:!0,generateMask:this._generateMask,mask:n,maskBuffer:this._maskBuffer,opcode:10,readOnly:o,rsv1:!1};this._deflating?this.enqueue([this.dispatch,e,!1,s,r]):this.sendFrame(t.frame(e,s),r)}send(e,n,r){let i=this._extensions[ou.extensionName],o=n.binary?2:1,s=n.compress,u,c;if(typeof e=="string"?(u=Buffer.byteLength(e),c=!1):(e=At(e),u=e.length,c=At.readOnly),this._firstFragment?(this._firstFragment=!1,s&&i&&i.params[i._isServer?"server_no_context_takeover":"client_no_context_takeover"]&&(s=u>=i._threshold),this._compress=s):(s=!1,o=0),n.fin&&(this._firstFragment=!0),i){let a={[ve]:u,fin:n.fin,generateMask:this._generateMask,mask:n.mask,maskBuffer:this._maskBuffer,opcode:o,readOnly:c,rsv1:s};this._deflating?this.enqueue([this.dispatch,e,this._compress,a,r]):this.dispatch(e,this._compress,a,r)}else this.sendFrame(t.frame(e,{[ve]:u,fin:n.fin,generateMask:this._generateMask,mask:n.mask,maskBuffer:this._maskBuffer,opcode:o,readOnly:c,rsv1:!1}),r)}dispatch(e,n,r,i){if(!n){this.sendFrame(t.frame(e,r),i);return}let o=this._extensions[ou.extensionName];this._bufferedBytes+=r[ve],this._deflating=!0,o.compress(e,r.fin,(s,u)=>{if(this._socket.destroyed){let c=new Error("The socket was closed while data was being compressed");typeof i=="function"&&i(c);for(let a=0;a<this._queue.length;a++){let l=this._queue[a],p=l[l.length-1];typeof p=="function"&&p(c)}return}this._bufferedBytes-=r[ve],this._deflating=!1,r.readOnly=!1,this.sendFrame(t.frame(u,r),i),this.dequeue()})}dequeue(){for(;!this._deflating&&this._queue.length;){let e=this._queue.shift();this._bufferedBytes-=e[3][ve],Reflect.apply(e[0],this,e.slice(1))}}enqueue(e){this._bufferedBytes+=e[3][ve],this._queue.push(e)}sendFrame(e,n){e.length===2?(this._socket.cork(),this._socket.write(e[0]),this._socket.write(e[1],n),this._socket.uncork()):this._socket.write(e[0],n)}};au.exports=Hi});var mu=g((ST,gu)=>{"use strict";var{kForOnEventAttribute:Zt,kListener:Yi}=Xe(),uu=Symbol("kCode"),cu=Symbol("kData"),lu=Symbol("kError"),fu=Symbol("kMessage"),du=Symbol("kReason"),It=Symbol("kTarget"),pu=Symbol("kType"),hu=Symbol("kWasClean"),$e=class{constructor(e){this[It]=null,this[pu]=e}get target(){return this[It]}get type(){return this[pu]}};Object.defineProperty($e.prototype,"target",{enumerable:!0});Object.defineProperty($e.prototype,"type",{enumerable:!0});var st=class extends $e{constructor(e,n={}){super(e),this[uu]=n.code===void 0?0:n.code,this[du]=n.reason===void 0?"":n.reason,this[hu]=n.wasClean===void 0?!1:n.wasClean}get code(){return this[uu]}get reason(){return this[du]}get wasClean(){return this[hu]}};Object.defineProperty(st.prototype,"code",{enumerable:!0});Object.defineProperty(st.prototype,"reason",{enumerable:!0});Object.defineProperty(st.prototype,"wasClean",{enumerable:!0});var Ot=class extends $e{constructor(e,n={}){super(e),this[lu]=n.error===void 0?null:n.error,this[fu]=n.message===void 0?"":n.message}get error(){return this[lu]}get message(){return this[fu]}};Object.defineProperty(Ot.prototype,"error",{enumerable:!0});Object.defineProperty(Ot.prototype,"message",{enumerable:!0});var en=class extends $e{constructor(e,n={}){super(e),this[cu]=n.data===void 0?null:n.data}get data(){return this[cu]}};Object.defineProperty(en.prototype,"data",{enumerable:!0});var vy={addEventListener(t,e,n={}){for(let i of this.listeners(t))if(!n[Zt]&&i[Yi]===e&&!i[Zt])return;let r;if(t==="message")r=function(o,s){let u=new en("message",{data:s?o:o.toString()});u[It]=this,gr(e,this,u)};else if(t==="close")r=function(o,s){let u=new st("close",{code:o,reason:s.toString(),wasClean:this._closeFrameReceived&&this._closeFrameSent});u[It]=this,gr(e,this,u)};else if(t==="error")r=function(o){let s=new Ot("error",{error:o,message:o.message});s[It]=this,gr(e,this,s)};else if(t==="open")r=function(){let o=new $e("open");o[It]=this,gr(e,this,o)};else return;r[Zt]=!!n[Zt],r[Yi]=e,n.once?this.once(t,r):this.on(t,r)},removeEventListener(t,e){for(let n of this.listeners(t))if(n[Yi]===e&&!n[Zt]){this.removeListener(t,n);break}}};gu.exports={CloseEvent:st,ErrorEvent:Ot,Event:$e,EventTarget:vy,MessageEvent:en};function gr(t,e,n){typeof t=="object"&&t.handleEvent?t.handleEvent.call(t,n):t.call(e,n)}});var zi=g((TT,yu)=>{"use strict";var{tokenChars:tn}=Qt();function je(t,e,n){t[e]===void 0?t[e]=[n]:t[e].push(n)}function by(t){let e=Object.create(null),n=Object.create(null),r=!1,i=!1,o=!1,s,u,c=-1,a=-1,l=-1,p=0;for(;p<t.length;p++)if(a=t.charCodeAt(p),s===void 0)if(l===-1&&tn[a]===1)c===-1&&(c=p);else if(p!==0&&(a===32||a===9))l===-1&&c!==-1&&(l=p);else if(a===59||a===44){if(c===-1)throw new SyntaxError(`Unexpected character at index ${p}`);l===-1&&(l=p);let b=t.slice(c,l);a===44?(je(e,b,n),n=Object.create(null)):s=b,c=l=-1}else throw new SyntaxError(`Unexpected character at index ${p}`);else if(u===void 0)if(l===-1&&tn[a]===1)c===-1&&(c=p);else if(a===32||a===9)l===-1&&c!==-1&&(l=p);else if(a===59||a===44){if(c===-1)throw new SyntaxError(`Unexpected character at index ${p}`);l===-1&&(l=p),je(n,t.slice(c,l),!0),a===44&&(je(e,s,n),n=Object.create(null),s=void 0),c=l=-1}else if(a===61&&c!==-1&&l===-1)u=t.slice(c,p),c=l=-1;else throw new SyntaxError(`Unexpected character at index ${p}`);else if(i){if(tn[a]!==1)throw new SyntaxError(`Unexpected character at index ${p}`);c===-1?c=p:r||(r=!0),i=!1}else if(o)if(tn[a]===1)c===-1&&(c=p);else if(a===34&&c!==-1)o=!1,l=p;else if(a===92)i=!0;else throw new SyntaxError(`Unexpected character at index ${p}`);else if(a===34&&t.charCodeAt(p-1)===61)o=!0;else if(l===-1&&tn[a]===1)c===-1&&(c=p);else if(c!==-1&&(a===32||a===9))l===-1&&(l=p);else if(a===59||a===44){if(c===-1)throw new SyntaxError(`Unexpected character at index ${p}`);l===-1&&(l=p);let b=t.slice(c,l);r&&(b=b.replace(/\\/g,""),r=!1),je(n,u,b),a===44&&(je(e,s,n),n=Object.create(null),s=void 0),u=void 0,c=l=-1}else throw new SyntaxError(`Unexpected character at index ${p}`);if(c===-1||o||a===32||a===9)throw new SyntaxError("Unexpected end of input");l===-1&&(l=p);let m=t.slice(c,l);return s===void 0?je(e,m,n):(u===void 0?je(n,m,!0):r?je(n,u,m.replace(/\\/g,"")):je(n,u,m),je(e,s,n)),e}function wy(t){return Object.keys(t).map(e=>{let n=t[e];return Array.isArray(n)||(n=[n]),n.map(r=>[e].concat(Object.keys(r).map(i=>{let o=r[i];return Array.isArray(o)||(o=[o]),o.map(s=>s===!0?i:`${i}=${s}`).join("; ")})).join("; ")).join(", ")}).join(", ")}yu.exports={format:wy,parse:by}});var eo=g((AT,Iu)=>{"use strict";var Sy=I("events"),Ty=I("https"),Ey=I("http"),bu=I("net"),xy=I("tls"),{randomBytes:Ay,createHash:Cy}=I("crypto"),{Duplex:ET,Readable:xT}=I("stream"),{URL:Ji}=I("url"),Ze=Xt(),Iy=$i(),Oy=Ki(),{BINARY_TYPES:_u,EMPTY_BUFFER:mr,GUID:Ny,kForOnEventAttribute:Xi,kListener:Py,kStatusCode:Ry,kWebSocket:Q,NOOP:wu}=Xe(),{EventTarget:{addEventListener:My,removeEventListener:Dy}}=mu(),{format:ky,parse:By}=zi(),{toBuffer:Ly}=Yt(),jy=30*1e3,Su=Symbol("kAborted"),Qi=[8,13],He=["CONNECTING","OPEN","CLOSING","CLOSED"],Fy=/^[!#$%&'*+\-.0-9A-Z^_`|a-z~]+$/,j=class t extends Sy{constructor(e,n,r){super(),this._binaryType=_u[0],this._closeCode=1006,this._closeFrameReceived=!1,this._closeFrameSent=!1,this._closeMessage=mr,this._closeTimer=null,this._extensions={},this._paused=!1,this._protocol="",this._readyState=t.CONNECTING,this._receiver=null,this._sender=null,this._socket=null,e!==null?(this._bufferedAmount=0,this._isServer=!1,this._redirects=0,n===void 0?n=[]:Array.isArray(n)||(typeof n=="object"&&n!==null?(r=n,n=[]):n=[n]),Tu(this,e,n,r)):(this._autoPong=r.autoPong,this._isServer=!0)}get binaryType(){return this._binaryType}set binaryType(e){_u.includes(e)&&(this._binaryType=e,this._receiver&&(this._receiver._binaryType=e))}get bufferedAmount(){return this._socket?this._socket._writableState.length+this._sender._bufferedBytes:this._bufferedAmount}get extensions(){return Object.keys(this._extensions).join()}get isPaused(){return this._paused}get onclose(){return null}get onerror(){return null}get onopen(){return null}get onmessage(){return null}get protocol(){return this._protocol}get readyState(){return this._readyState}get url(){return this._url}setSocket(e,n,r){let i=new Iy({allowSynchronousEvents:r.allowSynchronousEvents,binaryType:this.binaryType,extensions:this._extensions,isServer:this._isServer,maxPayload:r.maxPayload,skipUTF8Validation:r.skipUTF8Validation});this._sender=new Oy(e,this._extensions,r.generateMask),this._receiver=i,this._socket=e,i[Q]=this,e[Q]=this,i.on("conclude",Gy),i.on("drain",Wy),i.on("error",Vy),i.on("message",$y),i.on("ping",Hy),i.on("pong",Ky),e.setTimeout&&e.setTimeout(0),e.setNoDelay&&e.setNoDelay(),n.length>0&&e.unshift(n),e.on("close",xu),e.on("data",_r),e.on("end",Au),e.on("error",Cu),this._readyState=t.OPEN,this.emit("open")}emitClose(){if(!this._socket){this._readyState=t.CLOSED,this.emit("close",this._closeCode,this._closeMessage);return}this._extensions[Ze.extensionName]&&this._extensions[Ze.extensionName].cleanup(),this._receiver.removeAllListeners(),this._readyState=t.CLOSED,this.emit("close",this._closeCode,this._closeMessage)}close(e,n){if(this.readyState!==t.CLOSED){if(this.readyState===t.CONNECTING){ae(this,this._req,"WebSocket was closed before the connection was established");return}if(this.readyState===t.CLOSING){this._closeFrameSent&&(this._closeFrameReceived||this._receiver._writableState.errorEmitted)&&this._socket.end();return}this._readyState=t.CLOSING,this._sender.close(e,n,!this._isServer,r=>{r||(this._closeFrameSent=!0,(this._closeFrameReceived||this._receiver._writableState.errorEmitted)&&this._socket.end())}),this._closeTimer=setTimeout(this._socket.destroy.bind(this._socket),jy)}}pause(){this.readyState===t.CONNECTING||this.readyState===t.CLOSED||(this._paused=!0,this._socket.pause())}ping(e,n,r){if(this.readyState===t.CONNECTING)throw new Error("WebSocket is not open: readyState 0 (CONNECTING)");if(typeof e=="function"?(r=e,e=n=void 0):typeof n=="function"&&(r=n,n=void 0),typeof e=="number"&&(e=e.toString()),this.readyState!==t.OPEN){Zi(this,e,r);return}n===void 0&&(n=!this._isServer),this._sender.ping(e||mr,n,r)}pong(e,n,r){if(this.readyState===t.CONNECTING)throw new Error("WebSocket is not open: readyState 0 (CONNECTING)");if(typeof e=="function"?(r=e,e=n=void 0):typeof n=="function"&&(r=n,n=void 0),typeof e=="number"&&(e=e.toString()),this.readyState!==t.OPEN){Zi(this,e,r);return}n===void 0&&(n=!this._isServer),this._sender.pong(e||mr,n,r)}resume(){this.readyState===t.CONNECTING||this.readyState===t.CLOSED||(this._paused=!1,this._receiver._writableState.needDrain||this._socket.resume())}send(e,n,r){if(this.readyState===t.CONNECTING)throw new Error("WebSocket is not open: readyState 0 (CONNECTING)");if(typeof n=="function"&&(r=n,n={}),typeof e=="number"&&(e=e.toString()),this.readyState!==t.OPEN){Zi(this,e,r);return}let i={binary:typeof e!="string",mask:!this._isServer,compress:!0,fin:!0,...n};this._extensions[Ze.extensionName]||(i.compress=!1),this._sender.send(e||mr,i,r)}terminate(){if(this.readyState!==t.CLOSED){if(this.readyState===t.CONNECTING){ae(this,this._req,"WebSocket was closed before the connection was established");return}this._socket&&(this._readyState=t.CLOSING,this._socket.destroy())}}};Object.defineProperty(j,"CONNECTING",{enumerable:!0,value:He.indexOf("CONNECTING")});Object.defineProperty(j.prototype,"CONNECTING",{enumerable:!0,value:He.indexOf("CONNECTING")});Object.defineProperty(j,"OPEN",{enumerable:!0,value:He.indexOf("OPEN")});Object.defineProperty(j.prototype,"OPEN",{enumerable:!0,value:He.indexOf("OPEN")});Object.defineProperty(j,"CLOSING",{enumerable:!0,value:He.indexOf("CLOSING")});Object.defineProperty(j.prototype,"CLOSING",{enumerable:!0,value:He.indexOf("CLOSING")});Object.defineProperty(j,"CLOSED",{enumerable:!0,value:He.indexOf("CLOSED")});Object.defineProperty(j.prototype,"CLOSED",{enumerable:!0,value:He.indexOf("CLOSED")});["binaryType","bufferedAmount","extensions","isPaused","protocol","readyState","url"].forEach(t=>{Object.defineProperty(j.prototype,t,{enumerable:!0})});["open","error","close","message"].forEach(t=>{Object.defineProperty(j.prototype,`on${t}`,{enumerable:!0,get(){for(let e of this.listeners(t))if(e[Xi])return e[Py];return null},set(e){for(let n of this.listeners(t))if(n[Xi]){this.removeListener(t,n);break}typeof e=="function"&&this.addEventListener(t,e,{[Xi]:!0})}})});j.prototype.addEventListener=My;j.prototype.removeEventListener=Dy;Iu.exports=j;function Tu(t,e,n,r){let i={allowSynchronousEvents:!0,autoPong:!0,protocolVersion:Qi[1],maxPayload:*********,skipUTF8Validation:!1,perMessageDeflate:!0,followRedirects:!1,maxRedirects:10,...r,socketPath:void 0,hostname:void 0,protocol:void 0,timeout:void 0,method:"GET",host:void 0,path:void 0,port:void 0};if(t._autoPong=i.autoPong,!Qi.includes(i.protocolVersion))throw new RangeError(`Unsupported protocol version: ${i.protocolVersion} (supported versions: ${Qi.join(", ")})`);let o;if(e instanceof Ji)o=e;else try{o=new Ji(e)}catch{throw new SyntaxError(`Invalid URL: ${e}`)}o.protocol==="http:"?o.protocol="ws:":o.protocol==="https:"&&(o.protocol="wss:"),t._url=o.href;let s=o.protocol==="wss:",u=o.protocol==="ws+unix:",c;if(o.protocol!=="ws:"&&!s&&!u?c=`The URL's protocol must be one of "ws:", "wss:", "http:", "https", or "ws+unix:"`:u&&!o.pathname?c="The URL's pathname is empty":o.hash&&(c="The URL contains a fragment identifier"),c){let w=new SyntaxError(c);if(t._redirects===0)throw w;yr(t,w);return}let a=s?443:80,l=Ay(16).toString("base64"),p=s?Ty.request:Ey.request,m=new Set,b;if(i.createConnection=i.createConnection||(s?qy:Uy),i.defaultPort=i.defaultPort||a,i.port=o.port||a,i.host=o.hostname.startsWith("[")?o.hostname.slice(1,-1):o.hostname,i.headers={...i.headers,"Sec-WebSocket-Version":i.protocolVersion,"Sec-WebSocket-Key":l,Connection:"Upgrade",Upgrade:"websocket"},i.path=o.pathname+o.search,i.timeout=i.handshakeTimeout,i.perMessageDeflate&&(b=new Ze(i.perMessageDeflate!==!0?i.perMessageDeflate:{},!1,i.maxPayload),i.headers["Sec-WebSocket-Extensions"]=ky({[Ze.extensionName]:b.offer()})),n.length){for(let w of n){if(typeof w!="string"||!Fy.test(w)||m.has(w))throw new SyntaxError("An invalid or duplicated subprotocol was specified");m.add(w)}i.headers["Sec-WebSocket-Protocol"]=n.join(",")}if(i.origin&&(i.protocolVersion<13?i.headers["Sec-WebSocket-Origin"]=i.origin:i.headers.Origin=i.origin),(o.username||o.password)&&(i.auth=`${o.username}:${o.password}`),u){let w=i.path.split(":");i.socketPath=w[0],i.path=w[1]}let T;if(i.followRedirects){if(t._redirects===0){t._originalIpc=u,t._originalSecure=s,t._originalHostOrSocketPath=u?i.socketPath:o.host;let w=r&&r.headers;if(r={...r,headers:{}},w)for(let[R,K]of Object.entries(w))r.headers[R.toLowerCase()]=K}else if(t.listenerCount("redirect")===0){let w=u?t._originalIpc?i.socketPath===t._originalHostOrSocketPath:!1:t._originalIpc?!1:o.host===t._originalHostOrSocketPath;(!w||t._originalSecure&&!s)&&(delete i.headers.authorization,delete i.headers.cookie,w||delete i.headers.host,i.auth=void 0)}i.auth&&!r.headers.authorization&&(r.headers.authorization="Basic "+Buffer.from(i.auth).toString("base64")),T=t._req=p(i),t._redirects&&t.emit("redirect",t.url,T)}else T=t._req=p(i);i.timeout&&T.on("timeout",()=>{ae(t,T,"Opening handshake has timed out")}),T.on("error",w=>{T===null||T[Su]||(T=t._req=null,yr(t,w))}),T.on("response",w=>{let R=w.headers.location,K=w.statusCode;if(R&&i.followRedirects&&K>=300&&K<400){if(++t._redirects>i.maxRedirects){ae(t,T,"Maximum redirects exceeded");return}T.abort();let ue;try{ue=new Ji(R,e)}catch{let ge=new SyntaxError(`Invalid URL: ${R}`);yr(t,ge);return}Tu(t,ue,n,r)}else t.emit("unexpected-response",T,w)||ae(t,T,`Unexpected server response: ${w.statusCode}`)}),T.on("upgrade",(w,R,K)=>{if(t.emit("upgrade",w),t.readyState!==j.CONNECTING)return;T=t._req=null;let ue=w.headers.upgrade;if(ue===void 0||ue.toLowerCase()!=="websocket"){ae(t,R,"Invalid Upgrade header");return}let gt=Cy("sha1").update(l+Ny).digest("base64");if(w.headers["sec-websocket-accept"]!==gt){ae(t,R,"Invalid Sec-WebSocket-Accept header");return}let ge=w.headers["sec-websocket-protocol"],be;if(ge!==void 0?m.size?m.has(ge)||(be="Server sent an invalid subprotocol"):be="Server sent a subprotocol but none was requested":m.size&&(be="Server sent no subprotocol"),be){ae(t,R,be);return}ge&&(t._protocol=ge);let et=w.headers["sec-websocket-extensions"];if(et!==void 0){if(!b){ae(t,R,"Server sent a Sec-WebSocket-Extensions header but no extension was requested");return}let Ye;try{Ye=By(et)}catch{ae(t,R,"Invalid Sec-WebSocket-Extensions header");return}let wn=Object.keys(Ye);if(wn.length!==1||wn[0]!==Ze.extensionName){ae(t,R,"Server indicated an extension that was not requested");return}try{b.accept(Ye[Ze.extensionName])}catch{ae(t,R,"Invalid Sec-WebSocket-Extensions header");return}t._extensions[Ze.extensionName]=b}t.setSocket(R,K,{allowSynchronousEvents:i.allowSynchronousEvents,generateMask:i.generateMask,maxPayload:i.maxPayload,skipUTF8Validation:i.skipUTF8Validation})}),i.finishRequest?i.finishRequest(T,t):T.end()}function yr(t,e){t._readyState=j.CLOSING,t.emit("error",e),t.emitClose()}function Uy(t){return t.path=t.socketPath,bu.connect(t)}function qy(t){return t.path=void 0,!t.servername&&t.servername!==""&&(t.servername=bu.isIP(t.host)?"":t.host),xy.connect(t)}function ae(t,e,n){t._readyState=j.CLOSING;let r=new Error(n);Error.captureStackTrace(r,ae),e.setHeader?(e[Su]=!0,e.abort(),e.socket&&!e.socket.destroyed&&e.socket.destroy(),process.nextTick(yr,t,r)):(e.destroy(r),e.once("error",t.emit.bind(t,"error")),e.once("close",t.emitClose.bind(t)))}function Zi(t,e,n){if(e){let r=Ly(e).length;t._socket?t._sender._bufferedBytes+=r:t._bufferedAmount+=r}if(n){let r=new Error(`WebSocket is not open: readyState ${t.readyState} (${He[t.readyState]})`);process.nextTick(n,r)}}function Gy(t,e){let n=this[Q];n._closeFrameReceived=!0,n._closeMessage=e,n._closeCode=t,n._socket[Q]!==void 0&&(n._socket.removeListener("data",_r),process.nextTick(Eu,n._socket),t===1005?n.close():n.close(t,e))}function Wy(){let t=this[Q];t.isPaused||t._socket.resume()}function Vy(t){let e=this[Q];e._socket[Q]!==void 0&&(e._socket.removeListener("data",_r),process.nextTick(Eu,e._socket),e.close(t[Ry])),e.emit("error",t)}function vu(){this[Q].emitClose()}function $y(t,e){this[Q].emit("message",t,e)}function Hy(t){let e=this[Q];e._autoPong&&e.pong(t,!this._isServer,wu),e.emit("ping",t)}function Ky(t){this[Q].emit("pong",t)}function Eu(t){t.resume()}function xu(){let t=this[Q];this.removeListener("close",xu),this.removeListener("data",_r),this.removeListener("end",Au),t._readyState=j.CLOSING;let e;!this._readableState.endEmitted&&!t._closeFrameReceived&&!t._receiver._writableState.errorEmitted&&(e=t._socket.read())!==null&&t._receiver.write(e),t._receiver.end(),this[Q]=void 0,clearTimeout(t._closeTimer),t._receiver._writableState.finished||t._receiver._writableState.errorEmitted?t.emitClose():(t._receiver.on("error",vu),t._receiver.on("finish",vu))}function _r(t){this[Q]._receiver.write(t)||this.pause()}function Au(){let t=this[Q];t._readyState=j.CLOSING,t._receiver.end(),this.end()}function Cu(){let t=this[Q];this.removeListener("error",Cu),this.on("error",wu),t&&(t._readyState=j.CLOSING,this.destroy())}});var Nu=g((CT,Ou)=>{"use strict";var{tokenChars:Yy}=Qt();function zy(t){let e=new Set,n=-1,r=-1,i=0;for(i;i<t.length;i++){let s=t.charCodeAt(i);if(r===-1&&Yy[s]===1)n===-1&&(n=i);else if(i!==0&&(s===32||s===9))r===-1&&n!==-1&&(r=i);else if(s===44){if(n===-1)throw new SyntaxError(`Unexpected character at index ${i}`);r===-1&&(r=i);let u=t.slice(n,r);if(e.has(u))throw new SyntaxError(`The "${u}" subprotocol is duplicated`);e.add(u),n=r=-1}else throw new SyntaxError(`Unexpected character at index ${i}`)}if(n===-1||r!==-1)throw new SyntaxError("Unexpected end of input");let o=t.slice(n,i);if(e.has(o))throw new SyntaxError(`The "${o}" subprotocol is duplicated`);return e.add(o),e}Ou.exports={parse:zy}});var Lu=g((OT,Bu)=>{"use strict";var Jy=I("events"),vr=I("http"),{Duplex:IT}=I("stream"),{createHash:Xy}=I("crypto"),Pu=zi(),at=Xt(),Qy=Nu(),Zy=eo(),{GUID:e_,kWebSocket:t_}=Xe(),n_=/^[+/0-9A-Za-z]{22}==$/,Ru=0,Mu=1,ku=2,to=class extends Jy{constructor(e,n){if(super(),e={allowSynchronousEvents:!0,autoPong:!0,maxPayload:100*1024*1024,skipUTF8Validation:!1,perMessageDeflate:!1,handleProtocols:null,clientTracking:!0,verifyClient:null,noServer:!1,backlog:null,server:null,host:null,path:null,port:null,WebSocket:Zy,...e},e.port==null&&!e.server&&!e.noServer||e.port!=null&&(e.server||e.noServer)||e.server&&e.noServer)throw new TypeError('One and only one of the "port", "server", or "noServer" options must be specified');if(e.port!=null?(this._server=vr.createServer((r,i)=>{let o=vr.STATUS_CODES[426];i.writeHead(426,{"Content-Length":o.length,"Content-Type":"text/plain"}),i.end(o)}),this._server.listen(e.port,e.host,e.backlog,n)):e.server&&(this._server=e.server),this._server){let r=this.emit.bind(this,"connection");this._removeListeners=r_(this._server,{listening:this.emit.bind(this,"listening"),error:this.emit.bind(this,"error"),upgrade:(i,o,s)=>{this.handleUpgrade(i,o,s,r)}})}e.perMessageDeflate===!0&&(e.perMessageDeflate={}),e.clientTracking&&(this.clients=new Set,this._shouldEmitClose=!1),this.options=e,this._state=Ru}address(){if(this.options.noServer)throw new Error('The server is operating in "noServer" mode');return this._server?this._server.address():null}close(e){if(this._state===ku){e&&this.once("close",()=>{e(new Error("The server is not running"))}),process.nextTick(nn,this);return}if(e&&this.once("close",e),this._state!==Mu)if(this._state=Mu,this.options.noServer||this.options.server)this._server&&(this._removeListeners(),this._removeListeners=this._server=null),this.clients?this.clients.size?this._shouldEmitClose=!0:process.nextTick(nn,this):process.nextTick(nn,this);else{let n=this._server;this._removeListeners(),this._removeListeners=this._server=null,n.close(()=>{nn(this)})}}shouldHandle(e){if(this.options.path){let n=e.url.indexOf("?");if((n!==-1?e.url.slice(0,n):e.url)!==this.options.path)return!1}return!0}handleUpgrade(e,n,r,i){n.on("error",Du);let o=e.headers["sec-websocket-key"],s=e.headers.upgrade,u=+e.headers["sec-websocket-version"];if(e.method!=="GET"){ut(this,e,n,405,"Invalid HTTP method");return}if(s===void 0||s.toLowerCase()!=="websocket"){ut(this,e,n,400,"Invalid Upgrade header");return}if(o===void 0||!n_.test(o)){ut(this,e,n,400,"Missing or invalid Sec-WebSocket-Key header");return}if(u!==8&&u!==13){ut(this,e,n,400,"Missing or invalid Sec-WebSocket-Version header");return}if(!this.shouldHandle(e)){rn(n,400);return}let c=e.headers["sec-websocket-protocol"],a=new Set;if(c!==void 0)try{a=Qy.parse(c)}catch{ut(this,e,n,400,"Invalid Sec-WebSocket-Protocol header");return}let l=e.headers["sec-websocket-extensions"],p={};if(this.options.perMessageDeflate&&l!==void 0){let m=new at(this.options.perMessageDeflate,!0,this.options.maxPayload);try{let b=Pu.parse(l);b[at.extensionName]&&(m.accept(b[at.extensionName]),p[at.extensionName]=m)}catch{ut(this,e,n,400,"Invalid or unacceptable Sec-WebSocket-Extensions header");return}}if(this.options.verifyClient){let m={origin:e.headers[`${u===8?"sec-websocket-origin":"origin"}`],secure:!!(e.socket.authorized||e.socket.encrypted),req:e};if(this.options.verifyClient.length===2){this.options.verifyClient(m,(b,T,w,R)=>{if(!b)return rn(n,T||401,w,R);this.completeUpgrade(p,o,a,e,n,r,i)});return}if(!this.options.verifyClient(m))return rn(n,401)}this.completeUpgrade(p,o,a,e,n,r,i)}completeUpgrade(e,n,r,i,o,s,u){if(!o.readable||!o.writable)return o.destroy();if(o[t_])throw new Error("server.handleUpgrade() was called more than once with the same socket, possibly due to a misconfiguration");if(this._state>Ru)return rn(o,503);let a=["HTTP/1.1 101 Switching Protocols","Upgrade: websocket","Connection: Upgrade",`Sec-WebSocket-Accept: ${Xy("sha1").update(n+e_).digest("base64")}`],l=new this.options.WebSocket(null,void 0,this.options);if(r.size){let p=this.options.handleProtocols?this.options.handleProtocols(r,i):r.values().next().value;p&&(a.push(`Sec-WebSocket-Protocol: ${p}`),l._protocol=p)}if(e[at.extensionName]){let p=e[at.extensionName].params,m=Pu.format({[at.extensionName]:[p]});a.push(`Sec-WebSocket-Extensions: ${m}`),l._extensions=e}this.emit("headers",a,i),o.write(a.concat(`\r
`).join(`\r
`)),o.removeListener("error",Du),l.setSocket(o,s,{allowSynchronousEvents:this.options.allowSynchronousEvents,maxPayload:this.options.maxPayload,skipUTF8Validation:this.options.skipUTF8Validation}),this.clients&&(this.clients.add(l),l.on("close",()=>{this.clients.delete(l),this._shouldEmitClose&&!this.clients.size&&process.nextTick(nn,this)})),u(l,i)}};Bu.exports=to;function r_(t,e){for(let n of Object.keys(e))t.on(n,e[n]);return function(){for(let r of Object.keys(e))t.removeListener(r,e[r])}}function nn(t){t._state=ku,t.emit("close")}function Du(){this.destroy()}function rn(t,e,n,r){n=n||vr.STATUS_CODES[e],r={Connection:"close","Content-Type":"text/html","Content-Length":Buffer.byteLength(n),...r},t.once("finish",t.destroy),t.end(`HTTP/1.1 ${e} ${vr.STATUS_CODES[e]}\r
`+Object.keys(r).map(i=>`${i}: ${r[i]}`).join(`\r
`)+`\r
\r
`+n)}function ut(t,e,n,r,i){if(t.listenerCount("wsClientError")){let o=new Error(i);Error.captureStackTrace(o,ut),t.emit("wsClientError",o,n,e)}else rn(n,r,i)}});var Lc=g((rE,Bc)=>{Bc.exports=kc;kc.sync=iv;var Mc=I("fs");function rv(t,e){var n=e.pathExt!==void 0?e.pathExt:process.env.PATHEXT;if(!n||(n=n.split(";"),n.indexOf("")!==-1))return!0;for(var r=0;r<n.length;r++){var i=n[r].toLowerCase();if(i&&t.substr(-i.length).toLowerCase()===i)return!0}return!1}function Dc(t,e,n){return!t.isSymbolicLink()&&!t.isFile()?!1:rv(e,n)}function kc(t,e,n){Mc.stat(t,function(r,i){n(r,r?!1:Dc(i,t,e))})}function iv(t,e){return Dc(Mc.statSync(t),t,e)}});var Gc=g((iE,qc)=>{qc.exports=Fc;Fc.sync=ov;var jc=I("fs");function Fc(t,e,n){jc.stat(t,function(r,i){n(r,r?!1:Uc(i,e))})}function ov(t,e){return Uc(jc.statSync(t),e)}function Uc(t,e){return t.isFile()&&sv(t,e)}function sv(t,e){var n=t.mode,r=t.uid,i=t.gid,o=e.uid!==void 0?e.uid:process.getuid&&process.getuid(),s=e.gid!==void 0?e.gid:process.getgid&&process.getgid(),u=parseInt("100",8),c=parseInt("010",8),a=parseInt("001",8),l=u|c,p=n&a||n&c&&i===s||n&u&&r===o||n&l&&o===0;return p}});var Vc=g((sE,Wc)=>{var oE=I("fs"),Pr;process.platform==="win32"||global.TESTING_WINDOWS?Pr=Lc():Pr=Gc();Wc.exports=ho;ho.sync=av;function ho(t,e,n){if(typeof e=="function"&&(n=e,e={}),!n){if(typeof Promise!="function")throw new TypeError("callback not provided");return new Promise(function(r,i){ho(t,e||{},function(o,s){o?i(o):r(s)})})}Pr(t,e||{},function(r,i){r&&(r.code==="EACCES"||e&&e.ignoreErrors)&&(r=null,i=!1),n(r,i)})}function av(t,e){try{return Pr.sync(t,e||{})}catch(n){if(e&&e.ignoreErrors||n.code==="EACCES")return!1;throw n}}});var Xc=g((aE,Jc)=>{var Dt=process.platform==="win32"||process.env.OSTYPE==="cygwin"||process.env.OSTYPE==="msys",$c=I("path"),uv=Dt?";":":",Hc=Vc(),Kc=t=>Object.assign(new Error(`not found: ${t}`),{code:"ENOENT"}),Yc=(t,e)=>{let n=e.colon||uv,r=t.match(/\//)||Dt&&t.match(/\\/)?[""]:[...Dt?[process.cwd()]:[],...(e.path||process.env.PATH||"").split(n)],i=Dt?e.pathExt||process.env.PATHEXT||".EXE;.CMD;.BAT;.COM":"",o=Dt?i.split(n):[""];return Dt&&t.indexOf(".")!==-1&&o[0]!==""&&o.unshift(""),{pathEnv:r,pathExt:o,pathExtExe:i}},zc=(t,e,n)=>{typeof e=="function"&&(n=e,e={}),e||(e={});let{pathEnv:r,pathExt:i,pathExtExe:o}=Yc(t,e),s=[],u=a=>new Promise((l,p)=>{if(a===r.length)return e.all&&s.length?l(s):p(Kc(t));let m=r[a],b=/^".*"$/.test(m)?m.slice(1,-1):m,T=$c.join(b,t),w=!b&&/^\.[\\\/]/.test(t)?t.slice(0,2)+T:T;l(c(w,a,0))}),c=(a,l,p)=>new Promise((m,b)=>{if(p===i.length)return m(u(l+1));let T=i[p];Hc(a+T,{pathExt:o},(w,R)=>{if(!w&&R)if(e.all)s.push(a+T);else return m(a+T);return m(c(a,l,p+1))})});return n?u(0).then(a=>n(null,a),n):u(0)},cv=(t,e)=>{e=e||{};let{pathEnv:n,pathExt:r,pathExtExe:i}=Yc(t,e),o=[];for(let s=0;s<n.length;s++){let u=n[s],c=/^".*"$/.test(u)?u.slice(1,-1):u,a=$c.join(c,t),l=!c&&/^\.[\\\/]/.test(t)?t.slice(0,2)+a:a;for(let p=0;p<r.length;p++){let m=l+r[p];try{if(Hc.sync(m,{pathExt:i}))if(e.all)o.push(m);else return m}catch{}}}if(e.all&&o.length)return o;if(e.nothrow)return null;throw Kc(t)};Jc.exports=zc;zc.sync=cv});var mo=g((uE,go)=>{"use strict";var Qc=(t={})=>{let e=t.env||process.env;return(t.platform||process.platform)!=="win32"?"PATH":Object.keys(e).reverse().find(r=>r.toUpperCase()==="PATH")||"Path"};go.exports=Qc;go.exports.default=Qc});var nl=g((cE,tl)=>{"use strict";var Zc=I("path"),lv=Xc(),fv=mo();function el(t,e){let n=t.options.env||process.env,r=process.cwd(),i=t.options.cwd!=null,o=i&&process.chdir!==void 0&&!process.chdir.disabled;if(o)try{process.chdir(t.options.cwd)}catch{}let s;try{s=lv.sync(t.command,{path:n[fv({env:n})],pathExt:e?Zc.delimiter:void 0})}catch{}finally{o&&process.chdir(r)}return s&&(s=Zc.resolve(i?t.options.cwd:"",s)),s}function dv(t){return el(t)||el(t,!0)}tl.exports=dv});var rl=g((lE,_o)=>{"use strict";var yo=/([()\][%!^"`<>&|;, *?])/g;function pv(t){return t=t.replace(yo,"^$1"),t}function hv(t,e){return t=`${t}`,t=t.replace(/(?=(\\+?)?)\1"/g,'$1$1\\"'),t=t.replace(/(?=(\\+?)?)\1$/,"$1$1"),t=`"${t}"`,t=t.replace(yo,"^$1"),e&&(t=t.replace(yo,"^$1")),t}_o.exports.command=pv;_o.exports.argument=hv});var ol=g((fE,il)=>{"use strict";il.exports=/^#!(.*)/});var al=g((dE,sl)=>{"use strict";var gv=ol();sl.exports=(t="")=>{let e=t.match(gv);if(!e)return null;let[n,r]=e[0].replace(/#! ?/,"").split(" "),i=n.split("/").pop();return i==="env"?r:r?`${i} ${r}`:i}});var cl=g((pE,ul)=>{"use strict";var vo=I("fs"),mv=al();function yv(t){let n=Buffer.alloc(150),r;try{r=vo.openSync(t,"r"),vo.readSync(r,n,0,150,0),vo.closeSync(r)}catch{}return mv(n.toString())}ul.exports=yv});var pl=g((hE,dl)=>{"use strict";var _v=I("path"),ll=nl(),fl=rl(),vv=cl(),bv=process.platform==="win32",wv=/\.(?:com|exe)$/i,Sv=/node_modules[\\/].bin[\\/][^\\/]+\.cmd$/i;function Tv(t){t.file=ll(t);let e=t.file&&vv(t.file);return e?(t.args.unshift(t.file),t.command=e,ll(t)):t.file}function Ev(t){if(!bv)return t;let e=Tv(t),n=!wv.test(e);if(t.options.forceShell||n){let r=Sv.test(e);t.command=_v.normalize(t.command),t.command=fl.command(t.command),t.args=t.args.map(o=>fl.argument(o,r));let i=[t.command].concat(t.args).join(" ");t.args=["/d","/s","/c",`"${i}"`],t.command=process.env.comspec||"cmd.exe",t.options.windowsVerbatimArguments=!0}return t}function xv(t,e,n){e&&!Array.isArray(e)&&(n=e,e=null),e=e?e.slice(0):[],n=Object.assign({},n);let r={command:t,args:e,options:n,file:void 0,original:{command:t,args:e}};return n.shell?r:Ev(r)}dl.exports=xv});var ml=g((gE,gl)=>{"use strict";var bo=process.platform==="win32";function wo(t,e){return Object.assign(new Error(`${e} ${t.command} ENOENT`),{code:"ENOENT",errno:"ENOENT",syscall:`${e} ${t.command}`,path:t.command,spawnargs:t.args})}function Av(t,e){if(!bo)return;let n=t.emit;t.emit=function(r,i){if(r==="exit"){let o=hl(i,e);if(o)return n.call(t,"error",o)}return n.apply(t,arguments)}}function hl(t,e){return bo&&t===1&&!e.file?wo(e.original,"spawn"):null}function Cv(t,e){return bo&&t===1&&!e.file?wo(e.original,"spawnSync"):null}gl.exports={hookChildProcess:Av,verifyENOENT:hl,verifyENOENTSync:Cv,notFoundError:wo}});var vl=g((mE,kt)=>{"use strict";var yl=I("child_process"),So=pl(),To=ml();function _l(t,e,n){let r=So(t,e,n),i=yl.spawn(r.command,r.args,r.options);return To.hookChildProcess(i,r),i}function Iv(t,e,n){let r=So(t,e,n),i=yl.spawnSync(r.command,r.args,r.options);return i.error=i.error||To.verifyENOENTSync(i.status,r),i}kt.exports=_l;kt.exports.spawn=_l;kt.exports.sync=Iv;kt.exports._parse=So;kt.exports._enoent=To});var wl=g((yE,bl)=>{"use strict";bl.exports=t=>{let e=typeof t=="string"?`
`:10,n=typeof t=="string"?"\r":13;return t[t.length-1]===e&&(t=t.slice(0,t.length-1)),t[t.length-1]===n&&(t=t.slice(0,t.length-1)),t}});var El=g((_E,pn)=>{"use strict";var dn=I("path"),Sl=mo(),Tl=t=>{t={cwd:process.cwd(),path:process.env[Sl()],execPath:process.execPath,...t};let e,n=dn.resolve(t.cwd),r=[];for(;e!==n;)r.push(dn.join(n,"node_modules/.bin")),e=n,n=dn.resolve(n,"..");let i=dn.resolve(t.cwd,t.execPath,"..");return r.push(i),r.concat(t.path).join(dn.delimiter)};pn.exports=Tl;pn.exports.default=Tl;pn.exports.env=t=>{t={env:process.env,...t};let e={...t.env},n=Sl({env:e});return t.path=e[n],e[n]=pn.exports(t),e}});var Al=g((vE,Eo)=>{"use strict";var xl=(t,e)=>{for(let n of Reflect.ownKeys(e))Object.defineProperty(t,n,Object.getOwnPropertyDescriptor(e,n));return t};Eo.exports=xl;Eo.exports.default=xl});var Il=g((bE,Mr)=>{"use strict";var Ov=Al(),Rr=new WeakMap,Cl=(t,e={})=>{if(typeof t!="function")throw new TypeError("Expected a function");let n,r=0,i=t.displayName||t.name||"<anonymous>",o=function(...s){if(Rr.set(o,++r),r===1)n=t.apply(this,s),t=null;else if(e.throw===!0)throw new Error(`Function \`${i}\` can only be called once`);return n};return Ov(o,t),Rr.set(o,r),o};Mr.exports=Cl;Mr.exports.default=Cl;Mr.exports.callCount=t=>{if(!Rr.has(t))throw new Error(`The given function \`${t.name}\` is not wrapped by the \`onetime\` package`);return Rr.get(t)}});var Ol=g(Dr=>{"use strict";Object.defineProperty(Dr,"__esModule",{value:!0});Dr.SIGNALS=void 0;var Nv=[{name:"SIGHUP",number:1,action:"terminate",description:"Terminal closed",standard:"posix"},{name:"SIGINT",number:2,action:"terminate",description:"User interruption with CTRL-C",standard:"ansi"},{name:"SIGQUIT",number:3,action:"core",description:"User interruption with CTRL-\\",standard:"posix"},{name:"SIGILL",number:4,action:"core",description:"Invalid machine instruction",standard:"ansi"},{name:"SIGTRAP",number:5,action:"core",description:"Debugger breakpoint",standard:"posix"},{name:"SIGABRT",number:6,action:"core",description:"Aborted",standard:"ansi"},{name:"SIGIOT",number:6,action:"core",description:"Aborted",standard:"bsd"},{name:"SIGBUS",number:7,action:"core",description:"Bus error due to misaligned, non-existing address or paging error",standard:"bsd"},{name:"SIGEMT",number:7,action:"terminate",description:"Command should be emulated but is not implemented",standard:"other"},{name:"SIGFPE",number:8,action:"core",description:"Floating point arithmetic error",standard:"ansi"},{name:"SIGKILL",number:9,action:"terminate",description:"Forced termination",standard:"posix",forced:!0},{name:"SIGUSR1",number:10,action:"terminate",description:"Application-specific signal",standard:"posix"},{name:"SIGSEGV",number:11,action:"core",description:"Segmentation fault",standard:"ansi"},{name:"SIGUSR2",number:12,action:"terminate",description:"Application-specific signal",standard:"posix"},{name:"SIGPIPE",number:13,action:"terminate",description:"Broken pipe or socket",standard:"posix"},{name:"SIGALRM",number:14,action:"terminate",description:"Timeout or timer",standard:"posix"},{name:"SIGTERM",number:15,action:"terminate",description:"Termination",standard:"ansi"},{name:"SIGSTKFLT",number:16,action:"terminate",description:"Stack is empty or overflowed",standard:"other"},{name:"SIGCHLD",number:17,action:"ignore",description:"Child process terminated, paused or unpaused",standard:"posix"},{name:"SIGCLD",number:17,action:"ignore",description:"Child process terminated, paused or unpaused",standard:"other"},{name:"SIGCONT",number:18,action:"unpause",description:"Unpaused",standard:"posix",forced:!0},{name:"SIGSTOP",number:19,action:"pause",description:"Paused",standard:"posix",forced:!0},{name:"SIGTSTP",number:20,action:"pause",description:'Paused using CTRL-Z or "suspend"',standard:"posix"},{name:"SIGTTIN",number:21,action:"pause",description:"Background process cannot read terminal input",standard:"posix"},{name:"SIGBREAK",number:21,action:"terminate",description:"User interruption with CTRL-BREAK",standard:"other"},{name:"SIGTTOU",number:22,action:"pause",description:"Background process cannot write to terminal output",standard:"posix"},{name:"SIGURG",number:23,action:"ignore",description:"Socket received out-of-band data",standard:"bsd"},{name:"SIGXCPU",number:24,action:"core",description:"Process timed out",standard:"bsd"},{name:"SIGXFSZ",number:25,action:"core",description:"File too big",standard:"bsd"},{name:"SIGVTALRM",number:26,action:"terminate",description:"Timeout or timer",standard:"bsd"},{name:"SIGPROF",number:27,action:"terminate",description:"Timeout or timer",standard:"bsd"},{name:"SIGWINCH",number:28,action:"ignore",description:"Terminal window size changed",standard:"bsd"},{name:"SIGIO",number:29,action:"terminate",description:"I/O is available",standard:"other"},{name:"SIGPOLL",number:29,action:"terminate",description:"Watched event",standard:"other"},{name:"SIGINFO",number:29,action:"ignore",description:"Request for process information",standard:"other"},{name:"SIGPWR",number:30,action:"terminate",description:"Device running out of power",standard:"systemv"},{name:"SIGSYS",number:31,action:"core",description:"Invalid system call",standard:"other"},{name:"SIGUNUSED",number:31,action:"terminate",description:"Invalid system call",standard:"other"}];Dr.SIGNALS=Nv});var xo=g(Bt=>{"use strict";Object.defineProperty(Bt,"__esModule",{value:!0});Bt.SIGRTMAX=Bt.getRealtimeSignals=void 0;var Pv=function(){let t=Pl-Nl+1;return Array.from({length:t},Rv)};Bt.getRealtimeSignals=Pv;var Rv=function(t,e){return{name:`SIGRT${e+1}`,number:Nl+e,action:"terminate",description:"Application-specific signal (realtime)",standard:"posix"}},Nl=34,Pl=64;Bt.SIGRTMAX=Pl});var Rl=g(kr=>{"use strict";Object.defineProperty(kr,"__esModule",{value:!0});kr.getSignals=void 0;var Mv=I("os"),Dv=Ol(),kv=xo(),Bv=function(){let t=(0,kv.getRealtimeSignals)();return[...Dv.SIGNALS,...t].map(Lv)};kr.getSignals=Bv;var Lv=function({name:t,number:e,description:n,action:r,forced:i=!1,standard:o}){let{signals:{[t]:s}}=Mv.constants,u=s!==void 0;return{name:t,number:u?s:e,description:n,supported:u,action:r,forced:i,standard:o}}});var Dl=g(Lt=>{"use strict";Object.defineProperty(Lt,"__esModule",{value:!0});Lt.signalsByNumber=Lt.signalsByName=void 0;var jv=I("os"),Ml=Rl(),Fv=xo(),Uv=function(){return(0,Ml.getSignals)().reduce(qv,{})},qv=function(t,{name:e,number:n,description:r,supported:i,action:o,forced:s,standard:u}){return{...t,[e]:{name:e,number:n,description:r,supported:i,action:o,forced:s,standard:u}}},Gv=Uv();Lt.signalsByName=Gv;var Wv=function(){let t=(0,Ml.getSignals)(),e=Fv.SIGRTMAX+1,n=Array.from({length:e},(r,i)=>Vv(i,t));return Object.assign({},...n)},Vv=function(t,e){let n=$v(t,e);if(n===void 0)return{};let{name:r,description:i,supported:o,action:s,forced:u,standard:c}=n;return{[t]:{name:r,number:t,description:i,supported:o,action:s,forced:u,standard:c}}},$v=function(t,e){let n=e.find(({name:r})=>jv.constants.signals[r]===t);return n!==void 0?n:e.find(r=>r.number===t)},Hv=Wv();Lt.signalsByNumber=Hv});var Bl=g((xE,kl)=>{"use strict";var{signalsByName:Kv}=Dl(),Yv=({timedOut:t,timeout:e,errorCode:n,signal:r,signalDescription:i,exitCode:o,isCanceled:s})=>t?`timed out after ${e} milliseconds`:s?"was canceled":n!==void 0?`failed with ${n}`:r!==void 0?`was killed with ${r} (${i})`:o!==void 0?`failed with exit code ${o}`:"failed",zv=({stdout:t,stderr:e,all:n,error:r,signal:i,exitCode:o,command:s,escapedCommand:u,timedOut:c,isCanceled:a,killed:l,parsed:{options:{timeout:p}}})=>{o=o===null?void 0:o,i=i===null?void 0:i;let m=i===void 0?void 0:Kv[i].description,b=r&&r.code,w=`Command ${Yv({timedOut:c,timeout:p,errorCode:b,signal:i,signalDescription:m,exitCode:o,isCanceled:a})}: ${s}`,R=Object.prototype.toString.call(r)==="[object Error]",K=R?`${w}
${r.message}`:w,ue=[K,e,t].filter(Boolean).join(`
`);return R?(r.originalMessage=r.message,r.message=ue):r=new Error(ue),r.shortMessage=K,r.command=s,r.escapedCommand=u,r.exitCode=o,r.signal=i,r.signalDescription=m,r.stdout=t,r.stderr=e,n!==void 0&&(r.all=n),"bufferedData"in r&&delete r.bufferedData,r.failed=!0,r.timedOut=!!c,r.isCanceled=a,r.killed=l&&!c,r};kl.exports=zv});var jl=g((AE,Ao)=>{"use strict";var Br=["stdin","stdout","stderr"],Jv=t=>Br.some(e=>t[e]!==void 0),Ll=t=>{if(!t)return;let{stdio:e}=t;if(e===void 0)return Br.map(r=>t[r]);if(Jv(t))throw new Error(`It's not possible to provide \`stdio\` in combination with one of ${Br.map(r=>`\`${r}\``).join(", ")}`);if(typeof e=="string")return e;if(!Array.isArray(e))throw new TypeError(`Expected \`stdio\` to be of type \`string\` or \`Array\`, got \`${typeof e}\``);let n=Math.max(e.length,Br.length);return Array.from({length:n},(r,i)=>e[i])};Ao.exports=Ll;Ao.exports.node=t=>{let e=Ll(t);return e==="ipc"?"ipc":e===void 0||typeof e=="string"?[e,e,e,"ipc"]:e.includes("ipc")?e:[...e,"ipc"]}});var Fl=g((CE,Lr)=>{Lr.exports=["SIGABRT","SIGALRM","SIGHUP","SIGINT","SIGTERM"];process.platform!=="win32"&&Lr.exports.push("SIGVTALRM","SIGXCPU","SIGXFSZ","SIGUSR2","SIGTRAP","SIGSYS","SIGQUIT","SIGIOT");process.platform==="linux"&&Lr.exports.push("SIGIO","SIGPOLL","SIGPWR","SIGSTKFLT","SIGUNUSED")});var Gl=g((IE,mn)=>{var Xv=I("assert"),hn=Fl(),Qv=/^win/i.test(process.platform),jr=I("events");typeof jr!="function"&&(jr=jr.EventEmitter);var H;process.__signal_exit_emitter__?H=process.__signal_exit_emitter__:(H=process.__signal_exit_emitter__=new jr,H.count=0,H.emitted={});H.infinite||(H.setMaxListeners(1/0),H.infinite=!0);mn.exports=function(t,e){Xv.equal(typeof t,"function","a callback must be provided for exit handler"),gn===!1&&Ul();var n="exit";e&&e.alwaysLast&&(n="afterexit");var r=function(){H.removeListener(n,t),H.listeners("exit").length===0&&H.listeners("afterexit").length===0&&Io()};return H.on(n,t),r};mn.exports.unload=Io;function Io(){gn&&(gn=!1,hn.forEach(function(t){try{process.removeListener(t,Oo[t])}catch{}}),process.emit=Co,process.reallyExit=ql,H.count-=1)}function jt(t,e,n){H.emitted[t]||(H.emitted[t]=!0,H.emit(t,e,n))}var Oo={};hn.forEach(function(t){Oo[t]=function(){var n=process.listeners(t);n.length===H.count&&(Io(),jt("exit",null,t),jt("afterexit",null,t),Qv&&t==="SIGHUP"&&(t="SIGINT"),process.kill(process.pid,t))}});mn.exports.signals=function(){return hn};mn.exports.load=Ul;var gn=!1;function Ul(){gn||(gn=!0,H.count+=1,hn=hn.filter(function(t){try{return process.on(t,Oo[t]),!0}catch{return!1}}),process.emit=eb,process.reallyExit=Zv)}var ql=process.reallyExit;function Zv(t){process.exitCode=t||0,jt("exit",process.exitCode,null),jt("afterexit",process.exitCode,null),ql.call(process,process.exitCode)}var Co=process.emit;function eb(t,e){if(t==="exit"){e!==void 0&&(process.exitCode=e);var n=Co.apply(this,arguments);return jt("exit",process.exitCode,null),jt("afterexit",process.exitCode,null),n}else return Co.apply(this,arguments)}});var Vl=g((OE,Wl)=>{"use strict";var tb=I("os"),nb=Gl(),rb=1e3*5,ib=(t,e="SIGTERM",n={})=>{let r=t(e);return ob(t,e,n,r),r},ob=(t,e,n,r)=>{if(!sb(e,n,r))return;let i=ub(n),o=setTimeout(()=>{t("SIGKILL")},i);o.unref&&o.unref()},sb=(t,{forceKillAfterTimeout:e},n)=>ab(t)&&e!==!1&&n,ab=t=>t===tb.constants.signals.SIGTERM||typeof t=="string"&&t.toUpperCase()==="SIGTERM",ub=({forceKillAfterTimeout:t=!0})=>{if(t===!0)return rb;if(!Number.isFinite(t)||t<0)throw new TypeError(`Expected the \`forceKillAfterTimeout\` option to be a non-negative integer, got \`${t}\` (${typeof t})`);return t},cb=(t,e)=>{t.kill()&&(e.isCanceled=!0)},lb=(t,e,n)=>{t.kill(e),n(Object.assign(new Error("Timed out"),{timedOut:!0,signal:e}))},fb=(t,{timeout:e,killSignal:n="SIGTERM"},r)=>{if(e===0||e===void 0)return r;let i,o=new Promise((u,c)=>{i=setTimeout(()=>{lb(t,n,c)},e)}),s=r.finally(()=>{clearTimeout(i)});return Promise.race([o,s])},db=({timeout:t})=>{if(t!==void 0&&(!Number.isFinite(t)||t<0))throw new TypeError(`Expected the \`timeout\` option to be a non-negative integer, got \`${t}\` (${typeof t})`)},pb=async(t,{cleanup:e,detached:n},r)=>{if(!e||n)return r;let i=nb(()=>{t.kill()});return r.finally(()=>{i()})};Wl.exports={spawnedKill:ib,spawnedCancel:cb,setupTimeout:fb,validateTimeout:db,setExitHandler:pb}});var Hl=g((NE,$l)=>{"use strict";var qe=t=>t!==null&&typeof t=="object"&&typeof t.pipe=="function";qe.writable=t=>qe(t)&&t.writable!==!1&&typeof t._write=="function"&&typeof t._writableState=="object";qe.readable=t=>qe(t)&&t.readable!==!1&&typeof t._read=="function"&&typeof t._readableState=="object";qe.duplex=t=>qe.writable(t)&&qe.readable(t);qe.transform=t=>qe.duplex(t)&&typeof t._transform=="function"&&typeof t._transformState=="object";$l.exports=qe});var Yl=g((PE,Kl)=>{"use strict";var{PassThrough:hb}=I("stream");Kl.exports=t=>{t={...t};let{array:e}=t,{encoding:n}=t,r=n==="buffer",i=!1;e?i=!(n||r):n=n||"utf8",r&&(n=null);let o=new hb({objectMode:i});n&&o.setEncoding(n);let s=0,u=[];return o.on("data",c=>{u.push(c),i?s=u.length:s+=c.length}),o.getBufferedValue=()=>e?u:r?Buffer.concat(u,s):u.join(""),o.getBufferedLength=()=>s,o}});var zl=g((RE,yn)=>{"use strict";var{constants:gb}=I("buffer"),mb=I("stream"),{promisify:yb}=I("util"),_b=Yl(),vb=yb(mb.pipeline),Fr=class extends Error{constructor(){super("maxBuffer exceeded"),this.name="MaxBufferError"}};async function No(t,e){if(!t)throw new Error("Expected a stream");e={maxBuffer:1/0,...e};let{maxBuffer:n}=e,r=_b(e);return await new Promise((i,o)=>{let s=u=>{u&&r.getBufferedLength()<=gb.MAX_LENGTH&&(u.bufferedData=r.getBufferedValue()),o(u)};(async()=>{try{await vb(t,r),i()}catch(u){s(u)}})(),r.on("data",()=>{r.getBufferedLength()>n&&s(new Fr)})}),r.getBufferedValue()}yn.exports=No;yn.exports.buffer=(t,e)=>No(t,{...e,encoding:"buffer"});yn.exports.array=(t,e)=>No(t,{...e,array:!0});yn.exports.MaxBufferError=Fr});var Xl=g((ME,Jl)=>{"use strict";var{PassThrough:bb}=I("stream");Jl.exports=function(){var t=[],e=new bb({objectMode:!0});return e.setMaxListeners(0),e.add=n,e.isEmpty=r,e.on("unpipe",i),Array.prototype.slice.call(arguments).forEach(n),e;function n(o){return Array.isArray(o)?(o.forEach(n),this):(t.push(o),o.once("end",i.bind(null,o)),o.once("error",e.emit.bind(e,"error")),o.pipe(e,{end:!1}),this)}function r(){return t.length==0}function i(o){t=t.filter(function(s){return s!==o}),!t.length&&e.readable&&e.end()}}});var tf=g((DE,ef)=>{"use strict";var Zl=Hl(),Ql=zl(),wb=Xl(),Sb=(t,e)=>{e===void 0||t.stdin===void 0||(Zl(e)?e.pipe(t.stdin):t.stdin.end(e))},Tb=(t,{all:e})=>{if(!e||!t.stdout&&!t.stderr)return;let n=wb();return t.stdout&&n.add(t.stdout),t.stderr&&n.add(t.stderr),n},Po=async(t,e)=>{if(t){t.destroy();try{return await e}catch(n){return n.bufferedData}}},Ro=(t,{encoding:e,buffer:n,maxBuffer:r})=>{if(!(!t||!n))return e?Ql(t,{encoding:e,maxBuffer:r}):Ql.buffer(t,{maxBuffer:r})},Eb=async({stdout:t,stderr:e,all:n},{encoding:r,buffer:i,maxBuffer:o},s)=>{let u=Ro(t,{encoding:r,buffer:i,maxBuffer:o}),c=Ro(e,{encoding:r,buffer:i,maxBuffer:o}),a=Ro(n,{encoding:r,buffer:i,maxBuffer:o*2});try{return await Promise.all([s,u,c,a])}catch(l){return Promise.all([{error:l,signal:l.signal,timedOut:l.timedOut},Po(t,u),Po(e,c),Po(n,a)])}},xb=({input:t})=>{if(Zl(t))throw new TypeError("The `input` option cannot be a stream in sync mode")};ef.exports={handleInput:Sb,makeAllStream:Tb,getSpawnedResult:Eb,validateInputSync:xb}});var rf=g((kE,nf)=>{"use strict";var Ab=(async()=>{})().constructor.prototype,Cb=["then","catch","finally"].map(t=>[t,Reflect.getOwnPropertyDescriptor(Ab,t)]),Ib=(t,e)=>{for(let[n,r]of Cb){let i=typeof e=="function"?(...o)=>Reflect.apply(r.value,e(),o):r.value.bind(e);Reflect.defineProperty(t,n,{...r,value:i})}return t},Ob=t=>new Promise((e,n)=>{t.on("exit",(r,i)=>{e({exitCode:r,signal:i})}),t.on("error",r=>{n(r)}),t.stdin&&t.stdin.on("error",r=>{n(r)})});nf.exports={mergePromise:Ib,getSpawnedPromise:Ob}});var af=g((BE,sf)=>{"use strict";var of=(t,e=[])=>Array.isArray(e)?[t,...e]:[t],Nb=/^[\w.-]+$/,Pb=/"/g,Rb=t=>typeof t!="string"||Nb.test(t)?t:`"${t.replace(Pb,'\\"')}"`,Mb=(t,e)=>of(t,e).join(" "),Db=(t,e)=>of(t,e).map(n=>Rb(n)).join(" "),kb=/ +/g,Bb=t=>{let e=[];for(let n of t.trim().split(kb)){let r=e[e.length-1];r&&r.endsWith("\\")?e[e.length-1]=`${r.slice(0,-1)} ${n}`:e.push(n)}return e};sf.exports={joinCommand:Mb,getEscapedCommand:Db,parseCommand:Bb}});var hf=g((LE,Ft)=>{"use strict";var Lb=I("path"),Mo=I("child_process"),jb=vl(),Fb=wl(),Ub=El(),qb=Il(),Ur=Bl(),cf=jl(),{spawnedKill:Gb,spawnedCancel:Wb,setupTimeout:Vb,validateTimeout:$b,setExitHandler:Hb}=Vl(),{handleInput:Kb,getSpawnedResult:Yb,makeAllStream:zb,validateInputSync:Jb}=tf(),{mergePromise:uf,getSpawnedPromise:Xb}=rf(),{joinCommand:lf,parseCommand:ff,getEscapedCommand:df}=af(),Qb=1e3*1e3*100,Zb=({env:t,extendEnv:e,preferLocal:n,localDir:r,execPath:i})=>{let o=e?{...process.env,...t}:t;return n?Ub.env({env:o,cwd:r,execPath:i}):o},pf=(t,e,n={})=>{let r=jb._parse(t,e,n);return t=r.command,e=r.args,n=r.options,n={maxBuffer:Qb,buffer:!0,stripFinalNewline:!0,extendEnv:!0,preferLocal:!1,localDir:n.cwd||process.cwd(),execPath:process.execPath,encoding:"utf8",reject:!0,cleanup:!0,all:!1,windowsHide:!0,...n},n.env=Zb(n),n.stdio=cf(n),process.platform==="win32"&&Lb.basename(t,".exe")==="cmd"&&e.unshift("/q"),{file:t,args:e,options:n,parsed:r}},_n=(t,e,n)=>typeof e!="string"&&!Buffer.isBuffer(e)?n===void 0?void 0:"":t.stripFinalNewline?Fb(e):e,qr=(t,e,n)=>{let r=pf(t,e,n),i=lf(t,e),o=df(t,e);$b(r.options);let s;try{s=Mo.spawn(r.file,r.args,r.options)}catch(b){let T=new Mo.ChildProcess,w=Promise.reject(Ur({error:b,stdout:"",stderr:"",all:"",command:i,escapedCommand:o,parsed:r,timedOut:!1,isCanceled:!1,killed:!1}));return uf(T,w)}let u=Xb(s),c=Vb(s,r.options,u),a=Hb(s,r.options,c),l={isCanceled:!1};s.kill=Gb.bind(null,s.kill.bind(s)),s.cancel=Wb.bind(null,s,l);let m=qb(async()=>{let[{error:b,exitCode:T,signal:w,timedOut:R},K,ue,gt]=await Yb(s,r.options,a),ge=_n(r.options,K),be=_n(r.options,ue),et=_n(r.options,gt);if(b||T!==0||w!==null){let Ye=Ur({error:b,exitCode:T,signal:w,stdout:ge,stderr:be,all:et,command:i,escapedCommand:o,parsed:r,timedOut:R,isCanceled:l.isCanceled,killed:s.killed});if(!r.options.reject)return Ye;throw Ye}return{command:i,escapedCommand:o,exitCode:0,stdout:ge,stderr:be,all:et,failed:!1,timedOut:!1,isCanceled:!1,killed:!1}});return Kb(s,r.options.input),s.all=zb(s,r.options),uf(s,m)};Ft.exports=qr;Ft.exports.sync=(t,e,n)=>{let r=pf(t,e,n),i=lf(t,e),o=df(t,e);Jb(r.options);let s;try{s=Mo.spawnSync(r.file,r.args,r.options)}catch(a){throw Ur({error:a,stdout:"",stderr:"",all:"",command:i,escapedCommand:o,parsed:r,timedOut:!1,isCanceled:!1,killed:!1})}let u=_n(r.options,s.stdout,s.error),c=_n(r.options,s.stderr,s.error);if(s.error||s.status!==0||s.signal!==null){let a=Ur({stdout:u,stderr:c,error:s.error,signal:s.signal,exitCode:s.status,command:i,escapedCommand:o,parsed:r,timedOut:s.error&&s.error.code==="ETIMEDOUT",isCanceled:!1,killed:s.signal!==null});if(!r.options.reject)return a;throw a}return{command:i,escapedCommand:o,exitCode:0,stdout:u,stderr:c,failed:!1,timedOut:!1,isCanceled:!1,killed:!1}};Ft.exports.command=(t,e)=>{let[n,...r]=ff(t);return qr(n,r,e)};Ft.exports.commandSync=(t,e)=>{let[n,...r]=ff(t);return qr.sync(n,r,e)};Ft.exports.node=(t,e,n={})=>{e&&!Array.isArray(e)&&typeof e=="object"&&(n=e,e=[]);let r=cf.node(n),i=process.execArgv.filter(u=>!u.startsWith("--inspect")),{nodePath:o=process.execPath,nodeOptions:s=i}=n;return qr(o,[...s,t,...Array.isArray(e)?e:[]],{...n,stdin:void 0,stdout:void 0,stderr:void 0,stdio:r,shell:!1})}});var aA=Z(es());var Qf={runtime:null,"runtime.sourcecreate":null,"runtime.assertion":null,"runtime.launch":null,"runtime.target":null,"runtime.welcome":null,"runtime.exception":null,"runtime.sourcemap":null,"runtime.breakpoints":null,"sourcemap.parsing":null,"perf.function":null,"cdp.send":null,"cdp.receive":null,"dap.send":null,"dap.receive":null,internal:null,proxyActivity:null},pw=Object.keys(Qf),hw=Symbol("ILogger");var Ra=Z(Oi()),rr=Z(I("os"));var jm={"pwa-extensionHost":null,"node-terminal":null,"pwa-node":null,"pwa-chrome":null,"pwa-msedge":null},Fm={"extension.js-debug.addCustomBreakpoints":null,"extension.js-debug.addXHRBreakpoints":null,"extension.js-debug.editXHRBreakpoints":null,"extension.pwa-node-debug.attachNodeProcess":null,"extension.js-debug.clearAutoAttachVariables":null,"extension.js-debug.setAutoAttachVariables":null,"extension.js-debug.autoAttachToProcess":null,"extension.js-debug.createDebuggerTerminal":null,"extension.js-debug.createDiagnostics":null,"extension.js-debug.getDiagnosticLogs":null,"extension.js-debug.debugLink":null,"extension.js-debug.npmScript":null,"extension.js-debug.pickNodeProcess":null,"extension.js-debug.prettyPrint":null,"extension.js-debug.removeXHRBreakpoint":null,"extension.js-debug.removeAllCustomBreakpoints":null,"extension.js-debug.revealPage":null,"extension.js-debug.startProfile":null,"extension.js-debug.stopProfile":null,"extension.js-debug.toggleSkippingFile":null,"extension.node-debug.startWithStopOnEntry":null,"extension.js-debug.requestCDPProxy":null,"extension.js-debug.openEdgeDevTools":null,"extension.js-debug.callers.add":null,"extension.js-debug.callers.goToCaller":null,"extension.js-debug.callers.gotToTarget":null,"extension.js-debug.callers.remove":null,"extension.js-debug.callers.removeAll":null,"extension.js-debug.enableSourceMapStepping":null,"extension.js-debug.disableSourceMapStepping":null,"extension.js-debug.network.viewRequest":null,"extension.js-debug.network.copyUri":null,"extension.js-debug.network.openBody":null,"extension.js-debug.network.openBodyInHex":null,"extension.js-debug.network.replayXHR":null,"extension.js-debug.network.clear":null,"extension.js-debug.completion.nodeTool":null},gS=new Set(Object.keys(Fm)),mS=new Set(Object.keys(jm));var Ca="<node_internals>";var Ni=Symbol("unset");function Pi(t){let e=Ni,n=(...r)=>(e===Ni&&(n.value=e=t(...r)),e);return n.forget=()=>{e=Ni,n.value=void 0},n.value=void 0,n}function Ia(t){let e=new Map,n=r=>{if(e.has(r))return e.get(r);let i=t(r);return e.set(r,i),i};return n.clear=()=>e.clear(),n}var vS=2**31-1;var CS=Symbol("AnyLaunchConfiguration"),Oa={type:"",name:"",request:"",trace:!1,outputCapture:"console",timeout:1e4,timeouts:{},showAsyncStacks:!0,skipFiles:[],smartStep:!0,sourceMaps:!0,sourceMapRenames:!0,pauseForSourceMap:!0,resolveSourceMapLocations:null,rootPath:"${workspaceFolder}",outFiles:["${workspaceFolder}/**/*.(m|c|)js","!**/node_modules/**"],sourceMapPathOverrides:Pa("${workspaceFolder}"),enableContentValidation:!0,cascadeTerminateToConfigurations:[],enableDWARF:!0,__workspaceFolder:"",__remoteFilePrefix:void 0,__breakOnConditionalError:!1,customDescriptionGenerator:void 0,customPropertiesGenerator:void 0},Kt={...Oa,cwd:"${workspaceFolder}",env:{},envFile:null,pauseForSourceMap:!1,sourceMaps:!0,localRoot:null,remoteRoot:null,resolveSourceMapLocations:["**","!**/node_modules/**"],autoAttachChildProcesses:!0,runtimeSourcemapPausePatterns:[],skipFiles:[`${Ca}/**`]},Um={...Kt,showAsyncStacks:{onceBreakpointResolved:16},type:"node-terminal",request:"launch",name:"JavaScript Debug Terminal"},IS={...Kt,type:"node-terminal",request:"attach",name:Um.name,showAsyncStacks:{onceBreakpointResolved:16},delegateId:-1},OS={...Kt,type:"pwa-extensionHost",name:"Debug Extension",request:"launch",args:["--extensionDevelopmentPath=${workspaceFolder}"],outFiles:["${workspaceFolder}/out/**/*.js"],resolveSourceMapLocations:["${workspaceFolder}/**","!**/node_modules/**"],rendererDebugOptions:{},runtimeExecutable:"${execPath}",autoAttachChildProcesses:!1,debugWebviews:!1,debugWebWorkerHost:!1,__sessionId:""},NS={...Kt,type:"pwa-node",request:"launch",program:"",cwd:"${workspaceFolder}",stopOnEntry:!1,console:"internalConsole",restart:!1,args:[],runtimeExecutable:"node",runtimeVersion:"default",runtimeArgs:[],profileStartup:!1,attachSimplePort:null,experimentalNetworking:"auto",killBehavior:"forceful"},Na={...Oa,type:"pwa-chrome",request:"attach",address:"localhost",port:0,disableNetworkCache:!0,pathMapping:{},url:null,restart:!1,urlFilter:"",sourceMapPathOverrides:Pa("${webRoot}"),webRoot:"${workspaceFolder}",server:null,browserAttachLocation:"workspace",targetSelection:"automatic",vueComponentPaths:["${workspaceFolder}/**/*.vue","!**/node_modules/**"],perScriptSourcemaps:"auto"},PS={...Na,type:"pwa-msedge",useWebView:!1},qm={...Na,type:"pwa-chrome",request:"launch",cwd:null,file:null,env:{},urlFilter:"*",includeDefaultArgs:!0,includeLaunchArgs:!0,runtimeArgs:null,runtimeExecutable:"*",userDataDir:!0,browserLaunchLocation:"workspace",profileStartup:!1,cleanUp:"wholeBrowser",killBehavior:"forceful"},RS={...qm,type:"pwa-msedge",useWebView:!1},MS={...Kt,type:"pwa-node",attachExistingChildren:!0,address:"localhost",port:9229,restart:!1,request:"attach",continueOnAttach:!1};function Pa(t){return{"webpack:///./~/*":`${t}/node_modules/*`,"webpack:////*":"/*","webpack://@?:*/?:*/*":`${t}/*`,"webpack://?:*/*":`${t}/*`,"webpack:///([a-z]):/(.+)":"$1:/$2","meteor://\u{1F4BB}app/*":`${t}/*`,"turbopack://[project]/*":"${workspaceFolder}/*","turbopack:///[project]/*":"${workspaceFolder}/*"}}var tr="js-debug",Ri="1.102.0",Gm="ms-vscode",DS=tr.includes("nightly"),kS=`${Gm}.${tr}`;var nr=class{async setup(){}dispose(){}write(e){if(e.level>2)throw new Error(e.message);console.log(JSON.stringify(e))}};var Mi=class{constructor(e=512){this.size=e;this.items=[];this.i=0}write(e){this.items[this.i]=e,this.i=(this.i+1)%this.size}read(){return this.items.slice(this.i).concat(this.items.slice(0,this.i))}},pe=class{constructor(){this.logTarget={queue:[]};this.logBuffer=new Mi(1)}static async test(){let e=new pe;return e.setup({sinks:[new nr],showWelcome:!1}),e}info(e,n,r){this.log({tag:e,timestamp:Date.now(),message:n,metadata:r,level:1})}verbose(e,n,r){this.log({tag:e,timestamp:Date.now(),message:n,metadata:r,level:0})}warn(e,n,r){this.log({tag:e,timestamp:Date.now(),message:n,metadata:r,level:2})}error(e,n,r){this.log({tag:e,timestamp:Date.now(),message:n,metadata:r,level:3})}fatal(e,n,r){this.log({tag:e,timestamp:Date.now(),message:n,metadata:r,level:4})}assert(e,n){if(e===!1||e===void 0||e===null){if(this.error("runtime.assertion",n,{error:new Error("Assertion failed")}),process.env.JS_DEBUG_THROW_ASSERTIONS)throw new Error(n);debugger;return!1}return!0}log(e){if(this.logBuffer.write(e),"queue"in this.logTarget){this.logTarget.queue.push(e);return}for(let n of this.logTarget.sinks)n.write(e)}getRecentLogs(){return this.logBuffer.read()}dispose(){if("sinks"in this.logTarget){for(let e of this.logTarget.sinks)e.dispose();this.logTarget={queue:[]}}}forTarget(){return this}async setup(e){if(await Promise.all(e.sinks.map(r=>r.setup())),e.showWelcome!==!1){let r=Vm();for(let i of e.sinks)i.write(r)}let n=this.logTarget;this.logTarget={sinks:e.sinks.slice()},"sinks"in n?n.sinks.forEach(r=>r.dispose()):n.queue.forEach(r=>this.log(r))}};pe.null=(()=>{let e=new pe;return e.setup({sinks:[]}),e})(),pe=Sn([(0,Ra.injectable)()],pe);var Vm=()=>({timestamp:Date.now(),tag:"runtime.welcome",level:1,message:`${tr} v${Ri} started`,metadata:{os:`${rr.platform()} ${rr.arch()}`,nodeVersion:process.version,adapterVersion:Ri}});var Ma=Z(Oi());var ne=class{constructor(){this._listeners=new Set;this.event=(e,n,r)=>{let i={listener:e,thisArg:n};this._listeners.add(i);let o={dispose:()=>{o.dispose=()=>{},this._listeners.delete(i)}};return r&&r.push(o),o}}get size(){return this._listeners.size}fire(e){let n=!this._deliveryQueue;this._deliveryQueue||(this._deliveryQueue=[]);for(let r of this._listeners)this._deliveryQueue.push({data:r,event:e});if(n){for(let r=0;r<this._deliveryQueue.length;r++){let{data:i,event:o}=this._deliveryQueue[r];i.listener.call(i.thisArg,o)}this._deliveryQueue=void 0}}dispose(){this._listeners.clear(),this._deliveryQueue&&(this._deliveryQueue=[])}};var Et=class{constructor(){this.flushEmitter=new ne;this.onFlush=this.flushEmitter.event}report(){}reportOperation(){}attachDap(){}flush(){this.flushEmitter.fire()}dispose(){}};Et=Sn([(0,Ma.injectable)()],Et);var ir=Z(I("path"));function ka(t,e,n){let r=Da(t,e,"uncaughtException",n),i=Da(t,e,"unhandledRejection",n);return process.addListener("uncaughtException",r),process.addListener("unhandledRejection",i),{dispose:()=>{process.removeListener("uncaughtException",r),process.removeListener("unhandledRejection",i)}}}var Da=(t,e,n,r=!0)=>i=>{Km(i)&&(e.report("error",{"!error":i,error:r?void 0:i,exceptionType:n}),t.error("runtime.exception","Unhandled error in debug adapter",i))},$m=t=>typeof t=="object"&&!!t&&"stack"in t,Hm=ir.dirname(ir.dirname(ir.dirname(__dirname)));function Km(t){var e;return!Ym||$m(t)&&!!((e=t.stack)!=null&&e.includes(Hm))}var Ym=!1;var Of=Z(I("net"));var xt=class t{constructor(e=process.hrtime()){this.value=e}get ms(){return this.s*1e3}get s(){return this.value[0]+this.value[1]/1e9}elapsed(){return new t().subtract(this)}subtract(e){let n=this.value[1]-e.value[1],r=this.value[0]-e.value[0];return n<0&&(n+=1e9,r--),new t([r,n])}};var Ba=I("stream"),or=class extends Ba.Transform{constructor(n){super();this.prefix=[];this.splitSuffix=Buffer.alloc(0);if(typeof n=="string"&&n.length===1)this.splitter=n.charCodeAt(0);else if(typeof n=="number")this.splitter=n;else throw new Error("not implemented here")}_transform(n,r,i){let o=0;for(;o<n.length;){let s=n.indexOf(this.splitter,o);if(s===-1)break;let u=n.subarray(o,s),c=this.prefix.length||this.splitSuffix.length?Buffer.concat([...this.prefix,u,this.splitSuffix]):u;this.push(c),this.prefix.length=0,o=s+1}o<n.length&&this.prefix.push(n.subarray(o)),i()}_flush(n){this.prefix.length&&this.push(Buffer.concat([...this.prefix,this.splitSuffix])),n()}};var sr=class{constructor(e,n,r){this.logger=e;this.pipeWrite=n;this.pipeRead=r;this.messageEmitter=new ne;this.endEmitter=new ne;this.onMessage=this.messageEmitter.event;this.onEnd=this.endEmitter.event;this.onceEnded=Pi(()=>{var e;this.streams&&(this.beforeClose(),this.streams.read.removeAllListeners(),(e=this.pipeRead)==null||e.destroy(),this.streams.write.removeListener("end",this.onceEnded),this.streams.write.removeListener("error",this.onWriteError),this.streams.write.on("error",()=>{}),this.streams.write.end(),this.streams=void 0,this.endEmitter.fire())});this.onWriteError=e=>{this.logger.error("internal","pipeWrite error",{error:e})};let i=r||n;this.streams={read:i.on("error",o=>this.logger.error("internal","pipeRead error",{error:o})).pipe(new or(0)).on("data",o=>this.messageEmitter.fire([o.toString(),new xt])).on("end",this.onceEnded),write:n.on("end",this.onceEnded).on("error",this.onWriteError)}}send(e){var n;(n=this.streams)==null||n.write.write(e+"\0")}dispose(){this.onceEnded()}beforeClose(){}};var i_=Z(Ua(),1),o_=Z($i(),1),s_=Z(Ki(),1),ju=Z(eo(),1),a_=Z(Lu(),1);var Fu=ju.default;var on=class extends Error{get cause(){return this._cause}constructor(e){super("__errorMarker"in e?e.error.format:e.format),this._cause="__errorMarker"in e?e.error:e}};var Uu=t=>isFinite(t)?new Promise(e=>setTimeout(e,t)):new Promise(()=>{});function qu(){let t=null,e=null,n=!1,r,i=new Promise((o,s)=>{t=u=>{n=!0,r=u,o(u)},e=u=>{n=!0,s(u)}});return{resolve:t,reject:e,promise:i,get settledValue(){return r},hasSettled:()=>n}}var br=class extends on{constructor(e){super({id:9243,format:e,showUser:!0}),this._cause={id:9243,format:e,showUser:!0}}};function Gu(t,e,n){if(e.isCancellationRequested)return Promise.reject(new br(n||"Task cancelled"));let r=qu(),i=e.onCancellationRequested(r.resolve);return Promise.race([r.promise.then(()=>{throw new br(n||"Task cancelled")}),t.then(o=>(i.dispose(),o)).catch(o=>{throw i.dispose(),o})])}var Wu=Object.freeze(function(t,e){let n=setTimeout(t.bind(e),0);return{dispose(){clearTimeout(n)}}}),u_=Object.freeze({isCancellationRequested:!1,onCancellationRequested:()=>({dispose:()=>{}})}),c_=Object.freeze({isCancellationRequested:!0,onCancellationRequested:Wu}),Pt=class t{constructor(e){this._token=void 0;this._parentListener=void 0;this._parentListener=e&&e.onCancellationRequested(this.cancel,this)}static withTimeout(e,n){let r=new t(n),i=r._token=new Nt,o=setTimeout(()=>i.cancel(),e);return i.onCancellationRequested(()=>clearTimeout(o)),r}get token(){return this._token||(this._token=new Nt),this._token}cancel(){this._token?this._token instanceof Nt&&this._token.cancel():this._token=c_}dispose(e=!1){e&&this.cancel(),this._parentListener&&this._parentListener.dispose(),this._token?this._token instanceof Nt&&this._token.dispose():this._token=u_}},Nt=class{constructor(){this._isCancelled=!1;this._emitter=null}cancel(){this._isCancelled||(this._isCancelled=!0,this._emitter&&(this._emitter.fire(void 0),this.dispose()))}get isCancellationRequested(){return this._isCancelled}get onCancellationRequested(){return this._isCancelled?Wu:(this._emitter||(this._emitter=new ne),this._emitter.event)}dispose(){this._emitter&&(this._emitter.dispose(),this._emitter=null)}};var Ef=I("dns"),ht=Z(I("path")),bn=I("url");var Ke=class{constructor(e,n){this.lineNumber=e;this.columnNumber=n}get base0(){return this}get base1(){return new wr(this.lineNumber+1,this.columnNumber+1)}get base01(){return new Sr(this.lineNumber,this.columnNumber+1)}compare(e){let n=e.base0;return this.lineNumber-n.lineNumber||this.columnNumber-n.columnNumber}},wr=class{constructor(e,n){this.lineNumber=e;this.columnNumber=n}get base0(){return new Ke(this.lineNumber-1,this.columnNumber-1)}get base1(){return this}get base01(){return new Sr(this.lineNumber-1,this.columnNumber)}compare(e){let n=e.base1;return this.lineNumber-n.lineNumber||this.columnNumber-n.columnNumber||0}},Sr=class{constructor(e,n){this.lineNumber=e;this.columnNumber=n}get base0(){return new Ke(this.lineNumber-1,this.columnNumber)}get base1(){return new wr(this.lineNumber,this.columnNumber+1)}get base01(){return this}compare(e){let n=e.base01;return this.lineNumber-n.lineNumber||this.columnNumber-n.columnNumber}},ct=class ct{constructor(e,n){this.begin=e;this.end=n}static simplify(e){if(e.length===0)return[];let n=e.slice().sort((o,s)=>o.begin.compare(s.begin)),r=[],i=n[0];for(let o=1;o<n.length;o++){let s=n[o];i.end.compare(s.begin)>=0?i=new ct(i.begin,s.end):(r.push(i),i=s)}return r.push(i),r}contains(e){return e.compare(this.begin)>=0&&e.compare(this.end)<=0}containsRange(e){return this.contains(e.begin)&&this.contains(e.end)}toString(){let e=this.begin.base0,n=this.end.base0;return`Range[${e.lineNumber}:${e.columnNumber} -> ${n.lineNumber}:${n.columnNumber}]`}};ct.ZERO=new ct(new Ke(0,0),new Ke(0,0)),ct.INFINITE=new ct(new Ke(0,0),new Ke(1/0,1/0));var no=ct;var Vu,Rt,l_,sn=class{constructor(){this._indexes={__proto__:null},this.array=[]}};Vu=(t,e)=>t._indexes[e],Rt=(t,e)=>{let n=Vu(t,e);if(n!==void 0)return n;let{array:r,_indexes:i}=t;return i[e]=r.push(e)-1},l_=t=>{let{array:e,_indexes:n}=t;if(e.length===0)return;let r=e.pop();n[r]=void 0};var $u="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",Ku=new Uint8Array(64),Yu=new Uint8Array(128);for(let t=0;t<$u.length;t++){let e=$u.charCodeAt(t);Ku[t]=e,Yu[e]=t}var ro=typeof TextDecoder<"u"?new TextDecoder:typeof Buffer<"u"?{decode(t){return Buffer.from(t.buffer,t.byteOffset,t.byteLength).toString()}}:{decode(t){let e="";for(let n=0;n<t.length;n++)e+=String.fromCharCode(t[n]);return e}};function zu(t){let e=new Int32Array(5),n=[],r=0;do{let i=f_(t,r),o=[],s=!0,u=0;e[0]=0;for(let c=r;c<i;c++){let a;c=an(t,c,e,0);let l=e[0];l<u&&(s=!1),u=l,Hu(t,c,i)?(c=an(t,c,e,1),c=an(t,c,e,2),c=an(t,c,e,3),Hu(t,c,i)?(c=an(t,c,e,4),a=[l,e[1],e[2],e[3],e[4]]):a=[l,e[1],e[2],e[3]]):a=[l],o.push(a)}s||d_(o),n.push(o),r=i+1}while(r<=t.length);return n}function f_(t,e){let n=t.indexOf(";",e);return n===-1?t.length:n}function an(t,e,n,r){let i=0,o=0,s=0;do{let c=t.charCodeAt(e++);s=Yu[c],i|=(s&31)<<o,o+=5}while(s&32);let u=i&1;return i>>>=1,u&&(i=-2147483648|-i),n[r]+=i,e}function Hu(t,e,n){return e>=n?!1:t.charCodeAt(e)!==44}function d_(t){t.sort(p_)}function p_(t,e){return t[0]-e[0]}function Tr(t){let e=new Int32Array(5),n=1024*16,r=n-36,i=new Uint8Array(n),o=i.subarray(0,r),s=0,u="";for(let c=0;c<t.length;c++){let a=t[c];if(c>0&&(s===n&&(u+=ro.decode(i),s=0),i[s++]=59),a.length!==0){e[0]=0;for(let l=0;l<a.length;l++){let p=a[l];s>r&&(u+=ro.decode(o),i.copyWithin(0,r,s),s-=r),l>0&&(i[s++]=44),s=un(i,s,e,p,0),p.length!==1&&(s=un(i,s,e,p,1),s=un(i,s,e,p,2),s=un(i,s,e,p,3),p.length!==4&&(s=un(i,s,e,p,4)))}}}return u+ro.decode(i.subarray(0,s))}function un(t,e,n,r,i){let o=r[i],s=o-n[i];n[i]=o,s=s<0?-s<<1|1:s<<1;do{let u=s&31;s>>>=5,s>0&&(u|=32),t[e++]=Ku[u]}while(s>0);return e}var h_=/^[\w+.-]+:\/\//,g_=/^([\w+.-]+:)\/\/([^@/#?]*@)?([^:/#?]*)(:\d+)?(\/[^#?]*)?(\?[^#]*)?(#.*)?/,m_=/^file:(?:\/\/((?![a-z]:)[^/#?]*)?)?(\/?[^#?]*)(\?[^#]*)?(#.*)?/i,F;(function(t){t[t.Empty=1]="Empty",t[t.Hash=2]="Hash",t[t.Query=3]="Query",t[t.RelativePath=4]="RelativePath",t[t.AbsolutePath=5]="AbsolutePath",t[t.SchemeRelative=6]="SchemeRelative",t[t.Absolute=7]="Absolute"})(F||(F={}));function y_(t){return h_.test(t)}function __(t){return t.startsWith("//")}function Qu(t){return t.startsWith("/")}function v_(t){return t.startsWith("file:")}function Ju(t){return/^[.?#]/.test(t)}function Er(t){let e=g_.exec(t);return Zu(e[1],e[2]||"",e[3],e[4]||"",e[5]||"/",e[6]||"",e[7]||"")}function b_(t){let e=m_.exec(t),n=e[2];return Zu("file:","",e[1]||"","",Qu(n)?n:"/"+n,e[3]||"",e[4]||"")}function Zu(t,e,n,r,i,o,s){return{scheme:t,user:e,host:n,port:r,path:i,query:o,hash:s,type:F.Absolute}}function Xu(t){if(__(t)){let n=Er("http:"+t);return n.scheme="",n.type=F.SchemeRelative,n}if(Qu(t)){let n=Er("http://foo.com"+t);return n.scheme="",n.host="",n.type=F.AbsolutePath,n}if(v_(t))return b_(t);if(y_(t))return Er(t);let e=Er("http://foo.com/"+t);return e.scheme="",e.host="",e.type=t?t.startsWith("?")?F.Query:t.startsWith("#")?F.Hash:F.RelativePath:F.Empty,e}function w_(t){if(t.endsWith("/.."))return t;let e=t.lastIndexOf("/");return t.slice(0,e+1)}function S_(t,e){ec(e,e.type),t.path==="/"?t.path=e.path:t.path=w_(e.path)+t.path}function ec(t,e){let n=e<=F.RelativePath,r=t.path.split("/"),i=1,o=0,s=!1;for(let c=1;c<r.length;c++){let a=r[c];if(!a){s=!0;continue}if(s=!1,a!=="."){if(a===".."){o?(s=!0,o--,i--):n&&(r[i++]=a);continue}r[i++]=a,o++}}let u="";for(let c=1;c<i;c++)u+="/"+r[c];(!u||s&&!u.endsWith("/.."))&&(u+="/"),t.path=u}function tc(t,e){if(!t&&!e)return"";let n=Xu(t),r=n.type;if(e&&r!==F.Absolute){let o=Xu(e),s=o.type;switch(r){case F.Empty:n.hash=o.hash;case F.Hash:n.query=o.query;case F.Query:case F.RelativePath:S_(n,o);case F.AbsolutePath:n.user=o.user,n.host=o.host,n.port=o.port;case F.SchemeRelative:n.scheme=o.scheme}s>r&&(r=s)}ec(n,r);let i=n.query+n.hash;switch(r){case F.Hash:case F.Query:return i;case F.RelativePath:{let o=n.path.slice(1);return o?Ju(e||t)&&!Ju(o)?"./"+o+i:o+i:i||"."}case F.AbsolutePath:return n.path+i;default:return n.scheme+"//"+n.user+n.host+n.port+n.path+i}}function nc(t,e){return e&&!e.endsWith("/")&&(e+="/"),tc(t,e)}function T_(t){if(!t)return"";let e=t.lastIndexOf("/");return t.slice(0,e+1)}var Ue=0,ac=1,uc=2,cc=3,E_=4,lc=1,fc=2;function x_(t,e){let n=rc(t,0);if(n===t.length)return t;e||(t=t.slice());for(let r=n;r<t.length;r=rc(t,r+1))t[r]=C_(t[r],e);return t}function rc(t,e){for(let n=e;n<t.length;n++)if(!A_(t[n]))return n;return t.length}function A_(t){for(let e=1;e<t.length;e++)if(t[e][Ue]<t[e-1][Ue])return!1;return!0}function C_(t,e){return e||(t=t.slice()),t.sort(I_)}function I_(t,e){return t[Ue]-e[Ue]}var lt=!1;function O_(t,e,n,r){for(;n<=r;){let i=n+(r-n>>1),o=t[i][Ue]-e;if(o===0)return lt=!0,i;o<0?n=i+1:r=i-1}return lt=!1,n-1}function oo(t,e,n){for(let r=n+1;r<t.length&&t[r][Ue]===e;n=r++);return n}function dc(t,e,n){for(let r=n-1;r>=0&&t[r][Ue]===e;n=r--);return n}function pc(){return{lastKey:-1,lastNeedle:-1,lastIndex:-1}}function hc(t,e,n,r){let{lastKey:i,lastNeedle:o,lastIndex:s}=n,u=0,c=t.length-1;if(r===i){if(e===o)return lt=s!==-1&&t[s][Ue]===e,s;e>=o?u=s===-1?0:s:c=s}return n.lastKey=r,n.lastNeedle=e,n.lastIndex=O_(t,e,u,c)}function N_(t,e){let n=e.map(R_);for(let r=0;r<t.length;r++){let i=t[r];for(let o=0;o<i.length;o++){let s=i[o];if(s.length===1)continue;let u=s[ac],c=s[uc],a=s[cc],l=n[u],p=l[c]||(l[c]=[]),m=e[u],b=oo(p,a,hc(p,a,m,c));P_(p,m.lastIndex=b+1,[a,r,s[Ue]])}}return n}function P_(t,e,n){for(let r=t.length;r>e;r--)t[r]=t[r-1];t[e]=n}function R_(){return{__proto__:null}}var ic="`line` must be greater than 0 (lines start at line 1)",oc="`column` must be greater than or equal to 0 (columns start at column 0)",Cr=-1,ft=1,sc,Fe,M_,so,ao,uo,co,D_,k_,B_,L_,ln=class{constructor(e,n){let r=typeof e=="string";if(!r&&e._decodedMemo)return e;let i=r?JSON.parse(e):e,{version:o,file:s,names:u,sourceRoot:c,sources:a,sourcesContent:l}=i;this.version=o,this.file=s,this.names=u||[],this.sourceRoot=c,this.sources=a,this.sourcesContent=l;let p=nc(c||"",T_(n));this.resolvedSources=a.map(b=>nc(b||"",p));let{mappings:m}=i;typeof m=="string"?(this._encoded=m,this._decoded=void 0):(this._encoded=void 0,this._decoded=x_(m,r)),this._decodedMemo=pc(),this._bySources=void 0,this._bySourceMemos=void 0}};(()=>{sc=e=>{var n;return(n=e._encoded)!==null&&n!==void 0?n:e._encoded=Tr(e._decoded)},Fe=e=>e._decoded||(e._decoded=zu(e._encoded)),M_=(e,n,r)=>{let i=Fe(e);if(n>=i.length)return null;let o=i[n],s=Ar(o,e._decodedMemo,n,r,ft);return s===-1?null:o[s]},so=(e,{line:n,column:r,bias:i})=>{if(n--,n<0)throw new Error(ic);if(r<0)throw new Error(oc);let o=Fe(e);if(n>=o.length)return xr(null,null,null,null);let s=o[n],u=Ar(s,e._decodedMemo,n,r,i||ft);if(u===-1)return xr(null,null,null,null);let c=s[u];if(c.length===1)return xr(null,null,null,null);let{names:a,resolvedSources:l}=e;return xr(l[c[ac]],c[uc]+1,c[cc],c.length===5?a[c[E_]]:null)},uo=(e,{source:n,line:r,column:i,bias:o})=>t(e,n,r,i,o||Cr,!0),ao=(e,{source:n,line:r,column:i,bias:o})=>t(e,n,r,i,o||ft,!1),co=(e,n)=>{let r=Fe(e),{names:i,resolvedSources:o}=e;for(let s=0;s<r.length;s++){let u=r[s];for(let c=0;c<u.length;c++){let a=u[c],l=s+1,p=a[0],m=null,b=null,T=null,w=null;a.length!==1&&(m=o[a[1]],b=a[2]+1,T=a[3]),a.length===5&&(w=i[a[4]]),n({generatedLine:l,generatedColumn:p,source:m,originalLine:b,originalColumn:T,name:w})}}},D_=(e,n)=>{let{sources:r,resolvedSources:i,sourcesContent:o}=e;if(o==null)return null;let s=r.indexOf(n);return s===-1&&(s=i.indexOf(n)),s===-1?null:o[s]},k_=(e,n)=>{let r=new ln(io(e,[]),n);return r._decoded=e.mappings,r},B_=e=>io(e,Fe(e)),L_=e=>io(e,sc(e));function t(e,n,r,i,o,s){if(r--,r<0)throw new Error(ic);if(i<0)throw new Error(oc);let{sources:u,resolvedSources:c}=e,a=u.indexOf(n);if(a===-1&&(a=c.indexOf(n)),a===-1)return s?[]:cn(null,null);let p=(e._bySources||(e._bySources=N_(Fe(e),e._bySourceMemos=u.map(pc))))[a][r];if(p==null)return s?[]:cn(null,null);let m=e._bySourceMemos[a];if(s)return j_(p,m,r,i,o);let b=Ar(p,m,r,i,o);if(b===-1)return cn(null,null);let T=p[b];return cn(T[lc]+1,T[fc])}})();function io(t,e){return{version:t.version,file:t.file,names:t.names,sourceRoot:t.sourceRoot,sources:t.sources,sourcesContent:t.sourcesContent,mappings:e}}function xr(t,e,n,r){return{source:t,line:e,column:n,name:r}}function cn(t,e){return{line:t,column:e}}function Ar(t,e,n,r,i){let o=hc(t,r,e,n);return lt?o=(i===Cr?oo:dc)(t,r,o):i===Cr&&o++,o===-1||o===t.length?-1:o}function j_(t,e,n,r,i){let o=Ar(t,e,n,r,ft);if(!lt&&i===Cr&&o++,o===-1||o===t.length)return[];let s=lt?r:t[o][Ue];lt||(o=dc(t,s,o));let u=oo(t,s,o),c=[];for(;o<=u;o++){let a=t[o];c.push(cn(a[lc]+1,a[fc]))}return c}var _c=0,vc=1,bc=2,wc=3,Sc=4,Tc=-1,Ec,F_,U_,q_,xc,lo,G_,W_,V_,fn,Ir=class{constructor({file:e,sourceRoot:n}={}){this._names=new sn,this._sources=new sn,this._sourcesContent=[],this._mappings=[],this.file=e,this.sourceRoot=n}};Ec=(t,e,n,r,i,o,s,u)=>fn(!1,t,e,n,r,i,o,s,u),U_=(t,e,n,r,i,o,s,u)=>fn(!0,t,e,n,r,i,o,s,u),F_=(t,e)=>yc(!1,t,e),q_=(t,e)=>yc(!0,t,e),xc=(t,e,n)=>{let{_sources:r,_sourcesContent:i}=t;i[Rt(r,e)]=n},lo=t=>{let{file:e,sourceRoot:n,_mappings:r,_sources:i,_sourcesContent:o,_names:s}=t;return K_(r),{version:3,file:e||void 0,names:s.array,sourceRoot:n||void 0,sources:i.array,sourcesContent:o,mappings:r}},G_=t=>{let e=lo(t);return Object.assign(Object.assign({},e),{mappings:Tr(e.mappings)})},V_=t=>{let e=[],{_mappings:n,_sources:r,_names:i}=t;for(let o=0;o<n.length;o++){let s=n[o];for(let u=0;u<s.length;u++){let c=s[u],a={line:o+1,column:c[_c]},l,p,m;c.length!==1&&(l=r.array[c[vc]],p={line:c[bc]+1,column:c[wc]},c.length===5&&(m=i.array[c[Sc]])),e.push({generated:a,source:l,original:p,name:m})}}return e},W_=t=>{let e=new ln(t),n=new Ir({file:e.file,sourceRoot:e.sourceRoot});return mc(n._names,e.names),mc(n._sources,e.sources),n._sourcesContent=e.sourcesContent||e.sources.map(()=>null),n._mappings=Fe(e),n},fn=(t,e,n,r,i,o,s,u,c)=>{let{_mappings:a,_sources:l,_sourcesContent:p,_names:m}=e,b=$_(a,n),T=H_(b,r);if(!i)return t&&Y_(b,T)?void 0:gc(b,T,[r]);let w=Rt(l,i),R=u?Rt(m,u):Tc;if(w===p.length&&(p[w]=c!=null?c:null),!(t&&z_(b,T,w,o,s,R)))return gc(b,T,u?[r,w,o,s,R]:[r,w,o,s])};function $_(t,e){for(let n=t.length;n<=e;n++)t[n]=[];return t[e]}function H_(t,e){let n=t.length;for(let r=n-1;r>=0;n=r--){let i=t[r];if(e>=i[_c])break}return n}function gc(t,e,n){for(let r=t.length;r>e;r--)t[r]=t[r-1];t[e]=n}function K_(t){let{length:e}=t,n=e;for(let r=n-1;r>=0&&!(t[r].length>0);n=r,r--);n<e&&(t.length=n)}function mc(t,e){for(let n=0;n<e.length;n++)Rt(t,e[n])}function Y_(t,e){return e===0?!0:t[e-1].length===1}function z_(t,e,n,r,i,o){if(e===0)return!1;let s=t[e-1];return s.length===1?!1:n===s[vc]&&r===s[bc]&&i===s[wc]&&o===(s.length===5?s[Sc]:Tc)}function yc(t,e,n){let{generated:r,source:i,original:o,name:s,content:u}=n;if(!i)return fn(t,e,r.line-1,r.column,null,null,null,null,null);let c=i;return fn(t,e,r.line-1,r.column,c,o.line-1,o.column,s,u)}var{stringify:X_}=JSON;if(!String.prototype.repeat)throw new Error("String.prototype.repeat is undefined, see https://github.com/davidbonnet/astring#installation");if(!String.prototype.endsWith)throw new Error("String.prototype.endsWith is undefined, see https://github.com/davidbonnet/astring#installation");var Or={"||":2,"??":3,"&&":4,"|":5,"^":6,"&":7,"==":8,"!=":8,"===":8,"!==":8,"<":9,">":9,"<=":9,">=":9,in:9,instanceof:9,"<<":10,">>":10,">>>":10,"+":11,"-":11,"*":12,"%":12,"/":12,"**":13},dt=17;function Mt(t,e){let{generator:n}=t;if(t.write("("),e!=null&&e.length>0){n[e[0].type](e[0],t);let{length:r}=e;for(let i=1;i<r;i++){let o=e[i];t.write(", "),n[o.type](o,t)}}t.write(")")}function Rc(t,e,n,r){let i=t.expressionsPrecedence[e.type];if(i===dt)return!0;let o=t.expressionsPrecedence[n.type];return i!==o?!r&&i===15&&o===14&&n.operator==="**"||i<o:i!==13&&i!==14?!1:e.operator==="**"&&n.operator==="**"?!r:i===13&&o===13&&(e.operator==="??"||n.operator==="??")?!0:r?Or[e.operator]<=Or[n.operator]:Or[e.operator]<Or[n.operator]}function Nr(t,e,n,r){let{generator:i}=t;Rc(t,e,n,r)?(t.write("("),i[e.type](e,t),t.write(")")):i[e.type](e,t)}function Q_(t,e,n,r){let i=e.split(`
`),o=i.length-1;if(t.write(i[0].trim()),o>0){t.write(r);for(let s=1;s<o;s++)t.write(n+i[s].trim()+r);t.write(n+i[o].trim())}}function re(t,e,n,r){let{length:i}=e;for(let o=0;o<i;o++){let s=e[o];t.write(n),s.type[0]==="L"?t.write("// "+s.value.trim()+`
`,s):(t.write("/*"),Q_(t,s.value,n,r),t.write("*/"+r))}}function Z_(t){let e=t;for(;e!=null;){let{type:n}=e;if(n[0]==="C"&&n[1]==="a")return!0;if(n[0]==="M"&&n[1]==="e"&&n[2]==="m")e=e.object;else return!1}}function fo(t,e){let{generator:n}=t,{declarations:r}=e;t.write(e.kind+" ");let{length:i}=r;if(i>0){n.VariableDeclarator(r[0],t);for(let o=1;o<i;o++)t.write(", "),n.VariableDeclarator(r[o],t)}}var Ac,Cc,Ic,Oc,Nc,Pc,QT={Program(t,e){let n=e.indent.repeat(e.indentLevel),{lineEnd:r,writeComments:i}=e;i&&t.comments!=null&&re(e,t.comments,n,r);let o=t.body,{length:s}=o;for(let u=0;u<s;u++){let c=o[u];i&&c.comments!=null&&re(e,c.comments,n,r),e.write(n),this[c.type](c,e),e.write(r)}i&&t.trailingComments!=null&&re(e,t.trailingComments,n,r)},BlockStatement:Pc=function(t,e){let n=e.indent.repeat(e.indentLevel++),{lineEnd:r,writeComments:i}=e,o=n+e.indent;e.write("{");let s=t.body;if(s!=null&&s.length>0){e.write(r),i&&t.comments!=null&&re(e,t.comments,o,r);let{length:u}=s;for(let c=0;c<u;c++){let a=s[c];i&&a.comments!=null&&re(e,a.comments,o,r),e.write(o),this[a.type](a,e),e.write(r)}e.write(n)}else i&&t.comments!=null&&(e.write(r),re(e,t.comments,o,r),e.write(n));i&&t.trailingComments!=null&&re(e,t.trailingComments,o,r),e.write("}"),e.indentLevel--},ClassBody:Pc,StaticBlock(t,e){e.write("static "),this.BlockStatement(t,e)},EmptyStatement(t,e){e.write(";")},ExpressionStatement(t,e){let n=e.expressionsPrecedence[t.expression.type];n===dt||n===3&&t.expression.left.type[0]==="O"?(e.write("("),this[t.expression.type](t.expression,e),e.write(")")):this[t.expression.type](t.expression,e),e.write(";")},IfStatement(t,e){e.write("if ("),this[t.test.type](t.test,e),e.write(") "),this[t.consequent.type](t.consequent,e),t.alternate!=null&&(e.write(" else "),this[t.alternate.type](t.alternate,e))},LabeledStatement(t,e){this[t.label.type](t.label,e),e.write(": "),this[t.body.type](t.body,e)},BreakStatement(t,e){e.write("break"),t.label!=null&&(e.write(" "),this[t.label.type](t.label,e)),e.write(";")},ContinueStatement(t,e){e.write("continue"),t.label!=null&&(e.write(" "),this[t.label.type](t.label,e)),e.write(";")},WithStatement(t,e){e.write("with ("),this[t.object.type](t.object,e),e.write(") "),this[t.body.type](t.body,e)},SwitchStatement(t,e){let n=e.indent.repeat(e.indentLevel++),{lineEnd:r,writeComments:i}=e;e.indentLevel++;let o=n+e.indent,s=o+e.indent;e.write("switch ("),this[t.discriminant.type](t.discriminant,e),e.write(") {"+r);let{cases:u}=t,{length:c}=u;for(let a=0;a<c;a++){let l=u[a];i&&l.comments!=null&&re(e,l.comments,o,r),l.test?(e.write(o+"case "),this[l.test.type](l.test,e),e.write(":"+r)):e.write(o+"default:"+r);let{consequent:p}=l,{length:m}=p;for(let b=0;b<m;b++){let T=p[b];i&&T.comments!=null&&re(e,T.comments,s,r),e.write(s),this[T.type](T,e),e.write(r)}}e.indentLevel-=2,e.write(n+"}")},ReturnStatement(t,e){e.write("return"),t.argument&&(e.write(" "),this[t.argument.type](t.argument,e)),e.write(";")},ThrowStatement(t,e){e.write("throw "),this[t.argument.type](t.argument,e),e.write(";")},TryStatement(t,e){if(e.write("try "),this[t.block.type](t.block,e),t.handler){let{handler:n}=t;n.param==null?e.write(" catch "):(e.write(" catch ("),this[n.param.type](n.param,e),e.write(") ")),this[n.body.type](n.body,e)}t.finalizer&&(e.write(" finally "),this[t.finalizer.type](t.finalizer,e))},WhileStatement(t,e){e.write("while ("),this[t.test.type](t.test,e),e.write(") "),this[t.body.type](t.body,e)},DoWhileStatement(t,e){e.write("do "),this[t.body.type](t.body,e),e.write(" while ("),this[t.test.type](t.test,e),e.write(");")},ForStatement(t,e){if(e.write("for ("),t.init!=null){let{init:n}=t;n.type[0]==="V"?fo(e,n):this[n.type](n,e)}e.write("; "),t.test&&this[t.test.type](t.test,e),e.write("; "),t.update&&this[t.update.type](t.update,e),e.write(") "),this[t.body.type](t.body,e)},ForInStatement:Ac=function(t,e){e.write(`for ${t.await?"await ":""}(`);let{left:n}=t;n.type[0]==="V"?fo(e,n):this[n.type](n,e),e.write(t.type[3]==="I"?" in ":" of "),this[t.right.type](t.right,e),e.write(") "),this[t.body.type](t.body,e)},ForOfStatement:Ac,DebuggerStatement(t,e){e.write("debugger;",t)},FunctionDeclaration:Cc=function(t,e){e.write((t.async?"async ":"")+(t.generator?"function* ":"function ")+(t.id?t.id.name:""),t),Mt(e,t.params),e.write(" "),this[t.body.type](t.body,e)},FunctionExpression:Cc,VariableDeclaration(t,e){fo(e,t),e.write(";")},VariableDeclarator(t,e){this[t.id.type](t.id,e),t.init!=null&&(e.write(" = "),this[t.init.type](t.init,e))},ClassDeclaration(t,e){if(e.write("class "+(t.id?`${t.id.name} `:""),t),t.superClass){e.write("extends ");let{superClass:n}=t,{type:r}=n,i=e.expressionsPrecedence[r];(r[0]!=="C"||r[1]!=="l"||r[5]!=="E")&&(i===dt||i<e.expressionsPrecedence.ClassExpression)?(e.write("("),this[t.superClass.type](n,e),e.write(")")):this[n.type](n,e),e.write(" ")}this.ClassBody(t.body,e)},ImportDeclaration(t,e){e.write("import ");let{specifiers:n}=t,{length:r}=n,i=0;if(r>0){for(;i<r;){i>0&&e.write(", ");let o=n[i],s=o.type[6];if(s==="D")e.write(o.local.name,o),i++;else if(s==="N")e.write("* as "+o.local.name,o),i++;else break}if(i<r){for(e.write("{");;){let o=n[i],{name:s}=o.imported;if(e.write(s,o),s!==o.local.name&&e.write(" as "+o.local.name),++i<r)e.write(", ");else break}e.write("}")}e.write(" from ")}this.Literal(t.source,e),e.write(";")},ImportExpression(t,e){e.write("import("),this[t.source.type](t.source,e),e.write(")")},ExportDefaultDeclaration(t,e){e.write("export default "),this[t.declaration.type](t.declaration,e),e.expressionsPrecedence[t.declaration.type]!=null&&t.declaration.type[0]!=="F"&&e.write(";")},ExportNamedDeclaration(t,e){if(e.write("export "),t.declaration)this[t.declaration.type](t.declaration,e);else{e.write("{");let{specifiers:n}=t,{length:r}=n;if(r>0)for(let i=0;;){let o=n[i],{name:s}=o.local;if(e.write(s,o),s!==o.exported.name&&e.write(" as "+o.exported.name),++i<r)e.write(", ");else break}e.write("}"),t.source&&(e.write(" from "),this.Literal(t.source,e)),e.write(";")}},ExportAllDeclaration(t,e){t.exported!=null?e.write("export * as "+t.exported.name+" from "):e.write("export * from "),this.Literal(t.source,e),e.write(";")},MethodDefinition(t,e){t.static&&e.write("static ");let n=t.kind[0];(n==="g"||n==="s")&&e.write(t.kind+" "),t.value.async&&e.write("async "),t.value.generator&&e.write("*"),t.computed?(e.write("["),this[t.key.type](t.key,e),e.write("]")):this[t.key.type](t.key,e),Mt(e,t.value.params),e.write(" "),this[t.value.body.type](t.value.body,e)},ClassExpression(t,e){this.ClassDeclaration(t,e)},ArrowFunctionExpression(t,e){e.write(t.async?"async ":"",t);let{params:n}=t;n!=null&&(n.length===1&&n[0].type[0]==="I"?e.write(n[0].name,n[0]):Mt(e,t.params)),e.write(" => "),t.body.type[0]==="O"?(e.write("("),this.ObjectExpression(t.body,e),e.write(")")):this[t.body.type](t.body,e)},ThisExpression(t,e){e.write("this",t)},Super(t,e){e.write("super",t)},RestElement:Ic=function(t,e){e.write("..."),this[t.argument.type](t.argument,e)},SpreadElement:Ic,YieldExpression(t,e){e.write(t.delegate?"yield*":"yield"),t.argument&&(e.write(" "),this[t.argument.type](t.argument,e))},AwaitExpression(t,e){e.write("await ",t),Nr(e,t.argument,t)},TemplateLiteral(t,e){let{quasis:n,expressions:r}=t;e.write("`");let{length:i}=r;for(let s=0;s<i;s++){let u=r[s],c=n[s];e.write(c.value.raw,c),e.write("${"),this[u.type](u,e),e.write("}")}let o=n[n.length-1];e.write(o.value.raw,o),e.write("`")},TemplateElement(t,e){e.write(t.value.raw,t)},TaggedTemplateExpression(t,e){Nr(e,t.tag,t),this[t.quasi.type](t.quasi,e)},ArrayExpression:Nc=function(t,e){if(e.write("["),t.elements.length>0){let{elements:n}=t,{length:r}=n;for(let i=0;;){let o=n[i];if(o!=null&&this[o.type](o,e),++i<r)e.write(", ");else{o==null&&e.write(", ");break}}}e.write("]")},ArrayPattern:Nc,ObjectExpression(t,e){let n=e.indent.repeat(e.indentLevel++),{lineEnd:r,writeComments:i}=e,o=n+e.indent;if(e.write("{"),t.properties.length>0){e.write(r),i&&t.comments!=null&&re(e,t.comments,o,r);let s=","+r,{properties:u}=t,{length:c}=u;for(let a=0;;){let l=u[a];if(i&&l.comments!=null&&re(e,l.comments,o,r),e.write(o),this[l.type](l,e),++a<c)e.write(s);else break}e.write(r),i&&t.trailingComments!=null&&re(e,t.trailingComments,o,r),e.write(n+"}")}else i?t.comments!=null?(e.write(r),re(e,t.comments,o,r),t.trailingComments!=null&&re(e,t.trailingComments,o,r),e.write(n+"}")):t.trailingComments!=null?(e.write(r),re(e,t.trailingComments,o,r),e.write(n+"}")):e.write("}"):e.write("}");e.indentLevel--},Property(t,e){t.method||t.kind[0]!=="i"?this.MethodDefinition(t,e):(t.shorthand||(t.computed?(e.write("["),this[t.key.type](t.key,e),e.write("]")):this[t.key.type](t.key,e),e.write(": ")),this[t.value.type](t.value,e))},PropertyDefinition(t,e){if(t.static&&e.write("static "),t.computed&&e.write("["),this[t.key.type](t.key,e),t.computed&&e.write("]"),t.value==null){t.key.type[0]!=="F"&&e.write(";");return}e.write(" = "),this[t.value.type](t.value,e),e.write(";")},ObjectPattern(t,e){if(e.write("{"),t.properties.length>0){let{properties:n}=t,{length:r}=n;for(let i=0;this[n[i].type](n[i],e),++i<r;)e.write(", ")}e.write("}")},SequenceExpression(t,e){Mt(e,t.expressions)},UnaryExpression(t,e){if(t.prefix){let{operator:n,argument:r,argument:{type:i}}=t;e.write(n);let o=Rc(e,r,t);!o&&(n.length>1||i[0]==="U"&&(i[1]==="n"||i[1]==="p")&&r.prefix&&r.operator[0]===n&&(n==="+"||n==="-"))&&e.write(" "),o?(e.write(n.length>1?" (":"("),this[i](r,e),e.write(")")):this[i](r,e)}else this[t.argument.type](t.argument,e),e.write(t.operator)},UpdateExpression(t,e){t.prefix?(e.write(t.operator),this[t.argument.type](t.argument,e)):(this[t.argument.type](t.argument,e),e.write(t.operator))},AssignmentExpression(t,e){this[t.left.type](t.left,e),e.write(" "+t.operator+" "),this[t.right.type](t.right,e)},AssignmentPattern(t,e){this[t.left.type](t.left,e),e.write(" = "),this[t.right.type](t.right,e)},BinaryExpression:Oc=function(t,e){let n=t.operator==="in";n&&e.write("("),Nr(e,t.left,t,!1),e.write(" "+t.operator+" "),Nr(e,t.right,t,!0),n&&e.write(")")},LogicalExpression:Oc,ConditionalExpression(t,e){let{test:n}=t,r=e.expressionsPrecedence[n.type];r===dt||r<=e.expressionsPrecedence.ConditionalExpression?(e.write("("),this[n.type](n,e),e.write(")")):this[n.type](n,e),e.write(" ? "),this[t.consequent.type](t.consequent,e),e.write(" : "),this[t.alternate.type](t.alternate,e)},NewExpression(t,e){e.write("new ");let n=e.expressionsPrecedence[t.callee.type];n===dt||n<e.expressionsPrecedence.CallExpression||Z_(t.callee)?(e.write("("),this[t.callee.type](t.callee,e),e.write(")")):this[t.callee.type](t.callee,e),Mt(e,t.arguments)},CallExpression(t,e){let n=e.expressionsPrecedence[t.callee.type];n===dt||n<e.expressionsPrecedence.CallExpression?(e.write("("),this[t.callee.type](t.callee,e),e.write(")")):this[t.callee.type](t.callee,e),t.optional&&e.write("?."),Mt(e,t.arguments)},ChainExpression(t,e){this[t.expression.type](t.expression,e)},MemberExpression(t,e){let n=e.expressionsPrecedence[t.object.type];n===dt||n<e.expressionsPrecedence.MemberExpression?(e.write("("),this[t.object.type](t.object,e),e.write(")")):this[t.object.type](t.object,e),t.computed?(t.optional&&e.write("?."),e.write("["),this[t.property.type](t.property,e),e.write("]")):(t.optional?e.write("?."):e.write("."),this[t.property.type](t.property,e))},MetaProperty(t,e){e.write(t.meta.name+"."+t.property.name,t)},Identifier(t,e){e.write(t.name,t)},PrivateIdentifier(t,e){e.write(`#${t.name}`,t)},Literal(t,e){t.raw!=null?e.write(t.raw,t):t.regex!=null?this.RegExpLiteral(t,e):t.bigint!=null?e.write(t.bigint+"n",t):e.write(X_(t.value),t)},RegExpLiteral(t,e){let{regex:n}=t;e.write(`/${n.pattern}/${n.flags}`,t)}};var po={ArrayExpression:["elements"],ArrayPattern:["elements"],ArrowFunctionExpression:["params","body"],AssignmentExpression:["left","right"],AssignmentPattern:["left","right"],AwaitExpression:["argument"],BinaryExpression:["left","right"],BlockStatement:["body"],BreakStatement:["label"],CallExpression:["callee","arguments"],CatchClause:["param","body"],ChainExpression:["expression"],ClassBody:["body"],ClassDeclaration:["id","superClass","body"],ClassExpression:["id","superClass","body"],ConditionalExpression:["test","consequent","alternate"],ContinueStatement:["label"],DebuggerStatement:[],DoWhileStatement:["body","test"],EmptyStatement:[],ExperimentalRestProperty:["argument"],ExperimentalSpreadProperty:["argument"],ExportAllDeclaration:["exported","source"],ExportDefaultDeclaration:["declaration"],ExportNamedDeclaration:["declaration","specifiers","source"],ExportSpecifier:["exported","local"],ExpressionStatement:["expression"],ForInStatement:["left","right","body"],ForOfStatement:["left","right","body"],ForStatement:["init","test","update","body"],FunctionDeclaration:["id","params","body"],FunctionExpression:["id","params","body"],Identifier:[],IfStatement:["test","consequent","alternate"],ImportDeclaration:["specifiers","source"],ImportDefaultSpecifier:["local"],ImportExpression:["source"],ImportNamespaceSpecifier:["local"],ImportSpecifier:["imported","local"],JSXAttribute:["name","value"],JSXClosingElement:["name"],JSXClosingFragment:[],JSXElement:["openingElement","children","closingElement"],JSXEmptyExpression:[],JSXExpressionContainer:["expression"],JSXFragment:["openingFragment","children","closingFragment"],JSXIdentifier:[],JSXMemberExpression:["object","property"],JSXNamespacedName:["namespace","name"],JSXOpeningElement:["name","attributes"],JSXOpeningFragment:[],JSXSpreadAttribute:["argument"],JSXSpreadChild:["expression"],JSXText:[],LabeledStatement:["label","body"],Literal:[],LogicalExpression:["left","right"],MemberExpression:["object","property"],MetaProperty:["meta","property"],MethodDefinition:["key","value"],NewExpression:["callee","arguments"],ObjectExpression:["properties"],ObjectPattern:["properties"],PrivateIdentifier:[],Program:["body"],Property:["key","value"],PropertyDefinition:["key","value"],RestElement:["argument"],ReturnStatement:["argument"],SequenceExpression:["expressions"],SpreadElement:["argument"],StaticBlock:["body"],Super:[],SwitchCase:["test","consequent"],SwitchStatement:["discriminant","cases"],TaggedTemplateExpression:["tag","quasi"],TemplateElement:[],TemplateLiteral:["quasis","expressions"],ThisExpression:[],ThrowStatement:["argument"],TryStatement:["block","handler","finalizer"],UnaryExpression:["argument"],UpdateExpression:["argument"],VariableDeclaration:["declarations"],VariableDeclarator:["id","init"],WhileStatement:["test","body"],WithStatement:["object","body"],YieldExpression:["argument"]},ev=Object.keys(po);for(let t of ev)Object.freeze(po[t]);Object.freeze(po);var ew=Z(hf()),mf=I("os");var pt=Z(I("fs")),gf=Z(I("util"));var jE=gf.promisify(pt.writeFile);var FE=Symbol("FsUtils");var $E=process.platform==="win32"?"\\\\.\\pipe\\":(0,mf.tmpdir)();var vn="file:///",yf=t=>t.startsWith(vn)&&t[vn.length+1]===":";function tw(t,e=!1){if(!t)return t;if(yf(t)){let n=vn.length;t=vn+t[n].toLowerCase()+t.substr(n+1)}else vf(t)&&(t=(e?t[0].toUpperCase():t[0].toLowerCase())+t.substr(1));return t}function Do(t,e=!1){if(!t)return t;if(t=tw(t,e),yf(t)){let n=vn.length;t=t.substr(0,n+1)+t.substr(n+1).replace(/\//g,"\\")}else vf(t)&&(t=t.replace(/\//g,"\\"));return t}var _f=t=>t.startsWith("\\\\"),vf=t=>/^[A-Za-z]:/.test(t)||_f(t);var Gr=class Gr{constructor(e,n,r,i,o){this.original=e;this.metadata=n;this.actualRoot=r;this.actualSources=i;this.hasNames=o;this.sourceActualToOriginal=new Map;this.sourceOriginalToActual=new Map;this.id=Gr.idCounter++;if(i.length!==e.sources.length)throw new Error("Expected actualSources.length === original.source.length");for(let s=0;s<i.length;s++){let u=i[s],c=e.sources[s];u!==null&&c!==null&&(this.sourceActualToOriginal.set(u,c),this.sourceOriginalToActual.set(c,u))}}get sources(){return this.actualSources.slice()}get sourceRoot(){return this.actualRoot}computedSourceUrl(e){return Do(bf(wf(this.metadata.sourceMapUrl)?this.metadata.compiledPath:this.metadata.sourceMapUrl,this.sourceRoot+e))}originalPositionFor(e){var r;let n=so(this.original,e);return n.source&&(n.source=(r=this.sourceOriginalToActual.get(n.source))!=null?r:n.source),n}generatedPositionFor(e){var r;let n=(r=this.sourceActualToOriginal.get(e.source))!=null?r:e.source;if(!isFinite(e.line)){let i=e.bias||ft;return this.getBestGeneratedForOriginal(n,(o,s)=>nw(o,s)*i)}return ao(this.original,{...e,source:n})}allGeneratedPositionsFor(e){var n;return uo(this.original,{...e,source:(n=this.sourceActualToOriginal.get(e.source))!=null?n:e.source})}sourceContentFor(e){var r,i,o;e=(r=this.sourceActualToOriginal.get(e))!=null?r:e;let n=this.original.sources.indexOf(e);return n===-1?null:(o=(i=this.original.sourcesContent)==null?void 0:i[n])!=null?o:null}eachMapping(e){co(this.original,e)}decodedMappings(){return Fe(this.original)}names(){return this.original.names}getBestGeneratedForOriginal(e,n){let r;return this.eachMapping(i=>{i.source===e&&(!r||n(i,r)>0)&&(r=i)}),r?{column:r.generatedColumn,line:r.generatedLine}:{column:null,line:null}}};Gr.idCounter=0;var ko=Gr,nw=({originalLine:t,originalColumn:e},{originalLine:n,originalColumn:r})=>(t||0)-(n||0)||(e||0)-(r||0);var Wx=process.platform!=="win32";var Tf,Vx=process.platform==="win32"?(Tf=process.env.PATHEXT)==null?void 0:Tf.toLowerCase().split(";"):void 0;var iw=new Set(["localhost","127.0.0.1","::1"]);var Sf=t=>iw.has(t.toLowerCase()),ow=t=>{try{return new bn.URL(t).hostname.replace(/^\[|\]$/g,"")}catch{return t}};var xf=Ia(async t=>{let e=ow(t);if(Sf(e))return!0;try{let n=await Ef.promises.lookup(e);return Sf(n.address)}catch{return!1}});function bf(t,e){try{return new bn.URL(e),e}catch{}let n;try{n=new bn.URL(t||"")}catch{return e}let r=n.protocol+"//";return n.username&&(r+=n.username+":"+n.password+"@"),r+=n.host,r+=ht.dirname(n.pathname),r[r.length-1]!=="/"&&(r+="/"),r+=e,r}function wf(t){return!!t&&t.startsWith("data:")}var $x=process.platform;var sw=50,Wr=class t{constructor(e){this.messageEmitter=new ne;this.endEmitter=new ne;this.onMessage=this.messageEmitter.event;this.onEnd=this.endEmitter.event;this._ws=e,this._ws.addEventListener("message",n=>{this.messageEmitter.fire([n.data.toString("utf-8"),new xt])}),this._ws.addEventListener("close",()=>{this.endEmitter.fire(),this._ws=void 0}),this._ws.addEventListener("error",()=>{})}static async create(e,n,r){let i=!e.startsWith("ws://"),o=await xf(e);for(;;){let s=Date.now()+sw;try{let u={headers:{host:r!=null?r:"localhost"},perMessageDeflate:!1,maxPayload:268435456,rejectUnauthorized:!(i&&o),followRedirects:!0},c=new Fu(e,[],u);return await Gu(new Promise((a,l)=>{c.addEventListener("open",()=>a(new t(c))),c.addEventListener("error",p=>{let m=e===c.url?e:c.url.replace(/^http(s?):/,"ws$1:");if(m===e){l(p.error);return}this.create(m,n,r).then(a,l)})}),Pt.withTimeout(2e3,n).token,`Could not open ${e}`).catch(a=>{throw c.close(),a})}catch(u){if(n.isCancellationRequested)throw u;let c=s-Date.now();c>0&&await Uu(c)}}}send(e){var n;(n=this._ws)==null||n.send(e)}dispose(){return new Promise(e=>{if(!this._ws)return e();this._ws.addEventListener("close",()=>e()),this._ws.close()})}};var Af=I("crypto"),Cf=()=>(0,Af.randomBytes)(12).toString("hex");var If,Vr=class t{constructor(e,n){this.info=e;this.server=n;this.onEndEmitter=new ne;this.cts=new Pt;this.gracefulExit=!1;this.targetAlive=!1;this.targetInfo={targetId:(If=this.info.ownId)!=null?If:Cf(),processId:Number(this.info.pid)||0,type:this.info.waitForDebugger?"waitingForDebugger":"",title:this.info.scriptName,url:"file://"+this.info.scriptName,openerId:this.info.openerId,attached:!0,canAccessOpener:!1,processInspectorPort:Number(new URL(this.info.inspectorURL).port)};this.onEnd=this.onEndEmitter.event;this.listenToServer()}get isTargetAlive(){return this.targetAlive}static async attach(e){let n=await new Promise((i,o)=>{let s=Of.createConnection(e.ipcAddress,()=>i(s));s.on("error",o)}),r=new sr(pe.null,n);return new t(e,r)}listenToServer(){let{server:e,targetInfo:n}=this;e.send(JSON.stringify({method:"Target.targetCreated",params:{targetInfo:n}})),e.onMessage(async([r])=>{if(this.target&&!r.includes("Target.attachToTarget")&&!r.includes("Target.detachFromTarget")){this.target.send(r);return}let i=await this.execute(r);i&&e.send(JSON.stringify(i))}),e.onEnd(()=>{this.disposeTarget(),this.onEndEmitter.fire({killed:this.gracefulExit,code:this.gracefulExit?0:1})})}dispose(){this.gracefulExit=!0,this.cts.dispose(!0),this.disposeTarget(),this.server.dispose()}async execute(e){var r;let n=JSON.parse(e);switch(n.method){case"Target.attachToTarget":return this.target&&this.disposeTarget(),this.target=await this.createTarget(),{id:n.id,result:{sessionId:this.targetInfo.targetId,__dynamicAttach:this.info.dynamicAttach?!0:void 0}};case"Target.detachFromTarget":return this.gracefulExit=!0,this.disposeTarget(),{id:n.id,result:{}};default:(r=this.target)==null||r.send(n);return}}async createTarget(){this.gracefulExit=!1;let e=await Wr.create(this.info.inspectorURL,this.cts.token,this.info.remoteHostHeader);return e.onMessage(([n])=>this.server.send(n)),e.onEnd(()=>{e&&this.server.send(JSON.stringify({method:"Target.targetDestroyed",params:{targetId:this.targetInfo.targetId,sessionId:this.targetInfo.targetId}})),this.targetAlive=!1,this.server.dispose()}),e}disposeTarget(){this.target&&(this.target.dispose(),this.target=void 0)}};var $r=JSON.parse(process.env.NODE_INSPECTOR_INFO),Hr=new pe;Hr.setup({sinks:[]});ka(Hr,new Et);(async()=>{process.on("exit",()=>{Hr.info("runtime","Process exiting"),Hr.dispose(),$r.pid&&!$r.dynamicAttach&&(!t||t.isTargetAlive)&&process.kill(Number($r.pid))});let t=await Vr.attach($r);t.onEnd(()=>process.exit())})();})();
/*! Bundled license information:

reflect-metadata/Reflect.js:
  (*! *****************************************************************************
  Copyright (C) Microsoft. All rights reserved.
  Licensed under the Apache License, Version 2.0 (the "License"); you may not use
  this file except in compliance with the License. You may obtain a copy of the
  License at http://www.apache.org/licenses/LICENSE-2.0
  
  THIS CODE IS PROVIDED ON AN *AS IS* BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
  KIND, EITHER EXPRESS OR IMPLIED, INCLUDING WITHOUT LIMITATION ANY IMPLIED
  WARRANTIES OR CONDITIONS OF TITLE, FITNESS FOR A PARTICULAR PURPOSE,
  MERCHANTABLITY OR NON-INFRINGEMENT.
  
  See the Apache Version 2.0 License for specific language governing permissions
  and limitations under the License.
  ***************************************************************************** *)
*/
//# sourceMappingURL=watchdog.js.map
