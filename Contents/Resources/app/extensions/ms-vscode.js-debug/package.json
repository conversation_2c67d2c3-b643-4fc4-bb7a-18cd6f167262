{"name": "js-debug", "displayName": "JavaScript Debugger", "version": "1.102.0", "publisher": "ms-vscode", "author": {"name": "Microsoft Corporation"}, "keywords": ["pwa", "javascript", "node", "chrome", "debugger"], "description": "An extension for debugging Node.js programs and Chrome.", "license": "MIT", "engines": {"vscode": "^1.80.0", "node": ">=10"}, "icon": "resources/logo.png", "categories": ["Debuggers"], "private": true, "repository": {"type": "git", "url": "https://github.com/Microsoft/vscode-pwa.git"}, "bugs": {"url": "https://github.com/Microsoft/vscode-pwa/issues"}, "main": "./src/extension.js", "enabledApiProposals": ["portsAttributes", "workspaceTrust", "tunnels"], "extensionKind": ["workspace"], "capabilities": {"virtualWorkspaces": false, "untrustedWorkspaces": {"supported": "limited", "description": "%workspaceTrust.description%"}}, "activationEvents": ["onDebugDynamicConfigurations", "onDebugInitialConfigurations", "onFileSystem:jsDebugNetworkFs", "onDebugResolve:pwa-node", "onDebugResolve:node-terminal", "onDebugResolve:pwa-extensionHost", "onDebugResolve:pwa-chrome", "onDebugResolve:pwa-msedge", "onDebugResolve:node", "onDebugResolve:chrome", "onDebugResolve:extensionHost", "onDebugResolve:msedge", "onCommand:extension.js-debug.clearAutoAttachVariables", "onCommand:extension.js-debug.setAutoAttachVariables", "onCommand:extension.js-debug.autoAttachToProcess", "onCommand:extension.js-debug.pickNodeProcess", "onCommand:extension.js-debug.requestCDPProxy", "onCommand:extension.js-debug.completion.nodeTool"], "contributes": {"menus": {"commandPalette": [{"command": "extension.js-debug.prettyPrint", "title": "%pretty.print.script%", "when": "debugType == pwa-extensionHost && debugState == stopped || debugType == node-terminal && debugState == stopped || debugType == pwa-node && debugState == stopped || debugType == pwa-chrome && debugState == stopped || debugType == pwa-msedge && debugState == stopped"}, {"command": "extension.js-debug.startProfile", "title": "%profile.start%", "when": "debugType == pwa-extensionHost && inDebugMode && !jsDebugIsProfiling || debugType == node-terminal && inDebugMode && !jsDebugIsProfiling || debugType == pwa-node && inDebugMode && !jsDebugIsProfiling || debugType == pwa-chrome && inDebugMode && !jsDebugIsProfiling || debugType == pwa-msedge && inDebugMode && !jsDebugIsProfiling"}, {"command": "extension.js-debug.stopProfile", "title": "%profile.stop%", "when": "debugType == pwa-extensionHost && inDebugMode && jsDebugIsProfiling || debugType == node-terminal && inDebugMode && jsDebugIsProfiling || debugType == pwa-node && inDebugMode && jsDebugIsProfiling || debugType == pwa-chrome && inDebugMode && jsDebugIsProfiling || debugType == pwa-msedge && inDebugMode && jsDebugIsProfiling"}, {"command": "extension.js-debug.revealPage", "when": "false"}, {"command": "extension.js-debug.debugLink", "title": "%debugLink.label%", "when": "!isWeb"}, {"command": "extension.js-debug.createDiagnostics", "title": "%createDiagnostics.label%", "when": "debugType == pwa-extensionHost && inDebugMode || debugType == node-terminal && inDebugMode || debugType == pwa-node && inDebugMode || debugType == pwa-chrome && inDebugMode || debugType == pwa-msedge && inDebugMode"}, {"command": "extension.js-debug.getDiagnosticLogs", "title": "%getDiagnosticLogs.label%", "when": "debugType == pwa-extensionHost && inDebugMode || debugType == node-terminal && inDebugMode || debugType == pwa-node && inDebugMode || debugType == pwa-chrome && inDebugMode || debugType == pwa-msedge && inDebugMode"}, {"command": "extension.js-debug.openEdgeDevTools", "title": "%openEdgeDevTools.label%", "when": "debugType == pwa-msedge"}, {"command": "extension.js-debug.callers.add", "title": "%commands.callersAdd.paletteLabel%", "when": "debugType == pwa-extensionHost && debugState == \"stopped\" || debugType == node-terminal && debugState == \"stopped\" || debugType == pwa-node && debugState == \"stopped\" || debugType == pwa-chrome && debugState == \"stopped\" || debugType == pwa-msedge && debugState == \"stopped\""}, {"command": "extension.js-debug.callers.goToCaller", "when": "false"}, {"command": "extension.js-debug.callers.gotToTarget", "when": "false"}, {"command": "extension.js-debug.network.copyUri", "when": "false"}, {"command": "extension.js-debug.network.openBody", "when": "false"}, {"command": "extension.js-debug.network.openBodyInHex", "when": "false"}, {"command": "extension.js-debug.network.replayXHR", "when": "false"}, {"command": "extension.js-debug.network.viewRequest", "when": "false"}, {"command": "extension.js-debug.network.clear", "when": "false"}, {"command": "extension.js-debug.enableSourceMapStepping", "when": "jsDebugIsMapSteppingDisabled"}, {"command": "extension.js-debug.disableSourceMapStepping", "when": "!jsDebugIsMapSteppingDisabled"}], "debug/callstack/context": [{"command": "extension.js-debug.revealPage", "group": "navigation", "when": "debugType == pwa-chrome && callStackItemType == 'session' || debugType == pwa-msedge && callStackItemType == 'session'"}, {"command": "extension.js-debug.toggleSkippingFile", "group": "navigation", "when": "debugType == pwa-extensionHost && callStackItemType == 'session' || debugType == node-terminal && callStackItemType == 'session' || debugType == pwa-node && callStackItemType == 'session' || debugType == pwa-chrome && callStackItemType == 'session' || debugType == pwa-msedge && callStackItemType == 'session'"}, {"command": "extension.js-debug.startProfile", "group": "navigation", "when": "debugType == pwa-extensionHost && !jsDebugIsProfiling && callStackItemType == 'session' || debugType == node-terminal && !jsDebugIsProfiling && callStackItemType == 'session' || debugType == pwa-node && !jsDebugIsProfiling && callStackItemType == 'session' || debugType == pwa-chrome && !jsDebugIsProfiling && callStackItemType == 'session' || debugType == pwa-msedge && !jsDebugIsProfiling && callStackItemType == 'session'"}, {"command": "extension.js-debug.stopProfile", "group": "navigation", "when": "debugType == pwa-extensionHost && jsDebugIsProfiling && callStackItemType == 'session' || debugType == node-terminal && jsDebugIsProfiling && callStackItemType == 'session' || debugType == pwa-node && jsDebugIsProfiling && callStackItemType == 'session' || debugType == pwa-chrome && jsDebugIsProfiling && callStackItemType == 'session' || debugType == pwa-msedge && jsDebugIsProfiling && callStackItemType == 'session'"}, {"command": "extension.js-debug.startProfile", "group": "inline", "when": "debugType == pwa-extensionHost && !jsDebugIsProfiling || debugType == node-terminal && !jsDebugIsProfiling || debugType == pwa-node && !jsDebugIsProfiling || debugType == pwa-chrome && !jsDebugIsProfiling || debugType == pwa-msedge && !jsDebugIsProfiling"}, {"command": "extension.js-debug.stopProfile", "group": "inline", "when": "debugType == pwa-extensionHost && jsDebugIsProfiling || debugType == node-terminal && jsDebugIsProfiling || debugType == pwa-node && jsDebugIsProfiling || debugType == pwa-chrome && jsDebugIsProfiling || debugType == pwa-msedge && jsDebugIsProfiling"}, {"command": "extension.js-debug.callers.add", "when": "debugType == pwa-extensionHost && callStackItemType == 'stackFrame' || debugType == node-terminal && callStackItemType == 'stackFrame' || debugType == pwa-node && callStackItemType == 'stackFrame' || debugType == pwa-chrome && callStackItemType == 'stackFrame' || debugType == pwa-msedge && callStackItemType == 'stackFrame'"}], "debug/toolBar": [{"command": "extension.js-debug.stopProfile", "when": "debugType == pwa-extensionHost && jsDebugIsProfiling || debugType == node-terminal && jsDebugIsProfiling || debugType == pwa-node && jsDebugIsProfiling || debugType == pwa-chrome && jsDebugIsProfiling || debugType == pwa-msedge && jsDebugIsProfiling"}, {"command": "extension.js-debug.openEdgeDevTools", "when": "debugType == pwa-msedge"}, {"command": "extension.js-debug.enableSourceMapStepping", "when": "jsDebugIsMapSteppingDisabled"}], "view/title": [{"command": "extension.js-debug.addCustomBreakpoints", "when": "view == jsBrowserBreakpoints", "group": "navigation"}, {"command": "extension.js-debug.removeAllCustomBreakpoints", "when": "view == jsBrowserBreakpoints", "group": "navigation"}, {"command": "extension.js-debug.callers.removeAll", "group": "navigation", "when": "view == jsExcludedCallers"}, {"command": "extension.js-debug.disableSourceMapStepping", "group": "navigation", "when": "debugType == pwa-extensionHost && view == workbench.debug.callStackView && !jsDebugIsMapSteppingDisabled || debugType == node-terminal && view == workbench.debug.callStackView && !jsDebugIsMapSteppingDisabled || debugType == pwa-node && view == workbench.debug.callStackView && !jsDebugIsMapSteppingDisabled || debugType == pwa-chrome && view == workbench.debug.callStackView && !jsDebugIsMapSteppingDisabled || debugType == pwa-msedge && view == workbench.debug.callStackView && !jsDebugIsMapSteppingDisabled"}, {"command": "extension.js-debug.enableSourceMapStepping", "group": "navigation", "when": "debugType == pwa-extensionHost && view == workbench.debug.callStackView && jsDebugIsMapSteppingDisabled || debugType == node-terminal && view == workbench.debug.callStackView && jsDebugIsMapSteppingDisabled || debugType == pwa-node && view == workbench.debug.callStackView && jsDebugIsMapSteppingDisabled || debugType == pwa-chrome && view == workbench.debug.callStackView && jsDebugIsMapSteppingDisabled || debugType == pwa-msedge && view == workbench.debug.callStackView && jsDebugIsMapSteppingDisabled"}, {"command": "extension.js-debug.network.clear", "group": "navigation", "when": "view == jsDebugNetworkTree"}], "view/item/context": [{"command": "extension.js-debug.addXHRBreakpoints", "when": "view == jsBrowserBreakpoints && viewItem == xhrBreakpoint"}, {"command": "extension.js-debug.editXHRBreakpoints", "when": "view == jsBrowserBreakpoints && viewItem == xhrBreakpoint", "group": "inline"}, {"command": "extension.js-debug.editXHRBreakpoints", "when": "view == jsBrowserBreakpoints && viewItem == xhrBreakpoint"}, {"command": "extension.js-debug.removeXHRBreakpoint", "when": "view == jsBrowserBreakpoints && viewItem == xhrBreakpoint", "group": "inline"}, {"command": "extension.js-debug.removeXHRBreakpoint", "when": "view == jsBrowserBreakpoints && viewItem == xhrBreakpoint"}, {"command": "extension.js-debug.addXHRBreakpoints", "when": "view == jsBrowserBreakpoints && viewItem == xhrCategory", "group": "inline"}, {"command": "extension.js-debug.callers.goToCaller", "group": "inline", "when": "view == jsExcludedCallers"}, {"command": "extension.js-debug.callers.gotToTarget", "group": "inline", "when": "view == jsExcludedCallers"}, {"command": "extension.js-debug.callers.remove", "group": "inline", "when": "view == jsExcludedCallers"}, {"command": "extension.js-debug.network.viewRequest", "group": "inline@1", "when": "view == jsDebugNetworkTree"}, {"command": "extension.js-debug.network.openBody", "group": "body@1", "when": "view == jsDebugNetworkTree"}, {"command": "extension.js-debug.network.openBodyInHex", "group": "body@2", "when": "view == jsDebugNetworkTree"}, {"command": "extension.js-debug.network.copyUri", "group": "other@1", "when": "view == jsDebugNetworkTree"}, {"command": "extension.js-debug.network.replayXHR", "group": "other@2", "when": "view == jsDebugNetworkTree"}], "editor/title": [{"command": "extension.js-debug.prettyPrint", "group": "navigation", "when": "jsDebugCanPrettyPrint"}]}, "breakpoints": [{"language": "javascript"}, {"language": "typescript"}, {"language": "typescriptreact"}, {"language": "javascriptreact"}, {"language": "fsharp"}, {"language": "html"}, {"language": "wat"}, {"language": "c"}, {"language": "cpp"}, {"language": "rust"}, {"language": "zig"}], "debuggers": [{"aiKey": "0c6ae279ed8443289764825290e4f9e2-1a736e7c-1324-4338-be46-fc2a58ae4d14-7255", "configurationAttributes": {"attach": {"properties": {"address": {"default": "localhost", "description": "%node.address.description%", "type": "string"}, "attachExistingChildren": {"default": false, "description": "%node.attach.attachExistingChildren.description%", "type": "boolean"}, "autoAttachChildProcesses": {"default": true, "description": "%node.launch.autoAttachChildProcesses.description%", "type": "boolean"}, "cascadeTerminateToConfigurations": {"default": [], "description": "%base.cascadeTerminateToConfigurations.label%", "items": {"type": "string", "uniqueItems": true}, "type": "array"}, "continueOnAttach": {"default": true, "markdownDescription": "%node.attach.continueOnAttach%", "type": "boolean"}, "customDescriptionGenerator": {"description": "%customDescriptionGenerator.description%", "type": "string"}, "customPropertiesGenerator": {"deprecated": true, "description": "%customPropertiesGenerator.description%", "type": "string"}, "cwd": {"default": "${workspaceFolder}", "description": "%node.launch.cwd.description%", "tags": ["setup"], "type": "string"}, "enableContentValidation": {"default": true, "description": "%enableContentValidation.description%", "type": "boolean"}, "enableDWARF": {"default": true, "markdownDescription": "%base.enableDWARF.label%", "type": "boolean"}, "env": {"additionalProperties": {"type": ["string", "null"]}, "default": {}, "markdownDescription": "%node.launch.env.description%", "tags": ["setup"], "type": "object"}, "envFile": {"default": "${workspaceFolder}/.env", "description": "%node.launch.envFile.description%", "type": "string"}, "localRoot": {"default": null, "description": "%node.localRoot.description%", "type": ["string", "null"]}, "nodeVersionHint": {"default": 12, "description": "%node.versionHint.description%", "minimum": 8, "type": "number"}, "outFiles": {"default": ["${workspaceFolder}/**/*.(m|c|)js", "!**/node_modules/**"], "description": "%outFiles.description%", "items": {"type": "string"}, "tags": ["setup"], "type": ["array"]}, "outputCapture": {"default": "console", "enum": ["console", "std"], "markdownDescription": "%node.launch.outputCapture.description%"}, "pauseForSourceMap": {"default": false, "markdownDescription": "%node.pauseForSourceMap.description%", "type": "boolean"}, "port": {"default": 9229, "description": "%node.port.description%", "oneOf": [{"type": "integer"}, {"pattern": "^\\${.*}$", "type": "string"}], "tags": ["setup"]}, "processId": {"default": "${command:PickProcess}", "description": "%node.attach.processId.description%", "type": "string"}, "remoteHostHeader": {"description": "%node.remote.host.header.description%", "type": "string"}, "remoteRoot": {"default": null, "description": "%node.remoteRoot.description%", "type": ["string", "null"]}, "resolveSourceMapLocations": {"default": ["${workspaceFolder}/**", "!**/node_modules/**"], "description": "%node.resolveSourceMapLocations.description%", "items": {"type": "string"}, "type": ["array", "null"]}, "restart": {"default": true, "description": "%node.attach.restart.description%", "oneOf": [{"type": "boolean"}, {"properties": {"delay": {"default": 1000, "minimum": 0, "type": "number"}, "maxAttempts": {"default": 10, "minimum": 0, "type": "number"}}, "type": "object"}]}, "runtimeSourcemapPausePatterns": {"default": [], "items": {"type": "string"}, "markdownDescription": "%node.launch.runtimeSourcemapPausePatterns%", "type": "array"}, "showAsyncStacks": {"default": true, "description": "%node.showAsyncStacks.description%", "oneOf": [{"type": "boolean"}, {"properties": {"onAttach": {"default": 32, "type": "number"}}, "required": ["onAttach"], "type": "object"}, {"properties": {"onceBreakpointResolved": {"default": 32, "type": "number"}}, "required": ["onceBreakpointResolved"], "type": "object"}]}, "skipFiles": {"default": ["${/**"], "description": "%browser.skipFiles.description%", "type": "array"}, "smartStep": {"default": true, "description": "%smartStep.description%", "type": "boolean"}, "sourceMapPathOverrides": {"default": {"meteor://💻app/*": "${workspaceFolder}/*", "webpack:///./~/*": "${workspaceFolder}/node_modules/*", "webpack://?:*/*": "${workspaceFolder}/*"}, "description": "%node.sourceMapPathOverrides.description%", "type": "object"}, "sourceMapRenames": {"default": true, "description": "%browser.sourceMapRenames.description%", "type": "boolean"}, "sourceMaps": {"default": true, "description": "%browser.sourceMaps.description%", "type": "boolean"}, "timeout": {"default": 10000, "description": "%node.timeout.description%", "type": "number"}, "timeouts": {"additionalProperties": false, "default": {}, "description": "%timeouts.generalDescription%", "markdownDescription": "%timeouts.generalDescription.markdown%", "properties": {"hoverEvaluation": {"default": 500, "description": "%timeouts.hoverEvaluation.description%", "type": "number"}, "sourceMapCumulativePause": {"default": 1000, "description": "%timeouts.sourceMaps.sourceMapCumulativePause.description%", "type": "number"}, "sourceMapMinPause": {"default": 1000, "description": "%timeouts.sourceMaps.sourceMapMinPause.description%", "type": "number"}}, "type": "object"}, "trace": {"default": true, "description": "%trace.description%", "oneOf": [{"description": "%trace.boolean.description%", "type": "boolean"}, {"additionalProperties": false, "properties": {"logFile": {"description": "%trace.logFile.description%", "type": ["string", "null"]}, "stdio": {"description": "%trace.stdio.description%", "type": "boolean"}}, "type": "object"}]}, "websocketAddress": {"description": "%node.websocket.address.description%", "type": "string"}}}, "launch": {"properties": {"args": {"default": [], "description": "%node.launch.args.description%", "items": {"type": "string"}, "tags": ["setup"], "type": ["array", "string"]}, "attachSimplePort": {"default": 9229, "description": "%node.attachSimplePort.description%", "oneOf": [{"type": "integer"}, {"pattern": "^\\${.*}$", "type": "string"}]}, "autoAttachChildProcesses": {"default": true, "description": "%node.launch.autoAttachChildProcesses.description%", "type": "boolean"}, "cascadeTerminateToConfigurations": {"default": [], "description": "%base.cascadeTerminateToConfigurations.label%", "items": {"type": "string", "uniqueItems": true}, "type": "array"}, "console": {"default": "internalConsole", "description": "%node.launch.console.description%", "enum": ["internalConsole", "integratedTerminal", "externalTerminal"], "enumDescriptions": ["%node.launch.console.internalConsole.description%", "%node.launch.console.integratedTerminal.description%", "%node.launch.console.externalTerminal.description%"], "type": "string"}, "customDescriptionGenerator": {"description": "%customDescriptionGenerator.description%", "type": "string"}, "customPropertiesGenerator": {"deprecated": true, "description": "%customPropertiesGenerator.description%", "type": "string"}, "cwd": {"default": "${workspaceFolder}", "description": "%node.launch.cwd.description%", "tags": ["setup"], "type": "string"}, "enableContentValidation": {"default": true, "description": "%enableContentValidation.description%", "type": "boolean"}, "enableDWARF": {"default": true, "markdownDescription": "%base.enableDWARF.label%", "type": "boolean"}, "env": {"additionalProperties": {"type": ["string", "null"]}, "default": {}, "markdownDescription": "%node.launch.env.description%", "tags": ["setup"], "type": "object"}, "envFile": {"default": "${workspaceFolder}/.env", "description": "%node.launch.envFile.description%", "type": "string"}, "experimentalNetworking": {"default": "auto", "description": "%node.experimentalNetworking.description%", "enum": ["auto", "on", "off"], "type": "string"}, "killBehavior": {"default": "forceful", "enum": ["forceful", "polite", "none"], "markdownDescription": "%node.killBehavior.description%", "type": "string"}, "localRoot": {"default": null, "description": "%node.localRoot.description%", "type": ["string", "null"]}, "nodeVersionHint": {"default": 12, "description": "%node.versionHint.description%", "minimum": 8, "type": "number"}, "outFiles": {"default": ["${workspaceFolder}/**/*.(m|c|)js", "!**/node_modules/**"], "description": "%outFiles.description%", "items": {"type": "string"}, "tags": ["setup"], "type": ["array"]}, "outputCapture": {"default": "console", "enum": ["console", "std"], "markdownDescription": "%node.launch.outputCapture.description%"}, "pauseForSourceMap": {"default": false, "markdownDescription": "%node.pauseForSourceMap.description%", "type": "boolean"}, "profileStartup": {"default": true, "description": "%node.profileStartup.description%", "type": "boolean"}, "program": {"default": "", "description": "%node.launch.program.description%", "tags": ["setup"], "type": "string"}, "remoteRoot": {"default": null, "description": "%node.remoteRoot.description%", "type": ["string", "null"]}, "resolveSourceMapLocations": {"default": ["${workspaceFolder}/**", "!**/node_modules/**"], "description": "%node.resolveSourceMapLocations.description%", "items": {"type": "string"}, "type": ["array", "null"]}, "restart": {"default": true, "description": "%node.attach.restart.description%", "oneOf": [{"type": "boolean"}, {"properties": {"delay": {"default": 1000, "minimum": 0, "type": "number"}, "maxAttempts": {"default": 10, "minimum": 0, "type": "number"}}, "type": "object"}]}, "runtimeArgs": {"default": [], "description": "%node.launch.runtimeArgs.description%", "items": {"type": "string"}, "tags": ["setup"], "type": "array"}, "runtimeExecutable": {"default": "node", "markdownDescription": "%node.launch.runtimeExecutable.description%", "type": ["string", "null"]}, "runtimeSourcemapPausePatterns": {"default": [], "items": {"type": "string"}, "markdownDescription": "%node.launch.runtimeSourcemapPausePatterns%", "type": "array"}, "runtimeVersion": {"default": "default", "markdownDescription": "%node.launch.runtimeVersion.description%", "type": "string"}, "showAsyncStacks": {"default": true, "description": "%node.showAsyncStacks.description%", "oneOf": [{"type": "boolean"}, {"properties": {"onAttach": {"default": 32, "type": "number"}}, "required": ["onAttach"], "type": "object"}, {"properties": {"onceBreakpointResolved": {"default": 32, "type": "number"}}, "required": ["onceBreakpointResolved"], "type": "object"}]}, "skipFiles": {"default": ["${/**"], "description": "%browser.skipFiles.description%", "type": "array"}, "smartStep": {"default": true, "description": "%smartStep.description%", "type": "boolean"}, "sourceMapPathOverrides": {"default": {"meteor://💻app/*": "${workspaceFolder}/*", "webpack:///./~/*": "${workspaceFolder}/node_modules/*", "webpack://?:*/*": "${workspaceFolder}/*"}, "description": "%node.sourceMapPathOverrides.description%", "type": "object"}, "sourceMapRenames": {"default": true, "description": "%browser.sourceMapRenames.description%", "type": "boolean"}, "sourceMaps": {"default": true, "description": "%browser.sourceMaps.description%", "type": "boolean"}, "stopOnEntry": {"default": true, "description": "%node.stopOnEntry.description%", "type": ["boolean", "string"]}, "timeout": {"default": 10000, "description": "%node.timeout.description%", "type": "number"}, "timeouts": {"additionalProperties": false, "default": {}, "description": "%timeouts.generalDescription%", "markdownDescription": "%timeouts.generalDescription.markdown%", "properties": {"hoverEvaluation": {"default": 500, "description": "%timeouts.hoverEvaluation.description%", "type": "number"}, "sourceMapCumulativePause": {"default": 1000, "description": "%timeouts.sourceMaps.sourceMapCumulativePause.description%", "type": "number"}, "sourceMapMinPause": {"default": 1000, "description": "%timeouts.sourceMaps.sourceMapMinPause.description%", "type": "number"}}, "type": "object"}, "trace": {"default": true, "description": "%trace.description%", "oneOf": [{"description": "%trace.boolean.description%", "type": "boolean"}, {"additionalProperties": false, "properties": {"logFile": {"description": "%trace.logFile.description%", "type": ["string", "null"]}, "stdio": {"description": "%trace.stdio.description%", "type": "boolean"}}, "type": "object"}]}}}}, "configurationSnippets": [], "deprecated": "Please use type node instead", "label": "%node.label%", "languages": ["javascript", "typescript", "javascriptreact", "typescriptreact"], "strings": {"unverifiedBreakpoints": "%debug.unverifiedBreakpoints%"}, "type": "pwa-node", "variables": {"PickProcess": "extension.js-debug.pickNodeProcess"}}, {"aiKey": "0c6ae279ed8443289764825290e4f9e2-1a736e7c-1324-4338-be46-fc2a58ae4d14-7255", "configurationAttributes": {"attach": {"properties": {"address": {"default": "localhost", "description": "%node.address.description%", "type": "string"}, "attachExistingChildren": {"default": false, "description": "%node.attach.attachExistingChildren.description%", "type": "boolean"}, "autoAttachChildProcesses": {"default": true, "description": "%node.launch.autoAttachChildProcesses.description%", "type": "boolean"}, "cascadeTerminateToConfigurations": {"default": [], "description": "%base.cascadeTerminateToConfigurations.label%", "items": {"type": "string", "uniqueItems": true}, "type": "array"}, "continueOnAttach": {"default": true, "markdownDescription": "%node.attach.continueOnAttach%", "type": "boolean"}, "customDescriptionGenerator": {"description": "%customDescriptionGenerator.description%", "type": "string"}, "customPropertiesGenerator": {"deprecated": true, "description": "%customPropertiesGenerator.description%", "type": "string"}, "cwd": {"default": "${workspaceFolder}", "description": "%node.launch.cwd.description%", "tags": ["setup"], "type": "string"}, "enableContentValidation": {"default": true, "description": "%enableContentValidation.description%", "type": "boolean"}, "enableDWARF": {"default": true, "markdownDescription": "%base.enableDWARF.label%", "type": "boolean"}, "env": {"additionalProperties": {"type": ["string", "null"]}, "default": {}, "markdownDescription": "%node.launch.env.description%", "tags": ["setup"], "type": "object"}, "envFile": {"default": "${workspaceFolder}/.env", "description": "%node.launch.envFile.description%", "type": "string"}, "localRoot": {"default": null, "description": "%node.localRoot.description%", "type": ["string", "null"]}, "nodeVersionHint": {"default": 12, "description": "%node.versionHint.description%", "minimum": 8, "type": "number"}, "outFiles": {"default": ["${workspaceFolder}/**/*.(m|c|)js", "!**/node_modules/**"], "description": "%outFiles.description%", "items": {"type": "string"}, "tags": ["setup"], "type": ["array"]}, "outputCapture": {"default": "console", "enum": ["console", "std"], "markdownDescription": "%node.launch.outputCapture.description%"}, "pauseForSourceMap": {"default": false, "markdownDescription": "%node.pauseForSourceMap.description%", "type": "boolean"}, "port": {"default": 9229, "description": "%node.port.description%", "oneOf": [{"type": "integer"}, {"pattern": "^\\${.*}$", "type": "string"}], "tags": ["setup"]}, "processId": {"default": "${command:PickProcess}", "description": "%node.attach.processId.description%", "type": "string"}, "remoteHostHeader": {"description": "%node.remote.host.header.description%", "type": "string"}, "remoteRoot": {"default": null, "description": "%node.remoteRoot.description%", "type": ["string", "null"]}, "resolveSourceMapLocations": {"default": ["${workspaceFolder}/**", "!**/node_modules/**"], "description": "%node.resolveSourceMapLocations.description%", "items": {"type": "string"}, "type": ["array", "null"]}, "restart": {"default": true, "description": "%node.attach.restart.description%", "oneOf": [{"type": "boolean"}, {"properties": {"delay": {"default": 1000, "minimum": 0, "type": "number"}, "maxAttempts": {"default": 10, "minimum": 0, "type": "number"}}, "type": "object"}]}, "runtimeSourcemapPausePatterns": {"default": [], "items": {"type": "string"}, "markdownDescription": "%node.launch.runtimeSourcemapPausePatterns%", "type": "array"}, "showAsyncStacks": {"default": true, "description": "%node.showAsyncStacks.description%", "oneOf": [{"type": "boolean"}, {"properties": {"onAttach": {"default": 32, "type": "number"}}, "required": ["onAttach"], "type": "object"}, {"properties": {"onceBreakpointResolved": {"default": 32, "type": "number"}}, "required": ["onceBreakpointResolved"], "type": "object"}]}, "skipFiles": {"default": ["${/**"], "description": "%browser.skipFiles.description%", "type": "array"}, "smartStep": {"default": true, "description": "%smartStep.description%", "type": "boolean"}, "sourceMapPathOverrides": {"default": {"meteor://💻app/*": "${workspaceFolder}/*", "webpack:///./~/*": "${workspaceFolder}/node_modules/*", "webpack://?:*/*": "${workspaceFolder}/*"}, "description": "%node.sourceMapPathOverrides.description%", "type": "object"}, "sourceMapRenames": {"default": true, "description": "%browser.sourceMapRenames.description%", "type": "boolean"}, "sourceMaps": {"default": true, "description": "%browser.sourceMaps.description%", "type": "boolean"}, "timeout": {"default": 10000, "description": "%node.timeout.description%", "type": "number"}, "timeouts": {"additionalProperties": false, "default": {}, "description": "%timeouts.generalDescription%", "markdownDescription": "%timeouts.generalDescription.markdown%", "properties": {"hoverEvaluation": {"default": 500, "description": "%timeouts.hoverEvaluation.description%", "type": "number"}, "sourceMapCumulativePause": {"default": 1000, "description": "%timeouts.sourceMaps.sourceMapCumulativePause.description%", "type": "number"}, "sourceMapMinPause": {"default": 1000, "description": "%timeouts.sourceMaps.sourceMapMinPause.description%", "type": "number"}}, "type": "object"}, "trace": {"default": true, "description": "%trace.description%", "oneOf": [{"description": "%trace.boolean.description%", "type": "boolean"}, {"additionalProperties": false, "properties": {"logFile": {"description": "%trace.logFile.description%", "type": ["string", "null"]}, "stdio": {"description": "%trace.stdio.description%", "type": "boolean"}}, "type": "object"}]}, "websocketAddress": {"description": "%node.websocket.address.description%", "type": "string"}}}, "launch": {"properties": {"args": {"default": [], "description": "%node.launch.args.description%", "items": {"type": "string"}, "tags": ["setup"], "type": ["array", "string"]}, "attachSimplePort": {"default": 9229, "description": "%node.attachSimplePort.description%", "oneOf": [{"type": "integer"}, {"pattern": "^\\${.*}$", "type": "string"}]}, "autoAttachChildProcesses": {"default": true, "description": "%node.launch.autoAttachChildProcesses.description%", "type": "boolean"}, "cascadeTerminateToConfigurations": {"default": [], "description": "%base.cascadeTerminateToConfigurations.label%", "items": {"type": "string", "uniqueItems": true}, "type": "array"}, "console": {"default": "internalConsole", "description": "%node.launch.console.description%", "enum": ["internalConsole", "integratedTerminal", "externalTerminal"], "enumDescriptions": ["%node.launch.console.internalConsole.description%", "%node.launch.console.integratedTerminal.description%", "%node.launch.console.externalTerminal.description%"], "type": "string"}, "customDescriptionGenerator": {"description": "%customDescriptionGenerator.description%", "type": "string"}, "customPropertiesGenerator": {"deprecated": true, "description": "%customPropertiesGenerator.description%", "type": "string"}, "cwd": {"default": "${workspaceFolder}", "description": "%node.launch.cwd.description%", "tags": ["setup"], "type": "string"}, "enableContentValidation": {"default": true, "description": "%enableContentValidation.description%", "type": "boolean"}, "enableDWARF": {"default": true, "markdownDescription": "%base.enableDWARF.label%", "type": "boolean"}, "env": {"additionalProperties": {"type": ["string", "null"]}, "default": {}, "markdownDescription": "%node.launch.env.description%", "tags": ["setup"], "type": "object"}, "envFile": {"default": "${workspaceFolder}/.env", "description": "%node.launch.envFile.description%", "type": "string"}, "experimentalNetworking": {"default": "auto", "description": "%node.experimentalNetworking.description%", "enum": ["auto", "on", "off"], "type": "string"}, "killBehavior": {"default": "forceful", "enum": ["forceful", "polite", "none"], "markdownDescription": "%node.killBehavior.description%", "type": "string"}, "localRoot": {"default": null, "description": "%node.localRoot.description%", "type": ["string", "null"]}, "nodeVersionHint": {"default": 12, "description": "%node.versionHint.description%", "minimum": 8, "type": "number"}, "outFiles": {"default": ["${workspaceFolder}/**/*.(m|c|)js", "!**/node_modules/**"], "description": "%outFiles.description%", "items": {"type": "string"}, "tags": ["setup"], "type": ["array"]}, "outputCapture": {"default": "console", "enum": ["console", "std"], "markdownDescription": "%node.launch.outputCapture.description%"}, "pauseForSourceMap": {"default": false, "markdownDescription": "%node.pauseForSourceMap.description%", "type": "boolean"}, "profileStartup": {"default": true, "description": "%node.profileStartup.description%", "type": "boolean"}, "program": {"default": "", "description": "%node.launch.program.description%", "tags": ["setup"], "type": "string"}, "remoteRoot": {"default": null, "description": "%node.remoteRoot.description%", "type": ["string", "null"]}, "resolveSourceMapLocations": {"default": ["${workspaceFolder}/**", "!**/node_modules/**"], "description": "%node.resolveSourceMapLocations.description%", "items": {"type": "string"}, "type": ["array", "null"]}, "restart": {"default": true, "description": "%node.attach.restart.description%", "oneOf": [{"type": "boolean"}, {"properties": {"delay": {"default": 1000, "minimum": 0, "type": "number"}, "maxAttempts": {"default": 10, "minimum": 0, "type": "number"}}, "type": "object"}]}, "runtimeArgs": {"default": [], "description": "%node.launch.runtimeArgs.description%", "items": {"type": "string"}, "tags": ["setup"], "type": "array"}, "runtimeExecutable": {"default": "node", "markdownDescription": "%node.launch.runtimeExecutable.description%", "type": ["string", "null"]}, "runtimeSourcemapPausePatterns": {"default": [], "items": {"type": "string"}, "markdownDescription": "%node.launch.runtimeSourcemapPausePatterns%", "type": "array"}, "runtimeVersion": {"default": "default", "markdownDescription": "%node.launch.runtimeVersion.description%", "type": "string"}, "showAsyncStacks": {"default": true, "description": "%node.showAsyncStacks.description%", "oneOf": [{"type": "boolean"}, {"properties": {"onAttach": {"default": 32, "type": "number"}}, "required": ["onAttach"], "type": "object"}, {"properties": {"onceBreakpointResolved": {"default": 32, "type": "number"}}, "required": ["onceBreakpointResolved"], "type": "object"}]}, "skipFiles": {"default": ["${/**"], "description": "%browser.skipFiles.description%", "type": "array"}, "smartStep": {"default": true, "description": "%smartStep.description%", "type": "boolean"}, "sourceMapPathOverrides": {"default": {"meteor://💻app/*": "${workspaceFolder}/*", "webpack:///./~/*": "${workspaceFolder}/node_modules/*", "webpack://?:*/*": "${workspaceFolder}/*"}, "description": "%node.sourceMapPathOverrides.description%", "type": "object"}, "sourceMapRenames": {"default": true, "description": "%browser.sourceMapRenames.description%", "type": "boolean"}, "sourceMaps": {"default": true, "description": "%browser.sourceMaps.description%", "type": "boolean"}, "stopOnEntry": {"default": true, "description": "%node.stopOnEntry.description%", "type": ["boolean", "string"]}, "timeout": {"default": 10000, "description": "%node.timeout.description%", "type": "number"}, "timeouts": {"additionalProperties": false, "default": {}, "description": "%timeouts.generalDescription%", "markdownDescription": "%timeouts.generalDescription.markdown%", "properties": {"hoverEvaluation": {"default": 500, "description": "%timeouts.hoverEvaluation.description%", "type": "number"}, "sourceMapCumulativePause": {"default": 1000, "description": "%timeouts.sourceMaps.sourceMapCumulativePause.description%", "type": "number"}, "sourceMapMinPause": {"default": 1000, "description": "%timeouts.sourceMaps.sourceMapMinPause.description%", "type": "number"}}, "type": "object"}, "trace": {"default": true, "description": "%trace.description%", "oneOf": [{"description": "%trace.boolean.description%", "type": "boolean"}, {"additionalProperties": false, "properties": {"logFile": {"description": "%trace.logFile.description%", "type": ["string", "null"]}, "stdio": {"description": "%trace.stdio.description%", "type": "boolean"}}, "type": "object"}]}}}}, "configurationSnippets": [{"body": {"name": "${1:Attach}", "port": 9229, "request": "attach", "skipFiles": ["<node_internals>/**"], "type": "node"}, "description": "%node.snippet.attach.description%", "label": "%node.snippet.attach.label%"}, {"body": {"address": "${2:TCP/IP address of process to be debugged}", "localRoot": "^\"\\${workspaceFolder}\"", "name": "${1:Attach to Remote}", "port": 9229, "remoteRoot": "${3:Absolute path to the remote directory containing the program}", "request": "attach", "skipFiles": ["<node_internals>/**"], "type": "node"}, "description": "%node.snippet.remoteattach.description%", "label": "%node.snippet.remoteattach.label%"}, {"body": {"name": "${1:Attach by Process ID}", "processId": "^\"\\${command:PickProcess}\"", "request": "attach", "skipFiles": ["<node_internals>/**"], "type": "node"}, "description": "%node.snippet.attachProcess.description%", "label": "%node.snippet.attachProcess.label%"}, {"body": {"name": "${2:Launch Program}", "program": "^\"\\${workspaceFolder}/${1:app.js}\"", "request": "launch", "skipFiles": ["<node_internals>/**"], "type": "node"}, "description": "%node.snippet.launch.description%", "label": "%node.snippet.launch.label%"}, {"body": {"name": "${1:Launch via NPM}", "request": "launch", "runtimeArgs": ["run-script", "debug"], "runtimeExecutable": "npm", "skipFiles": ["<node_internals>/**"], "type": "node"}, "label": "%node.snippet.npm.label%", "markdownDescription": "%node.snippet.npm.description%"}, {"body": {"console": "integratedTerminal", "internalConsoleOptions": "neverOpen", "name": "nodemon", "program": "^\"\\${workspaceFolder}/${1:app.js}\"", "request": "launch", "restart": true, "runtimeExecutable": "nodemon", "skipFiles": ["<node_internals>/**"], "type": "node"}, "description": "%node.snippet.nodemon.description%", "label": "%node.snippet.nodemon.label%"}, {"body": {"args": ["-u", "tdd", "--timeout", "999999", "--colors", "^\"\\${workspaceFolder}/${1:test}\""], "internalConsoleOptions": "openOnSessionStart", "name": "<PERSON><PERSON>", "program": "^\"mocha\"", "request": "launch", "skipFiles": ["<node_internals>/**"], "type": "node"}, "description": "%node.snippet.mocha.description%", "label": "%node.snippet.mocha.label%"}, {"body": {"args": ["${1:generator}"], "console": "integratedTerminal", "internalConsoleOptions": "neverOpen", "name": "Yeoman ${1:generator}", "program": "^\"\\${workspaceFolder}/node_modules/yo/lib/cli.js\"", "request": "launch", "skipFiles": ["<node_internals>/**"], "type": "node"}, "label": "%node.snippet.yo.label%", "markdownDescription": "%node.snippet.yo.description%"}, {"body": {"args": ["${1:task}"], "name": "Gulp ${1:task}", "program": "^\"\\${workspaceFolder}/node_modules/gulp/bin/gulp.js\"", "request": "launch", "skipFiles": ["<node_internals>/**"], "type": "node"}, "description": "%node.snippet.gulp.description%", "label": "%node.snippet.gulp.label%"}, {"body": {"name": "Electron Main", "program": "^\"\\${workspaceFolder}/main.js\"", "request": "launch", "runtimeExecutable": "^\"electron\"", "skipFiles": ["<node_internals>/**"], "type": "node"}, "description": "%node.snippet.electron.description%", "label": "%node.snippet.electron.label%"}], "label": "%node.label%", "strings": {"unverifiedBreakpoints": "%debug.unverifiedBreakpoints%"}, "type": "node", "variables": {"PickProcess": "extension.js-debug.pickNodeProcess"}}, {"aiKey": "0c6ae279ed8443289764825290e4f9e2-1a736e7c-1324-4338-be46-fc2a58ae4d14-7255", "configurationAttributes": {"launch": {"properties": {"autoAttachChildProcesses": {"default": true, "description": "%node.launch.autoAttachChildProcesses.description%", "type": "boolean"}, "cascadeTerminateToConfigurations": {"default": [], "description": "%base.cascadeTerminateToConfigurations.label%", "items": {"type": "string", "uniqueItems": true}, "type": "array"}, "command": {"default": "npm start", "description": "%debug.terminal.program.description%", "tags": ["setup"], "type": ["string", "null"]}, "customDescriptionGenerator": {"description": "%customDescriptionGenerator.description%", "type": "string"}, "customPropertiesGenerator": {"deprecated": true, "description": "%customPropertiesGenerator.description%", "type": "string"}, "cwd": {"default": "${workspaceFolder}", "description": "%node.launch.cwd.description%", "tags": ["setup"], "type": "string"}, "enableContentValidation": {"default": true, "description": "%enableContentValidation.description%", "type": "boolean"}, "enableDWARF": {"default": true, "markdownDescription": "%base.enableDWARF.label%", "type": "boolean"}, "env": {"additionalProperties": {"type": ["string", "null"]}, "default": {}, "markdownDescription": "%node.launch.env.description%", "tags": ["setup"], "type": "object"}, "envFile": {"default": "${workspaceFolder}/.env", "description": "%node.launch.envFile.description%", "type": "string"}, "localRoot": {"default": null, "description": "%node.localRoot.description%", "type": ["string", "null"]}, "nodeVersionHint": {"default": 12, "description": "%node.versionHint.description%", "minimum": 8, "type": "number"}, "outFiles": {"default": ["${workspaceFolder}/**/*.(m|c|)js", "!**/node_modules/**"], "description": "%outFiles.description%", "items": {"type": "string"}, "tags": ["setup"], "type": ["array"]}, "outputCapture": {"default": "console", "enum": ["console", "std"], "markdownDescription": "%node.launch.outputCapture.description%"}, "pauseForSourceMap": {"default": false, "markdownDescription": "%node.pauseForSourceMap.description%", "type": "boolean"}, "remoteRoot": {"default": null, "description": "%node.remoteRoot.description%", "type": ["string", "null"]}, "resolveSourceMapLocations": {"default": ["${workspaceFolder}/**", "!**/node_modules/**"], "description": "%node.resolveSourceMapLocations.description%", "items": {"type": "string"}, "type": ["array", "null"]}, "runtimeSourcemapPausePatterns": {"default": [], "items": {"type": "string"}, "markdownDescription": "%node.launch.runtimeSourcemapPausePatterns%", "type": "array"}, "showAsyncStacks": {"default": true, "description": "%node.showAsyncStacks.description%", "oneOf": [{"type": "boolean"}, {"properties": {"onAttach": {"default": 32, "type": "number"}}, "required": ["onAttach"], "type": "object"}, {"properties": {"onceBreakpointResolved": {"default": 32, "type": "number"}}, "required": ["onceBreakpointResolved"], "type": "object"}]}, "skipFiles": {"default": ["${/**"], "description": "%browser.skipFiles.description%", "type": "array"}, "smartStep": {"default": true, "description": "%smartStep.description%", "type": "boolean"}, "sourceMapPathOverrides": {"default": {"meteor://💻app/*": "${workspaceFolder}/*", "webpack:///./~/*": "${workspaceFolder}/node_modules/*", "webpack://?:*/*": "${workspaceFolder}/*"}, "description": "%node.sourceMapPathOverrides.description%", "type": "object"}, "sourceMapRenames": {"default": true, "description": "%browser.sourceMapRenames.description%", "type": "boolean"}, "sourceMaps": {"default": true, "description": "%browser.sourceMaps.description%", "type": "boolean"}, "timeout": {"default": 10000, "description": "%node.timeout.description%", "type": "number"}, "timeouts": {"additionalProperties": false, "default": {}, "description": "%timeouts.generalDescription%", "markdownDescription": "%timeouts.generalDescription.markdown%", "properties": {"hoverEvaluation": {"default": 500, "description": "%timeouts.hoverEvaluation.description%", "type": "number"}, "sourceMapCumulativePause": {"default": 1000, "description": "%timeouts.sourceMaps.sourceMapCumulativePause.description%", "type": "number"}, "sourceMapMinPause": {"default": 1000, "description": "%timeouts.sourceMaps.sourceMapMinPause.description%", "type": "number"}}, "type": "object"}, "trace": {"default": true, "description": "%trace.description%", "oneOf": [{"description": "%trace.boolean.description%", "type": "boolean"}, {"additionalProperties": false, "properties": {"logFile": {"description": "%trace.logFile.description%", "type": ["string", "null"]}, "stdio": {"description": "%trace.stdio.description%", "type": "boolean"}}, "type": "object"}]}}}}, "configurationSnippets": [{"body": {"command": "npm start", "name": "Run npm start", "request": "launch", "type": "node-terminal"}, "description": "%debug.terminal.snippet.label%", "label": "%debug.terminal.snippet.label%"}], "label": "%debug.terminal.label%", "languages": [], "strings": {"unverifiedBreakpoints": "%debug.unverifiedBreakpoints%"}, "type": "node-terminal"}, {"aiKey": "0c6ae279ed8443289764825290e4f9e2-1a736e7c-1324-4338-be46-fc2a58ae4d14-7255", "configurationAttributes": {"launch": {"properties": {"args": {"default": ["--extensionDevelopmentPath=${workspaceFolder}"], "description": "%node.launch.args.description%", "items": {"type": "string"}, "tags": ["setup"], "type": "array"}, "autoAttachChildProcesses": {"default": true, "description": "%node.launch.autoAttachChildProcesses.description%", "type": "boolean"}, "cascadeTerminateToConfigurations": {"default": [], "description": "%base.cascadeTerminateToConfigurations.label%", "items": {"type": "string", "uniqueItems": true}, "type": "array"}, "customDescriptionGenerator": {"description": "%customDescriptionGenerator.description%", "type": "string"}, "customPropertiesGenerator": {"deprecated": true, "description": "%customPropertiesGenerator.description%", "type": "string"}, "cwd": {"default": "${workspaceFolder}", "description": "%node.launch.cwd.description%", "tags": ["setup"], "type": "string"}, "debugWebWorkerHost": {"default": true, "markdownDescription": "%extensionHost.launch.debugWebWorkerHost%", "type": ["boolean"]}, "debugWebviews": {"default": true, "markdownDescription": "%extensionHost.launch.debugWebviews%", "type": ["boolean"]}, "enableContentValidation": {"default": true, "description": "%enableContentValidation.description%", "type": "boolean"}, "enableDWARF": {"default": true, "markdownDescription": "%base.enableDWARF.label%", "type": "boolean"}, "env": {"additionalProperties": {"type": ["string", "null"]}, "default": {}, "markdownDescription": "%node.launch.env.description%", "tags": ["setup"], "type": "object"}, "envFile": {"default": "${workspaceFolder}/.env", "description": "%node.launch.envFile.description%", "type": "string"}, "localRoot": {"default": null, "description": "%node.localRoot.description%", "type": ["string", "null"]}, "nodeVersionHint": {"default": 12, "description": "%node.versionHint.description%", "minimum": 8, "type": "number"}, "outFiles": {"default": ["${workspaceFolder}/**/*.(m|c|)js", "!**/node_modules/**"], "description": "%outFiles.description%", "items": {"type": "string"}, "tags": ["setup"], "type": ["array"]}, "outputCapture": {"default": "console", "enum": ["console", "std"], "markdownDescription": "%node.launch.outputCapture.description%"}, "pauseForSourceMap": {"default": false, "markdownDescription": "%node.pauseForSourceMap.description%", "type": "boolean"}, "remoteRoot": {"default": null, "description": "%node.remoteRoot.description%", "type": ["string", "null"]}, "rendererDebugOptions": {"default": {"webRoot": "${workspaceFolder}"}, "markdownDescription": "%extensionHost.launch.rendererDebugOptions%", "properties": {"address": {"default": "localhost", "description": "%browser.address.description%", "type": "string"}, "browserAttachLocation": {"default": null, "description": "%browser.browserAttachLocation.description%", "oneOf": [{"type": "null"}, {"enum": ["ui", "workspace"], "type": "string"}]}, "cascadeTerminateToConfigurations": {"default": [], "description": "%base.cascadeTerminateToConfigurations.label%", "items": {"type": "string", "uniqueItems": true}, "type": "array"}, "customDescriptionGenerator": {"description": "%customDescriptionGenerator.description%", "type": "string"}, "customPropertiesGenerator": {"deprecated": true, "description": "%customPropertiesGenerator.description%", "type": "string"}, "disableNetworkCache": {"default": true, "description": "%browser.disableNetworkCache.description%", "type": "boolean"}, "enableContentValidation": {"default": true, "description": "%enableContentValidation.description%", "type": "boolean"}, "enableDWARF": {"default": true, "markdownDescription": "%base.enableDWARF.label%", "type": "boolean"}, "inspectUri": {"default": null, "description": "%browser.inspectUri.description%", "type": ["string", "null"]}, "outFiles": {"default": ["${workspaceFolder}/**/*.(m|c|)js", "!**/node_modules/**"], "description": "%outFiles.description%", "items": {"type": "string"}, "tags": ["setup"], "type": ["array"]}, "outputCapture": {"default": "console", "enum": ["console", "std"], "markdownDescription": "%node.launch.outputCapture.description%"}, "pathMapping": {"default": {}, "description": "%browser.pathMapping.description%", "type": "object"}, "pauseForSourceMap": {"default": false, "markdownDescription": "%node.pauseForSourceMap.description%", "type": "boolean"}, "perScriptSourcemaps": {"default": "auto", "description": "%browser.perScriptSourcemaps.description%", "enum": ["yes", "no", "auto"], "type": "string"}, "port": {"default": 9229, "description": "%browser.attach.port.description%", "oneOf": [{"type": "integer"}, {"pattern": "^\\${.*}$", "type": "string"}], "tags": ["setup"]}, "resolveSourceMapLocations": {"default": null, "description": "%node.resolveSourceMapLocations.description%", "items": {"type": "string"}, "type": ["array", "null"]}, "restart": {"default": false, "markdownDescription": "%browser.restart%", "type": "boolean"}, "server": {"oneOf": [{"additionalProperties": false, "default": {"program": "node my-server.js"}, "description": "%browser.server.description%", "properties": {"args": {"default": [], "description": "%node.launch.args.description%", "items": {"type": "string"}, "tags": ["setup"], "type": ["array", "string"]}, "attachSimplePort": {"default": 9229, "description": "%node.attachSimplePort.description%", "oneOf": [{"type": "integer"}, {"pattern": "^\\${.*}$", "type": "string"}]}, "autoAttachChildProcesses": {"default": true, "description": "%node.launch.autoAttachChildProcesses.description%", "type": "boolean"}, "cascadeTerminateToConfigurations": {"default": [], "description": "%base.cascadeTerminateToConfigurations.label%", "items": {"type": "string", "uniqueItems": true}, "type": "array"}, "console": {"default": "internalConsole", "description": "%node.launch.console.description%", "enum": ["internalConsole", "integratedTerminal", "externalTerminal"], "enumDescriptions": ["%node.launch.console.internalConsole.description%", "%node.launch.console.integratedTerminal.description%", "%node.launch.console.externalTerminal.description%"], "type": "string"}, "customDescriptionGenerator": {"description": "%customDescriptionGenerator.description%", "type": "string"}, "customPropertiesGenerator": {"deprecated": true, "description": "%customPropertiesGenerator.description%", "type": "string"}, "cwd": {"default": "${workspaceFolder}", "description": "%node.launch.cwd.description%", "tags": ["setup"], "type": "string"}, "enableContentValidation": {"default": true, "description": "%enableContentValidation.description%", "type": "boolean"}, "enableDWARF": {"default": true, "markdownDescription": "%base.enableDWARF.label%", "type": "boolean"}, "env": {"additionalProperties": {"type": ["string", "null"]}, "default": {}, "markdownDescription": "%node.launch.env.description%", "tags": ["setup"], "type": "object"}, "envFile": {"default": "${workspaceFolder}/.env", "description": "%node.launch.envFile.description%", "type": "string"}, "experimentalNetworking": {"default": "auto", "description": "%node.experimentalNetworking.description%", "enum": ["auto", "on", "off"], "type": "string"}, "killBehavior": {"default": "forceful", "enum": ["forceful", "polite", "none"], "markdownDescription": "%node.killBehavior.description%", "type": "string"}, "localRoot": {"default": null, "description": "%node.localRoot.description%", "type": ["string", "null"]}, "nodeVersionHint": {"default": 12, "description": "%node.versionHint.description%", "minimum": 8, "type": "number"}, "outFiles": {"default": ["${workspaceFolder}/**/*.(m|c|)js", "!**/node_modules/**"], "description": "%outFiles.description%", "items": {"type": "string"}, "tags": ["setup"], "type": ["array"]}, "outputCapture": {"default": "console", "enum": ["console", "std"], "markdownDescription": "%node.launch.outputCapture.description%"}, "pauseForSourceMap": {"default": false, "markdownDescription": "%node.pauseForSourceMap.description%", "type": "boolean"}, "profileStartup": {"default": true, "description": "%node.profileStartup.description%", "type": "boolean"}, "program": {"default": "", "description": "%node.launch.program.description%", "tags": ["setup"], "type": "string"}, "remoteRoot": {"default": null, "description": "%node.remoteRoot.description%", "type": ["string", "null"]}, "resolveSourceMapLocations": {"default": ["${workspaceFolder}/**", "!**/node_modules/**"], "description": "%node.resolveSourceMapLocations.description%", "items": {"type": "string"}, "type": ["array", "null"]}, "restart": {"default": true, "description": "%node.attach.restart.description%", "oneOf": [{"type": "boolean"}, {"properties": {"delay": {"default": 1000, "minimum": 0, "type": "number"}, "maxAttempts": {"default": 10, "minimum": 0, "type": "number"}}, "type": "object"}]}, "runtimeArgs": {"default": [], "description": "%node.launch.runtimeArgs.description%", "items": {"type": "string"}, "tags": ["setup"], "type": "array"}, "runtimeExecutable": {"default": "node", "markdownDescription": "%node.launch.runtimeExecutable.description%", "type": ["string", "null"]}, "runtimeSourcemapPausePatterns": {"default": [], "items": {"type": "string"}, "markdownDescription": "%node.launch.runtimeSourcemapPausePatterns%", "type": "array"}, "runtimeVersion": {"default": "default", "markdownDescription": "%node.launch.runtimeVersion.description%", "type": "string"}, "showAsyncStacks": {"default": true, "description": "%node.showAsyncStacks.description%", "oneOf": [{"type": "boolean"}, {"properties": {"onAttach": {"default": 32, "type": "number"}}, "required": ["onAttach"], "type": "object"}, {"properties": {"onceBreakpointResolved": {"default": 32, "type": "number"}}, "required": ["onceBreakpointResolved"], "type": "object"}]}, "skipFiles": {"default": ["${/**"], "description": "%browser.skipFiles.description%", "type": "array"}, "smartStep": {"default": true, "description": "%smartStep.description%", "type": "boolean"}, "sourceMapPathOverrides": {"default": {"meteor://💻app/*": "${workspaceFolder}/*", "webpack:///./~/*": "${workspaceFolder}/node_modules/*", "webpack://?:*/*": "${workspaceFolder}/*"}, "description": "%node.sourceMapPathOverrides.description%", "type": "object"}, "sourceMapRenames": {"default": true, "description": "%browser.sourceMapRenames.description%", "type": "boolean"}, "sourceMaps": {"default": true, "description": "%browser.sourceMaps.description%", "type": "boolean"}, "stopOnEntry": {"default": true, "description": "%node.stopOnEntry.description%", "type": ["boolean", "string"]}, "timeout": {"default": 10000, "description": "%node.timeout.description%", "type": "number"}, "timeouts": {"additionalProperties": false, "default": {}, "description": "%timeouts.generalDescription%", "markdownDescription": "%timeouts.generalDescription.markdown%", "properties": {"hoverEvaluation": {"default": 500, "description": "%timeouts.hoverEvaluation.description%", "type": "number"}, "sourceMapCumulativePause": {"default": 1000, "description": "%timeouts.sourceMaps.sourceMapCumulativePause.description%", "type": "number"}, "sourceMapMinPause": {"default": 1000, "description": "%timeouts.sourceMaps.sourceMapMinPause.description%", "type": "number"}}, "type": "object"}, "trace": {"default": true, "description": "%trace.description%", "oneOf": [{"description": "%trace.boolean.description%", "type": "boolean"}, {"additionalProperties": false, "properties": {"logFile": {"description": "%trace.logFile.description%", "type": ["string", "null"]}, "stdio": {"description": "%trace.stdio.description%", "type": "boolean"}}, "type": "object"}]}}, "type": "object"}, {"additionalProperties": false, "default": {"program": "npm start"}, "description": "%debug.terminal.label%", "properties": {"autoAttachChildProcesses": {"default": true, "description": "%node.launch.autoAttachChildProcesses.description%", "type": "boolean"}, "cascadeTerminateToConfigurations": {"default": [], "description": "%base.cascadeTerminateToConfigurations.label%", "items": {"type": "string", "uniqueItems": true}, "type": "array"}, "command": {"default": "npm start", "description": "%debug.terminal.program.description%", "tags": ["setup"], "type": ["string", "null"]}, "customDescriptionGenerator": {"description": "%customDescriptionGenerator.description%", "type": "string"}, "customPropertiesGenerator": {"deprecated": true, "description": "%customPropertiesGenerator.description%", "type": "string"}, "cwd": {"default": "${workspaceFolder}", "description": "%node.launch.cwd.description%", "docDefault": "localRoot || ${workspaceFolder}", "tags": ["setup"], "type": "string"}, "enableContentValidation": {"default": true, "description": "%enableContentValidation.description%", "type": "boolean"}, "enableDWARF": {"default": true, "markdownDescription": "%base.enableDWARF.label%", "type": "boolean"}, "env": {"additionalProperties": {"type": ["string", "null"]}, "default": {}, "markdownDescription": "%node.launch.env.description%", "tags": ["setup"], "type": "object"}, "envFile": {"default": "${workspaceFolder}/.env", "description": "%node.launch.envFile.description%", "type": "string"}, "localRoot": {"default": null, "description": "%node.localRoot.description%", "type": ["string", "null"]}, "nodeVersionHint": {"default": 12, "description": "%node.versionHint.description%", "minimum": 8, "type": "number"}, "outFiles": {"default": ["${workspaceFolder}/**/*.(m|c|)js", "!**/node_modules/**"], "description": "%outFiles.description%", "items": {"type": "string"}, "tags": ["setup"], "type": ["array"]}, "outputCapture": {"default": "console", "enum": ["console", "std"], "markdownDescription": "%node.launch.outputCapture.description%"}, "pauseForSourceMap": {"default": false, "markdownDescription": "%node.pauseForSourceMap.description%", "type": "boolean"}, "remoteRoot": {"default": null, "description": "%node.remoteRoot.description%", "type": ["string", "null"]}, "resolveSourceMapLocations": {"default": ["${workspaceFolder}/**", "!**/node_modules/**"], "description": "%node.resolveSourceMapLocations.description%", "items": {"type": "string"}, "type": ["array", "null"]}, "runtimeSourcemapPausePatterns": {"default": [], "items": {"type": "string"}, "markdownDescription": "%node.launch.runtimeSourcemapPausePatterns%", "type": "array"}, "showAsyncStacks": {"default": true, "description": "%node.showAsyncStacks.description%", "oneOf": [{"type": "boolean"}, {"properties": {"onAttach": {"default": 32, "type": "number"}}, "required": ["onAttach"], "type": "object"}, {"properties": {"onceBreakpointResolved": {"default": 32, "type": "number"}}, "required": ["onceBreakpointResolved"], "type": "object"}]}, "skipFiles": {"default": ["${/**"], "description": "%browser.skipFiles.description%", "type": "array"}, "smartStep": {"default": true, "description": "%smartStep.description%", "type": "boolean"}, "sourceMapPathOverrides": {"default": {"meteor://💻app/*": "${workspaceFolder}/*", "webpack:///./~/*": "${workspaceFolder}/node_modules/*", "webpack://?:*/*": "${workspaceFolder}/*"}, "description": "%node.sourceMapPathOverrides.description%", "type": "object"}, "sourceMapRenames": {"default": true, "description": "%browser.sourceMapRenames.description%", "type": "boolean"}, "sourceMaps": {"default": true, "description": "%browser.sourceMaps.description%", "type": "boolean"}, "timeout": {"default": 10000, "description": "%node.timeout.description%", "type": "number"}, "timeouts": {"additionalProperties": false, "default": {}, "description": "%timeouts.generalDescription%", "markdownDescription": "%timeouts.generalDescription.markdown%", "properties": {"hoverEvaluation": {"default": 500, "description": "%timeouts.hoverEvaluation.description%", "type": "number"}, "sourceMapCumulativePause": {"default": 1000, "description": "%timeouts.sourceMaps.sourceMapCumulativePause.description%", "type": "number"}, "sourceMapMinPause": {"default": 1000, "description": "%timeouts.sourceMaps.sourceMapMinPause.description%", "type": "number"}}, "type": "object"}, "trace": {"default": true, "description": "%trace.description%", "oneOf": [{"description": "%trace.boolean.description%", "type": "boolean"}, {"additionalProperties": false, "properties": {"logFile": {"description": "%trace.logFile.description%", "type": ["string", "null"]}, "stdio": {"description": "%trace.stdio.description%", "type": "boolean"}}, "type": "object"}]}}, "type": "object"}]}, "showAsyncStacks": {"default": true, "description": "%node.showAsyncStacks.description%", "oneOf": [{"type": "boolean"}, {"properties": {"onAttach": {"default": 32, "type": "number"}}, "required": ["onAttach"], "type": "object"}, {"properties": {"onceBreakpointResolved": {"default": 32, "type": "number"}}, "required": ["onceBreakpointResolved"], "type": "object"}]}, "skipFiles": {"default": ["${/**"], "description": "%browser.skipFiles.description%", "type": "array"}, "smartStep": {"default": true, "description": "%smartStep.description%", "type": "boolean"}, "sourceMapPathOverrides": {"default": {"meteor://💻app/*": "${workspaceFolder}/*", "webpack:///./~/*": "${workspaceFolder}/node_modules/*", "webpack://?:*/*": "${workspaceFolder}/*"}, "description": "%node.sourceMapPathOverrides.description%", "type": "object"}, "sourceMapRenames": {"default": true, "description": "%browser.sourceMapRenames.description%", "type": "boolean"}, "sourceMaps": {"default": true, "description": "%browser.sourceMaps.description%", "type": "boolean"}, "targetSelection": {"default": "automatic", "enum": ["pick", "automatic"], "markdownDescription": "%browser.targetSelection%", "type": "string"}, "timeout": {"default": 10000, "description": "%node.timeout.description%", "type": "number"}, "timeouts": {"additionalProperties": false, "default": {}, "description": "%timeouts.generalDescription%", "markdownDescription": "%timeouts.generalDescription.markdown%", "properties": {"hoverEvaluation": {"default": 500, "description": "%timeouts.hoverEvaluation.description%", "type": "number"}, "sourceMapCumulativePause": {"default": 1000, "description": "%timeouts.sourceMaps.sourceMapCumulativePause.description%", "type": "number"}, "sourceMapMinPause": {"default": 1000, "description": "%timeouts.sourceMaps.sourceMapMinPause.description%", "type": "number"}}, "type": "object"}, "trace": {"default": true, "description": "%trace.description%", "oneOf": [{"description": "%trace.boolean.description%", "type": "boolean"}, {"additionalProperties": false, "properties": {"logFile": {"description": "%trace.logFile.description%", "type": ["string", "null"]}, "stdio": {"description": "%trace.stdio.description%", "type": "boolean"}}, "type": "object"}]}, "url": {"default": "http://localhost:8080", "description": "%browser.url.description%", "tags": ["setup"], "type": "string"}, "urlFilter": {"default": "", "description": "%browser.urlFilter.description%", "type": "string"}, "vueComponentPaths": {"default": ["${workspaceFolder}/**/*.vue"], "description": "%browser.vueComponentPaths%", "type": "array"}, "webRoot": {"default": "${workspaceFolder}", "description": "%browser.webRoot.description%", "tags": ["setup"], "type": "string"}}, "type": "object"}, "resolveSourceMapLocations": {"default": ["${workspaceFolder}/**", "!**/node_modules/**"], "description": "%node.resolveSourceMapLocations.description%", "items": {"type": "string"}, "type": ["array", "null"]}, "runtimeExecutable": {"default": "node", "markdownDescription": "%extensionHost.launch.runtimeExecutable.description%", "type": ["string", "null"]}, "runtimeSourcemapPausePatterns": {"default": [], "items": {"type": "string"}, "markdownDescription": "%node.launch.runtimeSourcemapPausePatterns%", "type": "array"}, "showAsyncStacks": {"default": true, "description": "%node.showAsyncStacks.description%", "oneOf": [{"type": "boolean"}, {"properties": {"onAttach": {"default": 32, "type": "number"}}, "required": ["onAttach"], "type": "object"}, {"properties": {"onceBreakpointResolved": {"default": 32, "type": "number"}}, "required": ["onceBreakpointResolved"], "type": "object"}]}, "skipFiles": {"default": ["${/**"], "description": "%browser.skipFiles.description%", "type": "array"}, "smartStep": {"default": true, "description": "%smartStep.description%", "type": "boolean"}, "sourceMapPathOverrides": {"default": {"meteor://💻app/*": "${workspaceFolder}/*", "webpack:///./~/*": "${workspaceFolder}/node_modules/*", "webpack://?:*/*": "${workspaceFolder}/*"}, "description": "%node.sourceMapPathOverrides.description%", "type": "object"}, "sourceMapRenames": {"default": true, "description": "%browser.sourceMapRenames.description%", "type": "boolean"}, "sourceMaps": {"default": true, "description": "%browser.sourceMaps.description%", "type": "boolean"}, "testConfiguration": {"default": "${workspaceFolder}/.vscode-test.js", "markdownDescription": "%extensionHost.launch.testConfiguration%", "type": "string"}, "testConfigurationLabel": {"default": "", "markdownDescription": "%extensionHost.launch.testConfigurationLabel%", "type": "string"}, "timeout": {"default": 10000, "description": "%node.timeout.description%", "type": "number"}, "timeouts": {"additionalProperties": false, "default": {}, "description": "%timeouts.generalDescription%", "markdownDescription": "%timeouts.generalDescription.markdown%", "properties": {"hoverEvaluation": {"default": 500, "description": "%timeouts.hoverEvaluation.description%", "type": "number"}, "sourceMapCumulativePause": {"default": 1000, "description": "%timeouts.sourceMaps.sourceMapCumulativePause.description%", "type": "number"}, "sourceMapMinPause": {"default": 1000, "description": "%timeouts.sourceMaps.sourceMapMinPause.description%", "type": "number"}}, "type": "object"}, "trace": {"default": true, "description": "%trace.description%", "oneOf": [{"description": "%trace.boolean.description%", "type": "boolean"}, {"additionalProperties": false, "properties": {"logFile": {"description": "%trace.logFile.description%", "type": ["string", "null"]}, "stdio": {"description": "%trace.stdio.description%", "type": "boolean"}}, "type": "object"}]}}, "required": []}}, "configurationSnippets": [], "deprecated": "Please use type extensionHost instead", "label": "%extensionHost.label%", "languages": ["javascript", "typescript", "javascriptreact", "typescriptreact"], "strings": {"unverifiedBreakpoints": "%debug.unverifiedBreakpoints%"}, "type": "pwa-extensionHost"}, {"aiKey": "0c6ae279ed8443289764825290e4f9e2-1a736e7c-1324-4338-be46-fc2a58ae4d14-7255", "configurationAttributes": {"launch": {"properties": {"args": {"default": ["--extensionDevelopmentPath=${workspaceFolder}"], "description": "%node.launch.args.description%", "items": {"type": "string"}, "tags": ["setup"], "type": "array"}, "autoAttachChildProcesses": {"default": true, "description": "%node.launch.autoAttachChildProcesses.description%", "type": "boolean"}, "cascadeTerminateToConfigurations": {"default": [], "description": "%base.cascadeTerminateToConfigurations.label%", "items": {"type": "string", "uniqueItems": true}, "type": "array"}, "customDescriptionGenerator": {"description": "%customDescriptionGenerator.description%", "type": "string"}, "customPropertiesGenerator": {"deprecated": true, "description": "%customPropertiesGenerator.description%", "type": "string"}, "cwd": {"default": "${workspaceFolder}", "description": "%node.launch.cwd.description%", "tags": ["setup"], "type": "string"}, "debugWebWorkerHost": {"default": true, "markdownDescription": "%extensionHost.launch.debugWebWorkerHost%", "type": ["boolean"]}, "debugWebviews": {"default": true, "markdownDescription": "%extensionHost.launch.debugWebviews%", "type": ["boolean"]}, "enableContentValidation": {"default": true, "description": "%enableContentValidation.description%", "type": "boolean"}, "enableDWARF": {"default": true, "markdownDescription": "%base.enableDWARF.label%", "type": "boolean"}, "env": {"additionalProperties": {"type": ["string", "null"]}, "default": {}, "markdownDescription": "%node.launch.env.description%", "tags": ["setup"], "type": "object"}, "envFile": {"default": "${workspaceFolder}/.env", "description": "%node.launch.envFile.description%", "type": "string"}, "localRoot": {"default": null, "description": "%node.localRoot.description%", "type": ["string", "null"]}, "nodeVersionHint": {"default": 12, "description": "%node.versionHint.description%", "minimum": 8, "type": "number"}, "outFiles": {"default": ["${workspaceFolder}/**/*.(m|c|)js", "!**/node_modules/**"], "description": "%outFiles.description%", "items": {"type": "string"}, "tags": ["setup"], "type": ["array"]}, "outputCapture": {"default": "console", "enum": ["console", "std"], "markdownDescription": "%node.launch.outputCapture.description%"}, "pauseForSourceMap": {"default": false, "markdownDescription": "%node.pauseForSourceMap.description%", "type": "boolean"}, "remoteRoot": {"default": null, "description": "%node.remoteRoot.description%", "type": ["string", "null"]}, "rendererDebugOptions": {"default": {"webRoot": "${workspaceFolder}"}, "markdownDescription": "%extensionHost.launch.rendererDebugOptions%", "properties": {"address": {"default": "localhost", "description": "%browser.address.description%", "type": "string"}, "browserAttachLocation": {"default": null, "description": "%browser.browserAttachLocation.description%", "oneOf": [{"type": "null"}, {"enum": ["ui", "workspace"], "type": "string"}]}, "cascadeTerminateToConfigurations": {"default": [], "description": "%base.cascadeTerminateToConfigurations.label%", "items": {"type": "string", "uniqueItems": true}, "type": "array"}, "customDescriptionGenerator": {"description": "%customDescriptionGenerator.description%", "type": "string"}, "customPropertiesGenerator": {"deprecated": true, "description": "%customPropertiesGenerator.description%", "type": "string"}, "disableNetworkCache": {"default": true, "description": "%browser.disableNetworkCache.description%", "type": "boolean"}, "enableContentValidation": {"default": true, "description": "%enableContentValidation.description%", "type": "boolean"}, "enableDWARF": {"default": true, "markdownDescription": "%base.enableDWARF.label%", "type": "boolean"}, "inspectUri": {"default": null, "description": "%browser.inspectUri.description%", "type": ["string", "null"]}, "outFiles": {"default": ["${workspaceFolder}/**/*.(m|c|)js", "!**/node_modules/**"], "description": "%outFiles.description%", "items": {"type": "string"}, "tags": ["setup"], "type": ["array"]}, "outputCapture": {"default": "console", "enum": ["console", "std"], "markdownDescription": "%node.launch.outputCapture.description%"}, "pathMapping": {"default": {}, "description": "%browser.pathMapping.description%", "type": "object"}, "pauseForSourceMap": {"default": false, "markdownDescription": "%node.pauseForSourceMap.description%", "type": "boolean"}, "perScriptSourcemaps": {"default": "auto", "description": "%browser.perScriptSourcemaps.description%", "enum": ["yes", "no", "auto"], "type": "string"}, "port": {"default": 9229, "description": "%browser.attach.port.description%", "oneOf": [{"type": "integer"}, {"pattern": "^\\${.*}$", "type": "string"}], "tags": ["setup"]}, "resolveSourceMapLocations": {"default": null, "description": "%node.resolveSourceMapLocations.description%", "items": {"type": "string"}, "type": ["array", "null"]}, "restart": {"default": false, "markdownDescription": "%browser.restart%", "type": "boolean"}, "server": {"oneOf": [{"additionalProperties": false, "default": {"program": "node my-server.js"}, "description": "%browser.server.description%", "properties": {"args": {"default": [], "description": "%node.launch.args.description%", "items": {"type": "string"}, "tags": ["setup"], "type": ["array", "string"]}, "attachSimplePort": {"default": 9229, "description": "%node.attachSimplePort.description%", "oneOf": [{"type": "integer"}, {"pattern": "^\\${.*}$", "type": "string"}]}, "autoAttachChildProcesses": {"default": true, "description": "%node.launch.autoAttachChildProcesses.description%", "type": "boolean"}, "cascadeTerminateToConfigurations": {"default": [], "description": "%base.cascadeTerminateToConfigurations.label%", "items": {"type": "string", "uniqueItems": true}, "type": "array"}, "console": {"default": "internalConsole", "description": "%node.launch.console.description%", "enum": ["internalConsole", "integratedTerminal", "externalTerminal"], "enumDescriptions": ["%node.launch.console.internalConsole.description%", "%node.launch.console.integratedTerminal.description%", "%node.launch.console.externalTerminal.description%"], "type": "string"}, "customDescriptionGenerator": {"description": "%customDescriptionGenerator.description%", "type": "string"}, "customPropertiesGenerator": {"deprecated": true, "description": "%customPropertiesGenerator.description%", "type": "string"}, "cwd": {"default": "${workspaceFolder}", "description": "%node.launch.cwd.description%", "tags": ["setup"], "type": "string"}, "enableContentValidation": {"default": true, "description": "%enableContentValidation.description%", "type": "boolean"}, "enableDWARF": {"default": true, "markdownDescription": "%base.enableDWARF.label%", "type": "boolean"}, "env": {"additionalProperties": {"type": ["string", "null"]}, "default": {}, "markdownDescription": "%node.launch.env.description%", "tags": ["setup"], "type": "object"}, "envFile": {"default": "${workspaceFolder}/.env", "description": "%node.launch.envFile.description%", "type": "string"}, "experimentalNetworking": {"default": "auto", "description": "%node.experimentalNetworking.description%", "enum": ["auto", "on", "off"], "type": "string"}, "killBehavior": {"default": "forceful", "enum": ["forceful", "polite", "none"], "markdownDescription": "%node.killBehavior.description%", "type": "string"}, "localRoot": {"default": null, "description": "%node.localRoot.description%", "type": ["string", "null"]}, "nodeVersionHint": {"default": 12, "description": "%node.versionHint.description%", "minimum": 8, "type": "number"}, "outFiles": {"default": ["${workspaceFolder}/**/*.(m|c|)js", "!**/node_modules/**"], "description": "%outFiles.description%", "items": {"type": "string"}, "tags": ["setup"], "type": ["array"]}, "outputCapture": {"default": "console", "enum": ["console", "std"], "markdownDescription": "%node.launch.outputCapture.description%"}, "pauseForSourceMap": {"default": false, "markdownDescription": "%node.pauseForSourceMap.description%", "type": "boolean"}, "profileStartup": {"default": true, "description": "%node.profileStartup.description%", "type": "boolean"}, "program": {"default": "", "description": "%node.launch.program.description%", "tags": ["setup"], "type": "string"}, "remoteRoot": {"default": null, "description": "%node.remoteRoot.description%", "type": ["string", "null"]}, "resolveSourceMapLocations": {"default": ["${workspaceFolder}/**", "!**/node_modules/**"], "description": "%node.resolveSourceMapLocations.description%", "items": {"type": "string"}, "type": ["array", "null"]}, "restart": {"default": true, "description": "%node.attach.restart.description%", "oneOf": [{"type": "boolean"}, {"properties": {"delay": {"default": 1000, "minimum": 0, "type": "number"}, "maxAttempts": {"default": 10, "minimum": 0, "type": "number"}}, "type": "object"}]}, "runtimeArgs": {"default": [], "description": "%node.launch.runtimeArgs.description%", "items": {"type": "string"}, "tags": ["setup"], "type": "array"}, "runtimeExecutable": {"default": "node", "markdownDescription": "%node.launch.runtimeExecutable.description%", "type": ["string", "null"]}, "runtimeSourcemapPausePatterns": {"default": [], "items": {"type": "string"}, "markdownDescription": "%node.launch.runtimeSourcemapPausePatterns%", "type": "array"}, "runtimeVersion": {"default": "default", "markdownDescription": "%node.launch.runtimeVersion.description%", "type": "string"}, "showAsyncStacks": {"default": true, "description": "%node.showAsyncStacks.description%", "oneOf": [{"type": "boolean"}, {"properties": {"onAttach": {"default": 32, "type": "number"}}, "required": ["onAttach"], "type": "object"}, {"properties": {"onceBreakpointResolved": {"default": 32, "type": "number"}}, "required": ["onceBreakpointResolved"], "type": "object"}]}, "skipFiles": {"default": ["${/**"], "description": "%browser.skipFiles.description%", "type": "array"}, "smartStep": {"default": true, "description": "%smartStep.description%", "type": "boolean"}, "sourceMapPathOverrides": {"default": {"meteor://💻app/*": "${workspaceFolder}/*", "webpack:///./~/*": "${workspaceFolder}/node_modules/*", "webpack://?:*/*": "${workspaceFolder}/*"}, "description": "%node.sourceMapPathOverrides.description%", "type": "object"}, "sourceMapRenames": {"default": true, "description": "%browser.sourceMapRenames.description%", "type": "boolean"}, "sourceMaps": {"default": true, "description": "%browser.sourceMaps.description%", "type": "boolean"}, "stopOnEntry": {"default": true, "description": "%node.stopOnEntry.description%", "type": ["boolean", "string"]}, "timeout": {"default": 10000, "description": "%node.timeout.description%", "type": "number"}, "timeouts": {"additionalProperties": false, "default": {}, "description": "%timeouts.generalDescription%", "markdownDescription": "%timeouts.generalDescription.markdown%", "properties": {"hoverEvaluation": {"default": 500, "description": "%timeouts.hoverEvaluation.description%", "type": "number"}, "sourceMapCumulativePause": {"default": 1000, "description": "%timeouts.sourceMaps.sourceMapCumulativePause.description%", "type": "number"}, "sourceMapMinPause": {"default": 1000, "description": "%timeouts.sourceMaps.sourceMapMinPause.description%", "type": "number"}}, "type": "object"}, "trace": {"default": true, "description": "%trace.description%", "oneOf": [{"description": "%trace.boolean.description%", "type": "boolean"}, {"additionalProperties": false, "properties": {"logFile": {"description": "%trace.logFile.description%", "type": ["string", "null"]}, "stdio": {"description": "%trace.stdio.description%", "type": "boolean"}}, "type": "object"}]}}, "type": "object"}, {"additionalProperties": false, "default": {"program": "npm start"}, "description": "%debug.terminal.label%", "properties": {"autoAttachChildProcesses": {"default": true, "description": "%node.launch.autoAttachChildProcesses.description%", "type": "boolean"}, "cascadeTerminateToConfigurations": {"default": [], "description": "%base.cascadeTerminateToConfigurations.label%", "items": {"type": "string", "uniqueItems": true}, "type": "array"}, "command": {"default": "npm start", "description": "%debug.terminal.program.description%", "tags": ["setup"], "type": ["string", "null"]}, "customDescriptionGenerator": {"description": "%customDescriptionGenerator.description%", "type": "string"}, "customPropertiesGenerator": {"deprecated": true, "description": "%customPropertiesGenerator.description%", "type": "string"}, "cwd": {"default": "${workspaceFolder}", "description": "%node.launch.cwd.description%", "docDefault": "localRoot || ${workspaceFolder}", "tags": ["setup"], "type": "string"}, "enableContentValidation": {"default": true, "description": "%enableContentValidation.description%", "type": "boolean"}, "enableDWARF": {"default": true, "markdownDescription": "%base.enableDWARF.label%", "type": "boolean"}, "env": {"additionalProperties": {"type": ["string", "null"]}, "default": {}, "markdownDescription": "%node.launch.env.description%", "tags": ["setup"], "type": "object"}, "envFile": {"default": "${workspaceFolder}/.env", "description": "%node.launch.envFile.description%", "type": "string"}, "localRoot": {"default": null, "description": "%node.localRoot.description%", "type": ["string", "null"]}, "nodeVersionHint": {"default": 12, "description": "%node.versionHint.description%", "minimum": 8, "type": "number"}, "outFiles": {"default": ["${workspaceFolder}/**/*.(m|c|)js", "!**/node_modules/**"], "description": "%outFiles.description%", "items": {"type": "string"}, "tags": ["setup"], "type": ["array"]}, "outputCapture": {"default": "console", "enum": ["console", "std"], "markdownDescription": "%node.launch.outputCapture.description%"}, "pauseForSourceMap": {"default": false, "markdownDescription": "%node.pauseForSourceMap.description%", "type": "boolean"}, "remoteRoot": {"default": null, "description": "%node.remoteRoot.description%", "type": ["string", "null"]}, "resolveSourceMapLocations": {"default": ["${workspaceFolder}/**", "!**/node_modules/**"], "description": "%node.resolveSourceMapLocations.description%", "items": {"type": "string"}, "type": ["array", "null"]}, "runtimeSourcemapPausePatterns": {"default": [], "items": {"type": "string"}, "markdownDescription": "%node.launch.runtimeSourcemapPausePatterns%", "type": "array"}, "showAsyncStacks": {"default": true, "description": "%node.showAsyncStacks.description%", "oneOf": [{"type": "boolean"}, {"properties": {"onAttach": {"default": 32, "type": "number"}}, "required": ["onAttach"], "type": "object"}, {"properties": {"onceBreakpointResolved": {"default": 32, "type": "number"}}, "required": ["onceBreakpointResolved"], "type": "object"}]}, "skipFiles": {"default": ["${/**"], "description": "%browser.skipFiles.description%", "type": "array"}, "smartStep": {"default": true, "description": "%smartStep.description%", "type": "boolean"}, "sourceMapPathOverrides": {"default": {"meteor://💻app/*": "${workspaceFolder}/*", "webpack:///./~/*": "${workspaceFolder}/node_modules/*", "webpack://?:*/*": "${workspaceFolder}/*"}, "description": "%node.sourceMapPathOverrides.description%", "type": "object"}, "sourceMapRenames": {"default": true, "description": "%browser.sourceMapRenames.description%", "type": "boolean"}, "sourceMaps": {"default": true, "description": "%browser.sourceMaps.description%", "type": "boolean"}, "timeout": {"default": 10000, "description": "%node.timeout.description%", "type": "number"}, "timeouts": {"additionalProperties": false, "default": {}, "description": "%timeouts.generalDescription%", "markdownDescription": "%timeouts.generalDescription.markdown%", "properties": {"hoverEvaluation": {"default": 500, "description": "%timeouts.hoverEvaluation.description%", "type": "number"}, "sourceMapCumulativePause": {"default": 1000, "description": "%timeouts.sourceMaps.sourceMapCumulativePause.description%", "type": "number"}, "sourceMapMinPause": {"default": 1000, "description": "%timeouts.sourceMaps.sourceMapMinPause.description%", "type": "number"}}, "type": "object"}, "trace": {"default": true, "description": "%trace.description%", "oneOf": [{"description": "%trace.boolean.description%", "type": "boolean"}, {"additionalProperties": false, "properties": {"logFile": {"description": "%trace.logFile.description%", "type": ["string", "null"]}, "stdio": {"description": "%trace.stdio.description%", "type": "boolean"}}, "type": "object"}]}}, "type": "object"}]}, "showAsyncStacks": {"default": true, "description": "%node.showAsyncStacks.description%", "oneOf": [{"type": "boolean"}, {"properties": {"onAttach": {"default": 32, "type": "number"}}, "required": ["onAttach"], "type": "object"}, {"properties": {"onceBreakpointResolved": {"default": 32, "type": "number"}}, "required": ["onceBreakpointResolved"], "type": "object"}]}, "skipFiles": {"default": ["${/**"], "description": "%browser.skipFiles.description%", "type": "array"}, "smartStep": {"default": true, "description": "%smartStep.description%", "type": "boolean"}, "sourceMapPathOverrides": {"default": {"meteor://💻app/*": "${workspaceFolder}/*", "webpack:///./~/*": "${workspaceFolder}/node_modules/*", "webpack://?:*/*": "${workspaceFolder}/*"}, "description": "%node.sourceMapPathOverrides.description%", "type": "object"}, "sourceMapRenames": {"default": true, "description": "%browser.sourceMapRenames.description%", "type": "boolean"}, "sourceMaps": {"default": true, "description": "%browser.sourceMaps.description%", "type": "boolean"}, "targetSelection": {"default": "automatic", "enum": ["pick", "automatic"], "markdownDescription": "%browser.targetSelection%", "type": "string"}, "timeout": {"default": 10000, "description": "%node.timeout.description%", "type": "number"}, "timeouts": {"additionalProperties": false, "default": {}, "description": "%timeouts.generalDescription%", "markdownDescription": "%timeouts.generalDescription.markdown%", "properties": {"hoverEvaluation": {"default": 500, "description": "%timeouts.hoverEvaluation.description%", "type": "number"}, "sourceMapCumulativePause": {"default": 1000, "description": "%timeouts.sourceMaps.sourceMapCumulativePause.description%", "type": "number"}, "sourceMapMinPause": {"default": 1000, "description": "%timeouts.sourceMaps.sourceMapMinPause.description%", "type": "number"}}, "type": "object"}, "trace": {"default": true, "description": "%trace.description%", "oneOf": [{"description": "%trace.boolean.description%", "type": "boolean"}, {"additionalProperties": false, "properties": {"logFile": {"description": "%trace.logFile.description%", "type": ["string", "null"]}, "stdio": {"description": "%trace.stdio.description%", "type": "boolean"}}, "type": "object"}]}, "url": {"default": "http://localhost:8080", "description": "%browser.url.description%", "tags": ["setup"], "type": "string"}, "urlFilter": {"default": "", "description": "%browser.urlFilter.description%", "type": "string"}, "vueComponentPaths": {"default": ["${workspaceFolder}/**/*.vue"], "description": "%browser.vueComponentPaths%", "type": "array"}, "webRoot": {"default": "${workspaceFolder}", "description": "%browser.webRoot.description%", "tags": ["setup"], "type": "string"}}, "type": "object"}, "resolveSourceMapLocations": {"default": ["${workspaceFolder}/**", "!**/node_modules/**"], "description": "%node.resolveSourceMapLocations.description%", "items": {"type": "string"}, "type": ["array", "null"]}, "runtimeExecutable": {"default": "node", "markdownDescription": "%extensionHost.launch.runtimeExecutable.description%", "type": ["string", "null"]}, "runtimeSourcemapPausePatterns": {"default": [], "items": {"type": "string"}, "markdownDescription": "%node.launch.runtimeSourcemapPausePatterns%", "type": "array"}, "showAsyncStacks": {"default": true, "description": "%node.showAsyncStacks.description%", "oneOf": [{"type": "boolean"}, {"properties": {"onAttach": {"default": 32, "type": "number"}}, "required": ["onAttach"], "type": "object"}, {"properties": {"onceBreakpointResolved": {"default": 32, "type": "number"}}, "required": ["onceBreakpointResolved"], "type": "object"}]}, "skipFiles": {"default": ["${/**"], "description": "%browser.skipFiles.description%", "type": "array"}, "smartStep": {"default": true, "description": "%smartStep.description%", "type": "boolean"}, "sourceMapPathOverrides": {"default": {"meteor://💻app/*": "${workspaceFolder}/*", "webpack:///./~/*": "${workspaceFolder}/node_modules/*", "webpack://?:*/*": "${workspaceFolder}/*"}, "description": "%node.sourceMapPathOverrides.description%", "type": "object"}, "sourceMapRenames": {"default": true, "description": "%browser.sourceMapRenames.description%", "type": "boolean"}, "sourceMaps": {"default": true, "description": "%browser.sourceMaps.description%", "type": "boolean"}, "testConfiguration": {"default": "${workspaceFolder}/.vscode-test.js", "markdownDescription": "%extensionHost.launch.testConfiguration%", "type": "string"}, "testConfigurationLabel": {"default": "", "markdownDescription": "%extensionHost.launch.testConfigurationLabel%", "type": "string"}, "timeout": {"default": 10000, "description": "%node.timeout.description%", "type": "number"}, "timeouts": {"additionalProperties": false, "default": {}, "description": "%timeouts.generalDescription%", "markdownDescription": "%timeouts.generalDescription.markdown%", "properties": {"hoverEvaluation": {"default": 500, "description": "%timeouts.hoverEvaluation.description%", "type": "number"}, "sourceMapCumulativePause": {"default": 1000, "description": "%timeouts.sourceMaps.sourceMapCumulativePause.description%", "type": "number"}, "sourceMapMinPause": {"default": 1000, "description": "%timeouts.sourceMaps.sourceMapMinPause.description%", "type": "number"}}, "type": "object"}, "trace": {"default": true, "description": "%trace.description%", "oneOf": [{"description": "%trace.boolean.description%", "type": "boolean"}, {"additionalProperties": false, "properties": {"logFile": {"description": "%trace.logFile.description%", "type": ["string", "null"]}, "stdio": {"description": "%trace.stdio.description%", "type": "boolean"}}, "type": "object"}]}}, "required": []}}, "configurationSnippets": [{"body": {"args": ["^\"--extensionDevelopmentPath=\\${workspaceFolder}\""], "name": "%extensionHost.launch.config.name%", "outFiles": ["^\"\\${workspaceFolder}/out/**/*.js\""], "preLaunchTask": "npm", "request": "launch", "type": "extensionHost"}, "description": "%extensionHost.snippet.launch.description%", "label": "%extensionHost.snippet.launch.label%"}], "label": "%extensionHost.label%", "strings": {"unverifiedBreakpoints": "%debug.unverifiedBreakpoints%"}, "type": "extensionHost"}, {"aiKey": "0c6ae279ed8443289764825290e4f9e2-1a736e7c-1324-4338-be46-fc2a58ae4d14-7255", "configurationAttributes": {"attach": {"properties": {"address": {"default": "localhost", "description": "%browser.address.description%", "type": "string"}, "browserAttachLocation": {"default": null, "description": "%browser.browserAttachLocation.description%", "oneOf": [{"type": "null"}, {"enum": ["ui", "workspace"], "type": "string"}]}, "cascadeTerminateToConfigurations": {"default": [], "description": "%base.cascadeTerminateToConfigurations.label%", "items": {"type": "string", "uniqueItems": true}, "type": "array"}, "customDescriptionGenerator": {"description": "%customDescriptionGenerator.description%", "type": "string"}, "customPropertiesGenerator": {"deprecated": true, "description": "%customPropertiesGenerator.description%", "type": "string"}, "disableNetworkCache": {"default": true, "description": "%browser.disableNetworkCache.description%", "type": "boolean"}, "enableContentValidation": {"default": true, "description": "%enableContentValidation.description%", "type": "boolean"}, "enableDWARF": {"default": true, "markdownDescription": "%base.enableDWARF.label%", "type": "boolean"}, "inspectUri": {"default": null, "description": "%browser.inspectUri.description%", "type": ["string", "null"]}, "outFiles": {"default": ["${workspaceFolder}/**/*.(m|c|)js", "!**/node_modules/**"], "description": "%outFiles.description%", "items": {"type": "string"}, "tags": ["setup"], "type": ["array"]}, "outputCapture": {"default": "console", "enum": ["console", "std"], "markdownDescription": "%node.launch.outputCapture.description%"}, "pathMapping": {"default": {}, "description": "%browser.pathMapping.description%", "type": "object"}, "pauseForSourceMap": {"default": false, "markdownDescription": "%node.pauseForSourceMap.description%", "type": "boolean"}, "perScriptSourcemaps": {"default": "auto", "description": "%browser.perScriptSourcemaps.description%", "enum": ["yes", "no", "auto"], "type": "string"}, "port": {"default": 9229, "description": "%browser.attach.port.description%", "oneOf": [{"type": "integer"}, {"pattern": "^\\${.*}$", "type": "string"}], "tags": ["setup"]}, "resolveSourceMapLocations": {"default": null, "description": "%node.resolveSourceMapLocations.description%", "items": {"type": "string"}, "type": ["array", "null"]}, "restart": {"default": false, "markdownDescription": "%browser.restart%", "type": "boolean"}, "server": {"oneOf": [{"additionalProperties": false, "default": {"program": "node my-server.js"}, "description": "%browser.server.description%", "properties": {"args": {"default": [], "description": "%node.launch.args.description%", "items": {"type": "string"}, "tags": ["setup"], "type": ["array", "string"]}, "attachSimplePort": {"default": 9229, "description": "%node.attachSimplePort.description%", "oneOf": [{"type": "integer"}, {"pattern": "^\\${.*}$", "type": "string"}]}, "autoAttachChildProcesses": {"default": true, "description": "%node.launch.autoAttachChildProcesses.description%", "type": "boolean"}, "cascadeTerminateToConfigurations": {"default": [], "description": "%base.cascadeTerminateToConfigurations.label%", "items": {"type": "string", "uniqueItems": true}, "type": "array"}, "console": {"default": "internalConsole", "description": "%node.launch.console.description%", "enum": ["internalConsole", "integratedTerminal", "externalTerminal"], "enumDescriptions": ["%node.launch.console.internalConsole.description%", "%node.launch.console.integratedTerminal.description%", "%node.launch.console.externalTerminal.description%"], "type": "string"}, "customDescriptionGenerator": {"description": "%customDescriptionGenerator.description%", "type": "string"}, "customPropertiesGenerator": {"deprecated": true, "description": "%customPropertiesGenerator.description%", "type": "string"}, "cwd": {"default": "${workspaceFolder}", "description": "%node.launch.cwd.description%", "tags": ["setup"], "type": "string"}, "enableContentValidation": {"default": true, "description": "%enableContentValidation.description%", "type": "boolean"}, "enableDWARF": {"default": true, "markdownDescription": "%base.enableDWARF.label%", "type": "boolean"}, "env": {"additionalProperties": {"type": ["string", "null"]}, "default": {}, "markdownDescription": "%node.launch.env.description%", "tags": ["setup"], "type": "object"}, "envFile": {"default": "${workspaceFolder}/.env", "description": "%node.launch.envFile.description%", "type": "string"}, "experimentalNetworking": {"default": "auto", "description": "%node.experimentalNetworking.description%", "enum": ["auto", "on", "off"], "type": "string"}, "killBehavior": {"default": "forceful", "enum": ["forceful", "polite", "none"], "markdownDescription": "%node.killBehavior.description%", "type": "string"}, "localRoot": {"default": null, "description": "%node.localRoot.description%", "type": ["string", "null"]}, "nodeVersionHint": {"default": 12, "description": "%node.versionHint.description%", "minimum": 8, "type": "number"}, "outFiles": {"default": ["${workspaceFolder}/**/*.(m|c|)js", "!**/node_modules/**"], "description": "%outFiles.description%", "items": {"type": "string"}, "tags": ["setup"], "type": ["array"]}, "outputCapture": {"default": "console", "enum": ["console", "std"], "markdownDescription": "%node.launch.outputCapture.description%"}, "pauseForSourceMap": {"default": false, "markdownDescription": "%node.pauseForSourceMap.description%", "type": "boolean"}, "profileStartup": {"default": true, "description": "%node.profileStartup.description%", "type": "boolean"}, "program": {"default": "", "description": "%node.launch.program.description%", "tags": ["setup"], "type": "string"}, "remoteRoot": {"default": null, "description": "%node.remoteRoot.description%", "type": ["string", "null"]}, "resolveSourceMapLocations": {"default": ["${workspaceFolder}/**", "!**/node_modules/**"], "description": "%node.resolveSourceMapLocations.description%", "items": {"type": "string"}, "type": ["array", "null"]}, "restart": {"default": true, "description": "%node.attach.restart.description%", "oneOf": [{"type": "boolean"}, {"properties": {"delay": {"default": 1000, "minimum": 0, "type": "number"}, "maxAttempts": {"default": 10, "minimum": 0, "type": "number"}}, "type": "object"}]}, "runtimeArgs": {"default": [], "description": "%node.launch.runtimeArgs.description%", "items": {"type": "string"}, "tags": ["setup"], "type": "array"}, "runtimeExecutable": {"default": "node", "markdownDescription": "%node.launch.runtimeExecutable.description%", "type": ["string", "null"]}, "runtimeSourcemapPausePatterns": {"default": [], "items": {"type": "string"}, "markdownDescription": "%node.launch.runtimeSourcemapPausePatterns%", "type": "array"}, "runtimeVersion": {"default": "default", "markdownDescription": "%node.launch.runtimeVersion.description%", "type": "string"}, "showAsyncStacks": {"default": true, "description": "%node.showAsyncStacks.description%", "oneOf": [{"type": "boolean"}, {"properties": {"onAttach": {"default": 32, "type": "number"}}, "required": ["onAttach"], "type": "object"}, {"properties": {"onceBreakpointResolved": {"default": 32, "type": "number"}}, "required": ["onceBreakpointResolved"], "type": "object"}]}, "skipFiles": {"default": ["${/**"], "description": "%browser.skipFiles.description%", "type": "array"}, "smartStep": {"default": true, "description": "%smartStep.description%", "type": "boolean"}, "sourceMapPathOverrides": {"default": {"meteor://💻app/*": "${workspaceFolder}/*", "webpack:///./~/*": "${workspaceFolder}/node_modules/*", "webpack://?:*/*": "${workspaceFolder}/*"}, "description": "%node.sourceMapPathOverrides.description%", "type": "object"}, "sourceMapRenames": {"default": true, "description": "%browser.sourceMapRenames.description%", "type": "boolean"}, "sourceMaps": {"default": true, "description": "%browser.sourceMaps.description%", "type": "boolean"}, "stopOnEntry": {"default": true, "description": "%node.stopOnEntry.description%", "type": ["boolean", "string"]}, "timeout": {"default": 10000, "description": "%node.timeout.description%", "type": "number"}, "timeouts": {"additionalProperties": false, "default": {}, "description": "%timeouts.generalDescription%", "markdownDescription": "%timeouts.generalDescription.markdown%", "properties": {"hoverEvaluation": {"default": 500, "description": "%timeouts.hoverEvaluation.description%", "type": "number"}, "sourceMapCumulativePause": {"default": 1000, "description": "%timeouts.sourceMaps.sourceMapCumulativePause.description%", "type": "number"}, "sourceMapMinPause": {"default": 1000, "description": "%timeouts.sourceMaps.sourceMapMinPause.description%", "type": "number"}}, "type": "object"}, "trace": {"default": true, "description": "%trace.description%", "oneOf": [{"description": "%trace.boolean.description%", "type": "boolean"}, {"additionalProperties": false, "properties": {"logFile": {"description": "%trace.logFile.description%", "type": ["string", "null"]}, "stdio": {"description": "%trace.stdio.description%", "type": "boolean"}}, "type": "object"}]}}, "type": "object"}, {"additionalProperties": false, "default": {"program": "npm start"}, "description": "%debug.terminal.label%", "properties": {"autoAttachChildProcesses": {"default": true, "description": "%node.launch.autoAttachChildProcesses.description%", "type": "boolean"}, "cascadeTerminateToConfigurations": {"default": [], "description": "%base.cascadeTerminateToConfigurations.label%", "items": {"type": "string", "uniqueItems": true}, "type": "array"}, "command": {"default": "npm start", "description": "%debug.terminal.program.description%", "tags": ["setup"], "type": ["string", "null"]}, "customDescriptionGenerator": {"description": "%customDescriptionGenerator.description%", "type": "string"}, "customPropertiesGenerator": {"deprecated": true, "description": "%customPropertiesGenerator.description%", "type": "string"}, "cwd": {"default": "${workspaceFolder}", "description": "%node.launch.cwd.description%", "docDefault": "localRoot || ${workspaceFolder}", "tags": ["setup"], "type": "string"}, "enableContentValidation": {"default": true, "description": "%enableContentValidation.description%", "type": "boolean"}, "enableDWARF": {"default": true, "markdownDescription": "%base.enableDWARF.label%", "type": "boolean"}, "env": {"additionalProperties": {"type": ["string", "null"]}, "default": {}, "markdownDescription": "%node.launch.env.description%", "tags": ["setup"], "type": "object"}, "envFile": {"default": "${workspaceFolder}/.env", "description": "%node.launch.envFile.description%", "type": "string"}, "localRoot": {"default": null, "description": "%node.localRoot.description%", "type": ["string", "null"]}, "nodeVersionHint": {"default": 12, "description": "%node.versionHint.description%", "minimum": 8, "type": "number"}, "outFiles": {"default": ["${workspaceFolder}/**/*.(m|c|)js", "!**/node_modules/**"], "description": "%outFiles.description%", "items": {"type": "string"}, "tags": ["setup"], "type": ["array"]}, "outputCapture": {"default": "console", "enum": ["console", "std"], "markdownDescription": "%node.launch.outputCapture.description%"}, "pauseForSourceMap": {"default": false, "markdownDescription": "%node.pauseForSourceMap.description%", "type": "boolean"}, "remoteRoot": {"default": null, "description": "%node.remoteRoot.description%", "type": ["string", "null"]}, "resolveSourceMapLocations": {"default": ["${workspaceFolder}/**", "!**/node_modules/**"], "description": "%node.resolveSourceMapLocations.description%", "items": {"type": "string"}, "type": ["array", "null"]}, "runtimeSourcemapPausePatterns": {"default": [], "items": {"type": "string"}, "markdownDescription": "%node.launch.runtimeSourcemapPausePatterns%", "type": "array"}, "showAsyncStacks": {"default": true, "description": "%node.showAsyncStacks.description%", "oneOf": [{"type": "boolean"}, {"properties": {"onAttach": {"default": 32, "type": "number"}}, "required": ["onAttach"], "type": "object"}, {"properties": {"onceBreakpointResolved": {"default": 32, "type": "number"}}, "required": ["onceBreakpointResolved"], "type": "object"}]}, "skipFiles": {"default": ["${/**"], "description": "%browser.skipFiles.description%", "type": "array"}, "smartStep": {"default": true, "description": "%smartStep.description%", "type": "boolean"}, "sourceMapPathOverrides": {"default": {"meteor://💻app/*": "${workspaceFolder}/*", "webpack:///./~/*": "${workspaceFolder}/node_modules/*", "webpack://?:*/*": "${workspaceFolder}/*"}, "description": "%node.sourceMapPathOverrides.description%", "type": "object"}, "sourceMapRenames": {"default": true, "description": "%browser.sourceMapRenames.description%", "type": "boolean"}, "sourceMaps": {"default": true, "description": "%browser.sourceMaps.description%", "type": "boolean"}, "timeout": {"default": 10000, "description": "%node.timeout.description%", "type": "number"}, "timeouts": {"additionalProperties": false, "default": {}, "description": "%timeouts.generalDescription%", "markdownDescription": "%timeouts.generalDescription.markdown%", "properties": {"hoverEvaluation": {"default": 500, "description": "%timeouts.hoverEvaluation.description%", "type": "number"}, "sourceMapCumulativePause": {"default": 1000, "description": "%timeouts.sourceMaps.sourceMapCumulativePause.description%", "type": "number"}, "sourceMapMinPause": {"default": 1000, "description": "%timeouts.sourceMaps.sourceMapMinPause.description%", "type": "number"}}, "type": "object"}, "trace": {"default": true, "description": "%trace.description%", "oneOf": [{"description": "%trace.boolean.description%", "type": "boolean"}, {"additionalProperties": false, "properties": {"logFile": {"description": "%trace.logFile.description%", "type": ["string", "null"]}, "stdio": {"description": "%trace.stdio.description%", "type": "boolean"}}, "type": "object"}]}}, "type": "object"}]}, "showAsyncStacks": {"default": true, "description": "%node.showAsyncStacks.description%", "oneOf": [{"type": "boolean"}, {"properties": {"onAttach": {"default": 32, "type": "number"}}, "required": ["onAttach"], "type": "object"}, {"properties": {"onceBreakpointResolved": {"default": 32, "type": "number"}}, "required": ["onceBreakpointResolved"], "type": "object"}]}, "skipFiles": {"default": ["${/**"], "description": "%browser.skipFiles.description%", "type": "array"}, "smartStep": {"default": true, "description": "%smartStep.description%", "type": "boolean"}, "sourceMapPathOverrides": {"default": {"meteor://💻app/*": "${workspaceFolder}/*", "webpack:///./~/*": "${workspaceFolder}/node_modules/*", "webpack://?:*/*": "${workspaceFolder}/*"}, "description": "%node.sourceMapPathOverrides.description%", "type": "object"}, "sourceMapRenames": {"default": true, "description": "%browser.sourceMapRenames.description%", "type": "boolean"}, "sourceMaps": {"default": true, "description": "%browser.sourceMaps.description%", "type": "boolean"}, "targetSelection": {"default": "automatic", "enum": ["pick", "automatic"], "markdownDescription": "%browser.targetSelection%", "type": "string"}, "timeout": {"default": 10000, "description": "%node.timeout.description%", "type": "number"}, "timeouts": {"additionalProperties": false, "default": {}, "description": "%timeouts.generalDescription%", "markdownDescription": "%timeouts.generalDescription.markdown%", "properties": {"hoverEvaluation": {"default": 500, "description": "%timeouts.hoverEvaluation.description%", "type": "number"}, "sourceMapCumulativePause": {"default": 1000, "description": "%timeouts.sourceMaps.sourceMapCumulativePause.description%", "type": "number"}, "sourceMapMinPause": {"default": 1000, "description": "%timeouts.sourceMaps.sourceMapMinPause.description%", "type": "number"}}, "type": "object"}, "trace": {"default": true, "description": "%trace.description%", "oneOf": [{"description": "%trace.boolean.description%", "type": "boolean"}, {"additionalProperties": false, "properties": {"logFile": {"description": "%trace.logFile.description%", "type": ["string", "null"]}, "stdio": {"description": "%trace.stdio.description%", "type": "boolean"}}, "type": "object"}]}, "url": {"default": "http://localhost:8080", "description": "%browser.url.description%", "tags": ["setup"], "type": "string"}, "urlFilter": {"default": "", "description": "%browser.urlFilter.description%", "type": "string"}, "vueComponentPaths": {"default": ["${workspaceFolder}/**/*.vue"], "description": "%browser.vueComponentPaths%", "type": "array"}, "webRoot": {"default": "${workspaceFolder}", "description": "%browser.webRoot.description%", "tags": ["setup"], "type": "string"}}}, "launch": {"properties": {"browserLaunchLocation": {"default": null, "description": "%browser.browserLaunchLocation.description%", "oneOf": [{"type": "null"}, {"enum": ["ui", "workspace"], "type": "string"}]}, "cascadeTerminateToConfigurations": {"default": [], "description": "%base.cascadeTerminateToConfigurations.label%", "items": {"type": "string", "uniqueItems": true}, "type": "array"}, "cleanUp": {"default": "wholeBrowser", "description": "%browser.cleanUp.description%", "enum": ["wholeBrowser", "onlyTab"], "type": "string"}, "customDescriptionGenerator": {"description": "%customDescriptionGenerator.description%", "type": "string"}, "customPropertiesGenerator": {"deprecated": true, "description": "%customPropertiesGenerator.description%", "type": "string"}, "cwd": {"default": null, "description": "%browser.cwd.description%", "type": "string"}, "disableNetworkCache": {"default": true, "description": "%browser.disableNetworkCache.description%", "type": "boolean"}, "enableContentValidation": {"default": true, "description": "%enableContentValidation.description%", "type": "boolean"}, "enableDWARF": {"default": true, "markdownDescription": "%base.enableDWARF.label%", "type": "boolean"}, "env": {"default": {}, "description": "%browser.env.description%", "type": "object"}, "file": {"default": "${workspaceFolder}/index.html", "description": "%browser.file.description%", "tags": ["setup"], "type": "string"}, "includeDefaultArgs": {"default": true, "description": "%browser.includeDefaultArgs.description%", "type": "boolean"}, "includeLaunchArgs": {"default": true, "description": "%browser.includeLaunchArgs.description%", "type": "boolean"}, "inspectUri": {"default": null, "description": "%browser.inspectUri.description%", "type": ["string", "null"]}, "killBehavior": {"default": "forceful", "enum": ["forceful", "polite", "none"], "markdownDescription": "%browser.killBehavior.description%", "type": "string"}, "outFiles": {"default": ["${workspaceFolder}/**/*.(m|c|)js", "!**/node_modules/**"], "description": "%outFiles.description%", "items": {"type": "string"}, "tags": ["setup"], "type": ["array"]}, "outputCapture": {"default": "console", "enum": ["console", "std"], "markdownDescription": "%node.launch.outputCapture.description%"}, "pathMapping": {"default": {}, "description": "%browser.pathMapping.description%", "type": "object"}, "pauseForSourceMap": {"default": false, "markdownDescription": "%node.pauseForSourceMap.description%", "type": "boolean"}, "perScriptSourcemaps": {"default": "auto", "description": "%browser.perScriptSourcemaps.description%", "enum": ["yes", "no", "auto"], "type": "string"}, "port": {"default": 0, "description": "%browser.launch.port.description%", "type": "number"}, "profileStartup": {"default": true, "description": "%browser.profileStartup.description%", "type": "boolean"}, "resolveSourceMapLocations": {"default": null, "description": "%node.resolveSourceMapLocations.description%", "items": {"type": "string"}, "type": ["array", "null"]}, "runtimeArgs": {"default": [], "description": "%browser.runtimeArgs.description%", "items": {"type": "string"}, "type": "array"}, "runtimeExecutable": {"default": "stable", "description": "%browser.runtimeExecutable.description%", "type": ["string", "null"]}, "server": {"oneOf": [{"additionalProperties": false, "default": {"program": "node my-server.js"}, "description": "%browser.server.description%", "properties": {"args": {"default": [], "description": "%node.launch.args.description%", "items": {"type": "string"}, "tags": ["setup"], "type": ["array", "string"]}, "attachSimplePort": {"default": 9229, "description": "%node.attachSimplePort.description%", "oneOf": [{"type": "integer"}, {"pattern": "^\\${.*}$", "type": "string"}]}, "autoAttachChildProcesses": {"default": true, "description": "%node.launch.autoAttachChildProcesses.description%", "type": "boolean"}, "cascadeTerminateToConfigurations": {"default": [], "description": "%base.cascadeTerminateToConfigurations.label%", "items": {"type": "string", "uniqueItems": true}, "type": "array"}, "console": {"default": "internalConsole", "description": "%node.launch.console.description%", "enum": ["internalConsole", "integratedTerminal", "externalTerminal"], "enumDescriptions": ["%node.launch.console.internalConsole.description%", "%node.launch.console.integratedTerminal.description%", "%node.launch.console.externalTerminal.description%"], "type": "string"}, "customDescriptionGenerator": {"description": "%customDescriptionGenerator.description%", "type": "string"}, "customPropertiesGenerator": {"deprecated": true, "description": "%customPropertiesGenerator.description%", "type": "string"}, "cwd": {"default": "${workspaceFolder}", "description": "%node.launch.cwd.description%", "tags": ["setup"], "type": "string"}, "enableContentValidation": {"default": true, "description": "%enableContentValidation.description%", "type": "boolean"}, "enableDWARF": {"default": true, "markdownDescription": "%base.enableDWARF.label%", "type": "boolean"}, "env": {"additionalProperties": {"type": ["string", "null"]}, "default": {}, "markdownDescription": "%node.launch.env.description%", "tags": ["setup"], "type": "object"}, "envFile": {"default": "${workspaceFolder}/.env", "description": "%node.launch.envFile.description%", "type": "string"}, "experimentalNetworking": {"default": "auto", "description": "%node.experimentalNetworking.description%", "enum": ["auto", "on", "off"], "type": "string"}, "killBehavior": {"default": "forceful", "enum": ["forceful", "polite", "none"], "markdownDescription": "%node.killBehavior.description%", "type": "string"}, "localRoot": {"default": null, "description": "%node.localRoot.description%", "type": ["string", "null"]}, "nodeVersionHint": {"default": 12, "description": "%node.versionHint.description%", "minimum": 8, "type": "number"}, "outFiles": {"default": ["${workspaceFolder}/**/*.(m|c|)js", "!**/node_modules/**"], "description": "%outFiles.description%", "items": {"type": "string"}, "tags": ["setup"], "type": ["array"]}, "outputCapture": {"default": "console", "enum": ["console", "std"], "markdownDescription": "%node.launch.outputCapture.description%"}, "pauseForSourceMap": {"default": false, "markdownDescription": "%node.pauseForSourceMap.description%", "type": "boolean"}, "profileStartup": {"default": true, "description": "%node.profileStartup.description%", "type": "boolean"}, "program": {"default": "", "description": "%node.launch.program.description%", "tags": ["setup"], "type": "string"}, "remoteRoot": {"default": null, "description": "%node.remoteRoot.description%", "type": ["string", "null"]}, "resolveSourceMapLocations": {"default": ["${workspaceFolder}/**", "!**/node_modules/**"], "description": "%node.resolveSourceMapLocations.description%", "items": {"type": "string"}, "type": ["array", "null"]}, "restart": {"default": true, "description": "%node.attach.restart.description%", "oneOf": [{"type": "boolean"}, {"properties": {"delay": {"default": 1000, "minimum": 0, "type": "number"}, "maxAttempts": {"default": 10, "minimum": 0, "type": "number"}}, "type": "object"}]}, "runtimeArgs": {"default": [], "description": "%node.launch.runtimeArgs.description%", "items": {"type": "string"}, "tags": ["setup"], "type": "array"}, "runtimeExecutable": {"default": "node", "markdownDescription": "%node.launch.runtimeExecutable.description%", "type": ["string", "null"]}, "runtimeSourcemapPausePatterns": {"default": [], "items": {"type": "string"}, "markdownDescription": "%node.launch.runtimeSourcemapPausePatterns%", "type": "array"}, "runtimeVersion": {"default": "default", "markdownDescription": "%node.launch.runtimeVersion.description%", "type": "string"}, "showAsyncStacks": {"default": true, "description": "%node.showAsyncStacks.description%", "oneOf": [{"type": "boolean"}, {"properties": {"onAttach": {"default": 32, "type": "number"}}, "required": ["onAttach"], "type": "object"}, {"properties": {"onceBreakpointResolved": {"default": 32, "type": "number"}}, "required": ["onceBreakpointResolved"], "type": "object"}]}, "skipFiles": {"default": ["${/**"], "description": "%browser.skipFiles.description%", "type": "array"}, "smartStep": {"default": true, "description": "%smartStep.description%", "type": "boolean"}, "sourceMapPathOverrides": {"default": {"meteor://💻app/*": "${workspaceFolder}/*", "webpack:///./~/*": "${workspaceFolder}/node_modules/*", "webpack://?:*/*": "${workspaceFolder}/*"}, "description": "%node.sourceMapPathOverrides.description%", "type": "object"}, "sourceMapRenames": {"default": true, "description": "%browser.sourceMapRenames.description%", "type": "boolean"}, "sourceMaps": {"default": true, "description": "%browser.sourceMaps.description%", "type": "boolean"}, "stopOnEntry": {"default": true, "description": "%node.stopOnEntry.description%", "type": ["boolean", "string"]}, "timeout": {"default": 10000, "description": "%node.timeout.description%", "type": "number"}, "timeouts": {"additionalProperties": false, "default": {}, "description": "%timeouts.generalDescription%", "markdownDescription": "%timeouts.generalDescription.markdown%", "properties": {"hoverEvaluation": {"default": 500, "description": "%timeouts.hoverEvaluation.description%", "type": "number"}, "sourceMapCumulativePause": {"default": 1000, "description": "%timeouts.sourceMaps.sourceMapCumulativePause.description%", "type": "number"}, "sourceMapMinPause": {"default": 1000, "description": "%timeouts.sourceMaps.sourceMapMinPause.description%", "type": "number"}}, "type": "object"}, "trace": {"default": true, "description": "%trace.description%", "oneOf": [{"description": "%trace.boolean.description%", "type": "boolean"}, {"additionalProperties": false, "properties": {"logFile": {"description": "%trace.logFile.description%", "type": ["string", "null"]}, "stdio": {"description": "%trace.stdio.description%", "type": "boolean"}}, "type": "object"}]}}, "type": "object"}, {"additionalProperties": false, "default": {"program": "npm start"}, "description": "%debug.terminal.label%", "properties": {"autoAttachChildProcesses": {"default": true, "description": "%node.launch.autoAttachChildProcesses.description%", "type": "boolean"}, "cascadeTerminateToConfigurations": {"default": [], "description": "%base.cascadeTerminateToConfigurations.label%", "items": {"type": "string", "uniqueItems": true}, "type": "array"}, "command": {"default": "npm start", "description": "%debug.terminal.program.description%", "tags": ["setup"], "type": ["string", "null"]}, "customDescriptionGenerator": {"description": "%customDescriptionGenerator.description%", "type": "string"}, "customPropertiesGenerator": {"deprecated": true, "description": "%customPropertiesGenerator.description%", "type": "string"}, "cwd": {"default": "${workspaceFolder}", "description": "%node.launch.cwd.description%", "docDefault": "localRoot || ${workspaceFolder}", "tags": ["setup"], "type": "string"}, "enableContentValidation": {"default": true, "description": "%enableContentValidation.description%", "type": "boolean"}, "enableDWARF": {"default": true, "markdownDescription": "%base.enableDWARF.label%", "type": "boolean"}, "env": {"additionalProperties": {"type": ["string", "null"]}, "default": {}, "markdownDescription": "%node.launch.env.description%", "tags": ["setup"], "type": "object"}, "envFile": {"default": "${workspaceFolder}/.env", "description": "%node.launch.envFile.description%", "type": "string"}, "localRoot": {"default": null, "description": "%node.localRoot.description%", "type": ["string", "null"]}, "nodeVersionHint": {"default": 12, "description": "%node.versionHint.description%", "minimum": 8, "type": "number"}, "outFiles": {"default": ["${workspaceFolder}/**/*.(m|c|)js", "!**/node_modules/**"], "description": "%outFiles.description%", "items": {"type": "string"}, "tags": ["setup"], "type": ["array"]}, "outputCapture": {"default": "console", "enum": ["console", "std"], "markdownDescription": "%node.launch.outputCapture.description%"}, "pauseForSourceMap": {"default": false, "markdownDescription": "%node.pauseForSourceMap.description%", "type": "boolean"}, "remoteRoot": {"default": null, "description": "%node.remoteRoot.description%", "type": ["string", "null"]}, "resolveSourceMapLocations": {"default": ["${workspaceFolder}/**", "!**/node_modules/**"], "description": "%node.resolveSourceMapLocations.description%", "items": {"type": "string"}, "type": ["array", "null"]}, "runtimeSourcemapPausePatterns": {"default": [], "items": {"type": "string"}, "markdownDescription": "%node.launch.runtimeSourcemapPausePatterns%", "type": "array"}, "showAsyncStacks": {"default": true, "description": "%node.showAsyncStacks.description%", "oneOf": [{"type": "boolean"}, {"properties": {"onAttach": {"default": 32, "type": "number"}}, "required": ["onAttach"], "type": "object"}, {"properties": {"onceBreakpointResolved": {"default": 32, "type": "number"}}, "required": ["onceBreakpointResolved"], "type": "object"}]}, "skipFiles": {"default": ["${/**"], "description": "%browser.skipFiles.description%", "type": "array"}, "smartStep": {"default": true, "description": "%smartStep.description%", "type": "boolean"}, "sourceMapPathOverrides": {"default": {"meteor://💻app/*": "${workspaceFolder}/*", "webpack:///./~/*": "${workspaceFolder}/node_modules/*", "webpack://?:*/*": "${workspaceFolder}/*"}, "description": "%node.sourceMapPathOverrides.description%", "type": "object"}, "sourceMapRenames": {"default": true, "description": "%browser.sourceMapRenames.description%", "type": "boolean"}, "sourceMaps": {"default": true, "description": "%browser.sourceMaps.description%", "type": "boolean"}, "timeout": {"default": 10000, "description": "%node.timeout.description%", "type": "number"}, "timeouts": {"additionalProperties": false, "default": {}, "description": "%timeouts.generalDescription%", "markdownDescription": "%timeouts.generalDescription.markdown%", "properties": {"hoverEvaluation": {"default": 500, "description": "%timeouts.hoverEvaluation.description%", "type": "number"}, "sourceMapCumulativePause": {"default": 1000, "description": "%timeouts.sourceMaps.sourceMapCumulativePause.description%", "type": "number"}, "sourceMapMinPause": {"default": 1000, "description": "%timeouts.sourceMaps.sourceMapMinPause.description%", "type": "number"}}, "type": "object"}, "trace": {"default": true, "description": "%trace.description%", "oneOf": [{"description": "%trace.boolean.description%", "type": "boolean"}, {"additionalProperties": false, "properties": {"logFile": {"description": "%trace.logFile.description%", "type": ["string", "null"]}, "stdio": {"description": "%trace.stdio.description%", "type": "boolean"}}, "type": "object"}]}}, "type": "object"}]}, "showAsyncStacks": {"default": true, "description": "%node.showAsyncStacks.description%", "oneOf": [{"type": "boolean"}, {"properties": {"onAttach": {"default": 32, "type": "number"}}, "required": ["onAttach"], "type": "object"}, {"properties": {"onceBreakpointResolved": {"default": 32, "type": "number"}}, "required": ["onceBreakpointResolved"], "type": "object"}]}, "skipFiles": {"default": ["${/**"], "description": "%browser.skipFiles.description%", "type": "array"}, "smartStep": {"default": true, "description": "%smartStep.description%", "type": "boolean"}, "sourceMapPathOverrides": {"default": {"meteor://💻app/*": "${workspaceFolder}/*", "webpack:///./~/*": "${workspaceFolder}/node_modules/*", "webpack://?:*/*": "${workspaceFolder}/*"}, "description": "%node.sourceMapPathOverrides.description%", "type": "object"}, "sourceMapRenames": {"default": true, "description": "%browser.sourceMapRenames.description%", "type": "boolean"}, "sourceMaps": {"default": true, "description": "%browser.sourceMaps.description%", "type": "boolean"}, "timeout": {"default": 10000, "description": "%node.timeout.description%", "type": "number"}, "timeouts": {"additionalProperties": false, "default": {}, "description": "%timeouts.generalDescription%", "markdownDescription": "%timeouts.generalDescription.markdown%", "properties": {"hoverEvaluation": {"default": 500, "description": "%timeouts.hoverEvaluation.description%", "type": "number"}, "sourceMapCumulativePause": {"default": 1000, "description": "%timeouts.sourceMaps.sourceMapCumulativePause.description%", "type": "number"}, "sourceMapMinPause": {"default": 1000, "description": "%timeouts.sourceMaps.sourceMapMinPause.description%", "type": "number"}}, "type": "object"}, "trace": {"default": true, "description": "%trace.description%", "oneOf": [{"description": "%trace.boolean.description%", "type": "boolean"}, {"additionalProperties": false, "properties": {"logFile": {"description": "%trace.logFile.description%", "type": ["string", "null"]}, "stdio": {"description": "%trace.stdio.description%", "type": "boolean"}}, "type": "object"}]}, "url": {"default": "http://localhost:8080", "description": "%browser.url.description%", "tags": ["setup"], "type": "string"}, "urlFilter": {"default": "", "description": "%browser.urlFilter.description%", "type": "string"}, "userDataDir": {"default": true, "description": "%browser.userDataDir.description%", "type": ["string", "boolean"]}, "vueComponentPaths": {"default": ["${workspaceFolder}/**/*.vue"], "description": "%browser.vueComponentPaths%", "type": "array"}, "webRoot": {"default": "${workspaceFolder}", "description": "%browser.webRoot.description%", "tags": ["setup"], "type": "string"}}}}, "configurationSnippets": [], "deprecated": "Please use type chrome instead", "label": "%chrome.label%", "languages": ["javascript", "typescript", "javascriptreact", "typescriptreact", "html", "css", "coffeescript", "handlebars", "vue"], "strings": {"unverifiedBreakpoints": "%debug.unverifiedBreakpoints%"}, "type": "pwa-chrome"}, {"aiKey": "0c6ae279ed8443289764825290e4f9e2-1a736e7c-1324-4338-be46-fc2a58ae4d14-7255", "configurationAttributes": {"attach": {"properties": {"address": {"default": "localhost", "description": "%browser.address.description%", "type": "string"}, "browserAttachLocation": {"default": null, "description": "%browser.browserAttachLocation.description%", "oneOf": [{"type": "null"}, {"enum": ["ui", "workspace"], "type": "string"}]}, "cascadeTerminateToConfigurations": {"default": [], "description": "%base.cascadeTerminateToConfigurations.label%", "items": {"type": "string", "uniqueItems": true}, "type": "array"}, "customDescriptionGenerator": {"description": "%customDescriptionGenerator.description%", "type": "string"}, "customPropertiesGenerator": {"deprecated": true, "description": "%customPropertiesGenerator.description%", "type": "string"}, "disableNetworkCache": {"default": true, "description": "%browser.disableNetworkCache.description%", "type": "boolean"}, "enableContentValidation": {"default": true, "description": "%enableContentValidation.description%", "type": "boolean"}, "enableDWARF": {"default": true, "markdownDescription": "%base.enableDWARF.label%", "type": "boolean"}, "inspectUri": {"default": null, "description": "%browser.inspectUri.description%", "type": ["string", "null"]}, "outFiles": {"default": ["${workspaceFolder}/**/*.(m|c|)js", "!**/node_modules/**"], "description": "%outFiles.description%", "items": {"type": "string"}, "tags": ["setup"], "type": ["array"]}, "outputCapture": {"default": "console", "enum": ["console", "std"], "markdownDescription": "%node.launch.outputCapture.description%"}, "pathMapping": {"default": {}, "description": "%browser.pathMapping.description%", "type": "object"}, "pauseForSourceMap": {"default": false, "markdownDescription": "%node.pauseForSourceMap.description%", "type": "boolean"}, "perScriptSourcemaps": {"default": "auto", "description": "%browser.perScriptSourcemaps.description%", "enum": ["yes", "no", "auto"], "type": "string"}, "port": {"default": 9229, "description": "%browser.attach.port.description%", "oneOf": [{"type": "integer"}, {"pattern": "^\\${.*}$", "type": "string"}], "tags": ["setup"]}, "resolveSourceMapLocations": {"default": null, "description": "%node.resolveSourceMapLocations.description%", "items": {"type": "string"}, "type": ["array", "null"]}, "restart": {"default": false, "markdownDescription": "%browser.restart%", "type": "boolean"}, "server": {"oneOf": [{"additionalProperties": false, "default": {"program": "node my-server.js"}, "description": "%browser.server.description%", "properties": {"args": {"default": [], "description": "%node.launch.args.description%", "items": {"type": "string"}, "tags": ["setup"], "type": ["array", "string"]}, "attachSimplePort": {"default": 9229, "description": "%node.attachSimplePort.description%", "oneOf": [{"type": "integer"}, {"pattern": "^\\${.*}$", "type": "string"}]}, "autoAttachChildProcesses": {"default": true, "description": "%node.launch.autoAttachChildProcesses.description%", "type": "boolean"}, "cascadeTerminateToConfigurations": {"default": [], "description": "%base.cascadeTerminateToConfigurations.label%", "items": {"type": "string", "uniqueItems": true}, "type": "array"}, "console": {"default": "internalConsole", "description": "%node.launch.console.description%", "enum": ["internalConsole", "integratedTerminal", "externalTerminal"], "enumDescriptions": ["%node.launch.console.internalConsole.description%", "%node.launch.console.integratedTerminal.description%", "%node.launch.console.externalTerminal.description%"], "type": "string"}, "customDescriptionGenerator": {"description": "%customDescriptionGenerator.description%", "type": "string"}, "customPropertiesGenerator": {"deprecated": true, "description": "%customPropertiesGenerator.description%", "type": "string"}, "cwd": {"default": "${workspaceFolder}", "description": "%node.launch.cwd.description%", "tags": ["setup"], "type": "string"}, "enableContentValidation": {"default": true, "description": "%enableContentValidation.description%", "type": "boolean"}, "enableDWARF": {"default": true, "markdownDescription": "%base.enableDWARF.label%", "type": "boolean"}, "env": {"additionalProperties": {"type": ["string", "null"]}, "default": {}, "markdownDescription": "%node.launch.env.description%", "tags": ["setup"], "type": "object"}, "envFile": {"default": "${workspaceFolder}/.env", "description": "%node.launch.envFile.description%", "type": "string"}, "experimentalNetworking": {"default": "auto", "description": "%node.experimentalNetworking.description%", "enum": ["auto", "on", "off"], "type": "string"}, "killBehavior": {"default": "forceful", "enum": ["forceful", "polite", "none"], "markdownDescription": "%node.killBehavior.description%", "type": "string"}, "localRoot": {"default": null, "description": "%node.localRoot.description%", "type": ["string", "null"]}, "nodeVersionHint": {"default": 12, "description": "%node.versionHint.description%", "minimum": 8, "type": "number"}, "outFiles": {"default": ["${workspaceFolder}/**/*.(m|c|)js", "!**/node_modules/**"], "description": "%outFiles.description%", "items": {"type": "string"}, "tags": ["setup"], "type": ["array"]}, "outputCapture": {"default": "console", "enum": ["console", "std"], "markdownDescription": "%node.launch.outputCapture.description%"}, "pauseForSourceMap": {"default": false, "markdownDescription": "%node.pauseForSourceMap.description%", "type": "boolean"}, "profileStartup": {"default": true, "description": "%node.profileStartup.description%", "type": "boolean"}, "program": {"default": "", "description": "%node.launch.program.description%", "tags": ["setup"], "type": "string"}, "remoteRoot": {"default": null, "description": "%node.remoteRoot.description%", "type": ["string", "null"]}, "resolveSourceMapLocations": {"default": ["${workspaceFolder}/**", "!**/node_modules/**"], "description": "%node.resolveSourceMapLocations.description%", "items": {"type": "string"}, "type": ["array", "null"]}, "restart": {"default": true, "description": "%node.attach.restart.description%", "oneOf": [{"type": "boolean"}, {"properties": {"delay": {"default": 1000, "minimum": 0, "type": "number"}, "maxAttempts": {"default": 10, "minimum": 0, "type": "number"}}, "type": "object"}]}, "runtimeArgs": {"default": [], "description": "%node.launch.runtimeArgs.description%", "items": {"type": "string"}, "tags": ["setup"], "type": "array"}, "runtimeExecutable": {"default": "node", "markdownDescription": "%node.launch.runtimeExecutable.description%", "type": ["string", "null"]}, "runtimeSourcemapPausePatterns": {"default": [], "items": {"type": "string"}, "markdownDescription": "%node.launch.runtimeSourcemapPausePatterns%", "type": "array"}, "runtimeVersion": {"default": "default", "markdownDescription": "%node.launch.runtimeVersion.description%", "type": "string"}, "showAsyncStacks": {"default": true, "description": "%node.showAsyncStacks.description%", "oneOf": [{"type": "boolean"}, {"properties": {"onAttach": {"default": 32, "type": "number"}}, "required": ["onAttach"], "type": "object"}, {"properties": {"onceBreakpointResolved": {"default": 32, "type": "number"}}, "required": ["onceBreakpointResolved"], "type": "object"}]}, "skipFiles": {"default": ["${/**"], "description": "%browser.skipFiles.description%", "type": "array"}, "smartStep": {"default": true, "description": "%smartStep.description%", "type": "boolean"}, "sourceMapPathOverrides": {"default": {"meteor://💻app/*": "${workspaceFolder}/*", "webpack:///./~/*": "${workspaceFolder}/node_modules/*", "webpack://?:*/*": "${workspaceFolder}/*"}, "description": "%node.sourceMapPathOverrides.description%", "type": "object"}, "sourceMapRenames": {"default": true, "description": "%browser.sourceMapRenames.description%", "type": "boolean"}, "sourceMaps": {"default": true, "description": "%browser.sourceMaps.description%", "type": "boolean"}, "stopOnEntry": {"default": true, "description": "%node.stopOnEntry.description%", "type": ["boolean", "string"]}, "timeout": {"default": 10000, "description": "%node.timeout.description%", "type": "number"}, "timeouts": {"additionalProperties": false, "default": {}, "description": "%timeouts.generalDescription%", "markdownDescription": "%timeouts.generalDescription.markdown%", "properties": {"hoverEvaluation": {"default": 500, "description": "%timeouts.hoverEvaluation.description%", "type": "number"}, "sourceMapCumulativePause": {"default": 1000, "description": "%timeouts.sourceMaps.sourceMapCumulativePause.description%", "type": "number"}, "sourceMapMinPause": {"default": 1000, "description": "%timeouts.sourceMaps.sourceMapMinPause.description%", "type": "number"}}, "type": "object"}, "trace": {"default": true, "description": "%trace.description%", "oneOf": [{"description": "%trace.boolean.description%", "type": "boolean"}, {"additionalProperties": false, "properties": {"logFile": {"description": "%trace.logFile.description%", "type": ["string", "null"]}, "stdio": {"description": "%trace.stdio.description%", "type": "boolean"}}, "type": "object"}]}}, "type": "object"}, {"additionalProperties": false, "default": {"program": "npm start"}, "description": "%debug.terminal.label%", "properties": {"autoAttachChildProcesses": {"default": true, "description": "%node.launch.autoAttachChildProcesses.description%", "type": "boolean"}, "cascadeTerminateToConfigurations": {"default": [], "description": "%base.cascadeTerminateToConfigurations.label%", "items": {"type": "string", "uniqueItems": true}, "type": "array"}, "command": {"default": "npm start", "description": "%debug.terminal.program.description%", "tags": ["setup"], "type": ["string", "null"]}, "customDescriptionGenerator": {"description": "%customDescriptionGenerator.description%", "type": "string"}, "customPropertiesGenerator": {"deprecated": true, "description": "%customPropertiesGenerator.description%", "type": "string"}, "cwd": {"default": "${workspaceFolder}", "description": "%node.launch.cwd.description%", "docDefault": "localRoot || ${workspaceFolder}", "tags": ["setup"], "type": "string"}, "enableContentValidation": {"default": true, "description": "%enableContentValidation.description%", "type": "boolean"}, "enableDWARF": {"default": true, "markdownDescription": "%base.enableDWARF.label%", "type": "boolean"}, "env": {"additionalProperties": {"type": ["string", "null"]}, "default": {}, "markdownDescription": "%node.launch.env.description%", "tags": ["setup"], "type": "object"}, "envFile": {"default": "${workspaceFolder}/.env", "description": "%node.launch.envFile.description%", "type": "string"}, "localRoot": {"default": null, "description": "%node.localRoot.description%", "type": ["string", "null"]}, "nodeVersionHint": {"default": 12, "description": "%node.versionHint.description%", "minimum": 8, "type": "number"}, "outFiles": {"default": ["${workspaceFolder}/**/*.(m|c|)js", "!**/node_modules/**"], "description": "%outFiles.description%", "items": {"type": "string"}, "tags": ["setup"], "type": ["array"]}, "outputCapture": {"default": "console", "enum": ["console", "std"], "markdownDescription": "%node.launch.outputCapture.description%"}, "pauseForSourceMap": {"default": false, "markdownDescription": "%node.pauseForSourceMap.description%", "type": "boolean"}, "remoteRoot": {"default": null, "description": "%node.remoteRoot.description%", "type": ["string", "null"]}, "resolveSourceMapLocations": {"default": ["${workspaceFolder}/**", "!**/node_modules/**"], "description": "%node.resolveSourceMapLocations.description%", "items": {"type": "string"}, "type": ["array", "null"]}, "runtimeSourcemapPausePatterns": {"default": [], "items": {"type": "string"}, "markdownDescription": "%node.launch.runtimeSourcemapPausePatterns%", "type": "array"}, "showAsyncStacks": {"default": true, "description": "%node.showAsyncStacks.description%", "oneOf": [{"type": "boolean"}, {"properties": {"onAttach": {"default": 32, "type": "number"}}, "required": ["onAttach"], "type": "object"}, {"properties": {"onceBreakpointResolved": {"default": 32, "type": "number"}}, "required": ["onceBreakpointResolved"], "type": "object"}]}, "skipFiles": {"default": ["${/**"], "description": "%browser.skipFiles.description%", "type": "array"}, "smartStep": {"default": true, "description": "%smartStep.description%", "type": "boolean"}, "sourceMapPathOverrides": {"default": {"meteor://💻app/*": "${workspaceFolder}/*", "webpack:///./~/*": "${workspaceFolder}/node_modules/*", "webpack://?:*/*": "${workspaceFolder}/*"}, "description": "%node.sourceMapPathOverrides.description%", "type": "object"}, "sourceMapRenames": {"default": true, "description": "%browser.sourceMapRenames.description%", "type": "boolean"}, "sourceMaps": {"default": true, "description": "%browser.sourceMaps.description%", "type": "boolean"}, "timeout": {"default": 10000, "description": "%node.timeout.description%", "type": "number"}, "timeouts": {"additionalProperties": false, "default": {}, "description": "%timeouts.generalDescription%", "markdownDescription": "%timeouts.generalDescription.markdown%", "properties": {"hoverEvaluation": {"default": 500, "description": "%timeouts.hoverEvaluation.description%", "type": "number"}, "sourceMapCumulativePause": {"default": 1000, "description": "%timeouts.sourceMaps.sourceMapCumulativePause.description%", "type": "number"}, "sourceMapMinPause": {"default": 1000, "description": "%timeouts.sourceMaps.sourceMapMinPause.description%", "type": "number"}}, "type": "object"}, "trace": {"default": true, "description": "%trace.description%", "oneOf": [{"description": "%trace.boolean.description%", "type": "boolean"}, {"additionalProperties": false, "properties": {"logFile": {"description": "%trace.logFile.description%", "type": ["string", "null"]}, "stdio": {"description": "%trace.stdio.description%", "type": "boolean"}}, "type": "object"}]}}, "type": "object"}]}, "showAsyncStacks": {"default": true, "description": "%node.showAsyncStacks.description%", "oneOf": [{"type": "boolean"}, {"properties": {"onAttach": {"default": 32, "type": "number"}}, "required": ["onAttach"], "type": "object"}, {"properties": {"onceBreakpointResolved": {"default": 32, "type": "number"}}, "required": ["onceBreakpointResolved"], "type": "object"}]}, "skipFiles": {"default": ["${/**"], "description": "%browser.skipFiles.description%", "type": "array"}, "smartStep": {"default": true, "description": "%smartStep.description%", "type": "boolean"}, "sourceMapPathOverrides": {"default": {"meteor://💻app/*": "${workspaceFolder}/*", "webpack:///./~/*": "${workspaceFolder}/node_modules/*", "webpack://?:*/*": "${workspaceFolder}/*"}, "description": "%node.sourceMapPathOverrides.description%", "type": "object"}, "sourceMapRenames": {"default": true, "description": "%browser.sourceMapRenames.description%", "type": "boolean"}, "sourceMaps": {"default": true, "description": "%browser.sourceMaps.description%", "type": "boolean"}, "targetSelection": {"default": "automatic", "enum": ["pick", "automatic"], "markdownDescription": "%browser.targetSelection%", "type": "string"}, "timeout": {"default": 10000, "description": "%node.timeout.description%", "type": "number"}, "timeouts": {"additionalProperties": false, "default": {}, "description": "%timeouts.generalDescription%", "markdownDescription": "%timeouts.generalDescription.markdown%", "properties": {"hoverEvaluation": {"default": 500, "description": "%timeouts.hoverEvaluation.description%", "type": "number"}, "sourceMapCumulativePause": {"default": 1000, "description": "%timeouts.sourceMaps.sourceMapCumulativePause.description%", "type": "number"}, "sourceMapMinPause": {"default": 1000, "description": "%timeouts.sourceMaps.sourceMapMinPause.description%", "type": "number"}}, "type": "object"}, "trace": {"default": true, "description": "%trace.description%", "oneOf": [{"description": "%trace.boolean.description%", "type": "boolean"}, {"additionalProperties": false, "properties": {"logFile": {"description": "%trace.logFile.description%", "type": ["string", "null"]}, "stdio": {"description": "%trace.stdio.description%", "type": "boolean"}}, "type": "object"}]}, "url": {"default": "http://localhost:8080", "description": "%browser.url.description%", "tags": ["setup"], "type": "string"}, "urlFilter": {"default": "", "description": "%browser.urlFilter.description%", "type": "string"}, "vueComponentPaths": {"default": ["${workspaceFolder}/**/*.vue"], "description": "%browser.vueComponentPaths%", "type": "array"}, "webRoot": {"default": "${workspaceFolder}", "description": "%browser.webRoot.description%", "tags": ["setup"], "type": "string"}}}, "launch": {"properties": {"browserLaunchLocation": {"default": null, "description": "%browser.browserLaunchLocation.description%", "oneOf": [{"type": "null"}, {"enum": ["ui", "workspace"], "type": "string"}]}, "cascadeTerminateToConfigurations": {"default": [], "description": "%base.cascadeTerminateToConfigurations.label%", "items": {"type": "string", "uniqueItems": true}, "type": "array"}, "cleanUp": {"default": "wholeBrowser", "description": "%browser.cleanUp.description%", "enum": ["wholeBrowser", "onlyTab"], "type": "string"}, "customDescriptionGenerator": {"description": "%customDescriptionGenerator.description%", "type": "string"}, "customPropertiesGenerator": {"deprecated": true, "description": "%customPropertiesGenerator.description%", "type": "string"}, "cwd": {"default": null, "description": "%browser.cwd.description%", "type": "string"}, "disableNetworkCache": {"default": true, "description": "%browser.disableNetworkCache.description%", "type": "boolean"}, "enableContentValidation": {"default": true, "description": "%enableContentValidation.description%", "type": "boolean"}, "enableDWARF": {"default": true, "markdownDescription": "%base.enableDWARF.label%", "type": "boolean"}, "env": {"default": {}, "description": "%browser.env.description%", "type": "object"}, "file": {"default": "${workspaceFolder}/index.html", "description": "%browser.file.description%", "tags": ["setup"], "type": "string"}, "includeDefaultArgs": {"default": true, "description": "%browser.includeDefaultArgs.description%", "type": "boolean"}, "includeLaunchArgs": {"default": true, "description": "%browser.includeLaunchArgs.description%", "type": "boolean"}, "inspectUri": {"default": null, "description": "%browser.inspectUri.description%", "type": ["string", "null"]}, "killBehavior": {"default": "forceful", "enum": ["forceful", "polite", "none"], "markdownDescription": "%browser.killBehavior.description%", "type": "string"}, "outFiles": {"default": ["${workspaceFolder}/**/*.(m|c|)js", "!**/node_modules/**"], "description": "%outFiles.description%", "items": {"type": "string"}, "tags": ["setup"], "type": ["array"]}, "outputCapture": {"default": "console", "enum": ["console", "std"], "markdownDescription": "%node.launch.outputCapture.description%"}, "pathMapping": {"default": {}, "description": "%browser.pathMapping.description%", "type": "object"}, "pauseForSourceMap": {"default": false, "markdownDescription": "%node.pauseForSourceMap.description%", "type": "boolean"}, "perScriptSourcemaps": {"default": "auto", "description": "%browser.perScriptSourcemaps.description%", "enum": ["yes", "no", "auto"], "type": "string"}, "port": {"default": 0, "description": "%browser.launch.port.description%", "type": "number"}, "profileStartup": {"default": true, "description": "%browser.profileStartup.description%", "type": "boolean"}, "resolveSourceMapLocations": {"default": null, "description": "%node.resolveSourceMapLocations.description%", "items": {"type": "string"}, "type": ["array", "null"]}, "runtimeArgs": {"default": [], "description": "%browser.runtimeArgs.description%", "items": {"type": "string"}, "type": "array"}, "runtimeExecutable": {"default": "stable", "description": "%browser.runtimeExecutable.description%", "type": ["string", "null"]}, "server": {"oneOf": [{"additionalProperties": false, "default": {"program": "node my-server.js"}, "description": "%browser.server.description%", "properties": {"args": {"default": [], "description": "%node.launch.args.description%", "items": {"type": "string"}, "tags": ["setup"], "type": ["array", "string"]}, "attachSimplePort": {"default": 9229, "description": "%node.attachSimplePort.description%", "oneOf": [{"type": "integer"}, {"pattern": "^\\${.*}$", "type": "string"}]}, "autoAttachChildProcesses": {"default": true, "description": "%node.launch.autoAttachChildProcesses.description%", "type": "boolean"}, "cascadeTerminateToConfigurations": {"default": [], "description": "%base.cascadeTerminateToConfigurations.label%", "items": {"type": "string", "uniqueItems": true}, "type": "array"}, "console": {"default": "internalConsole", "description": "%node.launch.console.description%", "enum": ["internalConsole", "integratedTerminal", "externalTerminal"], "enumDescriptions": ["%node.launch.console.internalConsole.description%", "%node.launch.console.integratedTerminal.description%", "%node.launch.console.externalTerminal.description%"], "type": "string"}, "customDescriptionGenerator": {"description": "%customDescriptionGenerator.description%", "type": "string"}, "customPropertiesGenerator": {"deprecated": true, "description": "%customPropertiesGenerator.description%", "type": "string"}, "cwd": {"default": "${workspaceFolder}", "description": "%node.launch.cwd.description%", "tags": ["setup"], "type": "string"}, "enableContentValidation": {"default": true, "description": "%enableContentValidation.description%", "type": "boolean"}, "enableDWARF": {"default": true, "markdownDescription": "%base.enableDWARF.label%", "type": "boolean"}, "env": {"additionalProperties": {"type": ["string", "null"]}, "default": {}, "markdownDescription": "%node.launch.env.description%", "tags": ["setup"], "type": "object"}, "envFile": {"default": "${workspaceFolder}/.env", "description": "%node.launch.envFile.description%", "type": "string"}, "experimentalNetworking": {"default": "auto", "description": "%node.experimentalNetworking.description%", "enum": ["auto", "on", "off"], "type": "string"}, "killBehavior": {"default": "forceful", "enum": ["forceful", "polite", "none"], "markdownDescription": "%node.killBehavior.description%", "type": "string"}, "localRoot": {"default": null, "description": "%node.localRoot.description%", "type": ["string", "null"]}, "nodeVersionHint": {"default": 12, "description": "%node.versionHint.description%", "minimum": 8, "type": "number"}, "outFiles": {"default": ["${workspaceFolder}/**/*.(m|c|)js", "!**/node_modules/**"], "description": "%outFiles.description%", "items": {"type": "string"}, "tags": ["setup"], "type": ["array"]}, "outputCapture": {"default": "console", "enum": ["console", "std"], "markdownDescription": "%node.launch.outputCapture.description%"}, "pauseForSourceMap": {"default": false, "markdownDescription": "%node.pauseForSourceMap.description%", "type": "boolean"}, "profileStartup": {"default": true, "description": "%node.profileStartup.description%", "type": "boolean"}, "program": {"default": "", "description": "%node.launch.program.description%", "tags": ["setup"], "type": "string"}, "remoteRoot": {"default": null, "description": "%node.remoteRoot.description%", "type": ["string", "null"]}, "resolveSourceMapLocations": {"default": ["${workspaceFolder}/**", "!**/node_modules/**"], "description": "%node.resolveSourceMapLocations.description%", "items": {"type": "string"}, "type": ["array", "null"]}, "restart": {"default": true, "description": "%node.attach.restart.description%", "oneOf": [{"type": "boolean"}, {"properties": {"delay": {"default": 1000, "minimum": 0, "type": "number"}, "maxAttempts": {"default": 10, "minimum": 0, "type": "number"}}, "type": "object"}]}, "runtimeArgs": {"default": [], "description": "%node.launch.runtimeArgs.description%", "items": {"type": "string"}, "tags": ["setup"], "type": "array"}, "runtimeExecutable": {"default": "node", "markdownDescription": "%node.launch.runtimeExecutable.description%", "type": ["string", "null"]}, "runtimeSourcemapPausePatterns": {"default": [], "items": {"type": "string"}, "markdownDescription": "%node.launch.runtimeSourcemapPausePatterns%", "type": "array"}, "runtimeVersion": {"default": "default", "markdownDescription": "%node.launch.runtimeVersion.description%", "type": "string"}, "showAsyncStacks": {"default": true, "description": "%node.showAsyncStacks.description%", "oneOf": [{"type": "boolean"}, {"properties": {"onAttach": {"default": 32, "type": "number"}}, "required": ["onAttach"], "type": "object"}, {"properties": {"onceBreakpointResolved": {"default": 32, "type": "number"}}, "required": ["onceBreakpointResolved"], "type": "object"}]}, "skipFiles": {"default": ["${/**"], "description": "%browser.skipFiles.description%", "type": "array"}, "smartStep": {"default": true, "description": "%smartStep.description%", "type": "boolean"}, "sourceMapPathOverrides": {"default": {"meteor://💻app/*": "${workspaceFolder}/*", "webpack:///./~/*": "${workspaceFolder}/node_modules/*", "webpack://?:*/*": "${workspaceFolder}/*"}, "description": "%node.sourceMapPathOverrides.description%", "type": "object"}, "sourceMapRenames": {"default": true, "description": "%browser.sourceMapRenames.description%", "type": "boolean"}, "sourceMaps": {"default": true, "description": "%browser.sourceMaps.description%", "type": "boolean"}, "stopOnEntry": {"default": true, "description": "%node.stopOnEntry.description%", "type": ["boolean", "string"]}, "timeout": {"default": 10000, "description": "%node.timeout.description%", "type": "number"}, "timeouts": {"additionalProperties": false, "default": {}, "description": "%timeouts.generalDescription%", "markdownDescription": "%timeouts.generalDescription.markdown%", "properties": {"hoverEvaluation": {"default": 500, "description": "%timeouts.hoverEvaluation.description%", "type": "number"}, "sourceMapCumulativePause": {"default": 1000, "description": "%timeouts.sourceMaps.sourceMapCumulativePause.description%", "type": "number"}, "sourceMapMinPause": {"default": 1000, "description": "%timeouts.sourceMaps.sourceMapMinPause.description%", "type": "number"}}, "type": "object"}, "trace": {"default": true, "description": "%trace.description%", "oneOf": [{"description": "%trace.boolean.description%", "type": "boolean"}, {"additionalProperties": false, "properties": {"logFile": {"description": "%trace.logFile.description%", "type": ["string", "null"]}, "stdio": {"description": "%trace.stdio.description%", "type": "boolean"}}, "type": "object"}]}}, "type": "object"}, {"additionalProperties": false, "default": {"program": "npm start"}, "description": "%debug.terminal.label%", "properties": {"autoAttachChildProcesses": {"default": true, "description": "%node.launch.autoAttachChildProcesses.description%", "type": "boolean"}, "cascadeTerminateToConfigurations": {"default": [], "description": "%base.cascadeTerminateToConfigurations.label%", "items": {"type": "string", "uniqueItems": true}, "type": "array"}, "command": {"default": "npm start", "description": "%debug.terminal.program.description%", "tags": ["setup"], "type": ["string", "null"]}, "customDescriptionGenerator": {"description": "%customDescriptionGenerator.description%", "type": "string"}, "customPropertiesGenerator": {"deprecated": true, "description": "%customPropertiesGenerator.description%", "type": "string"}, "cwd": {"default": "${workspaceFolder}", "description": "%node.launch.cwd.description%", "docDefault": "localRoot || ${workspaceFolder}", "tags": ["setup"], "type": "string"}, "enableContentValidation": {"default": true, "description": "%enableContentValidation.description%", "type": "boolean"}, "enableDWARF": {"default": true, "markdownDescription": "%base.enableDWARF.label%", "type": "boolean"}, "env": {"additionalProperties": {"type": ["string", "null"]}, "default": {}, "markdownDescription": "%node.launch.env.description%", "tags": ["setup"], "type": "object"}, "envFile": {"default": "${workspaceFolder}/.env", "description": "%node.launch.envFile.description%", "type": "string"}, "localRoot": {"default": null, "description": "%node.localRoot.description%", "type": ["string", "null"]}, "nodeVersionHint": {"default": 12, "description": "%node.versionHint.description%", "minimum": 8, "type": "number"}, "outFiles": {"default": ["${workspaceFolder}/**/*.(m|c|)js", "!**/node_modules/**"], "description": "%outFiles.description%", "items": {"type": "string"}, "tags": ["setup"], "type": ["array"]}, "outputCapture": {"default": "console", "enum": ["console", "std"], "markdownDescription": "%node.launch.outputCapture.description%"}, "pauseForSourceMap": {"default": false, "markdownDescription": "%node.pauseForSourceMap.description%", "type": "boolean"}, "remoteRoot": {"default": null, "description": "%node.remoteRoot.description%", "type": ["string", "null"]}, "resolveSourceMapLocations": {"default": ["${workspaceFolder}/**", "!**/node_modules/**"], "description": "%node.resolveSourceMapLocations.description%", "items": {"type": "string"}, "type": ["array", "null"]}, "runtimeSourcemapPausePatterns": {"default": [], "items": {"type": "string"}, "markdownDescription": "%node.launch.runtimeSourcemapPausePatterns%", "type": "array"}, "showAsyncStacks": {"default": true, "description": "%node.showAsyncStacks.description%", "oneOf": [{"type": "boolean"}, {"properties": {"onAttach": {"default": 32, "type": "number"}}, "required": ["onAttach"], "type": "object"}, {"properties": {"onceBreakpointResolved": {"default": 32, "type": "number"}}, "required": ["onceBreakpointResolved"], "type": "object"}]}, "skipFiles": {"default": ["${/**"], "description": "%browser.skipFiles.description%", "type": "array"}, "smartStep": {"default": true, "description": "%smartStep.description%", "type": "boolean"}, "sourceMapPathOverrides": {"default": {"meteor://💻app/*": "${workspaceFolder}/*", "webpack:///./~/*": "${workspaceFolder}/node_modules/*", "webpack://?:*/*": "${workspaceFolder}/*"}, "description": "%node.sourceMapPathOverrides.description%", "type": "object"}, "sourceMapRenames": {"default": true, "description": "%browser.sourceMapRenames.description%", "type": "boolean"}, "sourceMaps": {"default": true, "description": "%browser.sourceMaps.description%", "type": "boolean"}, "timeout": {"default": 10000, "description": "%node.timeout.description%", "type": "number"}, "timeouts": {"additionalProperties": false, "default": {}, "description": "%timeouts.generalDescription%", "markdownDescription": "%timeouts.generalDescription.markdown%", "properties": {"hoverEvaluation": {"default": 500, "description": "%timeouts.hoverEvaluation.description%", "type": "number"}, "sourceMapCumulativePause": {"default": 1000, "description": "%timeouts.sourceMaps.sourceMapCumulativePause.description%", "type": "number"}, "sourceMapMinPause": {"default": 1000, "description": "%timeouts.sourceMaps.sourceMapMinPause.description%", "type": "number"}}, "type": "object"}, "trace": {"default": true, "description": "%trace.description%", "oneOf": [{"description": "%trace.boolean.description%", "type": "boolean"}, {"additionalProperties": false, "properties": {"logFile": {"description": "%trace.logFile.description%", "type": ["string", "null"]}, "stdio": {"description": "%trace.stdio.description%", "type": "boolean"}}, "type": "object"}]}}, "type": "object"}]}, "showAsyncStacks": {"default": true, "description": "%node.showAsyncStacks.description%", "oneOf": [{"type": "boolean"}, {"properties": {"onAttach": {"default": 32, "type": "number"}}, "required": ["onAttach"], "type": "object"}, {"properties": {"onceBreakpointResolved": {"default": 32, "type": "number"}}, "required": ["onceBreakpointResolved"], "type": "object"}]}, "skipFiles": {"default": ["${/**"], "description": "%browser.skipFiles.description%", "type": "array"}, "smartStep": {"default": true, "description": "%smartStep.description%", "type": "boolean"}, "sourceMapPathOverrides": {"default": {"meteor://💻app/*": "${workspaceFolder}/*", "webpack:///./~/*": "${workspaceFolder}/node_modules/*", "webpack://?:*/*": "${workspaceFolder}/*"}, "description": "%node.sourceMapPathOverrides.description%", "type": "object"}, "sourceMapRenames": {"default": true, "description": "%browser.sourceMapRenames.description%", "type": "boolean"}, "sourceMaps": {"default": true, "description": "%browser.sourceMaps.description%", "type": "boolean"}, "timeout": {"default": 10000, "description": "%node.timeout.description%", "type": "number"}, "timeouts": {"additionalProperties": false, "default": {}, "description": "%timeouts.generalDescription%", "markdownDescription": "%timeouts.generalDescription.markdown%", "properties": {"hoverEvaluation": {"default": 500, "description": "%timeouts.hoverEvaluation.description%", "type": "number"}, "sourceMapCumulativePause": {"default": 1000, "description": "%timeouts.sourceMaps.sourceMapCumulativePause.description%", "type": "number"}, "sourceMapMinPause": {"default": 1000, "description": "%timeouts.sourceMaps.sourceMapMinPause.description%", "type": "number"}}, "type": "object"}, "trace": {"default": true, "description": "%trace.description%", "oneOf": [{"description": "%trace.boolean.description%", "type": "boolean"}, {"additionalProperties": false, "properties": {"logFile": {"description": "%trace.logFile.description%", "type": ["string", "null"]}, "stdio": {"description": "%trace.stdio.description%", "type": "boolean"}}, "type": "object"}]}, "url": {"default": "http://localhost:8080", "description": "%browser.url.description%", "tags": ["setup"], "type": "string"}, "urlFilter": {"default": "", "description": "%browser.urlFilter.description%", "type": "string"}, "userDataDir": {"default": true, "description": "%browser.userDataDir.description%", "type": ["string", "boolean"]}, "vueComponentPaths": {"default": ["${workspaceFolder}/**/*.vue"], "description": "%browser.vueComponentPaths%", "type": "array"}, "webRoot": {"default": "${workspaceFolder}", "description": "%browser.webRoot.description%", "tags": ["setup"], "type": "string"}}}}, "configurationSnippets": [{"body": {"name": "Launch Chrome", "request": "launch", "type": "chrome", "url": "http://localhost:8080", "webRoot": "^\"${2:\\${workspaceFolder\\}}\""}, "description": "%chrome.launch.description%", "label": "%chrome.launch.label%"}, {"body": {"name": "Attach to Chrome", "port": 9222, "request": "attach", "type": "chrome", "webRoot": "^\"${2:\\${workspaceFolder\\}}\""}, "description": "%chrome.attach.description%", "label": "%chrome.attach.label%"}], "label": "%chrome.label%", "strings": {"unverifiedBreakpoints": "%debug.unverifiedBreakpoints%"}, "type": "chrome"}, {"aiKey": "0c6ae279ed8443289764825290e4f9e2-1a736e7c-1324-4338-be46-fc2a58ae4d14-7255", "configurationAttributes": {"attach": {"properties": {"address": {"default": "localhost", "description": "%browser.address.description%", "type": "string"}, "browserAttachLocation": {"default": null, "description": "%browser.browserAttachLocation.description%", "oneOf": [{"type": "null"}, {"enum": ["ui", "workspace"], "type": "string"}]}, "cascadeTerminateToConfigurations": {"default": [], "description": "%base.cascadeTerminateToConfigurations.label%", "items": {"type": "string", "uniqueItems": true}, "type": "array"}, "customDescriptionGenerator": {"description": "%customDescriptionGenerator.description%", "type": "string"}, "customPropertiesGenerator": {"deprecated": true, "description": "%customPropertiesGenerator.description%", "type": "string"}, "disableNetworkCache": {"default": true, "description": "%browser.disableNetworkCache.description%", "type": "boolean"}, "enableContentValidation": {"default": true, "description": "%enableContentValidation.description%", "type": "boolean"}, "enableDWARF": {"default": true, "markdownDescription": "%base.enableDWARF.label%", "type": "boolean"}, "inspectUri": {"default": null, "description": "%browser.inspectUri.description%", "type": ["string", "null"]}, "outFiles": {"default": ["${workspaceFolder}/**/*.(m|c|)js", "!**/node_modules/**"], "description": "%outFiles.description%", "items": {"type": "string"}, "tags": ["setup"], "type": ["array"]}, "outputCapture": {"default": "console", "enum": ["console", "std"], "markdownDescription": "%node.launch.outputCapture.description%"}, "pathMapping": {"default": {}, "description": "%browser.pathMapping.description%", "type": "object"}, "pauseForSourceMap": {"default": false, "markdownDescription": "%node.pauseForSourceMap.description%", "type": "boolean"}, "perScriptSourcemaps": {"default": "auto", "description": "%browser.perScriptSourcemaps.description%", "enum": ["yes", "no", "auto"], "type": "string"}, "port": {"default": 9229, "description": "%browser.attach.port.description%", "oneOf": [{"type": "integer"}, {"pattern": "^\\${.*}$", "type": "string"}], "tags": ["setup"]}, "resolveSourceMapLocations": {"default": null, "description": "%node.resolveSourceMapLocations.description%", "items": {"type": "string"}, "type": ["array", "null"]}, "restart": {"default": false, "markdownDescription": "%browser.restart%", "type": "boolean"}, "server": {"oneOf": [{"additionalProperties": false, "default": {"program": "node my-server.js"}, "description": "%browser.server.description%", "properties": {"args": {"default": [], "description": "%node.launch.args.description%", "items": {"type": "string"}, "tags": ["setup"], "type": ["array", "string"]}, "attachSimplePort": {"default": 9229, "description": "%node.attachSimplePort.description%", "oneOf": [{"type": "integer"}, {"pattern": "^\\${.*}$", "type": "string"}]}, "autoAttachChildProcesses": {"default": true, "description": "%node.launch.autoAttachChildProcesses.description%", "type": "boolean"}, "cascadeTerminateToConfigurations": {"default": [], "description": "%base.cascadeTerminateToConfigurations.label%", "items": {"type": "string", "uniqueItems": true}, "type": "array"}, "console": {"default": "internalConsole", "description": "%node.launch.console.description%", "enum": ["internalConsole", "integratedTerminal", "externalTerminal"], "enumDescriptions": ["%node.launch.console.internalConsole.description%", "%node.launch.console.integratedTerminal.description%", "%node.launch.console.externalTerminal.description%"], "type": "string"}, "customDescriptionGenerator": {"description": "%customDescriptionGenerator.description%", "type": "string"}, "customPropertiesGenerator": {"deprecated": true, "description": "%customPropertiesGenerator.description%", "type": "string"}, "cwd": {"default": "${workspaceFolder}", "description": "%node.launch.cwd.description%", "tags": ["setup"], "type": "string"}, "enableContentValidation": {"default": true, "description": "%enableContentValidation.description%", "type": "boolean"}, "enableDWARF": {"default": true, "markdownDescription": "%base.enableDWARF.label%", "type": "boolean"}, "env": {"additionalProperties": {"type": ["string", "null"]}, "default": {}, "markdownDescription": "%node.launch.env.description%", "tags": ["setup"], "type": "object"}, "envFile": {"default": "${workspaceFolder}/.env", "description": "%node.launch.envFile.description%", "type": "string"}, "experimentalNetworking": {"default": "auto", "description": "%node.experimentalNetworking.description%", "enum": ["auto", "on", "off"], "type": "string"}, "killBehavior": {"default": "forceful", "enum": ["forceful", "polite", "none"], "markdownDescription": "%node.killBehavior.description%", "type": "string"}, "localRoot": {"default": null, "description": "%node.localRoot.description%", "type": ["string", "null"]}, "nodeVersionHint": {"default": 12, "description": "%node.versionHint.description%", "minimum": 8, "type": "number"}, "outFiles": {"default": ["${workspaceFolder}/**/*.(m|c|)js", "!**/node_modules/**"], "description": "%outFiles.description%", "items": {"type": "string"}, "tags": ["setup"], "type": ["array"]}, "outputCapture": {"default": "console", "enum": ["console", "std"], "markdownDescription": "%node.launch.outputCapture.description%"}, "pauseForSourceMap": {"default": false, "markdownDescription": "%node.pauseForSourceMap.description%", "type": "boolean"}, "profileStartup": {"default": true, "description": "%node.profileStartup.description%", "type": "boolean"}, "program": {"default": "", "description": "%node.launch.program.description%", "tags": ["setup"], "type": "string"}, "remoteRoot": {"default": null, "description": "%node.remoteRoot.description%", "type": ["string", "null"]}, "resolveSourceMapLocations": {"default": ["${workspaceFolder}/**", "!**/node_modules/**"], "description": "%node.resolveSourceMapLocations.description%", "items": {"type": "string"}, "type": ["array", "null"]}, "restart": {"default": true, "description": "%node.attach.restart.description%", "oneOf": [{"type": "boolean"}, {"properties": {"delay": {"default": 1000, "minimum": 0, "type": "number"}, "maxAttempts": {"default": 10, "minimum": 0, "type": "number"}}, "type": "object"}]}, "runtimeArgs": {"default": [], "description": "%node.launch.runtimeArgs.description%", "items": {"type": "string"}, "tags": ["setup"], "type": "array"}, "runtimeExecutable": {"default": "node", "markdownDescription": "%node.launch.runtimeExecutable.description%", "type": ["string", "null"]}, "runtimeSourcemapPausePatterns": {"default": [], "items": {"type": "string"}, "markdownDescription": "%node.launch.runtimeSourcemapPausePatterns%", "type": "array"}, "runtimeVersion": {"default": "default", "markdownDescription": "%node.launch.runtimeVersion.description%", "type": "string"}, "showAsyncStacks": {"default": true, "description": "%node.showAsyncStacks.description%", "oneOf": [{"type": "boolean"}, {"properties": {"onAttach": {"default": 32, "type": "number"}}, "required": ["onAttach"], "type": "object"}, {"properties": {"onceBreakpointResolved": {"default": 32, "type": "number"}}, "required": ["onceBreakpointResolved"], "type": "object"}]}, "skipFiles": {"default": ["${/**"], "description": "%browser.skipFiles.description%", "type": "array"}, "smartStep": {"default": true, "description": "%smartStep.description%", "type": "boolean"}, "sourceMapPathOverrides": {"default": {"meteor://💻app/*": "${workspaceFolder}/*", "webpack:///./~/*": "${workspaceFolder}/node_modules/*", "webpack://?:*/*": "${workspaceFolder}/*"}, "description": "%node.sourceMapPathOverrides.description%", "type": "object"}, "sourceMapRenames": {"default": true, "description": "%browser.sourceMapRenames.description%", "type": "boolean"}, "sourceMaps": {"default": true, "description": "%browser.sourceMaps.description%", "type": "boolean"}, "stopOnEntry": {"default": true, "description": "%node.stopOnEntry.description%", "type": ["boolean", "string"]}, "timeout": {"default": 10000, "description": "%node.timeout.description%", "type": "number"}, "timeouts": {"additionalProperties": false, "default": {}, "description": "%timeouts.generalDescription%", "markdownDescription": "%timeouts.generalDescription.markdown%", "properties": {"hoverEvaluation": {"default": 500, "description": "%timeouts.hoverEvaluation.description%", "type": "number"}, "sourceMapCumulativePause": {"default": 1000, "description": "%timeouts.sourceMaps.sourceMapCumulativePause.description%", "type": "number"}, "sourceMapMinPause": {"default": 1000, "description": "%timeouts.sourceMaps.sourceMapMinPause.description%", "type": "number"}}, "type": "object"}, "trace": {"default": true, "description": "%trace.description%", "oneOf": [{"description": "%trace.boolean.description%", "type": "boolean"}, {"additionalProperties": false, "properties": {"logFile": {"description": "%trace.logFile.description%", "type": ["string", "null"]}, "stdio": {"description": "%trace.stdio.description%", "type": "boolean"}}, "type": "object"}]}}, "type": "object"}, {"additionalProperties": false, "default": {"program": "npm start"}, "description": "%debug.terminal.label%", "properties": {"autoAttachChildProcesses": {"default": true, "description": "%node.launch.autoAttachChildProcesses.description%", "type": "boolean"}, "cascadeTerminateToConfigurations": {"default": [], "description": "%base.cascadeTerminateToConfigurations.label%", "items": {"type": "string", "uniqueItems": true}, "type": "array"}, "command": {"default": "npm start", "description": "%debug.terminal.program.description%", "tags": ["setup"], "type": ["string", "null"]}, "customDescriptionGenerator": {"description": "%customDescriptionGenerator.description%", "type": "string"}, "customPropertiesGenerator": {"deprecated": true, "description": "%customPropertiesGenerator.description%", "type": "string"}, "cwd": {"default": "${workspaceFolder}", "description": "%node.launch.cwd.description%", "docDefault": "localRoot || ${workspaceFolder}", "tags": ["setup"], "type": "string"}, "enableContentValidation": {"default": true, "description": "%enableContentValidation.description%", "type": "boolean"}, "enableDWARF": {"default": true, "markdownDescription": "%base.enableDWARF.label%", "type": "boolean"}, "env": {"additionalProperties": {"type": ["string", "null"]}, "default": {}, "markdownDescription": "%node.launch.env.description%", "tags": ["setup"], "type": "object"}, "envFile": {"default": "${workspaceFolder}/.env", "description": "%node.launch.envFile.description%", "type": "string"}, "localRoot": {"default": null, "description": "%node.localRoot.description%", "type": ["string", "null"]}, "nodeVersionHint": {"default": 12, "description": "%node.versionHint.description%", "minimum": 8, "type": "number"}, "outFiles": {"default": ["${workspaceFolder}/**/*.(m|c|)js", "!**/node_modules/**"], "description": "%outFiles.description%", "items": {"type": "string"}, "tags": ["setup"], "type": ["array"]}, "outputCapture": {"default": "console", "enum": ["console", "std"], "markdownDescription": "%node.launch.outputCapture.description%"}, "pauseForSourceMap": {"default": false, "markdownDescription": "%node.pauseForSourceMap.description%", "type": "boolean"}, "remoteRoot": {"default": null, "description": "%node.remoteRoot.description%", "type": ["string", "null"]}, "resolveSourceMapLocations": {"default": ["${workspaceFolder}/**", "!**/node_modules/**"], "description": "%node.resolveSourceMapLocations.description%", "items": {"type": "string"}, "type": ["array", "null"]}, "runtimeSourcemapPausePatterns": {"default": [], "items": {"type": "string"}, "markdownDescription": "%node.launch.runtimeSourcemapPausePatterns%", "type": "array"}, "showAsyncStacks": {"default": true, "description": "%node.showAsyncStacks.description%", "oneOf": [{"type": "boolean"}, {"properties": {"onAttach": {"default": 32, "type": "number"}}, "required": ["onAttach"], "type": "object"}, {"properties": {"onceBreakpointResolved": {"default": 32, "type": "number"}}, "required": ["onceBreakpointResolved"], "type": "object"}]}, "skipFiles": {"default": ["${/**"], "description": "%browser.skipFiles.description%", "type": "array"}, "smartStep": {"default": true, "description": "%smartStep.description%", "type": "boolean"}, "sourceMapPathOverrides": {"default": {"meteor://💻app/*": "${workspaceFolder}/*", "webpack:///./~/*": "${workspaceFolder}/node_modules/*", "webpack://?:*/*": "${workspaceFolder}/*"}, "description": "%node.sourceMapPathOverrides.description%", "type": "object"}, "sourceMapRenames": {"default": true, "description": "%browser.sourceMapRenames.description%", "type": "boolean"}, "sourceMaps": {"default": true, "description": "%browser.sourceMaps.description%", "type": "boolean"}, "timeout": {"default": 10000, "description": "%node.timeout.description%", "type": "number"}, "timeouts": {"additionalProperties": false, "default": {}, "description": "%timeouts.generalDescription%", "markdownDescription": "%timeouts.generalDescription.markdown%", "properties": {"hoverEvaluation": {"default": 500, "description": "%timeouts.hoverEvaluation.description%", "type": "number"}, "sourceMapCumulativePause": {"default": 1000, "description": "%timeouts.sourceMaps.sourceMapCumulativePause.description%", "type": "number"}, "sourceMapMinPause": {"default": 1000, "description": "%timeouts.sourceMaps.sourceMapMinPause.description%", "type": "number"}}, "type": "object"}, "trace": {"default": true, "description": "%trace.description%", "oneOf": [{"description": "%trace.boolean.description%", "type": "boolean"}, {"additionalProperties": false, "properties": {"logFile": {"description": "%trace.logFile.description%", "type": ["string", "null"]}, "stdio": {"description": "%trace.stdio.description%", "type": "boolean"}}, "type": "object"}]}}, "type": "object"}]}, "showAsyncStacks": {"default": true, "description": "%node.showAsyncStacks.description%", "oneOf": [{"type": "boolean"}, {"properties": {"onAttach": {"default": 32, "type": "number"}}, "required": ["onAttach"], "type": "object"}, {"properties": {"onceBreakpointResolved": {"default": 32, "type": "number"}}, "required": ["onceBreakpointResolved"], "type": "object"}]}, "skipFiles": {"default": ["${/**"], "description": "%browser.skipFiles.description%", "type": "array"}, "smartStep": {"default": true, "description": "%smartStep.description%", "type": "boolean"}, "sourceMapPathOverrides": {"default": {"meteor://💻app/*": "${workspaceFolder}/*", "webpack:///./~/*": "${workspaceFolder}/node_modules/*", "webpack://?:*/*": "${workspaceFolder}/*"}, "description": "%node.sourceMapPathOverrides.description%", "type": "object"}, "sourceMapRenames": {"default": true, "description": "%browser.sourceMapRenames.description%", "type": "boolean"}, "sourceMaps": {"default": true, "description": "%browser.sourceMaps.description%", "type": "boolean"}, "targetSelection": {"default": "automatic", "enum": ["pick", "automatic"], "markdownDescription": "%browser.targetSelection%", "type": "string"}, "timeout": {"default": 10000, "description": "%node.timeout.description%", "type": "number"}, "timeouts": {"additionalProperties": false, "default": {}, "description": "%timeouts.generalDescription%", "markdownDescription": "%timeouts.generalDescription.markdown%", "properties": {"hoverEvaluation": {"default": 500, "description": "%timeouts.hoverEvaluation.description%", "type": "number"}, "sourceMapCumulativePause": {"default": 1000, "description": "%timeouts.sourceMaps.sourceMapCumulativePause.description%", "type": "number"}, "sourceMapMinPause": {"default": 1000, "description": "%timeouts.sourceMaps.sourceMapMinPause.description%", "type": "number"}}, "type": "object"}, "trace": {"default": true, "description": "%trace.description%", "oneOf": [{"description": "%trace.boolean.description%", "type": "boolean"}, {"additionalProperties": false, "properties": {"logFile": {"description": "%trace.logFile.description%", "type": ["string", "null"]}, "stdio": {"description": "%trace.stdio.description%", "type": "boolean"}}, "type": "object"}]}, "url": {"default": "http://localhost:8080", "description": "%browser.url.description%", "tags": ["setup"], "type": "string"}, "urlFilter": {"default": "", "description": "%browser.urlFilter.description%", "type": "string"}, "useWebView": {"default": {"pipeName": "MyPipeName"}, "description": "%edge.useWebView.attach.description%", "properties": {"pipeName": {"type": "string"}}, "type": "object"}, "vueComponentPaths": {"default": ["${workspaceFolder}/**/*.vue"], "description": "%browser.vueComponentPaths%", "type": "array"}, "webRoot": {"default": "${workspaceFolder}", "description": "%browser.webRoot.description%", "tags": ["setup"], "type": "string"}}}, "launch": {"properties": {"address": {"default": "localhost", "description": "%edge.address.description%", "type": "string"}, "browserLaunchLocation": {"default": null, "description": "%browser.browserLaunchLocation.description%", "oneOf": [{"type": "null"}, {"enum": ["ui", "workspace"], "type": "string"}]}, "cascadeTerminateToConfigurations": {"default": [], "description": "%base.cascadeTerminateToConfigurations.label%", "items": {"type": "string", "uniqueItems": true}, "type": "array"}, "cleanUp": {"default": "wholeBrowser", "description": "%browser.cleanUp.description%", "enum": ["wholeBrowser", "onlyTab"], "type": "string"}, "customDescriptionGenerator": {"description": "%customDescriptionGenerator.description%", "type": "string"}, "customPropertiesGenerator": {"deprecated": true, "description": "%customPropertiesGenerator.description%", "type": "string"}, "cwd": {"default": null, "description": "%browser.cwd.description%", "type": "string"}, "disableNetworkCache": {"default": true, "description": "%browser.disableNetworkCache.description%", "type": "boolean"}, "enableContentValidation": {"default": true, "description": "%enableContentValidation.description%", "type": "boolean"}, "enableDWARF": {"default": true, "markdownDescription": "%base.enableDWARF.label%", "type": "boolean"}, "env": {"default": {}, "description": "%browser.env.description%", "type": "object"}, "file": {"default": "${workspaceFolder}/index.html", "description": "%browser.file.description%", "tags": ["setup"], "type": "string"}, "includeDefaultArgs": {"default": true, "description": "%browser.includeDefaultArgs.description%", "type": "boolean"}, "includeLaunchArgs": {"default": true, "description": "%browser.includeLaunchArgs.description%", "type": "boolean"}, "inspectUri": {"default": null, "description": "%browser.inspectUri.description%", "type": ["string", "null"]}, "killBehavior": {"default": "forceful", "enum": ["forceful", "polite", "none"], "markdownDescription": "%browser.killBehavior.description%", "type": "string"}, "outFiles": {"default": ["${workspaceFolder}/**/*.(m|c|)js", "!**/node_modules/**"], "description": "%outFiles.description%", "items": {"type": "string"}, "tags": ["setup"], "type": ["array"]}, "outputCapture": {"default": "console", "enum": ["console", "std"], "markdownDescription": "%node.launch.outputCapture.description%"}, "pathMapping": {"default": {}, "description": "%browser.pathMapping.description%", "type": "object"}, "pauseForSourceMap": {"default": false, "markdownDescription": "%node.pauseForSourceMap.description%", "type": "boolean"}, "perScriptSourcemaps": {"default": "auto", "description": "%browser.perScriptSourcemaps.description%", "enum": ["yes", "no", "auto"], "type": "string"}, "port": {"default": 9229, "description": "%edge.port.description%", "type": "number"}, "profileStartup": {"default": true, "description": "%browser.profileStartup.description%", "type": "boolean"}, "resolveSourceMapLocations": {"default": null, "description": "%node.resolveSourceMapLocations.description%", "items": {"type": "string"}, "type": ["array", "null"]}, "runtimeArgs": {"default": [], "description": "%browser.runtimeArgs.description%", "items": {"type": "string"}, "type": "array"}, "runtimeExecutable": {"default": "stable", "description": "%browser.runtimeExecutable.edge.description%", "type": ["string", "null"]}, "server": {"oneOf": [{"additionalProperties": false, "default": {"program": "node my-server.js"}, "description": "%browser.server.description%", "properties": {"args": {"default": [], "description": "%node.launch.args.description%", "items": {"type": "string"}, "tags": ["setup"], "type": ["array", "string"]}, "attachSimplePort": {"default": 9229, "description": "%node.attachSimplePort.description%", "oneOf": [{"type": "integer"}, {"pattern": "^\\${.*}$", "type": "string"}]}, "autoAttachChildProcesses": {"default": true, "description": "%node.launch.autoAttachChildProcesses.description%", "type": "boolean"}, "cascadeTerminateToConfigurations": {"default": [], "description": "%base.cascadeTerminateToConfigurations.label%", "items": {"type": "string", "uniqueItems": true}, "type": "array"}, "console": {"default": "internalConsole", "description": "%node.launch.console.description%", "enum": ["internalConsole", "integratedTerminal", "externalTerminal"], "enumDescriptions": ["%node.launch.console.internalConsole.description%", "%node.launch.console.integratedTerminal.description%", "%node.launch.console.externalTerminal.description%"], "type": "string"}, "customDescriptionGenerator": {"description": "%customDescriptionGenerator.description%", "type": "string"}, "customPropertiesGenerator": {"deprecated": true, "description": "%customPropertiesGenerator.description%", "type": "string"}, "cwd": {"default": "${workspaceFolder}", "description": "%node.launch.cwd.description%", "tags": ["setup"], "type": "string"}, "enableContentValidation": {"default": true, "description": "%enableContentValidation.description%", "type": "boolean"}, "enableDWARF": {"default": true, "markdownDescription": "%base.enableDWARF.label%", "type": "boolean"}, "env": {"additionalProperties": {"type": ["string", "null"]}, "default": {}, "markdownDescription": "%node.launch.env.description%", "tags": ["setup"], "type": "object"}, "envFile": {"default": "${workspaceFolder}/.env", "description": "%node.launch.envFile.description%", "type": "string"}, "experimentalNetworking": {"default": "auto", "description": "%node.experimentalNetworking.description%", "enum": ["auto", "on", "off"], "type": "string"}, "killBehavior": {"default": "forceful", "enum": ["forceful", "polite", "none"], "markdownDescription": "%node.killBehavior.description%", "type": "string"}, "localRoot": {"default": null, "description": "%node.localRoot.description%", "type": ["string", "null"]}, "nodeVersionHint": {"default": 12, "description": "%node.versionHint.description%", "minimum": 8, "type": "number"}, "outFiles": {"default": ["${workspaceFolder}/**/*.(m|c|)js", "!**/node_modules/**"], "description": "%outFiles.description%", "items": {"type": "string"}, "tags": ["setup"], "type": ["array"]}, "outputCapture": {"default": "console", "enum": ["console", "std"], "markdownDescription": "%node.launch.outputCapture.description%"}, "pauseForSourceMap": {"default": false, "markdownDescription": "%node.pauseForSourceMap.description%", "type": "boolean"}, "profileStartup": {"default": true, "description": "%node.profileStartup.description%", "type": "boolean"}, "program": {"default": "", "description": "%node.launch.program.description%", "tags": ["setup"], "type": "string"}, "remoteRoot": {"default": null, "description": "%node.remoteRoot.description%", "type": ["string", "null"]}, "resolveSourceMapLocations": {"default": ["${workspaceFolder}/**", "!**/node_modules/**"], "description": "%node.resolveSourceMapLocations.description%", "items": {"type": "string"}, "type": ["array", "null"]}, "restart": {"default": true, "description": "%node.attach.restart.description%", "oneOf": [{"type": "boolean"}, {"properties": {"delay": {"default": 1000, "minimum": 0, "type": "number"}, "maxAttempts": {"default": 10, "minimum": 0, "type": "number"}}, "type": "object"}]}, "runtimeArgs": {"default": [], "description": "%node.launch.runtimeArgs.description%", "items": {"type": "string"}, "tags": ["setup"], "type": "array"}, "runtimeExecutable": {"default": "node", "markdownDescription": "%node.launch.runtimeExecutable.description%", "type": ["string", "null"]}, "runtimeSourcemapPausePatterns": {"default": [], "items": {"type": "string"}, "markdownDescription": "%node.launch.runtimeSourcemapPausePatterns%", "type": "array"}, "runtimeVersion": {"default": "default", "markdownDescription": "%node.launch.runtimeVersion.description%", "type": "string"}, "showAsyncStacks": {"default": true, "description": "%node.showAsyncStacks.description%", "oneOf": [{"type": "boolean"}, {"properties": {"onAttach": {"default": 32, "type": "number"}}, "required": ["onAttach"], "type": "object"}, {"properties": {"onceBreakpointResolved": {"default": 32, "type": "number"}}, "required": ["onceBreakpointResolved"], "type": "object"}]}, "skipFiles": {"default": ["${/**"], "description": "%browser.skipFiles.description%", "type": "array"}, "smartStep": {"default": true, "description": "%smartStep.description%", "type": "boolean"}, "sourceMapPathOverrides": {"default": {"meteor://💻app/*": "${workspaceFolder}/*", "webpack:///./~/*": "${workspaceFolder}/node_modules/*", "webpack://?:*/*": "${workspaceFolder}/*"}, "description": "%node.sourceMapPathOverrides.description%", "type": "object"}, "sourceMapRenames": {"default": true, "description": "%browser.sourceMapRenames.description%", "type": "boolean"}, "sourceMaps": {"default": true, "description": "%browser.sourceMaps.description%", "type": "boolean"}, "stopOnEntry": {"default": true, "description": "%node.stopOnEntry.description%", "type": ["boolean", "string"]}, "timeout": {"default": 10000, "description": "%node.timeout.description%", "type": "number"}, "timeouts": {"additionalProperties": false, "default": {}, "description": "%timeouts.generalDescription%", "markdownDescription": "%timeouts.generalDescription.markdown%", "properties": {"hoverEvaluation": {"default": 500, "description": "%timeouts.hoverEvaluation.description%", "type": "number"}, "sourceMapCumulativePause": {"default": 1000, "description": "%timeouts.sourceMaps.sourceMapCumulativePause.description%", "type": "number"}, "sourceMapMinPause": {"default": 1000, "description": "%timeouts.sourceMaps.sourceMapMinPause.description%", "type": "number"}}, "type": "object"}, "trace": {"default": true, "description": "%trace.description%", "oneOf": [{"description": "%trace.boolean.description%", "type": "boolean"}, {"additionalProperties": false, "properties": {"logFile": {"description": "%trace.logFile.description%", "type": ["string", "null"]}, "stdio": {"description": "%trace.stdio.description%", "type": "boolean"}}, "type": "object"}]}}, "type": "object"}, {"additionalProperties": false, "default": {"program": "npm start"}, "description": "%debug.terminal.label%", "properties": {"autoAttachChildProcesses": {"default": true, "description": "%node.launch.autoAttachChildProcesses.description%", "type": "boolean"}, "cascadeTerminateToConfigurations": {"default": [], "description": "%base.cascadeTerminateToConfigurations.label%", "items": {"type": "string", "uniqueItems": true}, "type": "array"}, "command": {"default": "npm start", "description": "%debug.terminal.program.description%", "tags": ["setup"], "type": ["string", "null"]}, "customDescriptionGenerator": {"description": "%customDescriptionGenerator.description%", "type": "string"}, "customPropertiesGenerator": {"deprecated": true, "description": "%customPropertiesGenerator.description%", "type": "string"}, "cwd": {"default": "${workspaceFolder}", "description": "%node.launch.cwd.description%", "docDefault": "localRoot || ${workspaceFolder}", "tags": ["setup"], "type": "string"}, "enableContentValidation": {"default": true, "description": "%enableContentValidation.description%", "type": "boolean"}, "enableDWARF": {"default": true, "markdownDescription": "%base.enableDWARF.label%", "type": "boolean"}, "env": {"additionalProperties": {"type": ["string", "null"]}, "default": {}, "markdownDescription": "%node.launch.env.description%", "tags": ["setup"], "type": "object"}, "envFile": {"default": "${workspaceFolder}/.env", "description": "%node.launch.envFile.description%", "type": "string"}, "localRoot": {"default": null, "description": "%node.localRoot.description%", "type": ["string", "null"]}, "nodeVersionHint": {"default": 12, "description": "%node.versionHint.description%", "minimum": 8, "type": "number"}, "outFiles": {"default": ["${workspaceFolder}/**/*.(m|c|)js", "!**/node_modules/**"], "description": "%outFiles.description%", "items": {"type": "string"}, "tags": ["setup"], "type": ["array"]}, "outputCapture": {"default": "console", "enum": ["console", "std"], "markdownDescription": "%node.launch.outputCapture.description%"}, "pauseForSourceMap": {"default": false, "markdownDescription": "%node.pauseForSourceMap.description%", "type": "boolean"}, "remoteRoot": {"default": null, "description": "%node.remoteRoot.description%", "type": ["string", "null"]}, "resolveSourceMapLocations": {"default": ["${workspaceFolder}/**", "!**/node_modules/**"], "description": "%node.resolveSourceMapLocations.description%", "items": {"type": "string"}, "type": ["array", "null"]}, "runtimeSourcemapPausePatterns": {"default": [], "items": {"type": "string"}, "markdownDescription": "%node.launch.runtimeSourcemapPausePatterns%", "type": "array"}, "showAsyncStacks": {"default": true, "description": "%node.showAsyncStacks.description%", "oneOf": [{"type": "boolean"}, {"properties": {"onAttach": {"default": 32, "type": "number"}}, "required": ["onAttach"], "type": "object"}, {"properties": {"onceBreakpointResolved": {"default": 32, "type": "number"}}, "required": ["onceBreakpointResolved"], "type": "object"}]}, "skipFiles": {"default": ["${/**"], "description": "%browser.skipFiles.description%", "type": "array"}, "smartStep": {"default": true, "description": "%smartStep.description%", "type": "boolean"}, "sourceMapPathOverrides": {"default": {"meteor://💻app/*": "${workspaceFolder}/*", "webpack:///./~/*": "${workspaceFolder}/node_modules/*", "webpack://?:*/*": "${workspaceFolder}/*"}, "description": "%node.sourceMapPathOverrides.description%", "type": "object"}, "sourceMapRenames": {"default": true, "description": "%browser.sourceMapRenames.description%", "type": "boolean"}, "sourceMaps": {"default": true, "description": "%browser.sourceMaps.description%", "type": "boolean"}, "timeout": {"default": 10000, "description": "%node.timeout.description%", "type": "number"}, "timeouts": {"additionalProperties": false, "default": {}, "description": "%timeouts.generalDescription%", "markdownDescription": "%timeouts.generalDescription.markdown%", "properties": {"hoverEvaluation": {"default": 500, "description": "%timeouts.hoverEvaluation.description%", "type": "number"}, "sourceMapCumulativePause": {"default": 1000, "description": "%timeouts.sourceMaps.sourceMapCumulativePause.description%", "type": "number"}, "sourceMapMinPause": {"default": 1000, "description": "%timeouts.sourceMaps.sourceMapMinPause.description%", "type": "number"}}, "type": "object"}, "trace": {"default": true, "description": "%trace.description%", "oneOf": [{"description": "%trace.boolean.description%", "type": "boolean"}, {"additionalProperties": false, "properties": {"logFile": {"description": "%trace.logFile.description%", "type": ["string", "null"]}, "stdio": {"description": "%trace.stdio.description%", "type": "boolean"}}, "type": "object"}]}}, "type": "object"}]}, "showAsyncStacks": {"default": true, "description": "%node.showAsyncStacks.description%", "oneOf": [{"type": "boolean"}, {"properties": {"onAttach": {"default": 32, "type": "number"}}, "required": ["onAttach"], "type": "object"}, {"properties": {"onceBreakpointResolved": {"default": 32, "type": "number"}}, "required": ["onceBreakpointResolved"], "type": "object"}]}, "skipFiles": {"default": ["${/**"], "description": "%browser.skipFiles.description%", "type": "array"}, "smartStep": {"default": true, "description": "%smartStep.description%", "type": "boolean"}, "sourceMapPathOverrides": {"default": {"meteor://💻app/*": "${workspaceFolder}/*", "webpack:///./~/*": "${workspaceFolder}/node_modules/*", "webpack://?:*/*": "${workspaceFolder}/*"}, "description": "%node.sourceMapPathOverrides.description%", "type": "object"}, "sourceMapRenames": {"default": true, "description": "%browser.sourceMapRenames.description%", "type": "boolean"}, "sourceMaps": {"default": true, "description": "%browser.sourceMaps.description%", "type": "boolean"}, "timeout": {"default": 10000, "description": "%node.timeout.description%", "type": "number"}, "timeouts": {"additionalProperties": false, "default": {}, "description": "%timeouts.generalDescription%", "markdownDescription": "%timeouts.generalDescription.markdown%", "properties": {"hoverEvaluation": {"default": 500, "description": "%timeouts.hoverEvaluation.description%", "type": "number"}, "sourceMapCumulativePause": {"default": 1000, "description": "%timeouts.sourceMaps.sourceMapCumulativePause.description%", "type": "number"}, "sourceMapMinPause": {"default": 1000, "description": "%timeouts.sourceMaps.sourceMapMinPause.description%", "type": "number"}}, "type": "object"}, "trace": {"default": true, "description": "%trace.description%", "oneOf": [{"description": "%trace.boolean.description%", "type": "boolean"}, {"additionalProperties": false, "properties": {"logFile": {"description": "%trace.logFile.description%", "type": ["string", "null"]}, "stdio": {"description": "%trace.stdio.description%", "type": "boolean"}}, "type": "object"}]}, "url": {"default": "http://localhost:8080", "description": "%browser.url.description%", "tags": ["setup"], "type": "string"}, "urlFilter": {"default": "", "description": "%browser.urlFilter.description%", "type": "string"}, "useWebView": {"default": false, "description": "%edge.useWebView.launch.description%", "type": "boolean"}, "userDataDir": {"default": true, "description": "%browser.userDataDir.description%", "type": ["string", "boolean"]}, "vueComponentPaths": {"default": ["${workspaceFolder}/**/*.vue"], "description": "%browser.vueComponentPaths%", "type": "array"}, "webRoot": {"default": "${workspaceFolder}", "description": "%browser.webRoot.description%", "tags": ["setup"], "type": "string"}}}}, "configurationSnippets": [], "deprecated": "Please use type msedge instead", "label": "%edge.label%", "languages": ["javascript", "typescript", "javascriptreact", "typescriptreact", "html", "css", "coffeescript", "handlebars", "vue"], "strings": {"unverifiedBreakpoints": "%debug.unverifiedBreakpoints%"}, "type": "pwa-msedge"}, {"aiKey": "0c6ae279ed8443289764825290e4f9e2-1a736e7c-1324-4338-be46-fc2a58ae4d14-7255", "configurationAttributes": {"attach": {"properties": {"address": {"default": "localhost", "description": "%browser.address.description%", "type": "string"}, "browserAttachLocation": {"default": null, "description": "%browser.browserAttachLocation.description%", "oneOf": [{"type": "null"}, {"enum": ["ui", "workspace"], "type": "string"}]}, "cascadeTerminateToConfigurations": {"default": [], "description": "%base.cascadeTerminateToConfigurations.label%", "items": {"type": "string", "uniqueItems": true}, "type": "array"}, "customDescriptionGenerator": {"description": "%customDescriptionGenerator.description%", "type": "string"}, "customPropertiesGenerator": {"deprecated": true, "description": "%customPropertiesGenerator.description%", "type": "string"}, "disableNetworkCache": {"default": true, "description": "%browser.disableNetworkCache.description%", "type": "boolean"}, "enableContentValidation": {"default": true, "description": "%enableContentValidation.description%", "type": "boolean"}, "enableDWARF": {"default": true, "markdownDescription": "%base.enableDWARF.label%", "type": "boolean"}, "inspectUri": {"default": null, "description": "%browser.inspectUri.description%", "type": ["string", "null"]}, "outFiles": {"default": ["${workspaceFolder}/**/*.(m|c|)js", "!**/node_modules/**"], "description": "%outFiles.description%", "items": {"type": "string"}, "tags": ["setup"], "type": ["array"]}, "outputCapture": {"default": "console", "enum": ["console", "std"], "markdownDescription": "%node.launch.outputCapture.description%"}, "pathMapping": {"default": {}, "description": "%browser.pathMapping.description%", "type": "object"}, "pauseForSourceMap": {"default": false, "markdownDescription": "%node.pauseForSourceMap.description%", "type": "boolean"}, "perScriptSourcemaps": {"default": "auto", "description": "%browser.perScriptSourcemaps.description%", "enum": ["yes", "no", "auto"], "type": "string"}, "port": {"default": 9229, "description": "%browser.attach.port.description%", "oneOf": [{"type": "integer"}, {"pattern": "^\\${.*}$", "type": "string"}], "tags": ["setup"]}, "resolveSourceMapLocations": {"default": null, "description": "%node.resolveSourceMapLocations.description%", "items": {"type": "string"}, "type": ["array", "null"]}, "restart": {"default": false, "markdownDescription": "%browser.restart%", "type": "boolean"}, "server": {"oneOf": [{"additionalProperties": false, "default": {"program": "node my-server.js"}, "description": "%browser.server.description%", "properties": {"args": {"default": [], "description": "%node.launch.args.description%", "items": {"type": "string"}, "tags": ["setup"], "type": ["array", "string"]}, "attachSimplePort": {"default": 9229, "description": "%node.attachSimplePort.description%", "oneOf": [{"type": "integer"}, {"pattern": "^\\${.*}$", "type": "string"}]}, "autoAttachChildProcesses": {"default": true, "description": "%node.launch.autoAttachChildProcesses.description%", "type": "boolean"}, "cascadeTerminateToConfigurations": {"default": [], "description": "%base.cascadeTerminateToConfigurations.label%", "items": {"type": "string", "uniqueItems": true}, "type": "array"}, "console": {"default": "internalConsole", "description": "%node.launch.console.description%", "enum": ["internalConsole", "integratedTerminal", "externalTerminal"], "enumDescriptions": ["%node.launch.console.internalConsole.description%", "%node.launch.console.integratedTerminal.description%", "%node.launch.console.externalTerminal.description%"], "type": "string"}, "customDescriptionGenerator": {"description": "%customDescriptionGenerator.description%", "type": "string"}, "customPropertiesGenerator": {"deprecated": true, "description": "%customPropertiesGenerator.description%", "type": "string"}, "cwd": {"default": "${workspaceFolder}", "description": "%node.launch.cwd.description%", "tags": ["setup"], "type": "string"}, "enableContentValidation": {"default": true, "description": "%enableContentValidation.description%", "type": "boolean"}, "enableDWARF": {"default": true, "markdownDescription": "%base.enableDWARF.label%", "type": "boolean"}, "env": {"additionalProperties": {"type": ["string", "null"]}, "default": {}, "markdownDescription": "%node.launch.env.description%", "tags": ["setup"], "type": "object"}, "envFile": {"default": "${workspaceFolder}/.env", "description": "%node.launch.envFile.description%", "type": "string"}, "experimentalNetworking": {"default": "auto", "description": "%node.experimentalNetworking.description%", "enum": ["auto", "on", "off"], "type": "string"}, "killBehavior": {"default": "forceful", "enum": ["forceful", "polite", "none"], "markdownDescription": "%node.killBehavior.description%", "type": "string"}, "localRoot": {"default": null, "description": "%node.localRoot.description%", "type": ["string", "null"]}, "nodeVersionHint": {"default": 12, "description": "%node.versionHint.description%", "minimum": 8, "type": "number"}, "outFiles": {"default": ["${workspaceFolder}/**/*.(m|c|)js", "!**/node_modules/**"], "description": "%outFiles.description%", "items": {"type": "string"}, "tags": ["setup"], "type": ["array"]}, "outputCapture": {"default": "console", "enum": ["console", "std"], "markdownDescription": "%node.launch.outputCapture.description%"}, "pauseForSourceMap": {"default": false, "markdownDescription": "%node.pauseForSourceMap.description%", "type": "boolean"}, "profileStartup": {"default": true, "description": "%node.profileStartup.description%", "type": "boolean"}, "program": {"default": "", "description": "%node.launch.program.description%", "tags": ["setup"], "type": "string"}, "remoteRoot": {"default": null, "description": "%node.remoteRoot.description%", "type": ["string", "null"]}, "resolveSourceMapLocations": {"default": ["${workspaceFolder}/**", "!**/node_modules/**"], "description": "%node.resolveSourceMapLocations.description%", "items": {"type": "string"}, "type": ["array", "null"]}, "restart": {"default": true, "description": "%node.attach.restart.description%", "oneOf": [{"type": "boolean"}, {"properties": {"delay": {"default": 1000, "minimum": 0, "type": "number"}, "maxAttempts": {"default": 10, "minimum": 0, "type": "number"}}, "type": "object"}]}, "runtimeArgs": {"default": [], "description": "%node.launch.runtimeArgs.description%", "items": {"type": "string"}, "tags": ["setup"], "type": "array"}, "runtimeExecutable": {"default": "node", "markdownDescription": "%node.launch.runtimeExecutable.description%", "type": ["string", "null"]}, "runtimeSourcemapPausePatterns": {"default": [], "items": {"type": "string"}, "markdownDescription": "%node.launch.runtimeSourcemapPausePatterns%", "type": "array"}, "runtimeVersion": {"default": "default", "markdownDescription": "%node.launch.runtimeVersion.description%", "type": "string"}, "showAsyncStacks": {"default": true, "description": "%node.showAsyncStacks.description%", "oneOf": [{"type": "boolean"}, {"properties": {"onAttach": {"default": 32, "type": "number"}}, "required": ["onAttach"], "type": "object"}, {"properties": {"onceBreakpointResolved": {"default": 32, "type": "number"}}, "required": ["onceBreakpointResolved"], "type": "object"}]}, "skipFiles": {"default": ["${/**"], "description": "%browser.skipFiles.description%", "type": "array"}, "smartStep": {"default": true, "description": "%smartStep.description%", "type": "boolean"}, "sourceMapPathOverrides": {"default": {"meteor://💻app/*": "${workspaceFolder}/*", "webpack:///./~/*": "${workspaceFolder}/node_modules/*", "webpack://?:*/*": "${workspaceFolder}/*"}, "description": "%node.sourceMapPathOverrides.description%", "type": "object"}, "sourceMapRenames": {"default": true, "description": "%browser.sourceMapRenames.description%", "type": "boolean"}, "sourceMaps": {"default": true, "description": "%browser.sourceMaps.description%", "type": "boolean"}, "stopOnEntry": {"default": true, "description": "%node.stopOnEntry.description%", "type": ["boolean", "string"]}, "timeout": {"default": 10000, "description": "%node.timeout.description%", "type": "number"}, "timeouts": {"additionalProperties": false, "default": {}, "description": "%timeouts.generalDescription%", "markdownDescription": "%timeouts.generalDescription.markdown%", "properties": {"hoverEvaluation": {"default": 500, "description": "%timeouts.hoverEvaluation.description%", "type": "number"}, "sourceMapCumulativePause": {"default": 1000, "description": "%timeouts.sourceMaps.sourceMapCumulativePause.description%", "type": "number"}, "sourceMapMinPause": {"default": 1000, "description": "%timeouts.sourceMaps.sourceMapMinPause.description%", "type": "number"}}, "type": "object"}, "trace": {"default": true, "description": "%trace.description%", "oneOf": [{"description": "%trace.boolean.description%", "type": "boolean"}, {"additionalProperties": false, "properties": {"logFile": {"description": "%trace.logFile.description%", "type": ["string", "null"]}, "stdio": {"description": "%trace.stdio.description%", "type": "boolean"}}, "type": "object"}]}}, "type": "object"}, {"additionalProperties": false, "default": {"program": "npm start"}, "description": "%debug.terminal.label%", "properties": {"autoAttachChildProcesses": {"default": true, "description": "%node.launch.autoAttachChildProcesses.description%", "type": "boolean"}, "cascadeTerminateToConfigurations": {"default": [], "description": "%base.cascadeTerminateToConfigurations.label%", "items": {"type": "string", "uniqueItems": true}, "type": "array"}, "command": {"default": "npm start", "description": "%debug.terminal.program.description%", "tags": ["setup"], "type": ["string", "null"]}, "customDescriptionGenerator": {"description": "%customDescriptionGenerator.description%", "type": "string"}, "customPropertiesGenerator": {"deprecated": true, "description": "%customPropertiesGenerator.description%", "type": "string"}, "cwd": {"default": "${workspaceFolder}", "description": "%node.launch.cwd.description%", "docDefault": "localRoot || ${workspaceFolder}", "tags": ["setup"], "type": "string"}, "enableContentValidation": {"default": true, "description": "%enableContentValidation.description%", "type": "boolean"}, "enableDWARF": {"default": true, "markdownDescription": "%base.enableDWARF.label%", "type": "boolean"}, "env": {"additionalProperties": {"type": ["string", "null"]}, "default": {}, "markdownDescription": "%node.launch.env.description%", "tags": ["setup"], "type": "object"}, "envFile": {"default": "${workspaceFolder}/.env", "description": "%node.launch.envFile.description%", "type": "string"}, "localRoot": {"default": null, "description": "%node.localRoot.description%", "type": ["string", "null"]}, "nodeVersionHint": {"default": 12, "description": "%node.versionHint.description%", "minimum": 8, "type": "number"}, "outFiles": {"default": ["${workspaceFolder}/**/*.(m|c|)js", "!**/node_modules/**"], "description": "%outFiles.description%", "items": {"type": "string"}, "tags": ["setup"], "type": ["array"]}, "outputCapture": {"default": "console", "enum": ["console", "std"], "markdownDescription": "%node.launch.outputCapture.description%"}, "pauseForSourceMap": {"default": false, "markdownDescription": "%node.pauseForSourceMap.description%", "type": "boolean"}, "remoteRoot": {"default": null, "description": "%node.remoteRoot.description%", "type": ["string", "null"]}, "resolveSourceMapLocations": {"default": ["${workspaceFolder}/**", "!**/node_modules/**"], "description": "%node.resolveSourceMapLocations.description%", "items": {"type": "string"}, "type": ["array", "null"]}, "runtimeSourcemapPausePatterns": {"default": [], "items": {"type": "string"}, "markdownDescription": "%node.launch.runtimeSourcemapPausePatterns%", "type": "array"}, "showAsyncStacks": {"default": true, "description": "%node.showAsyncStacks.description%", "oneOf": [{"type": "boolean"}, {"properties": {"onAttach": {"default": 32, "type": "number"}}, "required": ["onAttach"], "type": "object"}, {"properties": {"onceBreakpointResolved": {"default": 32, "type": "number"}}, "required": ["onceBreakpointResolved"], "type": "object"}]}, "skipFiles": {"default": ["${/**"], "description": "%browser.skipFiles.description%", "type": "array"}, "smartStep": {"default": true, "description": "%smartStep.description%", "type": "boolean"}, "sourceMapPathOverrides": {"default": {"meteor://💻app/*": "${workspaceFolder}/*", "webpack:///./~/*": "${workspaceFolder}/node_modules/*", "webpack://?:*/*": "${workspaceFolder}/*"}, "description": "%node.sourceMapPathOverrides.description%", "type": "object"}, "sourceMapRenames": {"default": true, "description": "%browser.sourceMapRenames.description%", "type": "boolean"}, "sourceMaps": {"default": true, "description": "%browser.sourceMaps.description%", "type": "boolean"}, "timeout": {"default": 10000, "description": "%node.timeout.description%", "type": "number"}, "timeouts": {"additionalProperties": false, "default": {}, "description": "%timeouts.generalDescription%", "markdownDescription": "%timeouts.generalDescription.markdown%", "properties": {"hoverEvaluation": {"default": 500, "description": "%timeouts.hoverEvaluation.description%", "type": "number"}, "sourceMapCumulativePause": {"default": 1000, "description": "%timeouts.sourceMaps.sourceMapCumulativePause.description%", "type": "number"}, "sourceMapMinPause": {"default": 1000, "description": "%timeouts.sourceMaps.sourceMapMinPause.description%", "type": "number"}}, "type": "object"}, "trace": {"default": true, "description": "%trace.description%", "oneOf": [{"description": "%trace.boolean.description%", "type": "boolean"}, {"additionalProperties": false, "properties": {"logFile": {"description": "%trace.logFile.description%", "type": ["string", "null"]}, "stdio": {"description": "%trace.stdio.description%", "type": "boolean"}}, "type": "object"}]}}, "type": "object"}]}, "showAsyncStacks": {"default": true, "description": "%node.showAsyncStacks.description%", "oneOf": [{"type": "boolean"}, {"properties": {"onAttach": {"default": 32, "type": "number"}}, "required": ["onAttach"], "type": "object"}, {"properties": {"onceBreakpointResolved": {"default": 32, "type": "number"}}, "required": ["onceBreakpointResolved"], "type": "object"}]}, "skipFiles": {"default": ["${/**"], "description": "%browser.skipFiles.description%", "type": "array"}, "smartStep": {"default": true, "description": "%smartStep.description%", "type": "boolean"}, "sourceMapPathOverrides": {"default": {"meteor://💻app/*": "${workspaceFolder}/*", "webpack:///./~/*": "${workspaceFolder}/node_modules/*", "webpack://?:*/*": "${workspaceFolder}/*"}, "description": "%node.sourceMapPathOverrides.description%", "type": "object"}, "sourceMapRenames": {"default": true, "description": "%browser.sourceMapRenames.description%", "type": "boolean"}, "sourceMaps": {"default": true, "description": "%browser.sourceMaps.description%", "type": "boolean"}, "targetSelection": {"default": "automatic", "enum": ["pick", "automatic"], "markdownDescription": "%browser.targetSelection%", "type": "string"}, "timeout": {"default": 10000, "description": "%node.timeout.description%", "type": "number"}, "timeouts": {"additionalProperties": false, "default": {}, "description": "%timeouts.generalDescription%", "markdownDescription": "%timeouts.generalDescription.markdown%", "properties": {"hoverEvaluation": {"default": 500, "description": "%timeouts.hoverEvaluation.description%", "type": "number"}, "sourceMapCumulativePause": {"default": 1000, "description": "%timeouts.sourceMaps.sourceMapCumulativePause.description%", "type": "number"}, "sourceMapMinPause": {"default": 1000, "description": "%timeouts.sourceMaps.sourceMapMinPause.description%", "type": "number"}}, "type": "object"}, "trace": {"default": true, "description": "%trace.description%", "oneOf": [{"description": "%trace.boolean.description%", "type": "boolean"}, {"additionalProperties": false, "properties": {"logFile": {"description": "%trace.logFile.description%", "type": ["string", "null"]}, "stdio": {"description": "%trace.stdio.description%", "type": "boolean"}}, "type": "object"}]}, "url": {"default": "http://localhost:8080", "description": "%browser.url.description%", "tags": ["setup"], "type": "string"}, "urlFilter": {"default": "", "description": "%browser.urlFilter.description%", "type": "string"}, "useWebView": {"default": {"pipeName": "MyPipeName"}, "description": "%edge.useWebView.attach.description%", "properties": {"pipeName": {"type": "string"}}, "type": "object"}, "vueComponentPaths": {"default": ["${workspaceFolder}/**/*.vue"], "description": "%browser.vueComponentPaths%", "type": "array"}, "webRoot": {"default": "${workspaceFolder}", "description": "%browser.webRoot.description%", "tags": ["setup"], "type": "string"}}}, "launch": {"properties": {"address": {"default": "localhost", "description": "%edge.address.description%", "type": "string"}, "browserLaunchLocation": {"default": null, "description": "%browser.browserLaunchLocation.description%", "oneOf": [{"type": "null"}, {"enum": ["ui", "workspace"], "type": "string"}]}, "cascadeTerminateToConfigurations": {"default": [], "description": "%base.cascadeTerminateToConfigurations.label%", "items": {"type": "string", "uniqueItems": true}, "type": "array"}, "cleanUp": {"default": "wholeBrowser", "description": "%browser.cleanUp.description%", "enum": ["wholeBrowser", "onlyTab"], "type": "string"}, "customDescriptionGenerator": {"description": "%customDescriptionGenerator.description%", "type": "string"}, "customPropertiesGenerator": {"deprecated": true, "description": "%customPropertiesGenerator.description%", "type": "string"}, "cwd": {"default": null, "description": "%browser.cwd.description%", "type": "string"}, "disableNetworkCache": {"default": true, "description": "%browser.disableNetworkCache.description%", "type": "boolean"}, "enableContentValidation": {"default": true, "description": "%enableContentValidation.description%", "type": "boolean"}, "enableDWARF": {"default": true, "markdownDescription": "%base.enableDWARF.label%", "type": "boolean"}, "env": {"default": {}, "description": "%browser.env.description%", "type": "object"}, "file": {"default": "${workspaceFolder}/index.html", "description": "%browser.file.description%", "tags": ["setup"], "type": "string"}, "includeDefaultArgs": {"default": true, "description": "%browser.includeDefaultArgs.description%", "type": "boolean"}, "includeLaunchArgs": {"default": true, "description": "%browser.includeLaunchArgs.description%", "type": "boolean"}, "inspectUri": {"default": null, "description": "%browser.inspectUri.description%", "type": ["string", "null"]}, "killBehavior": {"default": "forceful", "enum": ["forceful", "polite", "none"], "markdownDescription": "%browser.killBehavior.description%", "type": "string"}, "outFiles": {"default": ["${workspaceFolder}/**/*.(m|c|)js", "!**/node_modules/**"], "description": "%outFiles.description%", "items": {"type": "string"}, "tags": ["setup"], "type": ["array"]}, "outputCapture": {"default": "console", "enum": ["console", "std"], "markdownDescription": "%node.launch.outputCapture.description%"}, "pathMapping": {"default": {}, "description": "%browser.pathMapping.description%", "type": "object"}, "pauseForSourceMap": {"default": false, "markdownDescription": "%node.pauseForSourceMap.description%", "type": "boolean"}, "perScriptSourcemaps": {"default": "auto", "description": "%browser.perScriptSourcemaps.description%", "enum": ["yes", "no", "auto"], "type": "string"}, "port": {"default": 9229, "description": "%edge.port.description%", "type": "number"}, "profileStartup": {"default": true, "description": "%browser.profileStartup.description%", "type": "boolean"}, "resolveSourceMapLocations": {"default": null, "description": "%node.resolveSourceMapLocations.description%", "items": {"type": "string"}, "type": ["array", "null"]}, "runtimeArgs": {"default": [], "description": "%browser.runtimeArgs.description%", "items": {"type": "string"}, "type": "array"}, "runtimeExecutable": {"default": "stable", "description": "%browser.runtimeExecutable.edge.description%", "type": ["string", "null"]}, "server": {"oneOf": [{"additionalProperties": false, "default": {"program": "node my-server.js"}, "description": "%browser.server.description%", "properties": {"args": {"default": [], "description": "%node.launch.args.description%", "items": {"type": "string"}, "tags": ["setup"], "type": ["array", "string"]}, "attachSimplePort": {"default": 9229, "description": "%node.attachSimplePort.description%", "oneOf": [{"type": "integer"}, {"pattern": "^\\${.*}$", "type": "string"}]}, "autoAttachChildProcesses": {"default": true, "description": "%node.launch.autoAttachChildProcesses.description%", "type": "boolean"}, "cascadeTerminateToConfigurations": {"default": [], "description": "%base.cascadeTerminateToConfigurations.label%", "items": {"type": "string", "uniqueItems": true}, "type": "array"}, "console": {"default": "internalConsole", "description": "%node.launch.console.description%", "enum": ["internalConsole", "integratedTerminal", "externalTerminal"], "enumDescriptions": ["%node.launch.console.internalConsole.description%", "%node.launch.console.integratedTerminal.description%", "%node.launch.console.externalTerminal.description%"], "type": "string"}, "customDescriptionGenerator": {"description": "%customDescriptionGenerator.description%", "type": "string"}, "customPropertiesGenerator": {"deprecated": true, "description": "%customPropertiesGenerator.description%", "type": "string"}, "cwd": {"default": "${workspaceFolder}", "description": "%node.launch.cwd.description%", "tags": ["setup"], "type": "string"}, "enableContentValidation": {"default": true, "description": "%enableContentValidation.description%", "type": "boolean"}, "enableDWARF": {"default": true, "markdownDescription": "%base.enableDWARF.label%", "type": "boolean"}, "env": {"additionalProperties": {"type": ["string", "null"]}, "default": {}, "markdownDescription": "%node.launch.env.description%", "tags": ["setup"], "type": "object"}, "envFile": {"default": "${workspaceFolder}/.env", "description": "%node.launch.envFile.description%", "type": "string"}, "experimentalNetworking": {"default": "auto", "description": "%node.experimentalNetworking.description%", "enum": ["auto", "on", "off"], "type": "string"}, "killBehavior": {"default": "forceful", "enum": ["forceful", "polite", "none"], "markdownDescription": "%node.killBehavior.description%", "type": "string"}, "localRoot": {"default": null, "description": "%node.localRoot.description%", "type": ["string", "null"]}, "nodeVersionHint": {"default": 12, "description": "%node.versionHint.description%", "minimum": 8, "type": "number"}, "outFiles": {"default": ["${workspaceFolder}/**/*.(m|c|)js", "!**/node_modules/**"], "description": "%outFiles.description%", "items": {"type": "string"}, "tags": ["setup"], "type": ["array"]}, "outputCapture": {"default": "console", "enum": ["console", "std"], "markdownDescription": "%node.launch.outputCapture.description%"}, "pauseForSourceMap": {"default": false, "markdownDescription": "%node.pauseForSourceMap.description%", "type": "boolean"}, "profileStartup": {"default": true, "description": "%node.profileStartup.description%", "type": "boolean"}, "program": {"default": "", "description": "%node.launch.program.description%", "tags": ["setup"], "type": "string"}, "remoteRoot": {"default": null, "description": "%node.remoteRoot.description%", "type": ["string", "null"]}, "resolveSourceMapLocations": {"default": ["${workspaceFolder}/**", "!**/node_modules/**"], "description": "%node.resolveSourceMapLocations.description%", "items": {"type": "string"}, "type": ["array", "null"]}, "restart": {"default": true, "description": "%node.attach.restart.description%", "oneOf": [{"type": "boolean"}, {"properties": {"delay": {"default": 1000, "minimum": 0, "type": "number"}, "maxAttempts": {"default": 10, "minimum": 0, "type": "number"}}, "type": "object"}]}, "runtimeArgs": {"default": [], "description": "%node.launch.runtimeArgs.description%", "items": {"type": "string"}, "tags": ["setup"], "type": "array"}, "runtimeExecutable": {"default": "node", "markdownDescription": "%node.launch.runtimeExecutable.description%", "type": ["string", "null"]}, "runtimeSourcemapPausePatterns": {"default": [], "items": {"type": "string"}, "markdownDescription": "%node.launch.runtimeSourcemapPausePatterns%", "type": "array"}, "runtimeVersion": {"default": "default", "markdownDescription": "%node.launch.runtimeVersion.description%", "type": "string"}, "showAsyncStacks": {"default": true, "description": "%node.showAsyncStacks.description%", "oneOf": [{"type": "boolean"}, {"properties": {"onAttach": {"default": 32, "type": "number"}}, "required": ["onAttach"], "type": "object"}, {"properties": {"onceBreakpointResolved": {"default": 32, "type": "number"}}, "required": ["onceBreakpointResolved"], "type": "object"}]}, "skipFiles": {"default": ["${/**"], "description": "%browser.skipFiles.description%", "type": "array"}, "smartStep": {"default": true, "description": "%smartStep.description%", "type": "boolean"}, "sourceMapPathOverrides": {"default": {"meteor://💻app/*": "${workspaceFolder}/*", "webpack:///./~/*": "${workspaceFolder}/node_modules/*", "webpack://?:*/*": "${workspaceFolder}/*"}, "description": "%node.sourceMapPathOverrides.description%", "type": "object"}, "sourceMapRenames": {"default": true, "description": "%browser.sourceMapRenames.description%", "type": "boolean"}, "sourceMaps": {"default": true, "description": "%browser.sourceMaps.description%", "type": "boolean"}, "stopOnEntry": {"default": true, "description": "%node.stopOnEntry.description%", "type": ["boolean", "string"]}, "timeout": {"default": 10000, "description": "%node.timeout.description%", "type": "number"}, "timeouts": {"additionalProperties": false, "default": {}, "description": "%timeouts.generalDescription%", "markdownDescription": "%timeouts.generalDescription.markdown%", "properties": {"hoverEvaluation": {"default": 500, "description": "%timeouts.hoverEvaluation.description%", "type": "number"}, "sourceMapCumulativePause": {"default": 1000, "description": "%timeouts.sourceMaps.sourceMapCumulativePause.description%", "type": "number"}, "sourceMapMinPause": {"default": 1000, "description": "%timeouts.sourceMaps.sourceMapMinPause.description%", "type": "number"}}, "type": "object"}, "trace": {"default": true, "description": "%trace.description%", "oneOf": [{"description": "%trace.boolean.description%", "type": "boolean"}, {"additionalProperties": false, "properties": {"logFile": {"description": "%trace.logFile.description%", "type": ["string", "null"]}, "stdio": {"description": "%trace.stdio.description%", "type": "boolean"}}, "type": "object"}]}}, "type": "object"}, {"additionalProperties": false, "default": {"program": "npm start"}, "description": "%debug.terminal.label%", "properties": {"autoAttachChildProcesses": {"default": true, "description": "%node.launch.autoAttachChildProcesses.description%", "type": "boolean"}, "cascadeTerminateToConfigurations": {"default": [], "description": "%base.cascadeTerminateToConfigurations.label%", "items": {"type": "string", "uniqueItems": true}, "type": "array"}, "command": {"default": "npm start", "description": "%debug.terminal.program.description%", "tags": ["setup"], "type": ["string", "null"]}, "customDescriptionGenerator": {"description": "%customDescriptionGenerator.description%", "type": "string"}, "customPropertiesGenerator": {"deprecated": true, "description": "%customPropertiesGenerator.description%", "type": "string"}, "cwd": {"default": "${workspaceFolder}", "description": "%node.launch.cwd.description%", "docDefault": "localRoot || ${workspaceFolder}", "tags": ["setup"], "type": "string"}, "enableContentValidation": {"default": true, "description": "%enableContentValidation.description%", "type": "boolean"}, "enableDWARF": {"default": true, "markdownDescription": "%base.enableDWARF.label%", "type": "boolean"}, "env": {"additionalProperties": {"type": ["string", "null"]}, "default": {}, "markdownDescription": "%node.launch.env.description%", "tags": ["setup"], "type": "object"}, "envFile": {"default": "${workspaceFolder}/.env", "description": "%node.launch.envFile.description%", "type": "string"}, "localRoot": {"default": null, "description": "%node.localRoot.description%", "type": ["string", "null"]}, "nodeVersionHint": {"default": 12, "description": "%node.versionHint.description%", "minimum": 8, "type": "number"}, "outFiles": {"default": ["${workspaceFolder}/**/*.(m|c|)js", "!**/node_modules/**"], "description": "%outFiles.description%", "items": {"type": "string"}, "tags": ["setup"], "type": ["array"]}, "outputCapture": {"default": "console", "enum": ["console", "std"], "markdownDescription": "%node.launch.outputCapture.description%"}, "pauseForSourceMap": {"default": false, "markdownDescription": "%node.pauseForSourceMap.description%", "type": "boolean"}, "remoteRoot": {"default": null, "description": "%node.remoteRoot.description%", "type": ["string", "null"]}, "resolveSourceMapLocations": {"default": ["${workspaceFolder}/**", "!**/node_modules/**"], "description": "%node.resolveSourceMapLocations.description%", "items": {"type": "string"}, "type": ["array", "null"]}, "runtimeSourcemapPausePatterns": {"default": [], "items": {"type": "string"}, "markdownDescription": "%node.launch.runtimeSourcemapPausePatterns%", "type": "array"}, "showAsyncStacks": {"default": true, "description": "%node.showAsyncStacks.description%", "oneOf": [{"type": "boolean"}, {"properties": {"onAttach": {"default": 32, "type": "number"}}, "required": ["onAttach"], "type": "object"}, {"properties": {"onceBreakpointResolved": {"default": 32, "type": "number"}}, "required": ["onceBreakpointResolved"], "type": "object"}]}, "skipFiles": {"default": ["${/**"], "description": "%browser.skipFiles.description%", "type": "array"}, "smartStep": {"default": true, "description": "%smartStep.description%", "type": "boolean"}, "sourceMapPathOverrides": {"default": {"meteor://💻app/*": "${workspaceFolder}/*", "webpack:///./~/*": "${workspaceFolder}/node_modules/*", "webpack://?:*/*": "${workspaceFolder}/*"}, "description": "%node.sourceMapPathOverrides.description%", "type": "object"}, "sourceMapRenames": {"default": true, "description": "%browser.sourceMapRenames.description%", "type": "boolean"}, "sourceMaps": {"default": true, "description": "%browser.sourceMaps.description%", "type": "boolean"}, "timeout": {"default": 10000, "description": "%node.timeout.description%", "type": "number"}, "timeouts": {"additionalProperties": false, "default": {}, "description": "%timeouts.generalDescription%", "markdownDescription": "%timeouts.generalDescription.markdown%", "properties": {"hoverEvaluation": {"default": 500, "description": "%timeouts.hoverEvaluation.description%", "type": "number"}, "sourceMapCumulativePause": {"default": 1000, "description": "%timeouts.sourceMaps.sourceMapCumulativePause.description%", "type": "number"}, "sourceMapMinPause": {"default": 1000, "description": "%timeouts.sourceMaps.sourceMapMinPause.description%", "type": "number"}}, "type": "object"}, "trace": {"default": true, "description": "%trace.description%", "oneOf": [{"description": "%trace.boolean.description%", "type": "boolean"}, {"additionalProperties": false, "properties": {"logFile": {"description": "%trace.logFile.description%", "type": ["string", "null"]}, "stdio": {"description": "%trace.stdio.description%", "type": "boolean"}}, "type": "object"}]}}, "type": "object"}]}, "showAsyncStacks": {"default": true, "description": "%node.showAsyncStacks.description%", "oneOf": [{"type": "boolean"}, {"properties": {"onAttach": {"default": 32, "type": "number"}}, "required": ["onAttach"], "type": "object"}, {"properties": {"onceBreakpointResolved": {"default": 32, "type": "number"}}, "required": ["onceBreakpointResolved"], "type": "object"}]}, "skipFiles": {"default": ["${/**"], "description": "%browser.skipFiles.description%", "type": "array"}, "smartStep": {"default": true, "description": "%smartStep.description%", "type": "boolean"}, "sourceMapPathOverrides": {"default": {"meteor://💻app/*": "${workspaceFolder}/*", "webpack:///./~/*": "${workspaceFolder}/node_modules/*", "webpack://?:*/*": "${workspaceFolder}/*"}, "description": "%node.sourceMapPathOverrides.description%", "type": "object"}, "sourceMapRenames": {"default": true, "description": "%browser.sourceMapRenames.description%", "type": "boolean"}, "sourceMaps": {"default": true, "description": "%browser.sourceMaps.description%", "type": "boolean"}, "timeout": {"default": 10000, "description": "%node.timeout.description%", "type": "number"}, "timeouts": {"additionalProperties": false, "default": {}, "description": "%timeouts.generalDescription%", "markdownDescription": "%timeouts.generalDescription.markdown%", "properties": {"hoverEvaluation": {"default": 500, "description": "%timeouts.hoverEvaluation.description%", "type": "number"}, "sourceMapCumulativePause": {"default": 1000, "description": "%timeouts.sourceMaps.sourceMapCumulativePause.description%", "type": "number"}, "sourceMapMinPause": {"default": 1000, "description": "%timeouts.sourceMaps.sourceMapMinPause.description%", "type": "number"}}, "type": "object"}, "trace": {"default": true, "description": "%trace.description%", "oneOf": [{"description": "%trace.boolean.description%", "type": "boolean"}, {"additionalProperties": false, "properties": {"logFile": {"description": "%trace.logFile.description%", "type": ["string", "null"]}, "stdio": {"description": "%trace.stdio.description%", "type": "boolean"}}, "type": "object"}]}, "url": {"default": "http://localhost:8080", "description": "%browser.url.description%", "tags": ["setup"], "type": "string"}, "urlFilter": {"default": "", "description": "%browser.urlFilter.description%", "type": "string"}, "useWebView": {"default": false, "description": "%edge.useWebView.launch.description%", "type": "boolean"}, "userDataDir": {"default": true, "description": "%browser.userDataDir.description%", "type": ["string", "boolean"]}, "vueComponentPaths": {"default": ["${workspaceFolder}/**/*.vue"], "description": "%browser.vueComponentPaths%", "type": "array"}, "webRoot": {"default": "${workspaceFolder}", "description": "%browser.webRoot.description%", "tags": ["setup"], "type": "string"}}}}, "configurationSnippets": [{"body": {"name": "Launch Edge", "request": "launch", "type": "msedge", "url": "http://localhost:8080", "webRoot": "^\"${2:\\${workspaceFolder\\}}\""}, "description": "%edge.launch.description%", "label": "%edge.launch.label%"}, {"body": {"name": "Attach to Edge", "port": 9222, "request": "attach", "type": "msedge", "webRoot": "^\"${2:\\${workspaceFolder\\}}\""}, "description": "%edge.attach.description%", "label": "%edge.attach.label%"}], "label": "%edge.label%", "strings": {"unverifiedBreakpoints": "%debug.unverifiedBreakpoints%"}, "type": "msedge"}], "commands": [{"command": "extension.js-debug.prettyPrint", "title": "%pretty.print.script%", "category": "Debug", "icon": "$(json)"}, {"command": "extension.js-debug.toggleSkippingFile", "title": "%toggle.skipping.this.file%", "category": "Debug"}, {"command": "extension.js-debug.addCustomBreakpoints", "title": "%add.eventListener.breakpoint%", "icon": "$(add)"}, {"command": "extension.js-debug.removeAllCustomBreakpoints", "title": "%remove.eventListener.breakpoint.all%", "icon": "$(close-all)"}, {"command": "extension.js-debug.addXHRBreakpoints", "title": "%add.xhr.breakpoint%", "icon": "$(add)"}, {"command": "extension.js-debug.removeXHRBreakpoint", "title": "%remove.xhr.breakpoint%", "icon": "$(remove)"}, {"command": "extension.js-debug.editXHRBreakpoints", "title": "%edit.xhr.breakpoint%", "icon": "$(edit)"}, {"command": "extension.pwa-node-debug.attachNodeProcess", "title": "%attach.node.process%", "category": "Debug"}, {"command": "extension.js-debug.npmScript", "title": "%debug.npm.script%", "category": "Debug"}, {"command": "extension.js-debug.createDebuggerTerminal", "title": "%debug.terminal.label%", "category": "Debug"}, {"command": "extension.js-debug.startProfile", "title": "%profile.start%", "category": "Debug", "icon": "$(record)"}, {"command": "extension.js-debug.stopProfile", "title": "%profile.stop%", "category": "Debug", "icon": "resources/dark/stop-profiling.svg"}, {"command": "extension.js-debug.revealPage", "title": "%browser.revealPage%", "category": "Debug"}, {"command": "extension.js-debug.debugLink", "title": "%debugLink.label%", "category": "Debug"}, {"command": "extension.js-debug.createDiagnostics", "title": "%createDiagnostics.label%", "category": "Debug"}, {"command": "extension.js-debug.getDiagnosticLogs", "title": "%getDiagnosticLogs.label%", "category": "Debug"}, {"command": "extension.node-debug.startWithStopOnEntry", "title": "%startWithStopOnEntry.label%", "category": "Debug"}, {"command": "extension.js-debug.openEdgeDevTools", "title": "%openEdgeDevTools.label%", "icon": "$(inspect)", "category": "Debug"}, {"command": "extension.js-debug.callers.add", "title": "%commands.callersAdd.label%", "category": "Debug"}, {"command": "extension.js-debug.callers.remove", "title": "%commands.callersRemove.label%", "icon": "$(close)"}, {"command": "extension.js-debug.callers.removeAll", "title": "%commands.callersRemoveAll.label%", "icon": "$(clear-all)"}, {"command": "extension.js-debug.callers.goToCaller", "title": "%commands.callersGoToCaller.label%", "icon": "$(call-outgoing)"}, {"command": "extension.js-debug.callers.gotToTarget", "title": "%commands.callersGoToTarget.label%", "icon": "$(call-incoming)"}, {"command": "extension.js-debug.enableSourceMapStepping", "title": "%commands.enableSourceMapStepping.label%", "icon": "$(compass-dot)"}, {"command": "extension.js-debug.disableSourceMapStepping", "title": "%commands.disableSourceMapStepping.label%", "icon": "$(compass)"}, {"command": "extension.js-debug.network.viewRequest", "title": "%commands.networkViewRequest.label%", "icon": "$(arrow-right)"}, {"command": "extension.js-debug.network.clear", "title": "%commands.networkClear.label%", "icon": "$(clear-all)"}, {"command": "extension.js-debug.network.openBody", "title": "%commands.networkOpenBody.label%"}, {"command": "extension.js-debug.network.openBodyInHex", "title": "%commands.networkOpenBodyInHexEditor.label%"}, {"command": "extension.js-debug.network.replayXHR", "title": "%commands.networkReplayXHR.label%"}, {"command": "extension.js-debug.network.copyUri", "title": "%commands.networkCopyURI.label%"}], "keybindings": [{"command": "extension.node-debug.startWithStopOnEntry", "key": "F10", "mac": "F10", "when": "debugConfigurationType == pwa-node && !inDebugMode || debugConfigurationType == pwa-extensionHost && !inDebugMode || debugConfigurationType == node && !inDebugMode"}, {"command": "extension.node-debug.startWithStopOnEntry", "key": "F11", "mac": "F11", "when": "debugConfigurationType == pwa-node && !inDebugMode && activeViewlet == workbench.view.debug || debugConfigurationType == pwa-extensionHost && !inDebugMode && activeViewlet == workbench.view.debug || debugConfigurationType == node && !inDebugMode && activeViewlet == workbench.view.debug"}], "configuration": {"title": "JavaScript Debugger", "properties": {"debug.javascript.codelens.npmScripts": {"enum": ["top", "all", "never"], "default": "top", "description": "%configuration.npmScriptLensLocation%"}, "debug.javascript.terminalOptions": {"type": "object", "description": "%configuration.terminalOptions%", "default": {}, "properties": {"resolveSourceMapLocations": {"type": ["array", "null"], "description": "%node.resolveSourceMapLocations.description%", "default": ["${workspaceFolder}/**", "!**/node_modules/**"], "items": {"type": "string"}}, "outFiles": {"type": ["array"], "description": "%outFiles.description%", "default": ["${workspaceFolder}/**/*.(m|c|)js", "!**/node_modules/**"], "items": {"type": "string"}, "tags": ["setup"]}, "pauseForSourceMap": {"type": "boolean", "markdownDescription": "%node.pauseForSourceMap.description%", "default": false}, "showAsyncStacks": {"description": "%node.showAsyncStacks.description%", "default": true, "oneOf": [{"type": "boolean"}, {"type": "object", "required": ["onAttach"], "properties": {"onAttach": {"type": "number", "default": 32}}}, {"type": "object", "required": ["onceBreakpointResolved"], "properties": {"onceBreakpointResolved": {"type": "number", "default": 32}}}]}, "skipFiles": {"type": "array", "description": "%browser.skipFiles.description%", "default": ["${/**"]}, "smartStep": {"type": "boolean", "description": "%smartStep.description%", "default": true}, "sourceMaps": {"type": "boolean", "description": "%browser.sourceMaps.description%", "default": true}, "sourceMapRenames": {"type": "boolean", "default": true, "description": "%browser.sourceMapRenames.description%"}, "sourceMapPathOverrides": {"type": "object", "description": "%node.sourceMapPathOverrides.description%", "default": {"webpack://?:*/*": "${workspaceFolder}/*", "webpack:///./~/*": "${workspaceFolder}/node_modules/*", "meteor://💻app/*": "${workspaceFolder}/*"}}, "timeout": {"type": "number", "description": "%node.timeout.description%", "default": 10000}, "timeouts": {"type": "object", "description": "%timeouts.generalDescription%", "default": {}, "properties": {"sourceMapMinPause": {"type": "number", "description": "%timeouts.sourceMaps.sourceMapMinPause.description%", "default": 1000}, "sourceMapCumulativePause": {"type": "number", "description": "%timeouts.sourceMaps.sourceMapCumulativePause.description%", "default": 1000}, "hoverEvaluation": {"type": "number", "description": "%timeouts.hoverEvaluation.description%", "default": 500}}, "additionalProperties": false, "markdownDescription": "%timeouts.generalDescription.markdown%"}, "trace": {"description": "%trace.description%", "default": true, "oneOf": [{"type": "boolean", "description": "%trace.boolean.description%"}, {"type": "object", "additionalProperties": false, "properties": {"stdio": {"type": "boolean", "description": "%trace.stdio.description%"}, "logFile": {"type": ["string", "null"], "description": "%trace.logFile.description%"}}}]}, "outputCapture": {"enum": ["console", "std"], "markdownDescription": "%node.launch.outputCapture.description%", "default": "console"}, "enableContentValidation": {"default": true, "type": "boolean", "description": "%enableContentValidation.description%"}, "customDescriptionGenerator": {"type": "string", "description": "%customDescriptionGenerator.description%"}, "customPropertiesGenerator": {"type": "string", "deprecated": true, "description": "%customPropertiesGenerator.description%"}, "cascadeTerminateToConfigurations": {"type": "array", "items": {"type": "string", "uniqueItems": true}, "default": [], "description": "%base.cascadeTerminateToConfigurations.label%"}, "enableDWARF": {"type": "boolean", "default": true, "markdownDescription": "%base.enableDWARF.label%"}, "cwd": {"type": "string", "description": "%node.launch.cwd.description%", "default": "${workspaceFolder}", "docDefault": "localRoot || ${workspaceFolder}", "tags": ["setup"]}, "localRoot": {"type": ["string", "null"], "description": "%node.localRoot.description%", "default": null}, "remoteRoot": {"type": ["string", "null"], "description": "%node.remoteRoot.description%", "default": null}, "autoAttachChildProcesses": {"type": "boolean", "description": "%node.launch.autoAttachChildProcesses.description%", "default": true}, "env": {"type": "object", "additionalProperties": {"type": ["string", "null"]}, "markdownDescription": "%node.launch.env.description%", "default": {}, "tags": ["setup"]}, "envFile": {"type": "string", "description": "%node.launch.envFile.description%", "default": "${workspaceFolder}/.env"}, "runtimeSourcemapPausePatterns": {"type": "array", "items": {"type": "string"}, "markdownDescription": "%node.launch.runtimeSourcemapPausePatterns%", "default": []}, "nodeVersionHint": {"type": "number", "minimum": 8, "description": "%node.versionHint.description%", "default": 12}, "command": {"type": ["string", "null"], "description": "%debug.terminal.program.description%", "default": "npm start", "tags": ["setup"]}}}, "debug.javascript.automaticallyTunnelRemoteServer": {"type": "boolean", "description": "%configuration.automaticallyTunnelRemoteServer%", "default": true}, "debug.javascript.debugByLinkOptions": {"default": "on", "description": "%configuration.debugByLinkOptions%", "oneOf": [{"type": "string", "enum": ["on", "off", "always"]}, {"type": "object", "properties": {"resolveSourceMapLocations": {"type": ["array", "null"], "description": "%node.resolveSourceMapLocations.description%", "default": null, "items": {"type": "string"}}, "outFiles": {"type": ["array"], "description": "%outFiles.description%", "default": ["${workspaceFolder}/**/*.(m|c|)js", "!**/node_modules/**"], "items": {"type": "string"}, "tags": ["setup"]}, "pauseForSourceMap": {"type": "boolean", "markdownDescription": "%node.pauseForSourceMap.description%", "default": false}, "showAsyncStacks": {"description": "%node.showAsyncStacks.description%", "default": true, "oneOf": [{"type": "boolean"}, {"type": "object", "required": ["onAttach"], "properties": {"onAttach": {"type": "number", "default": 32}}}, {"type": "object", "required": ["onceBreakpointResolved"], "properties": {"onceBreakpointResolved": {"type": "number", "default": 32}}}]}, "skipFiles": {"type": "array", "description": "%browser.skipFiles.description%", "default": ["${/**"]}, "smartStep": {"type": "boolean", "description": "%smartStep.description%", "default": true}, "sourceMaps": {"type": "boolean", "description": "%browser.sourceMaps.description%", "default": true}, "sourceMapRenames": {"type": "boolean", "default": true, "description": "%browser.sourceMapRenames.description%"}, "sourceMapPathOverrides": {"type": "object", "description": "%node.sourceMapPathOverrides.description%", "default": {"webpack://?:*/*": "${workspaceFolder}/*", "webpack:///./~/*": "${workspaceFolder}/node_modules/*", "meteor://💻app/*": "${workspaceFolder}/*"}}, "timeout": {"type": "number", "description": "%node.timeout.description%", "default": 10000}, "timeouts": {"type": "object", "description": "%timeouts.generalDescription%", "default": {}, "properties": {"sourceMapMinPause": {"type": "number", "description": "%timeouts.sourceMaps.sourceMapMinPause.description%", "default": 1000}, "sourceMapCumulativePause": {"type": "number", "description": "%timeouts.sourceMaps.sourceMapCumulativePause.description%", "default": 1000}, "hoverEvaluation": {"type": "number", "description": "%timeouts.hoverEvaluation.description%", "default": 500}}, "additionalProperties": false, "markdownDescription": "%timeouts.generalDescription.markdown%"}, "trace": {"description": "%trace.description%", "default": true, "oneOf": [{"type": "boolean", "description": "%trace.boolean.description%"}, {"type": "object", "additionalProperties": false, "properties": {"stdio": {"type": "boolean", "description": "%trace.stdio.description%"}, "logFile": {"type": ["string", "null"], "description": "%trace.logFile.description%"}}}]}, "outputCapture": {"enum": ["console", "std"], "markdownDescription": "%node.launch.outputCapture.description%", "default": "console"}, "enableContentValidation": {"default": true, "type": "boolean", "description": "%enableContentValidation.description%"}, "customDescriptionGenerator": {"type": "string", "description": "%customDescriptionGenerator.description%"}, "customPropertiesGenerator": {"type": "string", "deprecated": true, "description": "%customPropertiesGenerator.description%"}, "cascadeTerminateToConfigurations": {"type": "array", "items": {"type": "string", "uniqueItems": true}, "default": [], "description": "%base.cascadeTerminateToConfigurations.label%"}, "enableDWARF": {"type": "boolean", "default": true, "markdownDescription": "%base.enableDWARF.label%"}, "disableNetworkCache": {"type": "boolean", "description": "%browser.disableNetworkCache.description%", "default": true}, "pathMapping": {"type": "object", "description": "%browser.pathMapping.description%", "default": {}}, "webRoot": {"type": "string", "description": "%browser.webRoot.description%", "default": "${workspaceFolder}", "tags": ["setup"]}, "urlFilter": {"type": "string", "description": "%browser.urlFilter.description%", "default": ""}, "url": {"type": "string", "description": "%browser.url.description%", "default": "http://localhost:8080", "tags": ["setup"]}, "inspectUri": {"type": ["string", "null"], "description": "%browser.inspectUri.description%", "default": null}, "vueComponentPaths": {"type": "array", "description": "%browser.vueComponentPaths%", "default": ["${workspaceFolder}/**/*.vue"]}, "server": {"oneOf": [{"type": "object", "description": "%browser.server.description%", "additionalProperties": false, "default": {"program": "node my-server.js"}, "properties": {"resolveSourceMapLocations": {"type": ["array", "null"], "description": "%node.resolveSourceMapLocations.description%", "default": ["${workspaceFolder}/**", "!**/node_modules/**"], "items": {"type": "string"}}, "outFiles": {"type": ["array"], "description": "%outFiles.description%", "default": ["${workspaceFolder}/**/*.(m|c|)js", "!**/node_modules/**"], "items": {"type": "string"}, "tags": ["setup"]}, "pauseForSourceMap": {"type": "boolean", "markdownDescription": "%node.pauseForSourceMap.description%", "default": false}, "showAsyncStacks": {"description": "%node.showAsyncStacks.description%", "default": true, "oneOf": [{"type": "boolean"}, {"type": "object", "required": ["onAttach"], "properties": {"onAttach": {"type": "number", "default": 32}}}, {"type": "object", "required": ["onceBreakpointResolved"], "properties": {"onceBreakpointResolved": {"type": "number", "default": 32}}}]}, "skipFiles": {"type": "array", "description": "%browser.skipFiles.description%", "default": ["${/**"]}, "smartStep": {"type": "boolean", "description": "%smartStep.description%", "default": true}, "sourceMaps": {"type": "boolean", "description": "%browser.sourceMaps.description%", "default": true}, "sourceMapRenames": {"type": "boolean", "default": true, "description": "%browser.sourceMapRenames.description%"}, "sourceMapPathOverrides": {"type": "object", "description": "%node.sourceMapPathOverrides.description%", "default": {"webpack://?:*/*": "${workspaceFolder}/*", "webpack:///./~/*": "${workspaceFolder}/node_modules/*", "meteor://💻app/*": "${workspaceFolder}/*"}}, "timeout": {"type": "number", "description": "%node.timeout.description%", "default": 10000}, "timeouts": {"type": "object", "description": "%timeouts.generalDescription%", "default": {}, "properties": {"sourceMapMinPause": {"type": "number", "description": "%timeouts.sourceMaps.sourceMapMinPause.description%", "default": 1000}, "sourceMapCumulativePause": {"type": "number", "description": "%timeouts.sourceMaps.sourceMapCumulativePause.description%", "default": 1000}, "hoverEvaluation": {"type": "number", "description": "%timeouts.hoverEvaluation.description%", "default": 500}}, "additionalProperties": false, "markdownDescription": "%timeouts.generalDescription.markdown%"}, "trace": {"description": "%trace.description%", "default": true, "oneOf": [{"type": "boolean", "description": "%trace.boolean.description%"}, {"type": "object", "additionalProperties": false, "properties": {"stdio": {"type": "boolean", "description": "%trace.stdio.description%"}, "logFile": {"type": ["string", "null"], "description": "%trace.logFile.description%"}}}]}, "outputCapture": {"enum": ["console", "std"], "markdownDescription": "%node.launch.outputCapture.description%", "default": "console"}, "enableContentValidation": {"default": true, "type": "boolean", "description": "%enableContentValidation.description%"}, "customDescriptionGenerator": {"type": "string", "description": "%customDescriptionGenerator.description%"}, "customPropertiesGenerator": {"type": "string", "deprecated": true, "description": "%customPropertiesGenerator.description%"}, "cascadeTerminateToConfigurations": {"type": "array", "items": {"type": "string", "uniqueItems": true}, "default": [], "description": "%base.cascadeTerminateToConfigurations.label%"}, "enableDWARF": {"type": "boolean", "default": true, "markdownDescription": "%base.enableDWARF.label%"}, "cwd": {"type": "string", "description": "%node.launch.cwd.description%", "default": "${workspaceFolder}", "tags": ["setup"]}, "localRoot": {"type": ["string", "null"], "description": "%node.localRoot.description%", "default": null}, "remoteRoot": {"type": ["string", "null"], "description": "%node.remoteRoot.description%", "default": null}, "autoAttachChildProcesses": {"type": "boolean", "description": "%node.launch.autoAttachChildProcesses.description%", "default": true}, "env": {"type": "object", "additionalProperties": {"type": ["string", "null"]}, "markdownDescription": "%node.launch.env.description%", "default": {}, "tags": ["setup"]}, "envFile": {"type": "string", "description": "%node.launch.envFile.description%", "default": "${workspaceFolder}/.env"}, "runtimeSourcemapPausePatterns": {"type": "array", "items": {"type": "string"}, "markdownDescription": "%node.launch.runtimeSourcemapPausePatterns%", "default": []}, "nodeVersionHint": {"type": "number", "minimum": 8, "description": "%node.versionHint.description%", "default": 12}, "program": {"type": "string", "description": "%node.launch.program.description%", "default": "", "tags": ["setup"]}, "stopOnEntry": {"type": ["boolean", "string"], "description": "%node.stopOnEntry.description%", "default": true}, "console": {"type": "string", "enum": ["internalConsole", "integratedTerminal", "externalTerminal"], "enumDescriptions": ["%node.launch.console.internalConsole.description%", "%node.launch.console.integratedTerminal.description%", "%node.launch.console.externalTerminal.description%"], "description": "%node.launch.console.description%", "default": "internalConsole"}, "args": {"type": ["array", "string"], "description": "%node.launch.args.description%", "items": {"type": "string"}, "default": [], "tags": ["setup"]}, "restart": {"description": "%node.attach.restart.description%", "default": true, "oneOf": [{"type": "boolean"}, {"type": "object", "properties": {"delay": {"type": "number", "minimum": 0, "default": 1000}, "maxAttempts": {"type": "number", "minimum": 0, "default": 10}}}]}, "runtimeExecutable": {"type": ["string", "null"], "markdownDescription": "%node.launch.runtimeExecutable.description%", "default": "node"}, "runtimeVersion": {"type": "string", "markdownDescription": "%node.launch.runtimeVersion.description%", "default": "default"}, "runtimeArgs": {"type": "array", "description": "%node.launch.runtimeArgs.description%", "items": {"type": "string"}, "default": [], "tags": ["setup"]}, "profileStartup": {"type": "boolean", "description": "%node.profileStartup.description%", "default": true}, "attachSimplePort": {"oneOf": [{"type": "integer"}, {"type": "string", "pattern": "^\\${.*}$"}], "description": "%node.attachSimplePort.description%", "default": 9229}, "killBehavior": {"type": "string", "enum": ["forceful", "polite", "none"], "default": "forceful", "markdownDescription": "%node.killBehavior.description%"}, "experimentalNetworking": {"type": "string", "default": "auto", "description": "%node.experimentalNetworking.description%", "enum": ["auto", "on", "off"]}}}, {"type": "object", "description": "%debug.terminal.label%", "additionalProperties": false, "default": {"program": "npm start"}, "properties": {"resolveSourceMapLocations": {"type": ["array", "null"], "description": "%node.resolveSourceMapLocations.description%", "default": ["${workspaceFolder}/**", "!**/node_modules/**"], "items": {"type": "string"}}, "outFiles": {"type": ["array"], "description": "%outFiles.description%", "default": ["${workspaceFolder}/**/*.(m|c|)js", "!**/node_modules/**"], "items": {"type": "string"}, "tags": ["setup"]}, "pauseForSourceMap": {"type": "boolean", "markdownDescription": "%node.pauseForSourceMap.description%", "default": false}, "showAsyncStacks": {"description": "%node.showAsyncStacks.description%", "default": true, "oneOf": [{"type": "boolean"}, {"type": "object", "required": ["onAttach"], "properties": {"onAttach": {"type": "number", "default": 32}}}, {"type": "object", "required": ["onceBreakpointResolved"], "properties": {"onceBreakpointResolved": {"type": "number", "default": 32}}}]}, "skipFiles": {"type": "array", "description": "%browser.skipFiles.description%", "default": ["${/**"]}, "smartStep": {"type": "boolean", "description": "%smartStep.description%", "default": true}, "sourceMaps": {"type": "boolean", "description": "%browser.sourceMaps.description%", "default": true}, "sourceMapRenames": {"type": "boolean", "default": true, "description": "%browser.sourceMapRenames.description%"}, "sourceMapPathOverrides": {"type": "object", "description": "%node.sourceMapPathOverrides.description%", "default": {"webpack://?:*/*": "${workspaceFolder}/*", "webpack:///./~/*": "${workspaceFolder}/node_modules/*", "meteor://💻app/*": "${workspaceFolder}/*"}}, "timeout": {"type": "number", "description": "%node.timeout.description%", "default": 10000}, "timeouts": {"type": "object", "description": "%timeouts.generalDescription%", "default": {}, "properties": {"sourceMapMinPause": {"type": "number", "description": "%timeouts.sourceMaps.sourceMapMinPause.description%", "default": 1000}, "sourceMapCumulativePause": {"type": "number", "description": "%timeouts.sourceMaps.sourceMapCumulativePause.description%", "default": 1000}, "hoverEvaluation": {"type": "number", "description": "%timeouts.hoverEvaluation.description%", "default": 500}}, "additionalProperties": false, "markdownDescription": "%timeouts.generalDescription.markdown%"}, "trace": {"description": "%trace.description%", "default": true, "oneOf": [{"type": "boolean", "description": "%trace.boolean.description%"}, {"type": "object", "additionalProperties": false, "properties": {"stdio": {"type": "boolean", "description": "%trace.stdio.description%"}, "logFile": {"type": ["string", "null"], "description": "%trace.logFile.description%"}}}]}, "outputCapture": {"enum": ["console", "std"], "markdownDescription": "%node.launch.outputCapture.description%", "default": "console"}, "enableContentValidation": {"default": true, "type": "boolean", "description": "%enableContentValidation.description%"}, "customDescriptionGenerator": {"type": "string", "description": "%customDescriptionGenerator.description%"}, "customPropertiesGenerator": {"type": "string", "deprecated": true, "description": "%customPropertiesGenerator.description%"}, "cascadeTerminateToConfigurations": {"type": "array", "items": {"type": "string", "uniqueItems": true}, "default": [], "description": "%base.cascadeTerminateToConfigurations.label%"}, "enableDWARF": {"type": "boolean", "default": true, "markdownDescription": "%base.enableDWARF.label%"}, "cwd": {"type": "string", "description": "%node.launch.cwd.description%", "default": "${workspaceFolder}", "docDefault": "localRoot || ${workspaceFolder}", "tags": ["setup"]}, "localRoot": {"type": ["string", "null"], "description": "%node.localRoot.description%", "default": null}, "remoteRoot": {"type": ["string", "null"], "description": "%node.remoteRoot.description%", "default": null}, "autoAttachChildProcesses": {"type": "boolean", "description": "%node.launch.autoAttachChildProcesses.description%", "default": true}, "env": {"type": "object", "additionalProperties": {"type": ["string", "null"]}, "markdownDescription": "%node.launch.env.description%", "default": {}, "tags": ["setup"]}, "envFile": {"type": "string", "description": "%node.launch.envFile.description%", "default": "${workspaceFolder}/.env"}, "runtimeSourcemapPausePatterns": {"type": "array", "items": {"type": "string"}, "markdownDescription": "%node.launch.runtimeSourcemapPausePatterns%", "default": []}, "nodeVersionHint": {"type": "number", "minimum": 8, "description": "%node.versionHint.description%", "default": 12}, "command": {"type": ["string", "null"], "description": "%debug.terminal.program.description%", "default": "npm start", "tags": ["setup"]}}}]}, "perScriptSourcemaps": {"type": "string", "default": "auto", "enum": ["yes", "no", "auto"], "description": "%browser.perScriptSourcemaps.description%"}, "port": {"type": "number", "description": "%browser.launch.port.description%", "default": 0}, "file": {"type": "string", "description": "%browser.file.description%", "default": "${workspaceFolder}/index.html", "tags": ["setup"]}, "userDataDir": {"type": ["string", "boolean"], "description": "%browser.userDataDir.description%", "default": true}, "includeDefaultArgs": {"type": "boolean", "description": "%browser.includeDefaultArgs.description%", "default": true}, "includeLaunchArgs": {"type": "boolean", "description": "%browser.includeLaunchArgs.description%", "default": true}, "runtimeExecutable": {"type": ["string", "null"], "description": "%browser.runtimeExecutable.description%", "default": "stable"}, "runtimeArgs": {"type": "array", "description": "%browser.runtimeArgs.description%", "items": {"type": "string"}, "default": []}, "env": {"type": "object", "description": "%browser.env.description%", "default": {}}, "cwd": {"type": "string", "description": "%browser.cwd.description%", "default": null}, "profileStartup": {"type": "boolean", "description": "%browser.profileStartup.description%", "default": true}, "cleanUp": {"type": "string", "enum": ["wholeBrowser", "onlyTab"], "description": "%browser.cleanUp.description%", "default": "wholeBrowser"}, "killBehavior": {"type": "string", "enum": ["forceful", "polite", "none"], "default": "forceful", "markdownDescription": "%browser.killBehavior.description%"}, "browserLaunchLocation": {"description": "%browser.browserLaunchLocation.description%", "default": null, "oneOf": [{"type": "null"}, {"type": "string", "enum": ["ui", "workspace"]}]}, "enabled": {"type": "string", "enum": ["on", "off", "always"]}}}]}, "debug.javascript.pickAndAttachOptions": {"type": "object", "default": {}, "markdownDescription": "%configuration.pickAndAttachOptions%", "properties": {"resolveSourceMapLocations": {"type": ["array", "null"], "description": "%node.resolveSourceMapLocations.description%", "default": ["${workspaceFolder}/**", "!**/node_modules/**"], "items": {"type": "string"}}, "outFiles": {"type": ["array"], "description": "%outFiles.description%", "default": ["${workspaceFolder}/**/*.(m|c|)js", "!**/node_modules/**"], "items": {"type": "string"}, "tags": ["setup"]}, "pauseForSourceMap": {"type": "boolean", "markdownDescription": "%node.pauseForSourceMap.description%", "default": false}, "showAsyncStacks": {"description": "%node.showAsyncStacks.description%", "default": true, "oneOf": [{"type": "boolean"}, {"type": "object", "required": ["onAttach"], "properties": {"onAttach": {"type": "number", "default": 32}}}, {"type": "object", "required": ["onceBreakpointResolved"], "properties": {"onceBreakpointResolved": {"type": "number", "default": 32}}}]}, "skipFiles": {"type": "array", "description": "%browser.skipFiles.description%", "default": ["${/**"]}, "smartStep": {"type": "boolean", "description": "%smartStep.description%", "default": true}, "sourceMaps": {"type": "boolean", "description": "%browser.sourceMaps.description%", "default": true}, "sourceMapRenames": {"type": "boolean", "default": true, "description": "%browser.sourceMapRenames.description%"}, "sourceMapPathOverrides": {"type": "object", "description": "%node.sourceMapPathOverrides.description%", "default": {"webpack://?:*/*": "${workspaceFolder}/*", "webpack:///./~/*": "${workspaceFolder}/node_modules/*", "meteor://💻app/*": "${workspaceFolder}/*"}}, "timeout": {"type": "number", "description": "%node.timeout.description%", "default": 10000}, "timeouts": {"type": "object", "description": "%timeouts.generalDescription%", "default": {}, "properties": {"sourceMapMinPause": {"type": "number", "description": "%timeouts.sourceMaps.sourceMapMinPause.description%", "default": 1000}, "sourceMapCumulativePause": {"type": "number", "description": "%timeouts.sourceMaps.sourceMapCumulativePause.description%", "default": 1000}, "hoverEvaluation": {"type": "number", "description": "%timeouts.hoverEvaluation.description%", "default": 500}}, "additionalProperties": false, "markdownDescription": "%timeouts.generalDescription.markdown%"}, "trace": {"description": "%trace.description%", "default": true, "oneOf": [{"type": "boolean", "description": "%trace.boolean.description%"}, {"type": "object", "additionalProperties": false, "properties": {"stdio": {"type": "boolean", "description": "%trace.stdio.description%"}, "logFile": {"type": ["string", "null"], "description": "%trace.logFile.description%"}}}]}, "outputCapture": {"enum": ["console", "std"], "markdownDescription": "%node.launch.outputCapture.description%", "default": "console"}, "enableContentValidation": {"default": true, "type": "boolean", "description": "%enableContentValidation.description%"}, "customDescriptionGenerator": {"type": "string", "description": "%customDescriptionGenerator.description%"}, "customPropertiesGenerator": {"type": "string", "deprecated": true, "description": "%customPropertiesGenerator.description%"}, "cascadeTerminateToConfigurations": {"type": "array", "items": {"type": "string", "uniqueItems": true}, "default": [], "description": "%base.cascadeTerminateToConfigurations.label%"}, "enableDWARF": {"type": "boolean", "default": true, "markdownDescription": "%base.enableDWARF.label%"}, "cwd": {"type": "string", "description": "%node.launch.cwd.description%", "default": "${workspaceFolder}", "docDefault": "localRoot || ${workspaceFolder}", "tags": ["setup"]}, "localRoot": {"type": ["string", "null"], "description": "%node.localRoot.description%", "default": null}, "remoteRoot": {"type": ["string", "null"], "description": "%node.remoteRoot.description%", "default": null}, "autoAttachChildProcesses": {"type": "boolean", "description": "%node.launch.autoAttachChildProcesses.description%", "default": true}, "env": {"type": "object", "additionalProperties": {"type": ["string", "null"]}, "markdownDescription": "%node.launch.env.description%", "default": {}, "tags": ["setup"]}, "envFile": {"type": "string", "description": "%node.launch.envFile.description%", "default": "${workspaceFolder}/.env"}, "runtimeSourcemapPausePatterns": {"type": "array", "items": {"type": "string"}, "markdownDescription": "%node.launch.runtimeSourcemapPausePatterns%", "default": []}, "nodeVersionHint": {"type": "number", "minimum": 8, "description": "%node.versionHint.description%", "default": 12}, "address": {"type": "string", "description": "%node.address.description%", "default": "localhost"}, "port": {"description": "%node.port.description%", "default": 9229, "oneOf": [{"type": "integer"}, {"type": "string", "pattern": "^\\${.*}$"}], "tags": ["setup"]}, "websocketAddress": {"type": "string", "description": "%node.websocket.address.description%"}, "remoteHostHeader": {"type": "string", "description": "%node.remote.host.header.description%"}, "restart": {"description": "%node.attach.restart.description%", "default": true, "oneOf": [{"type": "boolean"}, {"type": "object", "properties": {"delay": {"type": "number", "minimum": 0, "default": 1000}, "maxAttempts": {"type": "number", "minimum": 0, "default": 10}}}]}, "processId": {"type": "string", "description": "%node.attach.processId.description%", "default": "${command:PickProcess}"}, "attachExistingChildren": {"type": "boolean", "description": "%node.attach.attachExistingChildren.description%", "default": false}, "continueOnAttach": {"type": "boolean", "markdownDescription": "%node.attach.continueOnAttach%", "default": true}}}, "debug.javascript.autoAttachFilter": {"type": "string", "default": "disabled", "enum": ["always", "smart", "onlyWithFlag", "disabled"], "enumDescriptions": ["%configuration.autoAttachMode.always%", "%configuration.autoAttachMode.smart%", "%configuration.autoAttachMode.explicit%", "%configuration.autoAttachMode.disabled%"], "markdownDescription": "%configuration.autoAttachMode%"}, "debug.javascript.autoAttachSmartPattern": {"type": "array", "items": {"type": "string"}, "default": ["${workspaceFolder}/**", "!**/node_modules/**", "**/$KNOWN_TOOLS$/**"], "markdownDescription": "%configuration.autoAttachSmartPatterns%"}, "debug.javascript.breakOnConditionalError": {"type": "boolean", "default": false, "markdownDescription": "%configuration.breakOnConditionalError%"}, "debug.javascript.unmapMissingSources": {"type": "boolean", "default": false, "description": "%configuration.unmapMissingSources%"}, "debug.javascript.defaultRuntimeExecutable": {"type": "object", "default": {"pwa-node": "node"}, "markdownDescription": "%configuration.defaultRuntimeExecutables%", "properties": {"pwa-node": {"type": "string"}, "pwa-chrome": {"type": "string"}, "pwa-msedge": {"type": "string"}}}, "debug.javascript.resourceRequestOptions": {"type": "object", "default": {}, "markdownDescription": "%configuration.resourceRequestOptions%"}, "debug.javascript.enableNetworkView": {"type": "boolean", "default": true, "description": "%configuration.enableNetworkView%"}}}, "grammars": [{"language": "wat", "scopeName": "text.wat", "path": "./src/ui/basic-wat.tmLanguage.json"}], "languages": [{"id": "wat", "extensions": [".wat", ".wasm"], "aliases": ["WebAssembly Text Format"], "firstLine": "^\\(module", "mimetypes": ["text/wat"], "configuration": "./src/ui/basic-wat.configuration.json"}], "terminal": {"profiles": [{"id": "extension.js-debug.debugTerminal", "title": "%debug.terminal.label%", "icon": "$(debug)"}]}, "views": {"debug": [{"id": "jsBrowserBreakpoints", "name": "Event Listener Breakpoints", "when": "debugType == pwa-chrome || debugType == pwa-msedge"}, {"id": "jsExcludedCallers", "name": "Excluded Callers", "when": "debugType == pwa-extensionHost && jsDebugHasExcludedCallers || debugType == node-terminal && jsDebugHasExcludedCallers || debugType == pwa-node && jsDebugHasExcludedCallers || debugType == pwa-chrome && jsDebugHasExcludedCallers || debugType == pwa-msedge && jsDebugHasExcludedCallers"}, {"id": "jsDebugNetworkTree", "name": "Network", "when": "jsDebugNetworkAvailable"}]}, "viewsWelcome": [{"view": "debug", "contents": "%debug.terminal.welcomeWithLink%", "when": "debugStartLanguage == javascript && !isWeb || debugStartLanguage == typescript && !isWeb || debugStartLanguage == javascriptreact && !isWeb || debugStartLanguage == typescriptreact && !isWeb"}, {"view": "debug", "contents": "%debug.terminal.welcome%", "when": "debugStartLanguage == javascript && isWeb || debugStartLanguage == typescript && isWeb || debugStartLanguage == javascriptreact && isWeb || debugStartLanguage == typescriptreact && isWeb"}]}, "__metadata": {"id": "25629058-ddac-4e17-abba-74678e126c5d", "publisherId": {"publisherId": "5f5636e7-69ed-4afe-b5d6-8d231fb3d3ee", "publisherName": "ms-vscode", "displayName": "Microsoft", "flags": "verified"}, "publisherDisplayName": "Microsoft"}}