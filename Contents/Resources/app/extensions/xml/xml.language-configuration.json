{"comments": {"blockComment": ["<!--", "-->"]}, "brackets": [["<!--", "-->"], ["<", ">"], ["{", "}"], ["(", ")"]], "autoClosingPairs": [{"open": "{", "close": "}"}, {"open": "[", "close": "]"}, {"open": "(", "close": ")"}, {"open": "\"", "close": "\"", "notIn": ["string"]}, {"open": "'", "close": "'", "notIn": ["string"]}, {"open": "<!--", "close": "-->", "notIn": ["comment", "string"]}], "surroundingPairs": [{"open": "'", "close": "'"}, {"open": "\"", "close": "\""}, {"open": "{", "close": "}"}, {"open": "[", "close": "]"}, {"open": "(", "close": ")"}, {"open": "<", "close": ">"}], "colorizedBracketPairs": [], "folding": {"markers": {"start": "^\\s*<!--\\s*#region\\b.*-->", "end": "^\\s*<!--\\s*#endregion\\b.*-->"}}, "wordPattern": {"pattern": "(-?\\d*\\.\\d\\w*)|([^\\`\\~\\!\\@\\#\\$\\%\\^\\&\\*\\(\\)\\=\\+\\[\\{\\]\\}\\\\\\|\\;\\:\\'\\\"\\,\\.\\<\\>\\/\\?\\s]+)"}}