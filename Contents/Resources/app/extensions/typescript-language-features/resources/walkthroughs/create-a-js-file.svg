<svg width="524" height="158" viewBox="0 0 524 158" fill="none" xmlns="http://www.w3.org/2000/svg">
  <g>
    <g>
      <rect width="474" height="35" transform="translate(25.6238)" fill="var(--vscode-editorGroupHeader-tabsBackground, #292929)" />
      <g clip-path="url(#clip0_1720_12451)">
        <g>
          <rect width="123" height="35" transform="translate(25.6238)" fill="var(--vscode-tab-activeBackground, #252526)" />
          <g>
            <path d="M42.7438 13.74H44.4438V18.5C44.4438 19.5267 44.2038 20.2733 43.7238 20.74C43.2838 21.1667 42.6238 21.38 41.7438 21.38C41.5438 21.38 41.3238 21.3667 41.0838 21.34C40.8438 21.3 40.6504 21.2467 40.5038 21.18L40.6838 19.82C40.9371 19.94 41.2304 20 41.5638 20C41.9371 20 42.2171 19.9 42.4038 19.7C42.6304 19.46 42.7438 19.06 42.7438 18.5V13.74ZM45.9438 19.5C46.1838 19.6333 46.4638 19.7467 46.7838 19.84C47.1438 19.9467 47.4838 20 47.8038 20C48.2038 20 48.5038 19.9333 48.7038 19.8C48.9038 19.6533 49.0038 19.4533 49.0038 19.2C49.0038 18.9467 48.9104 18.7467 48.7238 18.6C48.5371 18.44 48.2104 18.28 47.7438 18.12C46.3704 17.64 45.6838 16.8933 45.6838 15.88C45.6838 15.2133 45.9371 14.6733 46.4438 14.26C46.9638 13.8333 47.6704 13.62 48.5638 13.62C49.2704 13.62 49.9171 13.7467 50.5038 14L50.1238 15.38L50.0038 15.32C49.7638 15.2267 49.5704 15.16 49.4238 15.12C49.1438 15.04 48.8571 15 48.5638 15C48.2038 15 47.9238 15.0733 47.7238 15.22C47.5371 15.3533 47.4438 15.5333 47.4438 15.76C47.4438 15.9867 47.5504 16.1733 47.7638 16.32C47.9238 16.44 48.2704 16.6067 48.8038 16.82C49.4704 17.0733 49.9571 17.38 50.2638 17.74C50.5838 18.1 50.7438 18.54 50.7438 19.06C50.7438 19.7267 50.4904 20.2667 49.9838 20.68C49.4371 21.1467 48.6704 21.38 47.6838 21.38C47.2971 21.38 46.8904 21.3267 46.4638 21.22C46.1304 21.1533 45.8304 21.06 45.5638 20.94L45.9438 19.5Z" fill="#CBCB41" />
          </g>
          <rect x="57.6238" y="14" width="79" height="7" rx="3.5" fill="var(--vscode-tab-inactiveForeground, #FFFFFF)" fill-opacity="0.12" />
        </g>
      </g>
    </g>
    <g filter="url(#filter0_d_1720_12451)">
      <rect x="16" y="35" width="492" height="105" rx="2" fill="var(--vscode-quickInput-background, #292929)" />
    </g>
    <g>
      <rect x="17" y="79" width="490" height="20" stroke="var(--vscode-editor-lineHighlightBorder, #333333)" stroke-width="2" />
      <g>
        <path d="M48.812 93.6382C48.4989 93.8201 48.1751 93.9556 47.8408 94.0444C47.5107 94.1375 47.1722 94.1841 46.8252 94.1841C45.7249 94.1841 44.8638 93.854 44.2417 93.1938C43.6239 92.5337 43.3149 91.6196 43.3149 90.4517C43.3149 89.2837 43.6239 88.3696 44.2417 87.7095C44.8638 87.0493 45.7249 86.7192 46.8252 86.7192C47.168 86.7192 47.5023 86.7637 47.8281 86.8525C48.154 86.9414 48.4819 87.0789 48.812 87.2651V88.4902C48.5031 88.2152 48.1921 88.0163 47.8789 87.8936C47.57 87.7708 47.2188 87.7095 46.8252 87.7095C46.0931 87.7095 45.5303 87.9465 45.1367 88.4204C44.7432 88.8944 44.5464 89.5715 44.5464 90.4517C44.5464 91.3276 44.7432 92.0047 45.1367 92.4829C45.5345 92.9569 46.0973 93.1938 46.8252 93.1938C47.2314 93.1938 47.5954 93.1325 47.917 93.0098C48.2386 92.8828 48.5369 92.6882 48.812 92.4258V93.6382ZM53.8203 87.7095C53.2279 87.7095 52.7793 87.9401 52.4746 88.4014C52.1699 88.8626 52.0176 89.5461 52.0176 90.4517C52.0176 91.353 52.1699 92.0365 52.4746 92.502C52.7793 92.9632 53.2279 93.1938 53.8203 93.1938C54.417 93.1938 54.8677 92.9632 55.1724 92.502C55.4771 92.0365 55.6294 91.353 55.6294 90.4517C55.6294 89.5461 55.4771 88.8626 55.1724 88.4014C54.8677 87.9401 54.417 87.7095 53.8203 87.7095ZM53.8203 86.7192C54.8063 86.7192 55.5596 87.0387 56.0801 87.6777C56.6048 88.3167 56.8672 89.2414 56.8672 90.4517C56.8672 91.6662 56.6069 92.5929 56.0864 93.2319C55.5659 93.8667 54.8105 94.1841 53.8203 94.1841C52.8343 94.1841 52.0811 93.8667 51.5605 93.2319C51.04 92.5929 50.7798 91.6662 50.7798 90.4517C50.7798 89.2414 51.04 88.3167 51.5605 87.6777C52.0811 87.0387 52.8343 86.7192 53.8203 86.7192ZM64.4146 89.5947V94H63.2402V89.5947C63.2402 88.9557 63.1281 88.486 62.9038 88.1855C62.6795 87.8851 62.3283 87.7349 61.8501 87.7349C61.3042 87.7349 60.8831 87.9295 60.5869 88.3188C60.2949 88.7039 60.1489 89.2583 60.1489 89.9819V94H58.981V86.8906H60.1489V87.957C60.3563 87.5508 60.6377 87.244 60.9932 87.0366C61.3486 86.825 61.7697 86.7192 62.2563 86.7192C62.98 86.7192 63.5195 86.9583 63.875 87.4365C64.2347 87.9105 64.4146 88.6299 64.4146 89.5947ZM71.7524 87.1382V88.2808C71.4181 88.0861 71.0817 87.9401 70.7432 87.8428C70.4046 87.7454 70.0597 87.6968 69.7085 87.6968C69.1795 87.6968 68.7839 87.7835 68.5215 87.957C68.2633 88.1263 68.1343 88.3866 68.1343 88.7378C68.1343 89.0552 68.2316 89.2922 68.4263 89.4487C68.6209 89.6053 69.1055 89.7576 69.8799 89.9058L70.3496 89.9946C70.9294 90.1047 71.3674 90.3247 71.6636 90.6548C71.964 90.9849 72.1143 91.4144 72.1143 91.9434C72.1143 92.6458 71.8646 93.196 71.3652 93.5938C70.8659 93.9873 70.1719 94.1841 69.2832 94.1841C68.932 94.1841 68.5638 94.146 68.1787 94.0698C67.7936 93.9979 67.3768 93.8879 66.9282 93.7397V92.5337C67.3641 92.758 67.7809 92.9272 68.1787 93.0415C68.5765 93.1515 68.9531 93.2065 69.3086 93.2065C69.8249 93.2065 70.2248 93.1029 70.5083 92.8955C70.7918 92.6839 70.9336 92.3898 70.9336 92.0132C70.9336 91.4715 70.4152 91.097 69.3784 90.8896L69.3276 90.877L68.8896 90.7881C68.2168 90.6569 67.7259 90.4368 67.417 90.1279C67.1081 89.8148 66.9536 89.3895 66.9536 88.8521C66.9536 88.1707 67.1842 87.646 67.6455 87.2778C68.1068 86.9054 68.7648 86.7192 69.6196 86.7192C70.0005 86.7192 70.3665 86.7552 70.7178 86.8271C71.069 86.8949 71.4139 86.9985 71.7524 87.1382ZM77.3193 87.7095C76.7269 87.7095 76.2783 87.9401 75.9736 88.4014C75.6689 88.8626 75.5166 89.5461 75.5166 90.4517C75.5166 91.353 75.6689 92.0365 75.9736 92.502C76.2783 92.9632 76.7269 93.1938 77.3193 93.1938C77.916 93.1938 78.3667 92.9632 78.6714 92.502C78.9761 92.0365 79.1284 91.353 79.1284 90.4517C79.1284 89.5461 78.9761 88.8626 78.6714 88.4014C78.3667 87.9401 77.916 87.7095 77.3193 87.7095ZM77.3193 86.7192C78.3053 86.7192 79.0586 87.0387 79.5791 87.6777C80.1038 88.3167 80.3662 89.2414 80.3662 90.4517C80.3662 91.6662 80.106 92.5929 79.5854 93.2319C79.0649 93.8667 78.3096 94.1841 77.3193 94.1841C76.3333 94.1841 75.5801 93.8667 75.0596 93.2319C74.5391 92.5929 74.2788 91.6662 74.2788 90.4517C74.2788 89.2414 74.5391 88.3167 75.0596 87.6777C75.5801 87.0387 76.3333 86.7192 77.3193 86.7192ZM85.8062 91.4292C85.8062 91.9539 85.9014 92.3496 86.0918 92.6162C86.2865 92.8828 86.5721 93.0161 86.9487 93.0161H88.3135V94H86.8345C86.1362 94 85.5946 93.7778 85.2095 93.3335C84.8286 92.8849 84.6382 92.2502 84.6382 91.4292V84.999H82.7656V84.085H85.8062V91.4292ZM96.1846 89.8613V90.4326H91.1255V90.4707C91.0916 91.4398 91.2757 92.1359 91.6777 92.5591C92.084 92.9823 92.6553 93.1938 93.3916 93.1938C93.764 93.1938 94.1533 93.1346 94.5596 93.0161C94.9658 92.8976 95.3996 92.7178 95.8608 92.4766V93.6382C95.4165 93.8201 94.987 93.9556 94.5723 94.0444C94.1618 94.1375 93.764 94.1841 93.3789 94.1841C92.2744 94.1841 91.4111 93.854 90.7891 93.1938C90.167 92.5295 89.856 91.6154 89.856 90.4517C89.856 89.3175 90.1606 88.4119 90.77 87.7349C91.3794 87.0578 92.1919 86.7192 93.2075 86.7192C94.1131 86.7192 94.8262 87.026 95.3467 87.6396C95.8714 88.2533 96.1507 88.9938 96.1846 89.8613ZM95.0166 89.5186C94.9658 89.0277 94.786 88.6045 94.4771 88.249C94.1724 87.8893 93.7323 87.7095 93.1567 87.7095C92.5939 87.7095 92.1305 87.8957 91.7666 88.2681C91.4027 88.6405 91.2038 89.0594 91.1699 89.5249L95.0166 89.5186Z" fill="#9CDCFE" />
        <path d="M99.3774 91.4355H101.612V94H99.3774V91.4355Z" fill="var(--vscode-editor-foreground, #FFFFFF)" />
        <path d="M109.305 91.4292C109.305 91.9539 109.4 92.3496 109.591 92.6162C109.785 92.8828 110.071 93.0161 110.448 93.0161H111.812V94H110.333C109.635 94 109.094 93.7778 108.708 93.3335C108.328 92.8849 108.137 92.2502 108.137 91.4292V84.999H106.265V84.085H109.305V91.4292ZM116.484 87.7095C115.892 87.7095 115.443 87.9401 115.139 88.4014C114.834 88.8626 114.682 89.5461 114.682 90.4517C114.682 91.353 114.834 92.0365 115.139 92.502C115.443 92.9632 115.892 93.1938 116.484 93.1938C117.081 93.1938 117.532 92.9632 117.836 92.502C118.141 92.0365 118.293 91.353 118.293 90.4517C118.293 89.5461 118.141 88.8626 117.836 88.4014C117.532 87.9401 117.081 87.7095 116.484 87.7095ZM116.484 86.7192C117.47 86.7192 118.224 87.0387 118.744 87.6777C119.269 88.3167 119.531 89.2414 119.531 90.4517C119.531 91.6662 119.271 92.5929 118.75 93.2319C118.23 93.8667 117.475 94.1841 116.484 94.1841C115.498 94.1841 114.745 93.8667 114.225 93.2319C113.704 92.5929 113.444 91.6662 113.444 90.4517C113.444 89.2414 113.704 88.3167 114.225 87.6777C114.745 87.0387 115.498 86.7192 116.484 86.7192ZM125.854 90.3882C125.854 89.5122 125.71 88.8478 125.422 88.395C125.138 87.938 124.724 87.7095 124.178 87.7095C123.606 87.7095 123.171 87.938 122.87 88.395C122.57 88.8478 122.419 89.5122 122.419 90.3882C122.419 91.2642 122.57 91.9328 122.87 92.394C123.175 92.8511 123.615 93.0796 124.19 93.0796C124.728 93.0796 125.138 92.849 125.422 92.3877C125.71 91.9264 125.854 91.2599 125.854 90.3882ZM127.021 93.543C127.021 94.6094 126.77 95.4176 126.266 95.9678C125.763 96.5179 125.022 96.793 124.044 96.793C123.723 96.793 123.386 96.7633 123.035 96.7041C122.684 96.6449 122.333 96.5581 121.981 96.4438V95.2886C122.396 95.4832 122.773 95.6271 123.111 95.7202C123.45 95.8133 123.761 95.8599 124.044 95.8599C124.675 95.8599 125.134 95.6885 125.422 95.3457C125.71 95.0029 125.854 94.4591 125.854 93.7144V93.6636V92.8701C125.667 93.2679 125.413 93.5641 125.092 93.7588C124.77 93.9535 124.379 94.0508 123.917 94.0508C123.088 94.0508 122.426 93.7186 121.931 93.0542C121.436 92.3898 121.188 91.5011 121.188 90.3882C121.188 89.271 121.436 88.3802 121.931 87.7158C122.426 87.0514 123.088 86.7192 123.917 86.7192C124.375 86.7192 124.762 86.8102 125.079 86.9922C125.396 87.1742 125.655 87.4556 125.854 87.8364V86.916H127.021V93.543Z" fill="#D7BA7D" />
      </g>
      <rect x="128.717" y="78" width="4.21333" height="22" fill="var(--vscode-editorCursor-foreground, #CCCCCC)" />
    </g>
    <rect opacity="0.5" x="35.7571" y="57" width="65.3067" height="8" rx="4" fill="var(--vscode-gitDecoration-renamedResourceForeground, #73C991)" />
    <rect opacity="0.5" x="107" y="57" width="142" height="8" rx="4" fill="var(--vscode-gitDecoration-renamedResourceForeground, #73C991)" />
  </g>
  <defs>
    <filter id="filter0_d_1720_12451" x="0" y="21" width="524" height="137" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix" />
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha" />
      <feOffset dy="2" />
      <feGaussianBlur stdDeviation="8" />
      <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.36 0" />
      <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1720_12451" />
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1720_12451" result="shape" />
    </filter>
  </defs>
</svg>
