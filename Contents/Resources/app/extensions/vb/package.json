{"name": "vb", "displayName": "%displayName%", "description": "%description%", "version": "1.0.0", "publisher": "vscode", "license": "MIT", "engines": {"vscode": "*"}, "scripts": {"update-grammar": "node ../node_modules/vscode-grammar-updater/bin textmate/asp.vb.net.tmbundle Syntaxes/ASP%20VB.net.plist ./syntaxes/asp-vb-net.tmLanguage.json"}, "categories": ["Programming Languages"], "contributes": {"languages": [{"id": "vb", "extensions": [".vb", ".brs", ".vbs", ".bas", ".vba"], "aliases": ["Visual Basic", "vb"], "configuration": "./language-configuration.json"}], "grammars": [{"language": "vb", "scopeName": "source.asp.vb.net", "path": "./syntaxes/asp-vb-net.tmLanguage.json"}], "snippets": [{"language": "vb", "path": "./snippets/vb.code-snippets"}]}, "repository": {"type": "git", "url": "https://github.com/microsoft/vscode.git"}}