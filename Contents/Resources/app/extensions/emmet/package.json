{"name": "emmet", "displayName": "<PERSON><PERSON>", "description": "%description%", "version": "1.0.0", "publisher": "vscode", "license": "MIT", "engines": {"vscode": "^1.13.0"}, "icon": "images/icon.png", "categories": ["Other"], "repository": {"type": "git", "url": "https://github.com/microsoft/vscode.git"}, "activationEvents": ["onCommand:emmet.expandAbbreviation", "onLanguage"], "main": "./dist/node/emmetNodeMain", "browser": "./dist/browser/emmetBrowserMain", "contributes": {"configuration": {"type": "object", "title": "<PERSON><PERSON>", "properties": {"emmet.showExpandedAbbreviation": {"type": ["string"], "enum": ["never", "always", "inMarkupAndStylesheetFilesOnly"], "default": "always", "markdownDescription": "%emmetShowExpandedAbbreviation%"}, "emmet.showAbbreviationSuggestions": {"type": "boolean", "default": true, "scope": "language-overridable", "markdownDescription": "%emmetShowAbbreviationSuggestions%"}, "emmet.includeLanguages": {"type": "object", "additionalProperties": {"type": "string"}, "default": {}, "markdownDescription": "%emmetIncludeLanguages%"}, "emmet.variables": {"type": "object", "properties": {"lang": {"type": "string", "default": "en"}, "charset": {"type": "string", "default": "UTF-8"}}, "additionalProperties": {"type": "string"}, "default": {}, "markdownDescription": "%emmetVariables%"}, "emmet.syntaxProfiles": {"type": "object", "default": {}, "markdownDescription": "%emmetSyntaxProfiles%"}, "emmet.excludeLanguages": {"type": "array", "items": {"type": "string"}, "default": ["markdown"], "markdownDescription": "%emmetExclude%"}, "emmet.extensionsPath": {"type": "array", "items": {"type": "string", "markdownDescription": "%emmetExtensionsPathItem%"}, "default": [], "scope": "machine-overridable", "markdownDescription": "%emmetExtensionsPath%"}, "emmet.triggerExpansionOnTab": {"type": "boolean", "default": false, "scope": "language-overridable", "markdownDescription": "%emmetTriggerExpansionOnTab%"}, "emmet.useInlineCompletions": {"type": "boolean", "default": false, "markdownDescription": "%emmetUseInlineCompletions%"}, "emmet.preferences": {"type": "object", "default": {}, "markdownDescription": "%emmetPreferences%", "properties": {"css.intUnit": {"type": "string", "default": "px", "markdownDescription": "%emmetPreferencesIntUnit%"}, "css.floatUnit": {"type": "string", "default": "em", "markdownDescription": "%emmetPreferencesFloatUnit%"}, "css.propertyEnd": {"type": "string", "default": ";", "markdownDescription": "%emmetPreferencesCssAfter%"}, "sass.propertyEnd": {"type": "string", "default": "", "markdownDescription": "%emmetPreferencesSassAfter%"}, "stylus.propertyEnd": {"type": "string", "default": "", "markdownDescription": "%emmetPreferencesStylusAfter%"}, "css.valueSeparator": {"type": "string", "default": ": ", "markdownDescription": "%emmetPreferencesCssBetween%"}, "sass.valueSeparator": {"type": "string", "default": ": ", "markdownDescription": "%emmetPreferencesSassBetween%"}, "stylus.valueSeparator": {"type": "string", "default": " ", "markdownDescription": "%emmetPreferencesStylusBetween%"}, "bem.elementSeparator": {"type": "string", "default": "__", "markdownDescription": "%emmetPreferencesBemElementSeparator%"}, "bem.modifierSeparator": {"type": "string", "default": "_", "markdownDescription": "%emmetPreferencesBemModifierSeparator%"}, "filter.commentBefore": {"type": "string", "default": "", "markdownDescription": "%emmetPreferencesFilterCommentBefore%"}, "filter.commentAfter": {"type": "string", "default": "\n<!-- /[#ID][.CLASS] -->", "markdownDescription": "%emmetPreferencesFilterCommentAfter%"}, "filter.commentTrigger": {"type": "array", "default": ["id", "class"], "markdownDescription": "%emmetPreferencesFilterCommentTrigger%"}, "format.noIndentTags": {"type": "array", "default": ["html"], "markdownDescription": "%emmetPreferencesFormatNoIndentTags%"}, "format.forceIndentationForTags": {"type": "array", "default": ["body"], "markdownDescription": "%emmetPreferencesFormatForceIndentTags%"}, "profile.allowCompactBoolean": {"type": "boolean", "default": false, "markdownDescription": "%emmetPreferencesAllowCompactBoolean%"}, "css.webkitProperties": {"type": "string", "default": null, "markdownDescription": "%emmetPreferencesCssWebkitProperties%"}, "css.mozProperties": {"type": "string", "default": null, "markdownDescription": "%emmetPreferencesCssMozProperties%"}, "css.oProperties": {"type": "string", "default": null, "markdownDescription": "%emmetPreferencesCssOProperties%"}, "css.msProperties": {"type": "string", "default": null, "markdownDescription": "%emmetPreferencesCssMsProperties%"}, "css.fuzzySearchMinScore": {"type": "number", "default": 0.3, "markdownDescription": "%emmetPreferencesCssFuzzySearchMinScore%"}, "output.inlineBreak": {"type": "number", "default": 0, "markdownDescription": "%emmetPreferencesOutputInlineBreak%"}, "output.reverseAttributes": {"type": "boolean", "default": false, "markdownDescription": "%emmetPreferencesOutputReverseAttributes%"}, "output.selfClosingStyle": {"type": "string", "enum": ["html", "xhtml", "xml"], "default": "html", "markdownDescription": "%emmetPreferencesOutputSelfClosingStyle%"}, "css.color.short": {"type": "boolean", "default": true, "markdownDescription": "%emmetPreferencesCssColorShort%"}}}, "emmet.showSuggestionsAsSnippets": {"type": "boolean", "default": false, "markdownDescription": "%emmetShowSuggestionsAsSnippets%"}, "emmet.optimizeStylesheetParsing": {"type": "boolean", "default": true, "markdownDescription": "%emmetOptimizeStylesheetParsing%"}}}, "commands": [{"command": "editor.emmet.action.wrapWithAbbreviation", "title": "%command.wrapWithAbbreviation%", "category": "<PERSON><PERSON>"}, {"command": "editor.emmet.action.removeTag", "title": "%command.removeTag%", "category": "<PERSON><PERSON>"}, {"command": "editor.emmet.action.updateTag", "title": "%command.updateTag%", "category": "<PERSON><PERSON>"}, {"command": "editor.emmet.action.matchTag", "title": "%command.matchTag%", "category": "<PERSON><PERSON>"}, {"command": "editor.emmet.action.balanceIn", "title": "%command.balanceIn%", "category": "<PERSON><PERSON>"}, {"command": "editor.emmet.action.balanceOut", "title": "%command.balanceOut%", "category": "<PERSON><PERSON>"}, {"command": "editor.emmet.action.prevEditPoint", "title": "%command.prevEditPoint%", "category": "<PERSON><PERSON>"}, {"command": "editor.emmet.action.nextEditPoint", "title": "%command.nextEditPoint%", "category": "<PERSON><PERSON>"}, {"command": "editor.emmet.action.mergeLines", "title": "%command.mergeLines%", "category": "<PERSON><PERSON>"}, {"command": "editor.emmet.action.selectPrevItem", "title": "%command.selectPrevItem%", "category": "<PERSON><PERSON>"}, {"command": "editor.emmet.action.selectNextItem", "title": "%command.selectNextItem%", "category": "<PERSON><PERSON>"}, {"command": "editor.emmet.action.splitJoinTag", "title": "%command.splitJoinTag%", "category": "<PERSON><PERSON>"}, {"command": "editor.emmet.action.toggleComment", "title": "%command.toggleComment%", "category": "<PERSON><PERSON>"}, {"command": "editor.emmet.action.evaluateMathExpression", "title": "%command.evaluateMathExpression%", "category": "<PERSON><PERSON>"}, {"command": "editor.emmet.action.updateImageSize", "title": "%command.updateImageSize%", "category": "<PERSON><PERSON>"}, {"command": "editor.emmet.action.incrementNumberByOneTenth", "title": "%command.incrementNumberByOneTenth%", "category": "<PERSON><PERSON>"}, {"command": "editor.emmet.action.incrementNumberByOne", "title": "%command.incrementNumberByOne%", "category": "<PERSON><PERSON>"}, {"command": "editor.emmet.action.incrementNumberByTen", "title": "%command.incrementNumberByTen%", "category": "<PERSON><PERSON>"}, {"command": "editor.emmet.action.decrementNumberByOneTenth", "title": "%command.decrementNumberByOneTenth%", "category": "<PERSON><PERSON>"}, {"command": "editor.emmet.action.decrementNumberByOne", "title": "%command.decrementNumberByOne%", "category": "<PERSON><PERSON>"}, {"command": "editor.emmet.action.decrementNumberByTen", "title": "%command.decrementNumberByTen%", "category": "<PERSON><PERSON>"}, {"command": "editor.emmet.action.reflectCSSValue", "title": "%command.reflectCSSValue%", "category": "<PERSON><PERSON>"}, {"command": "workbench.action.showEmmetCommands", "title": "%command.showEmmetCommands%", "category": ""}], "menus": {"commandPalette": [{"command": "editor.emmet.action.wrapWithAbbreviation", "when": "!activeEditorIsReadonly"}, {"command": "editor.emmet.action.removeTag", "when": "!activeEditorIsReadonly"}, {"command": "editor.emmet.action.updateTag", "when": "!activeEditorIsReadonly"}, {"command": "editor.emmet.action.matchTag", "when": "!activeEditorIsReadonly"}, {"command": "editor.emmet.action.balanceIn", "when": "!activeEditorIsReadonly"}, {"command": "editor.emmet.action.balanceOut", "when": "!activeEditorIsReadonly"}, {"command": "editor.emmet.action.prevEditPoint", "when": "!activeEditorIsReadonly"}, {"command": "editor.emmet.action.nextEditPoint", "when": "!activeEditorIsReadonly"}, {"command": "editor.emmet.action.mergeLines", "when": "!activeEditorIsReadonly"}, {"command": "editor.emmet.action.selectPrevItem", "when": "!activeEditorIsReadonly"}, {"command": "editor.emmet.action.selectNextItem", "when": "!activeEditorIsReadonly"}, {"command": "editor.emmet.action.splitJoinTag", "when": "!activeEditorIsReadonly"}, {"command": "editor.emmet.action.toggleComment", "when": "!activeEditorIsReadonly"}, {"command": "editor.emmet.action.evaluateMathExpression", "when": "!activeEditorIsReadonly"}, {"command": "editor.emmet.action.updateImageSize", "when": "!activeEditorIsReadonly"}, {"command": "editor.emmet.action.incrementNumberByOneTenth", "when": "!activeEditorIsReadonly"}, {"command": "editor.emmet.action.incrementNumberByOne", "when": "!activeEditorIsReadonly"}, {"command": "editor.emmet.action.incrementNumberByTen", "when": "!activeEditorIsReadonly"}, {"command": "editor.emmet.action.decrementNumberByOneTenth", "when": "!activeEditorIsReadonly"}, {"command": "editor.emmet.action.decrementNumberByOne", "when": "!activeEditorIsReadonly"}, {"command": "editor.emmet.action.decrementNumberByTen", "when": "!activeEditorIsReadonly"}, {"command": "editor.emmet.action.reflectCSSValue", "when": "!activeEditorIsReadonly"}]}}, "capabilities": {"virtualWorkspaces": true, "untrustedWorkspaces": {"supported": true}}}