{"$schema": "http://json-schema.org/draft-07/schema#", "type": "object", "properties": {"contributes": {"type": "object", "properties": {"markdown.previewStyles": {"type": "array", "description": "Contributed CSS files that change the look or layout of the Markdown preview", "items": {"type": "string", "description": "Extension relative path to a css file"}}, "markdown.previewScripts": {"type": "array", "description": "Contributed scripts that are executed in the Markdown preview", "items": {"type": "string", "description": "Extension relative path to a JavaScript file"}}, "markdown.markdownItPlugins": {"type": "boolean", "description": "Does this extension contribute a markdown-it plugin?"}}}}}