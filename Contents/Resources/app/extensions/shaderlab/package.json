{"name": "shaderlab", "displayName": "%displayName%", "description": "%description%", "version": "1.0.0", "publisher": "vscode", "license": "MIT", "engines": {"vscode": "*"}, "scripts": {"update-grammar": "node ../node_modules/vscode-grammar-updater/bin tgjones/shaders-tmLanguage grammars/shaderlab.json ./syntaxes/shaderlab.tmLanguage.json"}, "categories": ["Programming Languages"], "contributes": {"languages": [{"id": "shaderlab", "extensions": [".shader"], "aliases": ["ShaderLab", "shaderlab"], "configuration": "./language-configuration.json"}], "grammars": [{"language": "shaderlab", "path": "./syntaxes/shaderlab.tmLanguage.json", "scopeName": "source.shaderlab"}]}, "repository": {"type": "git", "url": "https://github.com/microsoft/vscode.git"}}