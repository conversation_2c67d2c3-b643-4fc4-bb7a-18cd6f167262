{"type": "dark", "colors": {"focusBorder": "#bbdaff", "errorForeground": "#a92049", "input.background": "#001733", "dropdown.background": "#001733", "quickInputList.focusBackground": "#ffffff60", "list.activeSelectionBackground": "#ffffff60", "list.inactiveSelectionBackground": "#ffffff40", "list.hoverBackground": "#ffffff30", "list.highlightForeground": "#bbdaff", "pickerGroup.foreground": "#bbdaff", "editor.background": "#002451", "editor.foreground": "#ffffff", "editor.selectionBackground": "#003f8e", "minimap.selectionHighlight": "#003f8e", "editor.lineHighlightBackground": "#00346e", "editorLineNumber.activeForeground": "#949494", "editorCursor.foreground": "#ffffff", "editorWhitespace.foreground": "#404f7d", "editorWidget.background": "#001c40", "editorHoverWidget.background": "#001c40", "editorHoverWidget.border": "#ffffff44", "editorGroup.border": "#404f7d", "editorGroupHeader.tabsBackground": "#001733", "editorGroup.dropBackground": "#25375daa", "peekViewResult.background": "#001c40", "tab.inactiveBackground": "#001c40", "tab.lastPinnedBorder": "#007acc80", "debugToolBar.background": "#001c40", "titleBar.activeBackground": "#001126", "statusBar.background": "#001126", "statusBarItem.remoteBackground": "#0e639c", "ports.iconRunningProcessForeground": "#bbdaff", "statusBar.noFolderBackground": "#001126", "statusBar.debuggingBackground": "#001126", "activityBar.background": "#001733", "progressBar.background": "#bbdaffcc", "badge.background": "#bbdaffcc", "badge.foreground": "#001733", "sideBar.background": "#001c40", "terminal.ansiBlack": "#111111", "terminal.ansiRed": "#ff9da4", "terminal.ansiGreen": "#d1f1a9", "terminal.ansiYellow": "#ffeead", "terminal.ansiBlue": "#bbdaff", "terminal.ansiMagenta": "#ebbbff", "terminal.ansiCyan": "#99ffff", "terminal.ansiWhite": "#cccccc", "terminal.ansiBrightBlack": "#333333", "terminal.ansiBrightRed": "#ff7882", "terminal.ansiBrightGreen": "#b8f171", "terminal.ansiBrightYellow": "#ffe580", "terminal.ansiBrightBlue": "#80baff", "terminal.ansiBrightMagenta": "#d778ff", "terminal.ansiBrightCyan": "#78ffff", "terminal.ansiBrightWhite": "#ffffff"}, "tokenColors": [{"settings": {"background": "#002451", "foreground": "#FFFFFF"}}, {"scope": ["meta.embedded", "source.groovy.embedded", "meta.jsx.children", "string meta.image.inline.markdown", "variable.legacy.builtin.python"], "settings": {"foreground": "#FFFFFF"}}, {"name": "Comment", "scope": "comment", "settings": {"foreground": "#7285B7"}}, {"name": "<PERSON><PERSON><PERSON>, Operator", "scope": "keyword.operator.class, keyword.operator, constant.other, source.php.embedded.line", "settings": {"fontStyle": "", "foreground": "#FFFFFF"}}, {"name": "Variable, String Link, Regular Expression, Tag Name, GitGutter deleted", "scope": "variable, support.other.variable, string.other.link, string.regexp, entity.name.tag, entity.other.attribute-name, meta.tag, declaration.tag, markup.deleted.git_gutter", "settings": {"foreground": "#FF9DA4"}}, {"name": "Number, Constant, Function Argument, Tag Attribute, Embedded", "scope": "constant.numeric, constant.language, support.constant, constant.character, variable.parameter, punctuation.section.embedded, keyword.other.unit", "settings": {"fontStyle": "", "foreground": "#FFC58F"}}, {"name": "Class, Support", "scope": "entity.name.class, entity.name.type, entity.name.namespace, entity.name.scope-resolution, support.type, support.class", "settings": {"fontStyle": "", "foreground": "#FFEEAD"}}, {"name": "String, Symbols, Inherited Class, <PERSON><PERSON>ing, <PERSON><PERSON><PERSON><PERSON> inserted", "scope": "string, constant.other.symbol, entity.other.inherited-class, punctuation.separator.namespace.ruby, markup.heading, markup.inserted.git_gutter", "settings": {"fontStyle": "", "foreground": "#D1F1A9"}}, {"name": "Operator, Misc", "scope": "keyword.operator, constant.other.color", "settings": {"foreground": "#99FFFF"}}, {"name": "Function, Special Method, Block Level, GitGutter changed", "scope": "entity.name.function, meta.function-call, support.function, keyword.other.special-method, meta.block-level, markup.changed.git_gutter", "settings": {"fontStyle": "", "foreground": "#BBDAFF"}}, {"name": "Keyword, Storage", "scope": "keyword, storage, storage.type, entity.name.tag.css, entity.name.tag.less", "settings": {"fontStyle": "", "foreground": "#EBBBFF"}}, {"name": "Invalid", "scope": "invalid", "settings": {"fontStyle": "", "foreground": "#a92049"}}, {"name": "Separator", "scope": "meta.separator", "settings": {"foreground": "#FFFFFF"}}, {"name": "Deprecated", "scope": "invalid.deprecated", "settings": {"fontStyle": "", "foreground": "#cd9731"}}, {"name": "Diff foreground", "scope": "markup.inserted.diff, markup.deleted.diff, meta.diff.header.to-file, meta.diff.header.from-file", "settings": {"foreground": "#FFFFFF"}}, {"name": "Diff insertion", "scope": "markup.inserted.diff, meta.diff.header.to-file", "settings": {"foreground": "#718c00"}}, {"name": "Diff deletion", "scope": "markup.deleted.diff, meta.diff.header.from-file", "settings": {"foreground": "#c82829"}}, {"name": "Diff header", "scope": "meta.diff.header.from-file, meta.diff.header.to-file", "settings": {"foreground": "#4271ae"}}, {"name": "Diff range", "scope": "meta.diff.range", "settings": {"fontStyle": "italic", "foreground": "#3e999f"}}, {"name": "<PERSON><PERSON> Quote", "scope": "markup.quote", "settings": {"foreground": "#FFC58F"}}, {"name": "Markup Lists", "scope": "markup.list", "settings": {"foreground": "#BBDAFF"}}, {"name": "<PERSON><PERSON>", "scope": "markup.bold, markup.italic", "settings": {"foreground": "#FFC58F"}}, {"name": "Markup: <PERSON>", "scope": "markup.bold", "settings": {"fontStyle": "bold"}}, {"name": "Markup: Emphasis", "scope": "markup.italic", "settings": {"fontStyle": "italic"}}, {"scope": "markup.strikethrough", "settings": {"fontStyle": "strikethrough"}}, {"name": "Markup Inline", "scope": "markup.inline.raw", "settings": {"fontStyle": "", "foreground": "#FF9DA4"}}, {"name": "<PERSON><PERSON>", "scope": "markup.heading", "settings": {"fontStyle": "bold"}}, {"scope": "token.info-token", "settings": {"foreground": "#6796e6"}}, {"scope": "token.warn-token", "settings": {"foreground": "#cd9731"}}, {"scope": "token.error-token", "settings": {"foreground": "#f44747"}}, {"scope": "token.debug-token", "settings": {"foreground": "#b267e6"}}], "semanticHighlighting": true}