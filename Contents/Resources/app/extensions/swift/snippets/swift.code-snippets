{"print": {"prefix": "print", "body": "print(\"$1\")\n$0", "description": "print(\"...\")"}, "print value": {"prefix": "printv", "body": "print(\"\\($1)\")\n$0", "description": "print(\"\\(...)\")"}, "while": {"prefix": "while", "body": ["while ${1:condition} {", "\t$0", "}"], "description": "while statement"}, "repeat-while": {"prefix": "repeat", "body": ["repeat {", "\t$0", "} while ${1:condition}"], "description": "repeat-while statement"}, "for": {"prefix": "for", "body": ["for ${1:item} in ${2:collection} {", "\t$0", "}"], "description": "for-in statement"}, "if": {"prefix": "if", "body": ["if ${1:condition} {", "\t$0", "}"], "description": "if statement"}, "else if": {"prefix": "elif", "body": ["else if ${1:condition} {", "\t$0", "}"], "description": "else clause with a nested if statement"}, "else": {"prefix": "else", "body": ["else {", "\t$0", "}"], "description": "else clause"}, "if let": {"prefix": "iflet", "body": ["if let ${1:value} = ${2:optional} {", "\t$0", "}"], "description": "if statement with optional binding"}, "guard": {"prefix": "guard", "body": ["guard ${1:condition} else {", "\t$0", "}"], "description": "guard statement"}, "guard let": {"prefix": "guardlet", "body": ["guard let ${1:value} = ${2:optional} else {", "\t$0", "}"], "description": "guard statement with optional binding"}, "switch": {"prefix": "switch", "body": ["switch ${1:value} {", "case ${2:pattern}:", "\t$0", "default:", "\t", "}"], "description": "switch statement"}, "do": {"prefix": "do", "body": ["do {", "\t$0", "} catch ${1:error} {", "\t$2", "}"], "description": "do statement"}, "func": {"prefix": "func", "body": ["func ${1:name}(${2:parameters}) -> ${3:Type} {", "\t$0", "}"], "description": "function declaration"}, "struct": {"prefix": "struct", "body": ["struct ${1:Name} {", "", "\t$0", "}"], "description": "struct declaration"}, "enum": {"prefix": "enum", "body": ["enum ${1:Name} {", "", "\tcase $0", "}"], "description": "enum declaration"}, "class": {"prefix": "class", "body": ["class ${1:Name} {", "", "\t$0", "}"], "description": "class declaration"}, "protocol": {"prefix": "protocol", "body": ["protocol ${1:Name} {", "", "\t$0", "}"], "description": "protocol declaration"}, "extension": {"prefix": "extension", "body": ["extension ${1:Type} {", "", "\t$0", "}"], "description": "extension declaration"}}