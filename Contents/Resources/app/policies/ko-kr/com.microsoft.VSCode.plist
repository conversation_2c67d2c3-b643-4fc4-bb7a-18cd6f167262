<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>pfm_app_url</key>
    <string>https://code.visualstudio.com/</string>
    <key>pfm_description</key>
    <string>Visual Studio Code Managed Settings</string>
    <key>pfm_documentation_url</key>
    <string>https://code.visualstudio.com/docs/setup/enterprise</string>
    <key>pfm_domain</key>
    <string>com.microsoft.VSCode</string>
    <key>pfm_format_version</key>
    <integer>1</integer>
    <key>pfm_interaction</key>
    <string>combined</string>
    <key>pfm_last_modified</key>
    <date>2025-08-12T16:47:15Z</date>
    <key>pfm_platforms</key>
    <array>
        <string>macOS</string>
    </array>
    <key>pfm_subkeys</key>
    <array>
	
		<dict>
			<key>pfm_default</key>
			<string>Configure Visual Studio Code</string>
			<key>pfm_name</key>
			<string>PayloadDescription</string>
			<key>pfm_title</key>
			<string>Payload Description</string>
			<key>pfm_type</key>
			<string>string</string>
		</dict>
		<dict>
			<key>pfm_default</key>
			<string>Visual Studio Code</string>
			<key>pfm_name</key>
			<string>PayloadDisplayName</string>
			<key>pfm_require</key>
			<string>always</string>
			<key>pfm_title</key>
			<string>Payload Display Name</string>
			<key>pfm_type</key>
			<string>string</string>
		</dict>
		<dict>
			<key>pfm_default</key>
			<string>com.microsoft.VSCode</string>
			<key>pfm_name</key>
			<string>PayloadIdentifier</string>
			<key>pfm_require</key>
			<string>always</string>
			<key>pfm_title</key>
			<string>Payload Identifier</string>
			<key>pfm_type</key>
			<string>string</string>
		</dict>
		<dict>
			<key>pfm_default</key>
			<string>com.microsoft.VSCode</string>
			<key>pfm_name</key>
			<string>PayloadType</string>
			<key>pfm_require</key>
			<string>always</string>
			<key>pfm_title</key>
			<string>Payload Type</string>
			<key>pfm_type</key>
			<string>string</string>
		</dict>
		<dict>
			<key>pfm_default</key>
			<string></string>
			<key>pfm_name</key>
			<string>PayloadUUID</string>
			<key>pfm_require</key>
			<string>always</string>
			<key>pfm_title</key>
			<string>Payload UUID</string>
			<key>pfm_type</key>
			<string>string</string>
		</dict>
		<dict>
			<key>pfm_default</key>
			<integer>1</integer>
			<key>pfm_name</key>
			<string>PayloadVersion</string>
			<key>pfm_range_list</key>
			<array>
				<integer>1</integer>
			</array>
			<key>pfm_require</key>
			<string>always</string>
			<key>pfm_title</key>
			<string>Payload Version</string>
			<key>pfm_type</key>
			<string>integer</string>
		</dict>
		<dict>
			<key>pfm_default</key>
			<string>Microsoft</string>
			<key>pfm_name</key>
			<string>PayloadOrganization</string>
			<key>pfm_title</key>
			<string>Payload Organization</string>
			<key>pfm_type</key>
			<string>string</string>
		</dict>
	<dict>
<key>pfm_default</key>
<string></string>
<key>pfm_description</key>
<string>연결할 Marketplace 서비스 URL 구성</string>
<key>pfm_name</key>
<string>ExtensionGalleryServiceUrl</string>
<key>pfm_title</key>
<string>ExtensionGalleryServiceUrl</string>
<key>pfm_type</key>
<string>string</string>
</dict><dict>
<key>pfm_default</key>
<false/>
<key>pfm_description</key>
<string>도구 사용을 자동으로 승인할지 여부를 제어합니다. 모든 도구가 사용자 확인 없이 자동으로 실행되도록 허용하여 터미널 자동 승인과 같은 도구별 설정을 재정의합니다. 주의해서 사용: 선택한 도구를 신중하게 검토하고 프롬프트 삽입의 가능한 원인에 대해 각별히 주의하세요!</string>
<key>pfm_name</key>
<string>ChatToolsAutoApprove</string>
<key>pfm_title</key>
<string>ChatToolsAutoApprove</string>
<key>pfm_type</key>
<string>boolean</string>
</dict><dict>
<key>pfm_default</key>
<false/>
<key>pfm_description</key>
<string>추가 도구와 기능을 제공하기 위해 모델 컨텍스트 프로토콜 서버와의 통합을 가능하게 합니다.</string>
<key>pfm_name</key>
<string>ChatMCP</string>
<key>pfm_title</key>
<string>ChatMCP</string>
<key>pfm_type</key>
<string>boolean</string>
</dict><dict>
<key>pfm_default</key>
<false/>
<key>pfm_description</key>
<string>타사 확장에서 제공하는 도구를 사용하여 사용하도록 설정합니다.</string>
<key>pfm_name</key>
<string>ChatAgentExtensionTools</string>
<key>pfm_title</key>
<string>ChatAgentExtensionTools</string>
<key>pfm_type</key>
<string>boolean</string>
</dict><dict>
<key>pfm_default</key>
<false/>
<key>pfm_description</key>
<string>{0}에 에이전트 모드를 사용하도록 설정합니다. 이 기능을 사용하도록 설정하면 보기의 드롭다운을 통해 에이전트 모드를 활성화할 수 있습니다.</string>
<key>pfm_name</key>
<string>ChatAgentMode</string>
<key>pfm_title</key>
<string>ChatAgentMode</string>
<key>pfm_type</key>
<string>boolean</string>
</dict><dict>
<key>pfm_default</key>
<string></string>
<key>pfm_description</key>
<string>MCP 갤러리 서비스 URL을 연결할 수 있도록 구성하세요.</string>
<key>pfm_name</key>
<string>McpGalleryServiceUrl</string>
<key>pfm_title</key>
<string>McpGalleryServiceUrl</string>
<key>pfm_type</key>
<string>string</string>
</dict><dict>
<key>pfm_default</key>
<false/>
<key>pfm_description</key>
<string>채팅 세션에서 재사용 가능한 프롬프트 및 명령 파일을 사용하도록 설정합니다.</string>
<key>pfm_name</key>
<string>ChatPromptFiles</string>
<key>pfm_title</key>
<string>ChatPromptFiles</string>
<key>pfm_type</key>
<string>boolean</string>
</dict><dict>
<key>pfm_default</key>
<string></string>
<key>pfm_description</key>
<string>원격 분석 수준을 제어합니다.</string>
<key>pfm_name</key>
<string>TelemetryLevel</string>
<key>pfm_title</key>
<string>TelemetryLevel</string>
<key>pfm_type</key>
<string>string</string>
</dict><dict>
<key>pfm_default</key>
<false/>
<key>pfm_description</key>
<string>Copilot Chat과 같은 기능에서 문제 보고자, 설문 조사, 피드백 옵션과 같은 피드백 메커니즘을 활성화합니다.</string>
<key>pfm_name</key>
<string>EnableFeedback</string>
<key>pfm_title</key>
<string>EnableFeedback</string>
<key>pfm_type</key>
<string>boolean</string>
</dict><dict>
<key>pfm_default</key>
<string></string>
<key>pfm_description</key>
<string>사용할 수 있는 확장 목록을 지정합니다. 이는 권한 없는 확장의 사용을 제한하여 안전하고 일관된 개발 환경을 유지하는 데 도움이 됩니다. 추가 정보: https://code.visualstudio.com/docs/setup/enterprise#_configure-allowed-extensions</string>
<key>pfm_name</key>
<string>AllowedExtensions</string>
<key>pfm_title</key>
<string>AllowedExtensions</string>
<key>pfm_type</key>
<string>string</string>

</dict><dict>
<key>pfm_default</key>
<string>none</string>
<key>pfm_description</key>
<string>자동 업데이트를 받을지 여부를 구성합니다. 변경 후 다시 시작해야 합니다. 업데이트는 Microsoft 온라인 서비스에서 가져옵니다.</string>
<key>pfm_name</key>
<string>UpdateMode</string>
<key>pfm_title</key>
<string>UpdateMode</string>
<key>pfm_type</key>
<string>string</string>
<key>pfm_range_list</key>
<array>
	<string>none</string>
	<string>manual</string>
	<string>start</string>
	<string>default</string>
</array>
</dict>
    </array>
    <key>pfm_title</key>
    <string>Visual Studio Code</string>
    <key>pfm_unique</key>
    <true/>
    <key>pfm_version</key>
    <integer>1</integer>
</dict>
</plist>