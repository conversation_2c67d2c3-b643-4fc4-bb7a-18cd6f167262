<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>pfm_app_url</key>
    <string>https://code.visualstudio.com/</string>
    <key>pfm_description</key>
    <string>Visual Studio Code Managed Settings</string>
    <key>pfm_documentation_url</key>
    <string>https://code.visualstudio.com/docs/setup/enterprise</string>
    <key>pfm_domain</key>
    <string>com.microsoft.VSCode</string>
    <key>pfm_format_version</key>
    <integer>1</integer>
    <key>pfm_interaction</key>
    <string>combined</string>
    <key>pfm_last_modified</key>
    <date>2025-08-12T16:47:15Z</date>
    <key>pfm_platforms</key>
    <array>
        <string>macOS</string>
    </array>
    <key>pfm_subkeys</key>
    <array>
	
		<dict>
			<key>pfm_default</key>
			<string>Configure Visual Studio Code</string>
			<key>pfm_name</key>
			<string>PayloadDescription</string>
			<key>pfm_title</key>
			<string>Payload Description</string>
			<key>pfm_type</key>
			<string>string</string>
		</dict>
		<dict>
			<key>pfm_default</key>
			<string>Visual Studio Code</string>
			<key>pfm_name</key>
			<string>PayloadDisplayName</string>
			<key>pfm_require</key>
			<string>always</string>
			<key>pfm_title</key>
			<string>Payload Display Name</string>
			<key>pfm_type</key>
			<string>string</string>
		</dict>
		<dict>
			<key>pfm_default</key>
			<string>com.microsoft.VSCode</string>
			<key>pfm_name</key>
			<string>PayloadIdentifier</string>
			<key>pfm_require</key>
			<string>always</string>
			<key>pfm_title</key>
			<string>Payload Identifier</string>
			<key>pfm_type</key>
			<string>string</string>
		</dict>
		<dict>
			<key>pfm_default</key>
			<string>com.microsoft.VSCode</string>
			<key>pfm_name</key>
			<string>PayloadType</string>
			<key>pfm_require</key>
			<string>always</string>
			<key>pfm_title</key>
			<string>Payload Type</string>
			<key>pfm_type</key>
			<string>string</string>
		</dict>
		<dict>
			<key>pfm_default</key>
			<string></string>
			<key>pfm_name</key>
			<string>PayloadUUID</string>
			<key>pfm_require</key>
			<string>always</string>
			<key>pfm_title</key>
			<string>Payload UUID</string>
			<key>pfm_type</key>
			<string>string</string>
		</dict>
		<dict>
			<key>pfm_default</key>
			<integer>1</integer>
			<key>pfm_name</key>
			<string>PayloadVersion</string>
			<key>pfm_range_list</key>
			<array>
				<integer>1</integer>
			</array>
			<key>pfm_require</key>
			<string>always</string>
			<key>pfm_title</key>
			<string>Payload Version</string>
			<key>pfm_type</key>
			<string>integer</string>
		</dict>
		<dict>
			<key>pfm_default</key>
			<string>Microsoft</string>
			<key>pfm_name</key>
			<string>PayloadOrganization</string>
			<key>pfm_title</key>
			<string>Payload Organization</string>
			<key>pfm_type</key>
			<string>string</string>
		</dict>
	<dict>
<key>pfm_default</key>
<string></string>
<key>pfm_description</key>
<string>Şuraya bağlanmak için Market hizmeti URL'sini yapılandır</string>
<key>pfm_name</key>
<string>ExtensionGalleryServiceUrl</string>
<key>pfm_title</key>
<string>ExtensionGalleryServiceUrl</string>
<key>pfm_type</key>
<string>string</string>
</dict><dict>
<key>pfm_default</key>
<false/>
<key>pfm_description</key>
<string>Araç kullanımının otomatik olarak onaylanıp onaylanmayacağını denetler. Tüm araçların kullanıcı onayı olmadan otomatik olarak çalışmasına olanak tanır ve terminal otomatik onayı gibi araçlara özgü ayarları geçersiz kılar. Dikkatli kullanın: Seçilen araçları dikkatlice gözden geçirin ve olası istem ekleme kaynaklarına karşı ekstra dikkatli olun!</string>
<key>pfm_name</key>
<string>ChatToolsAutoApprove</string>
<key>pfm_title</key>
<string>ChatToolsAutoApprove</string>
<key>pfm_type</key>
<string>boolean</string>
</dict><dict>
<key>pfm_default</key>
<false/>
<key>pfm_description</key>
<string>Model Bağlam Protokolü sunucularıyla tümleştirme sağlayarak ek araçlar ve işlevler sunar.</string>
<key>pfm_name</key>
<string>ChatMCP</string>
<key>pfm_title</key>
<string>ChatMCP</string>
<key>pfm_type</key>
<string>boolean</string>
</dict><dict>
<key>pfm_default</key>
<false/>
<key>pfm_description</key>
<string>Üçüncü taraf uzantıları tarafından sağlanan araçların kullanımını etkinleştirin.</string>
<key>pfm_name</key>
<string>ChatAgentExtensionTools</string>
<key>pfm_title</key>
<string>ChatAgentExtensionTools</string>
<key>pfm_type</key>
<string>boolean</string>
</dict><dict>
<key>pfm_default</key>
<false/>
<key>pfm_description</key>
<string>{0} için aracı modunu etkinleştirin. Etkinleştirildiğinde, aracı modu görünümdeki açılır listeden etkinleştirilebilir.</string>
<key>pfm_name</key>
<string>ChatAgentMode</string>
<key>pfm_title</key>
<string>ChatAgentMode</string>
<key>pfm_type</key>
<string>boolean</string>
</dict><dict>
<key>pfm_default</key>
<string></string>
<key>pfm_description</key>
<string>MCP Galeri hizmeti URL'sini şuna bağlanacak şekilde yapılandır</string>
<key>pfm_name</key>
<string>McpGalleryServiceUrl</string>
<key>pfm_title</key>
<string>McpGalleryServiceUrl</string>
<key>pfm_type</key>
<string>string</string>
</dict><dict>
<key>pfm_default</key>
<false/>
<key>pfm_description</key>
<string>Sohbet oturumlarında yeniden kullanılabilir istem ve yönerge dosyalarını etkinleştirin.</string>
<key>pfm_name</key>
<string>ChatPromptFiles</string>
<key>pfm_title</key>
<string>ChatPromptFiles</string>
<key>pfm_type</key>
<string>boolean</string>
</dict><dict>
<key>pfm_default</key>
<string></string>
<key>pfm_description</key>
<string>Telemetri düzeyini kontrol eder.</string>
<key>pfm_name</key>
<string>TelemetryLevel</string>
<key>pfm_title</key>
<string>TelemetryLevel</string>
<key>pfm_type</key>
<string>string</string>
</dict><dict>
<key>pfm_default</key>
<false/>
<key>pfm_description</key>
<string>Copilot Chat gibi özelliklerde sorun bildirici, anketler ve geri bildirim seçenekleri gibi geri bildirim mekanizmalarını etkinleştirin.</string>
<key>pfm_name</key>
<string>EnableFeedback</string>
<key>pfm_title</key>
<string>EnableFeedback</string>
<key>pfm_type</key>
<string>boolean</string>
</dict><dict>
<key>pfm_default</key>
<string></string>
<key>pfm_description</key>
<string>Kullanılmasına izin verilen uzantıların bir listesini belirtin. Bu, yetkisiz uzantıların kullanımını kısıtlayarak güvenli ve tutarlı bir geliştirme ortamının korunmasına yardımcı olur. Daha fazla bilgi: https://code.visualstudio.com/docs/setup/enterprise#_configure-allowed-extensions</string>
<key>pfm_name</key>
<string>AllowedExtensions</string>
<key>pfm_title</key>
<string>AllowedExtensions</string>
<key>pfm_type</key>
<string>string</string>

</dict><dict>
<key>pfm_default</key>
<string>none</string>
<key>pfm_description</key>
<string>Otomatik güncelleştirmeleri alıp almayacağınızı yapılandırın. Değişiklikten sonra yeniden başlatma gerektirir. Güncelleştirmeler bir Microsoft çevrimiçi hizmetinden getirilir.</string>
<key>pfm_name</key>
<string>UpdateMode</string>
<key>pfm_title</key>
<string>UpdateMode</string>
<key>pfm_type</key>
<string>string</string>
<key>pfm_range_list</key>
<array>
	<string>none</string>
	<string>manual</string>
	<string>start</string>
	<string>default</string>
</array>
</dict>
    </array>
    <key>pfm_title</key>
    <string>Visual Studio Code</string>
    <key>pfm_unique</key>
    <true/>
    <key>pfm_version</key>
    <integer>1</integer>
</dict>
</plist>