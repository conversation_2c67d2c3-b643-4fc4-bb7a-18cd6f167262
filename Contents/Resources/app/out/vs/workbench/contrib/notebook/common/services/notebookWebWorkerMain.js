/*!--------------------------------------------------------
 * Copyright (C) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------*/var Te=function(e,t){return Te=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,r){n.__proto__=r}||function(n,r){for(var i in r)Object.prototype.hasOwnProperty.call(r,i)&&(n[i]=r[i])},Te(e,t)};export function __extends(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");Te(e,t);function n(){this.constructor=e}e.prototype=t===null?Object.create(t):(n.prototype=t.prototype,new n)}export var __assign=function(){return __assign=Object.assign||function(t){for(var n,r=1,i=arguments.length;r<i;r++){n=arguments[r];for(var s in n)Object.prototype.hasOwnProperty.call(n,s)&&(t[s]=n[s])}return t},__assign.apply(this,arguments)};export function __rest(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var i=0,r=Object.getOwnPropertySymbols(e);i<r.length;i++)t.indexOf(r[i])<0&&Object.prototype.propertyIsEnumerable.call(e,r[i])&&(n[r[i]]=e[r[i]]);return n}export function __decorate(e,t,n,r){var i=arguments.length,s=i<3?t:r===null?r=Object.getOwnPropertyDescriptor(t,n):r,o;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")s=Reflect.decorate(e,t,n,r);else for(var l=e.length-1;l>=0;l--)(o=e[l])&&(s=(i<3?o(s):i>3?o(t,n,s):o(t,n))||s);return i>3&&s&&Object.defineProperty(t,n,s),s}export function __param(e,t){return function(n,r){t(n,r,e)}}export function __esDecorate(e,t,n,r,i,s){function o(w){if(w!==void 0&&typeof w!="function")throw new TypeError("Function expected");return w}for(var l=r.kind,u=l==="getter"?"get":l==="setter"?"set":"value",a=!t&&e?r.static?e:e.prototype:null,c=t||(a?Object.getOwnPropertyDescriptor(a,r.name):{}),h,f=!1,g=n.length-1;g>=0;g--){var m={};for(var p in r)m[p]=p==="access"?{}:r[p];for(var p in r.access)m.access[p]=r.access[p];m.addInitializer=function(w){if(f)throw new TypeError("Cannot add initializers after decoration has completed");s.push(o(w||null))};var d=(0,n[g])(l==="accessor"?{get:c.get,set:c.set}:c[u],m);if(l==="accessor"){if(d===void 0)continue;if(d===null||typeof d!="object")throw new TypeError("Object expected");(h=o(d.get))&&(c.get=h),(h=o(d.set))&&(c.set=h),(h=o(d.init))&&i.unshift(h)}else(h=o(d))&&(l==="field"?i.unshift(h):c[u]=h)}a&&Object.defineProperty(a,r.name,c),f=!0}export function __runInitializers(e,t,n){for(var r=arguments.length>2,i=0;i<t.length;i++)n=r?t[i].call(e,n):t[i].call(e);return r?n:void 0}export function __propKey(e){return typeof e=="symbol"?e:"".concat(e)}export function __setFunctionName(e,t,n){return typeof t=="symbol"&&(t=t.description?"[".concat(t.description,"]"):""),Object.defineProperty(e,"name",{configurable:!0,value:n?"".concat(n," ",t):t})}export function __metadata(e,t){if(typeof Reflect=="object"&&typeof Reflect.metadata=="function")return Reflect.metadata(e,t)}export function __awaiter(e,t,n,r){function i(s){return s instanceof n?s:new n(function(o){o(s)})}return new(n||(n=Promise))(function(s,o){function l(c){try{a(r.next(c))}catch(h){o(h)}}function u(c){try{a(r.throw(c))}catch(h){o(h)}}function a(c){c.done?s(c.value):i(c.value).then(l,u)}a((r=r.apply(e,t||[])).next())})}export function __generator(e,t){var n={label:0,sent:function(){if(s[0]&1)throw s[1];return s[1]},trys:[],ops:[]},r,i,s,o;return o={next:l(0),throw:l(1),return:l(2)},typeof Symbol=="function"&&(o[Symbol.iterator]=function(){return this}),o;function l(a){return function(c){return u([a,c])}}function u(a){if(r)throw new TypeError("Generator is already executing.");for(;o&&(o=0,a[0]&&(n=0)),n;)try{if(r=1,i&&(s=a[0]&2?i.return:a[0]?i.throw||((s=i.return)&&s.call(i),0):i.next)&&!(s=s.call(i,a[1])).done)return s;switch(i=0,s&&(a=[a[0]&2,s.value]),a[0]){case 0:case 1:s=a;break;case 4:return n.label++,{value:a[1],done:!1};case 5:n.label++,i=a[1],a=[0];continue;case 7:a=n.ops.pop(),n.trys.pop();continue;default:if(s=n.trys,!(s=s.length>0&&s[s.length-1])&&(a[0]===6||a[0]===2)){n=0;continue}if(a[0]===3&&(!s||a[1]>s[0]&&a[1]<s[3])){n.label=a[1];break}if(a[0]===6&&n.label<s[1]){n.label=s[1],s=a;break}if(s&&n.label<s[2]){n.label=s[2],n.ops.push(a);break}s[2]&&n.ops.pop(),n.trys.pop();continue}a=t.call(e,n)}catch(c){a=[6,c],i=0}finally{r=s=0}if(a[0]&5)throw a[1];return{value:a[0]?a[1]:void 0,done:!0}}}export var __createBinding=Object.create?function(e,t,n,r){r===void 0&&(r=n);var i=Object.getOwnPropertyDescriptor(t,n);(!i||("get"in i?!t.__esModule:i.writable||i.configurable))&&(i={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,i)}:function(e,t,n,r){r===void 0&&(r=n),e[r]=t[n]};export function __exportStar(e,t){for(var n in e)n!=="default"&&!Object.prototype.hasOwnProperty.call(t,n)&&__createBinding(t,e,n)}export function __values(e){var t=typeof Symbol=="function"&&Symbol.iterator,n=t&&e[t],r=0;if(n)return n.call(e);if(e&&typeof e.length=="number")return{next:function(){return e&&r>=e.length&&(e=void 0),{value:e&&e[r++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")}export function __read(e,t){var n=typeof Symbol=="function"&&e[Symbol.iterator];if(!n)return e;var r=n.call(e),i,s=[],o;try{for(;(t===void 0||t-- >0)&&!(i=r.next()).done;)s.push(i.value)}catch(l){o={error:l}}finally{try{i&&!i.done&&(n=r.return)&&n.call(r)}finally{if(o)throw o.error}}return s}export function __spread(){for(var e=[],t=0;t<arguments.length;t++)e=e.concat(__read(arguments[t]));return e}export function __spreadArrays(){for(var e=0,t=0,n=arguments.length;t<n;t++)e+=arguments[t].length;for(var r=Array(e),i=0,t=0;t<n;t++)for(var s=arguments[t],o=0,l=s.length;o<l;o++,i++)r[i]=s[o];return r}export function __spreadArray(e,t,n){if(n||arguments.length===2)for(var r=0,i=t.length,s;r<i;r++)(s||!(r in t))&&(s||(s=Array.prototype.slice.call(t,0,r)),s[r]=t[r]);return e.concat(s||Array.prototype.slice.call(t))}export function __await(e){return this instanceof __await?(this.v=e,this):new __await(e)}export function __asyncGenerator(e,t,n){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var r=n.apply(e,t||[]),i,s=[];return i={},l("next"),l("throw"),l("return",o),i[Symbol.asyncIterator]=function(){return this},i;function o(g){return function(m){return Promise.resolve(m).then(g,h)}}function l(g,m){r[g]&&(i[g]=function(p){return new Promise(function(d,w){s.push([g,p,d,w])>1||u(g,p)})},m&&(i[g]=m(i[g])))}function u(g,m){try{a(r[g](m))}catch(p){f(s[0][3],p)}}function a(g){g.value instanceof __await?Promise.resolve(g.value.v).then(c,h):f(s[0][2],g)}function c(g){u("next",g)}function h(g){u("throw",g)}function f(g,m){g(m),s.shift(),s.length&&u(s[0][0],s[0][1])}}export function __asyncDelegator(e){var t,n;return t={},r("next"),r("throw",function(i){throw i}),r("return"),t[Symbol.iterator]=function(){return this},t;function r(i,s){t[i]=e[i]?function(o){return(n=!n)?{value:__await(e[i](o)),done:!1}:s?s(o):o}:s}}export function __asyncValues(e){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var t=e[Symbol.asyncIterator],n;return t?t.call(e):(e=typeof __values=="function"?__values(e):e[Symbol.iterator](),n={},r("next"),r("throw"),r("return"),n[Symbol.asyncIterator]=function(){return this},n);function r(s){n[s]=e[s]&&function(o){return new Promise(function(l,u){o=e[s](o),i(l,u,o.done,o.value)})}}function i(s,o,l,u){Promise.resolve(u).then(function(a){s({value:a,done:l})},o)}}export function __makeTemplateObject(e,t){return Object.defineProperty?Object.defineProperty(e,"raw",{value:t}):e.raw=t,e}var Ei=Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t};export function __importStar(e){if(e&&e.__esModule)return e;var t={};if(e!=null)for(var n in e)n!=="default"&&Object.prototype.hasOwnProperty.call(e,n)&&__createBinding(t,e,n);return Ei(t,e),t}export function __importDefault(e){return e&&e.__esModule?e:{default:e}}export function __classPrivateFieldGet(e,t,n,r){if(n==="a"&&!r)throw new TypeError("Private accessor was defined without a getter");if(typeof t=="function"?e!==t||!r:!t.has(e))throw new TypeError("Cannot read private member from an object whose class did not declare it");return n==="m"?r:n==="a"?r.call(e):r?r.value:t.get(e)}export function __classPrivateFieldSet(e,t,n,r,i){if(r==="m")throw new TypeError("Private method is not writable");if(r==="a"&&!i)throw new TypeError("Private accessor was defined without a setter");if(typeof t=="function"?e!==t||!i:!t.has(e))throw new TypeError("Cannot write private member to an object whose class did not declare it");return r==="a"?i.call(e,n):i?i.value=n:t.set(e,n),n}export function __classPrivateFieldIn(e,t){if(t===null||typeof t!="object"&&typeof t!="function")throw new TypeError("Cannot use 'in' operator on non-object");return typeof e=="function"?t===e:e.has(t)}export function __addDisposableResource(e,t,n){if(t!=null){if(typeof t!="object"&&typeof t!="function")throw new TypeError("Object expected.");var r,i;if(n){if(!Symbol.asyncDispose)throw new TypeError("Symbol.asyncDispose is not defined.");r=t[Symbol.asyncDispose]}if(r===void 0){if(!Symbol.dispose)throw new TypeError("Symbol.dispose is not defined.");r=t[Symbol.dispose],n&&(i=r)}if(typeof r!="function")throw new TypeError("Object not disposable.");i&&(r=function(){try{i.call(this)}catch(s){return Promise.reject(s)}}),e.stack.push({value:t,dispose:r,async:n})}else n&&e.stack.push({async:!0});return t}var Ni=typeof SuppressedError=="function"?SuppressedError:function(e,t,n){var r=new Error(n);return r.name="SuppressedError",r.error=e,r.suppressed=t,r};export function __disposeResources(e){function t(r){e.error=e.hasError?new Ni(r,e.error,"An error was suppressed during disposal."):r,e.hasError=!0}function n(){for(;e.stack.length;){var r=e.stack.pop();try{var i=r.dispose&&r.dispose.call(r.value);if(r.async)return Promise.resolve(i).then(n,function(s){return t(s),n()})}catch(s){t(s)}}if(e.hasError)throw e.error}return n()}export default{__extends,__assign,__rest,__decorate,__param,__metadata,__awaiter,__generator,__createBinding,__exportStar,__values,__read,__spread,__spreadArrays,__spreadArray,__await,__asyncGenerator,__asyncDelegator,__asyncValues,__makeTemplateObject,__importStar,__importDefault,__classPrivateFieldGet,__classPrivateFieldSet,__classPrivateFieldIn,__addDisposableResource,__disposeResources};var Ii=class{constructor(){this.b=[],this.a=function(e){setTimeout(()=>{throw e.stack?We.isErrorNoTelemetry(e)?new We(e.message+`

`+e.stack):new Error(e.message+`

`+e.stack):e},0)}}addListener(e){return this.b.push(e),()=>{this.d(e)}}c(e){this.b.forEach(t=>{t(e)})}d(e){this.b.splice(this.b.indexOf(e),1)}setUnexpectedErrorHandler(e){this.a=e}getUnexpectedErrorHandler(){return this.a}onUnexpectedError(e){this.a(e),this.c(e)}onUnexpectedExternalError(e){this.a(e)}},Si=new Ii;function ge(e){$i(e)||Si.onUnexpectedError(e)}function Be(e){if(e instanceof Error){const{name:t,message:n,cause:r}=e,i=e.stacktrace||e.stack;return{$isError:!0,name:t,message:n,stack:i,noTelemetry:We.isErrorNoTelemetry(e),cause:r?Be(r):void 0,code:e.code}}return e}var Ve="Canceled";function $i(e){return e instanceof Oi?!0:e instanceof Error&&e.name===Ve&&e.message===Ve}var Oi=class extends Error{constructor(){super(Ve),this.name=this.message}},ul=class Pe extends Error{static{this.a="PendingMigrationError"}static is(t){return t instanceof Pe||t instanceof Error&&t.name===Pe.a}constructor(t){super(t),this.name=Pe.a}};function _i(e){return e?new Error(`Illegal state: ${e}`):new Error("Illegal state")}var We=class j1 extends Error{constructor(t){super(t),this.name="CodeExpectedError"}static fromError(t){if(t instanceof j1)return t;const n=new j1;return n.message=t.message,n.stack=t.stack,n}static isErrorNoTelemetry(t){return t.name==="CodeExpectedError"}},H1;function Mi(e,t){const n=Object.create(null);for(const r of e){const i=t(r);let s=n[i];s||(s=n[i]=[]),s.push(r)}return n}var hl=class{static{H1=Symbol.toStringTag}constructor(e,t){this.b=t,this.a=new Map,this[H1]="SetWithKey";for(const n of e)this.add(n)}get size(){return this.a.size}add(e){const t=this.b(e);return this.a.set(t,e),this}delete(e){return this.a.delete(this.b(e))}has(e){return this.a.has(this.b(e))}*entries(){for(const e of this.a.values())yield[e,e]}keys(){return this.values()}*values(){for(const e of this.a.values())yield e}clear(){this.a.clear()}forEach(e,t){this.a.forEach(n=>e.call(t,n,n,this))}[Symbol.iterator](){return this.values()}};function Di(e,t){const n=this;let r=!1,i;return function(){if(r)return i;if(r=!0,t)try{i=e.apply(n,arguments)}finally{t()}else i=e.apply(n,arguments);return i}}function Ri(e,t,n=0,r=e.length){let i=n,s=r;for(;i<s;){const o=Math.floor((i+s)/2);t(e[o])?i=o+1:s=o}return i-1}var cl=class ni{static{this.assertInvariants=!1}constructor(t){this.e=t,this.c=0}findLastMonotonous(t){if(ni.assertInvariants){if(this.d){for(const r of this.e)if(this.d(r)&&!t(r))throw new Error("MonotonousArray: current predicate must be weaker than (or equal to) the previous predicate.")}this.d=t}const n=Ri(this.e,t,this.c);return this.c=n+1,n===-1?void 0:this.e[n]}},He;(function(e){function t(s){return s<0}e.isLessThan=t;function n(s){return s<=0}e.isLessThanOrEqual=n;function r(s){return s>0}e.isGreaterThan=r;function i(s){return s===0}e.isNeitherLessOrGreaterThan=i,e.greaterThan=1,e.lessThan=-1,e.neitherLessOrGreaterThan=0})(He||(He={}));function Pi(e,t){return(n,r)=>t(e(n),e(r))}var ki=(e,t)=>e-t,fl=class ke{static{this.empty=new ke(t=>{})}constructor(t){this.iterate=t}forEach(t){this.iterate(n=>(t(n),!0))}toArray(){const t=[];return this.iterate(n=>(t.push(n),!0)),t}filter(t){return new ke(n=>this.iterate(r=>t(r)?n(r):!0))}map(t){return new ke(n=>this.iterate(r=>n(t(r))))}some(t){let n=!1;return this.iterate(r=>(n=t(r),!n)),n}findFirst(t){let n;return this.iterate(r=>t(r)?(n=r,!1):!0),n}findLast(t){let n;return this.iterate(r=>(t(r)&&(n=r),!0)),n}findLastMaxBy(t){let n,r=!0;return this.iterate(i=>((r||He.isGreaterThan(t(i,n)))&&(r=!1,n=i),!0)),n}},G1,K1,J1,Fi=class{constructor(e,t){this.uri=e,this.value=t}};function qi(e){return Array.isArray(e)}var Ge=class oe{static{this.c=t=>t.toString()}constructor(t,n){if(this[G1]="ResourceMap",t instanceof oe)this.d=new Map(t.d),this.e=n??oe.c;else if(qi(t)){this.d=new Map,this.e=n??oe.c;for(const[r,i]of t)this.set(r,i)}else this.d=new Map,this.e=t??oe.c}set(t,n){return this.d.set(this.e(t),new Fi(t,n)),this}get(t){return this.d.get(this.e(t))?.value}has(t){return this.d.has(this.e(t))}get size(){return this.d.size}clear(){this.d.clear()}delete(t){return this.d.delete(this.e(t))}forEach(t,n){typeof n<"u"&&(t=t.bind(n));for(const[r,i]of this.d)t(i.value,i.uri,this)}*values(){for(const t of this.d.values())yield t.value}*keys(){for(const t of this.d.values())yield t.uri}*entries(){for(const t of this.d.values())yield[t.uri,t.value]}*[(G1=Symbol.toStringTag,Symbol.iterator)](){for(const[,t]of this.d)yield[t.uri,t.value]}},dl=class{constructor(e,t){this[K1]="ResourceSet",!e||typeof e=="function"?this.c=new Ge(e):(this.c=new Ge(t),e.forEach(this.add,this))}get size(){return this.c.size}add(e){return this.c.set(e,e),this}clear(){this.c.clear()}delete(e){return this.c.delete(e)}forEach(e,t){this.c.forEach((n,r)=>e.call(t,r,r,this))}has(e){return this.c.has(e)}entries(){return this.c.entries()}keys(){return this.c.keys()}values(){return this.c.keys()}[(K1=Symbol.toStringTag,Symbol.iterator)](){return this.keys()}},X1;(function(e){e[e.None=0]="None",e[e.AsOld=1]="AsOld",e[e.AsNew=2]="AsNew"})(X1||(X1={}));var Ui=class{constructor(){this[J1]="LinkedMap",this.c=new Map,this.d=void 0,this.e=void 0,this.f=0,this.g=0}clear(){this.c.clear(),this.d=void 0,this.e=void 0,this.f=0,this.g++}isEmpty(){return!this.d&&!this.e}get size(){return this.f}get first(){return this.d?.value}get last(){return this.e?.value}has(e){return this.c.has(e)}get(e,t=0){const n=this.c.get(e);if(n)return t!==0&&this.n(n,t),n.value}set(e,t,n=0){let r=this.c.get(e);if(r)r.value=t,n!==0&&this.n(r,n);else{switch(r={key:e,value:t,next:void 0,previous:void 0},n){case 0:this.l(r);break;case 1:this.k(r);break;case 2:this.l(r);break;default:this.l(r);break}this.c.set(e,r),this.f++}return this}delete(e){return!!this.remove(e)}remove(e){const t=this.c.get(e);if(t)return this.c.delete(e),this.m(t),this.f--,t.value}shift(){if(!this.d&&!this.e)return;if(!this.d||!this.e)throw new Error("Invalid list");const e=this.d;return this.c.delete(e.key),this.m(e),this.f--,e.value}forEach(e,t){const n=this.g;let r=this.d;for(;r;){if(t?e.bind(t)(r.value,r.key,this):e(r.value,r.key,this),this.g!==n)throw new Error("LinkedMap got modified during iteration.");r=r.next}}keys(){const e=this,t=this.g;let n=this.d;const r={[Symbol.iterator](){return r},next(){if(e.g!==t)throw new Error("LinkedMap got modified during iteration.");if(n){const i={value:n.key,done:!1};return n=n.next,i}else return{value:void 0,done:!0}}};return r}values(){const e=this,t=this.g;let n=this.d;const r={[Symbol.iterator](){return r},next(){if(e.g!==t)throw new Error("LinkedMap got modified during iteration.");if(n){const i={value:n.value,done:!1};return n=n.next,i}else return{value:void 0,done:!0}}};return r}entries(){const e=this,t=this.g;let n=this.d;const r={[Symbol.iterator](){return r},next(){if(e.g!==t)throw new Error("LinkedMap got modified during iteration.");if(n){const i={value:[n.key,n.value],done:!1};return n=n.next,i}else return{value:void 0,done:!0}}};return r}[(J1=Symbol.toStringTag,Symbol.iterator)](){return this.entries()}h(e){if(e>=this.size)return;if(e===0){this.clear();return}let t=this.d,n=this.size;for(;t&&n>e;)this.c.delete(t.key),t=t.next,n--;this.d=t,this.f=n,t&&(t.previous=void 0),this.g++}j(e){if(e>=this.size)return;if(e===0){this.clear();return}let t=this.e,n=this.size;for(;t&&n>e;)this.c.delete(t.key),t=t.previous,n--;this.e=t,this.f=n,t&&(t.next=void 0),this.g++}k(e){if(!this.d&&!this.e)this.e=e;else if(this.d)e.next=this.d,this.d.previous=e;else throw new Error("Invalid list");this.d=e,this.g++}l(e){if(!this.d&&!this.e)this.d=e;else if(this.e)e.previous=this.e,this.e.next=e;else throw new Error("Invalid list");this.e=e,this.g++}m(e){if(e===this.d&&e===this.e)this.d=void 0,this.e=void 0;else if(e===this.d){if(!e.next)throw new Error("Invalid list");e.next.previous=void 0,this.d=e.next}else if(e===this.e){if(!e.previous)throw new Error("Invalid list");e.previous.next=void 0,this.e=e.previous}else{const t=e.next,n=e.previous;if(!t||!n)throw new Error("Invalid list");t.previous=n,n.next=t}e.next=void 0,e.previous=void 0,this.g++}n(e,t){if(!this.d||!this.e)throw new Error("Invalid list");if(!(t!==1&&t!==2)){if(t===1){if(e===this.d)return;const n=e.next,r=e.previous;e===this.e?(r.next=void 0,this.e=r):(n.previous=r,r.next=n),e.previous=void 0,e.next=this.d,this.d.previous=e,this.d=e,this.g++}else if(t===2){if(e===this.e)return;const n=e.next,r=e.previous;e===this.d?(n.previous=void 0,this.d=n):(n.previous=r,r.next=n),e.next=void 0,e.previous=this.e,this.e.next=e,this.e=e,this.g++}}}toJSON(){const e=[];return this.forEach((t,n)=>{e.push([n,t])}),e}fromJSON(e){this.clear();for(const[t,n]of e)this.set(t,n)}},ji=class extends Ui{constructor(e,t=1){super(),this.o=e,this.p=Math.min(Math.max(0,t),1)}get limit(){return this.o}set limit(e){this.o=e,this.q()}get ratio(){return this.p}set ratio(e){this.p=Math.min(Math.max(0,e),1),this.q()}get(e,t=2){return super.get(e,t)}peek(e){return super.get(e,0)}set(e,t){return super.set(e,t,2),this}q(){this.size>this.o&&this.r(Math.round(this.o*this.p))}},Q1=class extends ji{constructor(e,t=1){super(e,t)}r(e){this.h(e)}set(e,t){return super.set(e,t),this.q(),this}},zi=class{constructor(){this.c=new Map}add(e,t){let n=this.c.get(e);n||(n=new Set,this.c.set(e,n)),n.add(t)}delete(e,t){const n=this.c.get(e);n&&(n.delete(t),n.size===0&&this.c.delete(e))}forEach(e,t){const n=this.c.get(e);n&&n.forEach(t)}get(e){const t=this.c.get(e);return t||new Set}};function Ti(e){return!!e&&typeof e[Symbol.iterator]=="function"}var me;(function(e){function t(C){return!!C&&typeof C=="object"&&typeof C[Symbol.iterator]=="function"}e.is=t;const n=Object.freeze([]);function r(){return n}e.empty=r;function*i(C){yield C}e.single=i;function s(C){return t(C)?C:i(C)}e.wrap=s;function o(C){return C||n}e.from=o;function*l(C){for(let v=C.length-1;v>=0;v--)yield C[v]}e.reverse=l;function u(C){return!C||C[Symbol.iterator]().next().done===!0}e.isEmpty=u;function a(C){return C[Symbol.iterator]().next().value}e.first=a;function c(C,v){let I=0;for(const q of C)if(v(q,I++))return!0;return!1}e.some=c;function h(C,v){let I=0;for(const q of C)if(!v(q,I++))return!1;return!0}e.every=h;function f(C,v){for(const I of C)if(v(I))return I}e.find=f;function*g(C,v){for(const I of C)v(I)&&(yield I)}e.filter=g;function*m(C,v){let I=0;for(const q of C)yield v(q,I++)}e.map=m;function*p(C,v){let I=0;for(const q of C)yield*v(q,I++)}e.flatMap=p;function*d(...C){for(const v of C)Ti(v)?yield*v:yield v}e.concat=d;function w(C,v,I){let q=I;for(const R of C)q=v(q,R);return q}e.reduce=w;function b(C){let v=0;for(const I of C)v++;return v}e.length=b;function*L(C,v,I=C.length){for(v<-C.length&&(v=0),v<0&&(v+=C.length),I<0?I+=C.length:I>C.length&&(I=C.length);v<I;v++)yield C[v]}e.slice=L;function D(C,v=Number.POSITIVE_INFINITY){const I=[];if(v===0)return[I,C];const q=C[Symbol.iterator]();for(let R=0;R<v;R++){const O=q.next();if(O.done)return[I,e.empty()];I.push(O.value)}return[I,{[Symbol.iterator](){return q}}]}e.consume=D;async function $(C){const v=[];for await(const I of C)v.push(I);return v}e.asyncToArray=$;async function z(C){let v=[];for await(const I of C)v=v.concat(I);return v}e.asyncToArrayFlat=z})(me||(me={}));var Bi=!1,jt=null,gl=class ri{constructor(){this.b=new Map}static{this.a=0}c(t){let n=this.b.get(t);return n||(n={parent:null,source:null,isSingleton:!1,value:t,idx:ri.a++},this.b.set(t,n)),n}trackDisposable(t){const n=this.c(t);n.source||(n.source=new Error().stack)}setParent(t,n){const r=this.c(t);r.parent=n}markAsDisposed(t){this.b.delete(t)}markAsSingleton(t){this.c(t).isSingleton=!0}f(t,n){const r=n.get(t);if(r)return r;const i=t.parent?this.f(this.c(t.parent),n):t;return n.set(t,i),i}getTrackedDisposables(){const t=new Map;return[...this.b.entries()].filter(([,r])=>r.source!==null&&!this.f(r,t).isSingleton).flatMap(([r])=>r)}computeLeakingDisposables(t=10,n){let r;if(n)r=n;else{const u=new Map,a=[...this.b.values()].filter(h=>h.source!==null&&!this.f(h,u).isSingleton);if(a.length===0)return;const c=new Set(a.map(h=>h.value));if(r=a.filter(h=>!(h.parent&&c.has(h.parent))),r.length===0)throw new Error("There are cyclic diposable chains!")}if(!r)return;function i(u){function a(h,f){for(;h.length>0&&f.some(g=>typeof g=="string"?g===h[0]:h[0].match(g));)h.shift()}const c=u.source.split(`
`).map(h=>h.trim().replace("at ","")).filter(h=>h!=="");return a(c,["Error",/^trackDisposable \(.*\)$/,/^DisposableTracker.trackDisposable \(.*\)$/]),c.reverse()}const s=new zi;for(const u of r){const a=i(u);for(let c=0;c<=a.length;c++)s.add(a.slice(0,c).join(`
`),u)}r.sort(Pi(u=>u.idx,ki));let o="",l=0;for(const u of r.slice(0,t)){l++;const a=i(u),c=[];for(let h=0;h<a.length;h++){let f=a[h];f=`(shared with ${s.get(a.slice(0,h+1).join(`
`)).size}/${r.length} leaks) at ${f}`;const m=s.get(a.slice(0,h).join(`
`)),p=Mi([...m].map(d=>i(d)[h]),d=>d);delete p[a[h]];for(const[d,w]of Object.entries(p))c.unshift(`    - stacktraces of ${w.length} other leaks continue with ${d}`);c.unshift(f)}o+=`


==================== Leaking disposable ${l}/${r.length}: ${u.value.constructor.name} ====================
${c.join(`
`)}
============================================================

`}return r.length>t&&(o+=`


... and ${r.length-t} more leaking disposables

`),{leaks:r,details:o}}};function Vi(e){jt=e}if(Bi){const e="__is_disposable_tracked__";Vi(new class{trackDisposable(t){const n=new Error("Potentially leaked disposable").stack;setTimeout(()=>{t[e]||console.log(n)},3e3)}setParent(t,n){if(t&&t!==zt.None)try{t[e]=!0}catch{}}markAsDisposed(t){if(t&&t!==zt.None)try{t[e]=!0}catch{}}markAsSingleton(t){}})}function Ke(e){return jt?.trackDisposable(e),e}function Je(e){jt?.markAsDisposed(e)}function Xe(e,t){jt?.setParent(e,t)}function Wi(e,t){if(jt)for(const n of e)jt.setParent(n,t)}function Z1(e){if(me.is(e)){const t=[];for(const n of e)if(n)try{n.dispose()}catch(r){t.push(r)}if(t.length===1)throw t[0];if(t.length>1)throw new AggregateError(t,"Encountered errors while disposing of store");return Array.isArray(e)?[]:e}else if(e)return e.dispose(),e}function Hi(...e){const t=Y1(()=>Z1(e));return Wi(e,t),t}function Y1(e){const t=Ke({dispose:Di(()=>{Je(t),e()})});return t}var Qe=class ii{static{this.DISABLE_DISPOSED_WARNING=!1}constructor(){this.f=new Set,this.g=!1,Ke(this)}dispose(){this.g||(Je(this),this.g=!0,this.clear())}get isDisposed(){return this.g}clear(){if(this.f.size!==0)try{Z1(this.f)}finally{this.f.clear()}}add(t){if(!t)return t;if(t===this)throw new Error("Cannot register a disposable on itself!");return Xe(t,this),this.g?ii.DISABLE_DISPOSED_WARNING||console.warn(new Error("Trying to add a disposable to a DisposableStore that has already been disposed of. The added object will be leaked!").stack):this.f.add(t),t}delete(t){if(t){if(t===this)throw new Error("Cannot dispose a disposable on itself!");this.f.delete(t),t.dispose()}}deleteAndLeak(t){t&&this.f.has(t)&&(this.f.delete(t),Xe(t,null))}},zt=class{static{this.None=Object.freeze({dispose(){}})}constructor(){this.q=new Qe,Ke(this),Xe(this.q,this)}dispose(){Je(this),this.q.dispose()}B(e){if(e===this)throw new Error("Cannot register a disposable on itself!");return this.q.add(e)}},T=class Fe{static{this.Undefined=new Fe(void 0)}constructor(t){this.element=t,this.next=Fe.Undefined,this.prev=Fe.Undefined}},Gi=class{constructor(){this.a=T.Undefined,this.b=T.Undefined,this.c=0}get size(){return this.c}isEmpty(){return this.a===T.Undefined}clear(){let e=this.a;for(;e!==T.Undefined;){const t=e.next;e.prev=T.Undefined,e.next=T.Undefined,e=t}this.a=T.Undefined,this.b=T.Undefined,this.c=0}unshift(e){return this.d(e,!1)}push(e){return this.d(e,!0)}d(e,t){const n=new T(e);if(this.a===T.Undefined)this.a=n,this.b=n;else if(t){const i=this.b;this.b=n,n.prev=i,i.next=n}else{const i=this.a;this.a=n,n.next=i,i.prev=n}this.c+=1;let r=!1;return()=>{r||(r=!0,this.e(n))}}shift(){if(this.a!==T.Undefined){const e=this.a.element;return this.e(this.a),e}}pop(){if(this.b!==T.Undefined){const e=this.b.element;return this.e(this.b),e}}e(e){if(e.prev!==T.Undefined&&e.next!==T.Undefined){const t=e.prev;t.next=e.next,e.next.prev=t}else e.prev===T.Undefined&&e.next===T.Undefined?(this.a=T.Undefined,this.b=T.Undefined):e.next===T.Undefined?(this.b=this.b.prev,this.b.next=T.Undefined):e.prev===T.Undefined&&(this.a=this.a.next,this.a.prev=T.Undefined);this.c-=1}*[Symbol.iterator](){let e=this.a;for(;e!==T.Undefined;)yield e.element,e=e.next}},Ki=globalThis.performance.now.bind(globalThis.performance),Ji=class si{static create(t){return new si(t)}constructor(t){this.c=t===!1?Date.now:Ki,this.a=this.c(),this.b=-1}stop(){this.b=this.c()}reset(){this.a=this.c(),this.b=-1}elapsed(){return this.b!==-1?this.b-this.a:this.c()-this.a}},tn=!1,Xi=!1,pe;(function(e){e.None=()=>zt.None;function t(N){if(Xi){const{onDidAddListener:y}=N,E=Ze.create();let A=0;N.onDidAddListener=()=>{++A===2&&(console.warn("snapshotted emitter LIKELY used public and SHOULD HAVE BEEN created with DisposableStore. snapshotted here"),E.print()),y?.()}}}function n(N,y){return g(N,()=>{},0,void 0,!0,void 0,y)}e.defer=n;function r(N){return(y,E=null,A)=>{let S=!1,M;return M=N(k=>{if(!S)return M?M.dispose():S=!0,y.call(E,k)},null,A),S&&M.dispose(),M}}e.once=r;function i(N,y){return e.once(e.filter(N,y))}e.onceIf=i;function s(N,y,E){return h((A,S=null,M)=>N(k=>A.call(S,y(k)),null,M),E)}e.map=s;function o(N,y,E){return h((A,S=null,M)=>N(k=>{y(k),A.call(S,k)},null,M),E)}e.forEach=o;function l(N,y,E){return h((A,S=null,M)=>N(k=>y(k)&&A.call(S,k),null,M),E)}e.filter=l;function u(N){return N}e.signal=u;function a(...N){return(y,E=null,A)=>{const S=Hi(...N.map(M=>M(k=>y.call(E,k))));return f(S,A)}}e.any=a;function c(N,y,E,A){let S=E;return s(N,M=>(S=y(S,M),S),A)}e.reduce=c;function h(N,y){let E;const A={onWillAddFirstListener(){E=N(S.fire,S)},onDidRemoveLastListener(){E?.dispose()}};y||t(A);const S=new ht(A);return y?.add(S),S.event}function f(N,y){return y instanceof Array?y.push(N):y&&y.add(N),N}function g(N,y,E=100,A=!1,S=!1,M,k){let G,X,_t,fe=0,Jt;const W1={leakWarningThreshold:M,onWillAddFirstListener(){G=N(Ai=>{fe++,X=y(X,Ai),A&&!_t&&(de.fire(X),X=void 0),Jt=()=>{const xi=X;X=void 0,_t=void 0,(!A||fe>1)&&de.fire(xi),fe=0},typeof E=="number"?(_t&&clearTimeout(_t),_t=setTimeout(Jt,E)):_t===void 0&&(_t=null,queueMicrotask(Jt))})},onWillRemoveListener(){S&&fe>0&&Jt?.()},onDidRemoveLastListener(){Jt=void 0,G.dispose()}};k||t(W1);const de=new ht(W1);return k?.add(de),de.event}e.debounce=g;function m(N,y=0,E){return e.debounce(N,(A,S)=>A?(A.push(S),A):[S],y,void 0,!0,void 0,E)}e.accumulate=m;function p(N,y=(A,S)=>A===S,E){let A=!0,S;return l(N,M=>{const k=A||!y(M,S);return A=!1,S=M,k},E)}e.latch=p;function d(N,y,E){return[e.filter(N,y,E),e.filter(N,A=>!y(A),E)]}e.split=d;function w(N,y=!1,E=[],A){let S=E.slice(),M=N(X=>{S?S.push(X):G.fire(X)});A&&A.add(M);const k=()=>{S?.forEach(X=>G.fire(X)),S=null},G=new ht({onWillAddFirstListener(){M||(M=N(X=>G.fire(X)),A&&A.add(M))},onDidAddFirstListener(){S&&(y?setTimeout(k):k())},onDidRemoveLastListener(){M&&M.dispose(),M=null}});return A&&A.add(G),G.event}e.buffer=w;function b(N,y){return(A,S,M)=>{const k=y(new D);return N(function(G){const X=k.evaluate(G);X!==L&&A.call(S,X)},void 0,M)}}e.chain=b;const L=Symbol("HaltChainable");class D{constructor(){this.f=[]}map(y){return this.f.push(y),this}forEach(y){return this.f.push(E=>(y(E),E)),this}filter(y){return this.f.push(E=>y(E)?E:L),this}reduce(y,E){let A=E;return this.f.push(S=>(A=y(A,S),A)),this}latch(y=(E,A)=>E===A){let E=!0,A;return this.f.push(S=>{const M=E||!y(S,A);return E=!1,A=S,M?S:L}),this}evaluate(y){for(const E of this.f)if(y=E(y),y===L)break;return y}}function $(N,y,E=A=>A){const A=(...G)=>k.fire(E(...G)),S=()=>N.on(y,A),M=()=>N.removeListener(y,A),k=new ht({onWillAddFirstListener:S,onDidRemoveLastListener:M});return k.event}e.fromNodeEventEmitter=$;function z(N,y,E=A=>A){const A=(...G)=>k.fire(E(...G)),S=()=>N.addEventListener(y,A),M=()=>N.removeEventListener(y,A),k=new ht({onWillAddFirstListener:S,onDidRemoveLastListener:M});return k.event}e.fromDOMEventEmitter=z;function C(N,y){let E;const A=new Promise((S,M)=>{const k=r(N)(S,null,y);E=()=>k.dispose()});return A.cancel=E,A}e.toPromise=C;function v(N){const y=new ht;return N.then(E=>{y.fire(E)},()=>{y.fire(void 0)}).finally(()=>{y.dispose()}),y.event}e.fromPromise=v;function I(N,y){return N(E=>y.fire(E))}e.forward=I;function q(N,y,E){return y(E),N(A=>y(A))}e.runAndSubscribe=q;class R{constructor(y,E){this._observable=y,this.f=0,this.g=!1;const A={onWillAddFirstListener:()=>{y.addObserver(this),this._observable.reportChanges()},onDidRemoveLastListener:()=>{y.removeObserver(this)}};E||t(A),this.emitter=new ht(A),E&&E.add(this.emitter)}beginUpdate(y){this.f++}handlePossibleChange(y){}handleChange(y,E){this.g=!0}endUpdate(y){this.f--,this.f===0&&(this._observable.reportChanges(),this.g&&(this.g=!1,this.emitter.fire(this._observable.get())))}}function O(N,y){return new R(N,y).emitter.event}e.fromObservable=O;function It(N){return(y,E,A)=>{let S=0,M=!1;const k={beginUpdate(){S++},endUpdate(){S--,S===0&&(N.reportChanges(),M&&(M=!1,y.call(E)))},handlePossibleChange(){},handleChange(){M=!0}};N.addObserver(k),N.reportChanges();const G={dispose(){N.removeObserver(k)}};return A instanceof Qe?A.add(G):Array.isArray(A)&&A.push(G),G}}e.fromObservableLight=It})(pe||(pe={}));var Qi=class z1{static{this.all=new Set}static{this.f=0}constructor(t){this.listenerCount=0,this.invocationCount=0,this.elapsedOverall=0,this.durations=[],this.name=`${t}_${z1.f++}`,z1.all.add(this)}start(t){this.g=new Ji,this.listenerCount=t}stop(){if(this.g){const t=this.g.elapsed();this.durations.push(t),this.elapsedOverall+=t,this.invocationCount+=1,this.g=void 0}}},en=-1,Zi=class oi{static{this.f=1}constructor(t,n,r=(oi.f++).toString(16).padStart(3,"0")){this.j=t,this.threshold=n,this.name=r,this.h=0}dispose(){this.g?.clear()}check(t,n){const r=this.threshold;if(r<=0||n<r)return;this.g||(this.g=new Map);const i=this.g.get(t.value)||0;if(this.g.set(t.value,i+1),this.h-=1,this.h<=0){this.h=r*.5;const[s,o]=this.getMostFrequentStack(),l=`[${this.name}] potential listener LEAK detected, having ${n} listeners already. MOST frequent listener (${o}):`;console.warn(l),console.warn(s);const u=new Yi(l,s);this.j(u)}return()=>{const s=this.g.get(t.value)||0;this.g.set(t.value,s-1)}}getMostFrequentStack(){if(!this.g)return;let t,n=0;for(const[r,i]of this.g)(!t||n<i)&&(t=[r,i],n=i);return t}},Ze=class li{static create(){const t=new Error;return new li(t.stack??"")}constructor(t){this.value=t}print(){console.warn(this.value.split(`
`).slice(2).join(`
`))}},Yi=class extends Error{constructor(e,t){super(e),this.name="ListenerLeakError",this.stack=t}},ts=class extends Error{constructor(e,t){super(e),this.name="ListenerRefusalError",this.stack=t}},es=0,be=class{constructor(e){this.value=e,this.id=es++}},ns=2,rs=(e,t)=>{if(e instanceof be)t(e);else for(let n=0;n<e.length;n++){const r=e[n];r&&t(r)}},ht=class{constructor(e){this.A=0,this.g=e,this.j=en>0||this.g?.leakWarningThreshold?new Zi(e?.onListenerError??ge,this.g?.leakWarningThreshold??en):void 0,this.m=this.g?._profName?new Qi(this.g._profName):void 0,this.z=this.g?.deliveryQueue}dispose(){if(!this.q){if(this.q=!0,this.z?.current===this&&this.z.reset(),this.w){if(tn){const e=this.w;queueMicrotask(()=>{rs(e,t=>t.stack?.print())})}this.w=void 0,this.A=0}this.g?.onDidRemoveLastListener?.(),this.j?.dispose()}}get event(){return this.u??=(e,t,n)=>{if(this.j&&this.A>this.j.threshold**2){const l=`[${this.j.name}] REFUSES to accept new listeners because it exceeded its threshold by far (${this.A} vs ${this.j.threshold})`;console.warn(l);const u=this.j.getMostFrequentStack()??["UNKNOWN stack",-1],a=new ts(`${l}. HINT: Stack shows most frequent listener (${u[1]}-times)`,u[0]);return(this.g?.onListenerError||ge)(a),zt.None}if(this.q)return zt.None;t&&(e=e.bind(t));const r=new be(e);let i,s;this.j&&this.A>=Math.ceil(this.j.threshold*.2)&&(r.stack=Ze.create(),i=this.j.check(r.stack,this.A+1)),tn&&(r.stack=s??Ze.create()),this.w?this.w instanceof be?(this.z??=new is,this.w=[this.w,r]):this.w.push(r):(this.g?.onWillAddFirstListener?.(this),this.w=r,this.g?.onDidAddFirstListener?.(this)),this.g?.onDidAddListener?.(this),this.A++;const o=Y1(()=>{i?.(),this.B(r)});return n instanceof Qe?n.add(o):Array.isArray(n)&&n.push(o),o},this.u}B(e){if(this.g?.onWillRemoveListener?.(this),!this.w)return;if(this.A===1){this.w=void 0,this.g?.onDidRemoveLastListener?.(this),this.A=0;return}const t=this.w,n=t.indexOf(e);if(n===-1)throw console.log("disposed?",this.q),console.log("size?",this.A),console.log("arr?",JSON.stringify(this.w)),new Error("Attempted to dispose unknown listener");this.A--,t[n]=void 0;const r=this.z.current===this;if(this.A*ns<=t.length){let i=0;for(let s=0;s<t.length;s++)t[s]?t[i++]=t[s]:r&&i<this.z.end&&(this.z.end--,i<this.z.i&&this.z.i--);t.length=i}}C(e,t){if(!e)return;const n=this.g?.onListenerError||ge;if(!n){e.value(t);return}try{e.value(t)}catch(r){n(r)}}D(e){const t=e.current.w;for(;e.i<e.end;)this.C(t[e.i++],e.value);e.reset()}fire(e){if(this.z?.current&&(this.D(this.z),this.m?.stop()),this.m?.start(this.A),this.w)if(this.w instanceof be)this.C(this.w,e);else{const t=this.z;t.enqueue(this,e,this.w.length),this.D(t)}this.m?.stop()}hasListeners(){return this.A>0}},is=class{constructor(){this.i=-1,this.end=0}enqueue(e,t,n){this.i=0,this.end=n,this.current=e,this.value=t}reset(){this.i=this.end,this.current=void 0,this.value=void 0}};function ss(){return globalThis._VSCODE_NLS_MESSAGES}function nn(){return globalThis._VSCODE_NLS_LANGUAGE}var os=nn()==="pseudo"||typeof document<"u"&&document.location&&typeof document.location.hash=="string"&&document.location.hash.indexOf("pseudo=true")>=0;function rn(e,t){let n;return t.length===0?n=e:n=e.replace(/\{(\d+)\}/g,(r,i)=>{const s=i[0],o=t[s];let l=r;return typeof o=="string"?l=o:(typeof o=="number"||typeof o=="boolean"||o===void 0||o===null)&&(l=String(o)),l}),os&&(n="\uFF3B"+n.replace(/[aouei]/g,"$&$&")+"\uFF3D"),n}function tt(e,t,...n){return rn(typeof e=="number"?ls(e,t):t,n)}function ls(e,t){const n=ss()?.[e];if(typeof n!="string"){if(typeof t=="string")return t;throw new Error(`!!! NLS MISSING: ${e} !!!`)}return n}var Mt="en",ve=!1,we=!1,Xt=!1,as=!1,sn=!1,Ye=!1,us=!1,hs=!1,cs=!1,fs=!1,Ce=void 0,Le=Mt,on=Mt,ds=void 0,mt=void 0,pt=globalThis,et=void 0;typeof pt.vscode<"u"&&typeof pt.vscode.process<"u"?et=pt.vscode.process:typeof process<"u"&&typeof process?.versions?.node=="string"&&(et=process);var ln=typeof et?.versions?.electron=="string",gs=ln&&et?.type==="renderer";if(typeof et=="object"){ve=et.platform==="win32",we=et.platform==="darwin",Xt=et.platform==="linux",as=Xt&&!!et.env.SNAP&&!!et.env.SNAP_REVISION,us=ln,cs=!!et.env.CI||!!et.env.BUILD_ARTIFACTSTAGINGDIRECTORY||!!et.env.GITHUB_WORKSPACE,Ce=Mt,Le=Mt;const e=et.env.VSCODE_NLS_CONFIG;if(e)try{const t=JSON.parse(e);Ce=t.userLocale,on=t.osLocale,Le=t.resolvedLanguage||Mt,ds=t.languagePack?.translationsConfigFile}catch{}sn=!0}else typeof navigator=="object"&&!gs?(mt=navigator.userAgent,ve=mt.indexOf("Windows")>=0,we=mt.indexOf("Macintosh")>=0,hs=(mt.indexOf("Macintosh")>=0||mt.indexOf("iPad")>=0||mt.indexOf("iPhone")>=0)&&!!navigator.maxTouchPoints&&navigator.maxTouchPoints>0,Xt=mt.indexOf("Linux")>=0,fs=mt?.indexOf("Mobi")>=0,Ye=!0,Le=nn()||Mt,Ce=navigator.language.toLowerCase(),on=Ce):console.error("Unable to resolve platform.");var an;(function(e){e[e.Web=0]="Web",e[e.Mac=1]="Mac",e[e.Linux=2]="Linux",e[e.Windows=3]="Windows"})(an||(an={}));var t1=0;we?t1=1:ve?t1=3:Xt&&(t1=2);var Dt=ve,e1=we,un=Xt,ms=sn,n1=Ye,ps=Ye&&typeof pt.importScripts=="function",bs=ps?pt.origin:void 0,dt=mt,St=Le,hn;(function(e){function t(){return St}e.value=t;function n(){return St.length===2?St==="en":St.length>=3?St[0]==="e"&&St[1]==="n"&&St[2]==="-":!1}e.isDefaultVariant=n;function r(){return St==="en"}e.isDefault=r})(hn||(hn={}));var vs=typeof pt.postMessage=="function"&&!pt.importScripts,ws=(()=>{if(vs){const e=[];pt.addEventListener("message",n=>{if(n.data&&n.data.vscodeScheduleAsyncWork)for(let r=0,i=e.length;r<i;r++){const s=e[r];if(s.id===n.data.vscodeScheduleAsyncWork){e.splice(r,1),s.callback();return}}});let t=0;return n=>{const r=++t;e.push({id:r,callback:n}),pt.postMessage({vscodeScheduleAsyncWork:r},"*")}}return e=>setTimeout(e)})(),cn;(function(e){e[e.Windows=1]="Windows",e[e.Macintosh=2]="Macintosh",e[e.Linux=3]="Linux"})(cn||(cn={}));var fn=!!(dt&&dt.indexOf("Chrome")>=0),Cs=!!(dt&&dt.indexOf("Firefox")>=0),Ls=!!(!fn&&dt&&dt.indexOf("Safari")>=0),ys=!!(dt&&dt.indexOf("Edg/")>=0),ml=!!(dt&&dt.indexOf("Android")>=0),dn=Object.freeze(function(e,t){const n=setTimeout(e.bind(t),0);return{dispose(){clearTimeout(n)}}}),gn;(function(e){function t(n){return n===e.None||n===e.Cancelled||n instanceof As?!0:!n||typeof n!="object"?!1:typeof n.isCancellationRequested=="boolean"&&typeof n.onCancellationRequested=="function"}e.isCancellationToken=t,e.None=Object.freeze({isCancellationRequested:!1,onCancellationRequested:pe.None}),e.Cancelled=Object.freeze({isCancellationRequested:!0,onCancellationRequested:dn})})(gn||(gn={}));var As=class{constructor(){this.a=!1,this.b=null}cancel(){this.a||(this.a=!0,this.b&&(this.b.fire(void 0),this.dispose()))}get isCancellationRequested(){return this.a}get onCancellationRequested(){return this.a?dn:(this.b||(this.b=new ht),this.b.event)}dispose(){this.b&&(this.b.dispose(),this.b=null)}};function xs(e){return e}var Es=class{constructor(e,t){this.a=void 0,this.b=void 0,typeof e=="function"?(this.c=e,this.d=xs):(this.c=t,this.d=e.getCacheKey)}get(e){const t=this.d(e);return this.b!==t&&(this.b=t,this.a=this.c(e)),this.a}},$t=class{constructor(e){this.d=e,this.a=!1}get hasValue(){return this.a}get value(){if(!this.a)try{this.b=this.d()}catch(e){this.c=e}finally{this.a=!0}if(this.c)throw this.c;return this.b}get rawValue(){return this.b}};function Ns(e){return!e||typeof e!="string"?!0:e.trim().length===0}function Is(e){return e.replace(/[\\\{\}\*\+\?\|\^\$\.\[\]\(\)]/g,"\\$&")}function Ss(e,t,n={}){if(!e)throw new Error("Cannot create regex from empty string");t||(e=Is(e)),n.wholeWord&&(/\B/.test(e.charAt(0))||(e="\\b"+e),/\B/.test(e.charAt(e.length-1))||(e=e+"\\b"));let r="";return n.global&&(r+="g"),n.matchCase||(r+="i"),n.multiline&&(r+="m"),n.unicode&&(r+="u"),new RegExp(e,r)}function $s(e){return e.split(/\r\n|\r|\n/)}function r1(e){for(let t=0,n=e.length;t<n;t++){const r=e.charCodeAt(t);if(r!==32&&r!==9)return t}return-1}function Os(e,t=e.length-1){for(let n=t;n>=0;n--){const r=e.charCodeAt(n);if(r!==32&&r!==9)return n}return-1}function _s(e,t){return e<t?-1:e>t?1:0}function Ms(e,t,n=0,r=e.length,i=0,s=t.length){for(;n<r&&i<s;n++,i++){const u=e.charCodeAt(n),a=t.charCodeAt(i);if(u<a)return-1;if(u>a)return 1}const o=r-n,l=s-i;return o<l?-1:o>l?1:0}function mn(e,t,n=0,r=e.length,i=0,s=t.length){for(;n<r&&i<s;n++,i++){let u=e.charCodeAt(n),a=t.charCodeAt(i);if(u===a)continue;if(u>=128||a>=128)return Ms(e.toLowerCase(),t.toLowerCase(),n,r,i,s);pn(u)&&(u-=32),pn(a)&&(a-=32);const c=u-a;if(c!==0)return c}const o=r-n,l=s-i;return o<l?-1:o>l?1:0}function pn(e){return e>=97&&e<=122}function bn(e){return e>=65&&e<=90}function Ds(e,t){return e.length===t.length&&mn(e,t)===0}function Rs(e,t){const n=t.length;return t.length>e.length?!1:mn(e,t,0,n)===0}function i1(e){return 55296<=e&&e<=56319}function s1(e){return 56320<=e&&e<=57343}function vn(e,t){return(e-55296<<10)+(t-56320)+65536}function Ps(e,t,n){const r=e.charCodeAt(n);if(i1(r)&&n+1<t){const i=e.charCodeAt(n+1);if(s1(i))return vn(r,i)}return r}var o1=void 0;function ks(){return/(?:[\u05BE\u05C0\u05C3\u05C6\u05D0-\u05F4\u0608\u060B\u060D\u061B-\u064A\u066D-\u066F\u0671-\u06D5\u06E5\u06E6\u06EE\u06EF\u06FA-\u0710\u0712-\u072F\u074D-\u07A5\u07B1-\u07EA\u07F4\u07F5\u07FA\u07FE-\u0815\u081A\u0824\u0828\u0830-\u0858\u085E-\u088E\u08A0-\u08C9\u200F\uFB1D\uFB1F-\uFB28\uFB2A-\uFD3D\uFD50-\uFDC7\uFDF0-\uFDFC\uFE70-\uFEFC]|\uD802[\uDC00-\uDD1B\uDD20-\uDE00\uDE10-\uDE35\uDE40-\uDEE4\uDEEB-\uDF35\uDF40-\uDFFF]|\uD803[\uDC00-\uDD23\uDE80-\uDEA9\uDEAD-\uDF45\uDF51-\uDF81\uDF86-\uDFF6]|\uD83A[\uDC00-\uDCCF\uDD00-\uDD43\uDD4B-\uDFFF]|\uD83B[\uDC00-\uDEBB])/}function wn(e){return o1||(o1=ks()),o1.test(e)}var Fs=/^[\t\n\r\x20-\x7E]*$/;function qs(e){return Fs.test(e)}var Us=/[\u2028\u2029]/;function Cn(e){return Us.test(e)}var js=/(?:\x1b\[|\x9b)[=?>!]?[\d;:]*["$#'* ]?[a-zA-Z@^`{}|~]/,zs=/(?:\x1b\]|\x9d).*?(?:\x1b\\|\x07|\x9c)/,Ts=/\x1b(?:[ #%\(\)\*\+\-\.\/]?[a-zA-Z0-9\|}~@])/,pl=new RegExp("(?:"+[js.source,zs.source,Ts.source].join("|")+")","g"),Bs="\uFEFF";function Vs(e){return!!(e&&e.length>0&&e.charCodeAt(0)===65279)}var Ln;(function(e){e[e.Other=0]="Other",e[e.Prepend=1]="Prepend",e[e.CR=2]="CR",e[e.LF=3]="LF",e[e.Control=4]="Control",e[e.Extend=5]="Extend",e[e.Regional_Indicator=6]="Regional_Indicator",e[e.SpacingMark=7]="SpacingMark",e[e.L=8]="L",e[e.V=9]="V",e[e.T=10]="T",e[e.LV=11]="LV",e[e.LVT=12]="LVT",e[e.ZWJ=13]="ZWJ",e[e.Extended_Pictographic=14]="Extended_Pictographic"})(Ln||(Ln={}));var bl=class le{static{this.c=null}static getInstance(){return le.c||(le.c=new le),le.c}constructor(){this.d=Ws()}getGraphemeBreakType(t){if(t<32)return t===10?3:t===13?2:4;if(t<127)return 0;const n=this.d,r=n.length/3;let i=1;for(;i<=r;)if(t<n[3*i])i=2*i;else if(t>n[3*i+1])i=2*i+1;else return n[3*i+2];return 0}};function Ws(){return JSON.parse("[0,0,0,51229,51255,12,44061,44087,12,127462,127487,6,7083,7085,5,47645,47671,12,54813,54839,12,128678,128678,14,3270,3270,5,9919,9923,14,45853,45879,12,49437,49463,12,53021,53047,12,71216,71218,7,128398,128399,14,129360,129374,14,2519,2519,5,4448,4519,9,9742,9742,14,12336,12336,14,44957,44983,12,46749,46775,12,48541,48567,12,50333,50359,12,52125,52151,12,53917,53943,12,69888,69890,5,73018,73018,5,127990,127990,14,128558,128559,14,128759,128760,14,129653,129655,14,2027,2035,5,2891,2892,7,3761,3761,5,6683,6683,5,8293,8293,4,9825,9826,14,9999,9999,14,43452,43453,5,44509,44535,12,45405,45431,12,46301,46327,12,47197,47223,12,48093,48119,12,48989,49015,12,49885,49911,12,50781,50807,12,51677,51703,12,52573,52599,12,53469,53495,12,54365,54391,12,65279,65279,4,70471,70472,7,72145,72147,7,119173,119179,5,127799,127818,14,128240,128244,14,128512,128512,14,128652,128652,14,128721,128722,14,129292,129292,14,129445,129450,14,129734,129743,14,1476,1477,5,2366,2368,7,2750,2752,7,3076,3076,5,3415,3415,5,4141,4144,5,6109,6109,5,6964,6964,5,7394,7400,5,9197,9198,14,9770,9770,14,9877,9877,14,9968,9969,14,10084,10084,14,43052,43052,5,43713,43713,5,44285,44311,12,44733,44759,12,45181,45207,12,45629,45655,12,46077,46103,12,46525,46551,12,46973,46999,12,47421,47447,12,47869,47895,12,48317,48343,12,48765,48791,12,49213,49239,12,49661,49687,12,50109,50135,12,50557,50583,12,51005,51031,12,51453,51479,12,51901,51927,12,52349,52375,12,52797,52823,12,53245,53271,12,53693,53719,12,54141,54167,12,54589,54615,12,55037,55063,12,69506,69509,5,70191,70193,5,70841,70841,7,71463,71467,5,72330,72342,5,94031,94031,5,123628,123631,5,127763,127765,14,127941,127941,14,128043,128062,14,128302,128317,14,128465,128467,14,128539,128539,14,128640,128640,14,128662,128662,14,128703,128703,14,128745,128745,14,129004,129007,14,129329,129330,14,129402,129402,14,129483,129483,14,129686,129704,14,130048,131069,14,173,173,4,1757,1757,1,2200,2207,5,2434,2435,7,2631,2632,5,2817,2817,5,3008,3008,5,3201,3201,5,3387,3388,5,3542,3542,5,3902,3903,7,4190,4192,5,6002,6003,5,6439,6440,5,6765,6770,7,7019,7027,5,7154,7155,7,8205,8205,13,8505,8505,14,9654,9654,14,9757,9757,14,9792,9792,14,9852,9853,14,9890,9894,14,9937,9937,14,9981,9981,14,10035,10036,14,11035,11036,14,42654,42655,5,43346,43347,7,43587,43587,5,44006,44007,7,44173,44199,12,44397,44423,12,44621,44647,12,44845,44871,12,45069,45095,12,45293,45319,12,45517,45543,12,45741,45767,12,45965,45991,12,46189,46215,12,46413,46439,12,46637,46663,12,46861,46887,12,47085,47111,12,47309,47335,12,47533,47559,12,47757,47783,12,47981,48007,12,48205,48231,12,48429,48455,12,48653,48679,12,48877,48903,12,49101,49127,12,49325,49351,12,49549,49575,12,49773,49799,12,49997,50023,12,50221,50247,12,50445,50471,12,50669,50695,12,50893,50919,12,51117,51143,12,51341,51367,12,51565,51591,12,51789,51815,12,52013,52039,12,52237,52263,12,52461,52487,12,52685,52711,12,52909,52935,12,53133,53159,12,53357,53383,12,53581,53607,12,53805,53831,12,54029,54055,12,54253,54279,12,54477,54503,12,54701,54727,12,54925,54951,12,55149,55175,12,68101,68102,5,69762,69762,7,70067,70069,7,70371,70378,5,70720,70721,7,71087,71087,5,71341,71341,5,71995,71996,5,72249,72249,7,72850,72871,5,73109,73109,5,118576,118598,5,121505,121519,5,127245,127247,14,127568,127569,14,127777,127777,14,127872,127891,14,127956,127967,14,128015,128016,14,128110,128172,14,128259,128259,14,128367,128368,14,128424,128424,14,128488,128488,14,128530,128532,14,128550,128551,14,128566,128566,14,128647,128647,14,128656,128656,14,128667,128673,14,128691,128693,14,128715,128715,14,128728,128732,14,128752,128752,14,128765,128767,14,129096,129103,14,129311,129311,14,129344,129349,14,129394,129394,14,129413,129425,14,129466,129471,14,129511,129535,14,129664,129666,14,129719,129722,14,129760,129767,14,917536,917631,5,13,13,2,1160,1161,5,1564,1564,4,1807,1807,1,2085,2087,5,2307,2307,7,2382,2383,7,2497,2500,5,2563,2563,7,2677,2677,5,2763,2764,7,2879,2879,5,2914,2915,5,3021,3021,5,3142,3144,5,3263,3263,5,3285,3286,5,3398,3400,7,3530,3530,5,3633,3633,5,3864,3865,5,3974,3975,5,4155,4156,7,4229,4230,5,5909,5909,7,6078,6085,7,6277,6278,5,6451,6456,7,6744,6750,5,6846,6846,5,6972,6972,5,7074,7077,5,7146,7148,7,7222,7223,5,7416,7417,5,8234,8238,4,8417,8417,5,9000,9000,14,9203,9203,14,9730,9731,14,9748,9749,14,9762,9763,14,9776,9783,14,9800,9811,14,9831,9831,14,9872,9873,14,9882,9882,14,9900,9903,14,9929,9933,14,9941,9960,14,9974,9974,14,9989,9989,14,10006,10006,14,10062,10062,14,10160,10160,14,11647,11647,5,12953,12953,14,43019,43019,5,43232,43249,5,43443,43443,5,43567,43568,7,43696,43696,5,43765,43765,7,44013,44013,5,44117,44143,12,44229,44255,12,44341,44367,12,44453,44479,12,44565,44591,12,44677,44703,12,44789,44815,12,44901,44927,12,45013,45039,12,45125,45151,12,45237,45263,12,45349,45375,12,45461,45487,12,45573,45599,12,45685,45711,12,45797,45823,12,45909,45935,12,46021,46047,12,46133,46159,12,46245,46271,12,46357,46383,12,46469,46495,12,46581,46607,12,46693,46719,12,46805,46831,12,46917,46943,12,47029,47055,12,47141,47167,12,47253,47279,12,47365,47391,12,47477,47503,12,47589,47615,12,47701,47727,12,47813,47839,12,47925,47951,12,48037,48063,12,48149,48175,12,48261,48287,12,48373,48399,12,48485,48511,12,48597,48623,12,48709,48735,12,48821,48847,12,48933,48959,12,49045,49071,12,49157,49183,12,49269,49295,12,49381,49407,12,49493,49519,12,49605,49631,12,49717,49743,12,49829,49855,12,49941,49967,12,50053,50079,12,50165,50191,12,50277,50303,12,50389,50415,12,50501,50527,12,50613,50639,12,50725,50751,12,50837,50863,12,50949,50975,12,51061,51087,12,51173,51199,12,51285,51311,12,51397,51423,12,51509,51535,12,51621,51647,12,51733,51759,12,51845,51871,12,51957,51983,12,52069,52095,12,52181,52207,12,52293,52319,12,52405,52431,12,52517,52543,12,52629,52655,12,52741,52767,12,52853,52879,12,52965,52991,12,53077,53103,12,53189,53215,12,53301,53327,12,53413,53439,12,53525,53551,12,53637,53663,12,53749,53775,12,53861,53887,12,53973,53999,12,54085,54111,12,54197,54223,12,54309,54335,12,54421,54447,12,54533,54559,12,54645,54671,12,54757,54783,12,54869,54895,12,54981,55007,12,55093,55119,12,55243,55291,10,66045,66045,5,68325,68326,5,69688,69702,5,69817,69818,5,69957,69958,7,70089,70092,5,70198,70199,5,70462,70462,5,70502,70508,5,70750,70750,5,70846,70846,7,71100,71101,5,71230,71230,7,71351,71351,5,71737,71738,5,72000,72000,7,72160,72160,5,72273,72278,5,72752,72758,5,72882,72883,5,73031,73031,5,73461,73462,7,94192,94193,7,119149,119149,7,121403,121452,5,122915,122916,5,126980,126980,14,127358,127359,14,127535,127535,14,127759,127759,14,127771,127771,14,127792,127793,14,127825,127867,14,127897,127899,14,127945,127945,14,127985,127986,14,128000,128007,14,128021,128021,14,128066,128100,14,128184,128235,14,128249,128252,14,128266,128276,14,128335,128335,14,128379,128390,14,128407,128419,14,128444,128444,14,128481,128481,14,128499,128499,14,128526,128526,14,128536,128536,14,128543,128543,14,128556,128556,14,128564,128564,14,128577,128580,14,128643,128645,14,128649,128649,14,128654,128654,14,128660,128660,14,128664,128664,14,128675,128675,14,128686,128689,14,128695,128696,14,128705,128709,14,128717,128719,14,128725,128725,14,128736,128741,14,128747,128748,14,128755,128755,14,128762,128762,14,128981,128991,14,129009,129023,14,129160,129167,14,129296,129304,14,129320,129327,14,129340,129342,14,129356,129356,14,129388,129392,14,129399,129400,14,129404,129407,14,129432,129442,14,129454,129455,14,129473,129474,14,129485,129487,14,129648,129651,14,129659,129660,14,129671,129679,14,129709,129711,14,129728,129730,14,129751,129753,14,129776,129782,14,917505,917505,4,917760,917999,5,10,10,3,127,159,4,768,879,5,1471,1471,5,1536,1541,1,1648,1648,5,1767,1768,5,1840,1866,5,2070,2073,5,2137,2139,5,2274,2274,1,2363,2363,7,2377,2380,7,2402,2403,5,2494,2494,5,2507,2508,7,2558,2558,5,2622,2624,7,2641,2641,5,2691,2691,7,2759,2760,5,2786,2787,5,2876,2876,5,2881,2884,5,2901,2902,5,3006,3006,5,3014,3016,7,3072,3072,5,3134,3136,5,3157,3158,5,3260,3260,5,3266,3266,5,3274,3275,7,3328,3329,5,3391,3392,7,3405,3405,5,3457,3457,5,3536,3537,7,3551,3551,5,3636,3642,5,3764,3772,5,3895,3895,5,3967,3967,7,3993,4028,5,4146,4151,5,4182,4183,7,4226,4226,5,4253,4253,5,4957,4959,5,5940,5940,7,6070,6070,7,6087,6088,7,6158,6158,4,6432,6434,5,6448,6449,7,6679,6680,5,6742,6742,5,6754,6754,5,6783,6783,5,6912,6915,5,6966,6970,5,6978,6978,5,7042,7042,7,7080,7081,5,7143,7143,7,7150,7150,7,7212,7219,5,7380,7392,5,7412,7412,5,8203,8203,4,8232,8232,4,8265,8265,14,8400,8412,5,8421,8432,5,8617,8618,14,9167,9167,14,9200,9200,14,9410,9410,14,9723,9726,14,9733,9733,14,9745,9745,14,9752,9752,14,9760,9760,14,9766,9766,14,9774,9774,14,9786,9786,14,9794,9794,14,9823,9823,14,9828,9828,14,9833,9850,14,9855,9855,14,9875,9875,14,9880,9880,14,9885,9887,14,9896,9897,14,9906,9916,14,9926,9927,14,9935,9935,14,9939,9939,14,9962,9962,14,9972,9972,14,9978,9978,14,9986,9986,14,9997,9997,14,10002,10002,14,10017,10017,14,10055,10055,14,10071,10071,14,10133,10135,14,10548,10549,14,11093,11093,14,12330,12333,5,12441,12442,5,42608,42610,5,43010,43010,5,43045,43046,5,43188,43203,7,43302,43309,5,43392,43394,5,43446,43449,5,43493,43493,5,43571,43572,7,43597,43597,7,43703,43704,5,43756,43757,5,44003,44004,7,44009,44010,7,44033,44059,12,44089,44115,12,44145,44171,12,44201,44227,12,44257,44283,12,44313,44339,12,44369,44395,12,44425,44451,12,44481,44507,12,44537,44563,12,44593,44619,12,44649,44675,12,44705,44731,12,44761,44787,12,44817,44843,12,44873,44899,12,44929,44955,12,44985,45011,12,45041,45067,12,45097,45123,12,45153,45179,12,45209,45235,12,45265,45291,12,45321,45347,12,45377,45403,12,45433,45459,12,45489,45515,12,45545,45571,12,45601,45627,12,45657,45683,12,45713,45739,12,45769,45795,12,45825,45851,12,45881,45907,12,45937,45963,12,45993,46019,12,46049,46075,12,46105,46131,12,46161,46187,12,46217,46243,12,46273,46299,12,46329,46355,12,46385,46411,12,46441,46467,12,46497,46523,12,46553,46579,12,46609,46635,12,46665,46691,12,46721,46747,12,46777,46803,12,46833,46859,12,46889,46915,12,46945,46971,12,47001,47027,12,47057,47083,12,47113,47139,12,47169,47195,12,47225,47251,12,47281,47307,12,47337,47363,12,47393,47419,12,47449,47475,12,47505,47531,12,47561,47587,12,47617,47643,12,47673,47699,12,47729,47755,12,47785,47811,12,47841,47867,12,47897,47923,12,47953,47979,12,48009,48035,12,48065,48091,12,48121,48147,12,48177,48203,12,48233,48259,12,48289,48315,12,48345,48371,12,48401,48427,12,48457,48483,12,48513,48539,12,48569,48595,12,48625,48651,12,48681,48707,12,48737,48763,12,48793,48819,12,48849,48875,12,48905,48931,12,48961,48987,12,49017,49043,12,49073,49099,12,49129,49155,12,49185,49211,12,49241,49267,12,49297,49323,12,49353,49379,12,49409,49435,12,49465,49491,12,49521,49547,12,49577,49603,12,49633,49659,12,49689,49715,12,49745,49771,12,49801,49827,12,49857,49883,12,49913,49939,12,49969,49995,12,50025,50051,12,50081,50107,12,50137,50163,12,50193,50219,12,50249,50275,12,50305,50331,12,50361,50387,12,50417,50443,12,50473,50499,12,50529,50555,12,50585,50611,12,50641,50667,12,50697,50723,12,50753,50779,12,50809,50835,12,50865,50891,12,50921,50947,12,50977,51003,12,51033,51059,12,51089,51115,12,51145,51171,12,51201,51227,12,51257,51283,12,51313,51339,12,51369,51395,12,51425,51451,12,51481,51507,12,51537,51563,12,51593,51619,12,51649,51675,12,51705,51731,12,51761,51787,12,51817,51843,12,51873,51899,12,51929,51955,12,51985,52011,12,52041,52067,12,52097,52123,12,52153,52179,12,52209,52235,12,52265,52291,12,52321,52347,12,52377,52403,12,52433,52459,12,52489,52515,12,52545,52571,12,52601,52627,12,52657,52683,12,52713,52739,12,52769,52795,12,52825,52851,12,52881,52907,12,52937,52963,12,52993,53019,12,53049,53075,12,53105,53131,12,53161,53187,12,53217,53243,12,53273,53299,12,53329,53355,12,53385,53411,12,53441,53467,12,53497,53523,12,53553,53579,12,53609,53635,12,53665,53691,12,53721,53747,12,53777,53803,12,53833,53859,12,53889,53915,12,53945,53971,12,54001,54027,12,54057,54083,12,54113,54139,12,54169,54195,12,54225,54251,12,54281,54307,12,54337,54363,12,54393,54419,12,54449,54475,12,54505,54531,12,54561,54587,12,54617,54643,12,54673,54699,12,54729,54755,12,54785,54811,12,54841,54867,12,54897,54923,12,54953,54979,12,55009,55035,12,55065,55091,12,55121,55147,12,55177,55203,12,65024,65039,5,65520,65528,4,66422,66426,5,68152,68154,5,69291,69292,5,69633,69633,5,69747,69748,5,69811,69814,5,69826,69826,5,69932,69932,7,70016,70017,5,70079,70080,7,70095,70095,5,70196,70196,5,70367,70367,5,70402,70403,7,70464,70464,5,70487,70487,5,70709,70711,7,70725,70725,7,70833,70834,7,70843,70844,7,70849,70849,7,71090,71093,5,71103,71104,5,71227,71228,7,71339,71339,5,71344,71349,5,71458,71461,5,71727,71735,5,71985,71989,7,71998,71998,5,72002,72002,7,72154,72155,5,72193,72202,5,72251,72254,5,72281,72283,5,72344,72345,5,72766,72766,7,72874,72880,5,72885,72886,5,73023,73029,5,73104,73105,5,73111,73111,5,92912,92916,5,94095,94098,5,113824,113827,4,119142,119142,7,119155,119162,4,119362,119364,5,121476,121476,5,122888,122904,5,123184,123190,5,125252,125258,5,127183,127183,14,127340,127343,14,127377,127386,14,127491,127503,14,127548,127551,14,127744,127756,14,127761,127761,14,127769,127769,14,127773,127774,14,127780,127788,14,127796,127797,14,127820,127823,14,127869,127869,14,127894,127895,14,127902,127903,14,127943,127943,14,127947,127950,14,127972,127972,14,127988,127988,14,127992,127994,14,128009,128011,14,128019,128019,14,128023,128041,14,128064,128064,14,128102,128107,14,128174,128181,14,128238,128238,14,128246,128247,14,128254,128254,14,128264,128264,14,128278,128299,14,128329,128330,14,128348,128359,14,128371,128377,14,128392,128393,14,128401,128404,14,128421,128421,14,128433,128434,14,128450,128452,14,128476,128478,14,128483,128483,14,128495,128495,14,128506,128506,14,128519,128520,14,128528,128528,14,128534,128534,14,128538,128538,14,128540,128542,14,128544,128549,14,128552,128555,14,128557,128557,14,128560,128563,14,128565,128565,14,128567,128576,14,128581,128591,14,128641,128642,14,128646,128646,14,128648,128648,14,128650,128651,14,128653,128653,14,128655,128655,14,128657,128659,14,128661,128661,14,128663,128663,14,128665,128666,14,128674,128674,14,128676,128677,14,128679,128685,14,128690,128690,14,128694,128694,14,128697,128702,14,128704,128704,14,128710,128714,14,128716,128716,14,128720,128720,14,128723,128724,14,128726,128727,14,128733,128735,14,128742,128744,14,128746,128746,14,128749,128751,14,128753,128754,14,128756,128758,14,128761,128761,14,128763,128764,14,128884,128895,14,128992,129003,14,129008,129008,14,129036,129039,14,129114,129119,14,129198,129279,14,129293,129295,14,129305,129310,14,129312,129319,14,129328,129328,14,129331,129338,14,129343,129343,14,129351,129355,14,129357,129359,14,129375,129387,14,129393,129393,14,129395,129398,14,129401,129401,14,129403,129403,14,129408,129412,14,129426,129431,14,129443,129444,14,129451,129453,14,129456,129465,14,129472,129472,14,129475,129482,14,129484,129484,14,129488,129510,14,129536,129647,14,129652,129652,14,129656,129658,14,129661,129663,14,129667,129670,14,129680,129685,14,129705,129708,14,129712,129718,14,129723,129727,14,129731,129733,14,129744,129750,14,129754,129759,14,129768,129775,14,129783,129791,14,917504,917504,4,917506,917535,4,917632,917759,4,918000,921599,4,0,9,4,11,12,4,14,31,4,169,169,14,174,174,14,1155,1159,5,1425,1469,5,1473,1474,5,1479,1479,5,1552,1562,5,1611,1631,5,1750,1756,5,1759,1764,5,1770,1773,5,1809,1809,5,1958,1968,5,2045,2045,5,2075,2083,5,2089,2093,5,2192,2193,1,2250,2273,5,2275,2306,5,2362,2362,5,2364,2364,5,2369,2376,5,2381,2381,5,2385,2391,5,2433,2433,5,2492,2492,5,2495,2496,7,2503,2504,7,2509,2509,5,2530,2531,5,2561,2562,5,2620,2620,5,2625,2626,5,2635,2637,5,2672,2673,5,2689,2690,5,2748,2748,5,2753,2757,5,2761,2761,7,2765,2765,5,2810,2815,5,2818,2819,7,2878,2878,5,2880,2880,7,2887,2888,7,2893,2893,5,2903,2903,5,2946,2946,5,3007,3007,7,3009,3010,7,3018,3020,7,3031,3031,5,3073,3075,7,3132,3132,5,3137,3140,7,3146,3149,5,3170,3171,5,3202,3203,7,3262,3262,7,3264,3265,7,3267,3268,7,3271,3272,7,3276,3277,5,3298,3299,5,3330,3331,7,3390,3390,5,3393,3396,5,3402,3404,7,3406,3406,1,3426,3427,5,3458,3459,7,3535,3535,5,3538,3540,5,3544,3550,7,3570,3571,7,3635,3635,7,3655,3662,5,3763,3763,7,3784,3789,5,3893,3893,5,3897,3897,5,3953,3966,5,3968,3972,5,3981,3991,5,4038,4038,5,4145,4145,7,4153,4154,5,4157,4158,5,4184,4185,5,4209,4212,5,4228,4228,7,4237,4237,5,4352,4447,8,4520,4607,10,5906,5908,5,5938,5939,5,5970,5971,5,6068,6069,5,6071,6077,5,6086,6086,5,6089,6099,5,6155,6157,5,6159,6159,5,6313,6313,5,6435,6438,7,6441,6443,7,6450,6450,5,6457,6459,5,6681,6682,7,6741,6741,7,6743,6743,7,6752,6752,5,6757,6764,5,6771,6780,5,6832,6845,5,6847,6862,5,6916,6916,7,6965,6965,5,6971,6971,7,6973,6977,7,6979,6980,7,7040,7041,5,7073,7073,7,7078,7079,7,7082,7082,7,7142,7142,5,7144,7145,5,7149,7149,5,7151,7153,5,7204,7211,7,7220,7221,7,7376,7378,5,7393,7393,7,7405,7405,5,7415,7415,7,7616,7679,5,8204,8204,5,8206,8207,4,8233,8233,4,8252,8252,14,8288,8292,4,8294,8303,4,8413,8416,5,8418,8420,5,8482,8482,14,8596,8601,14,8986,8987,14,9096,9096,14,9193,9196,14,9199,9199,14,9201,9202,14,9208,9210,14,9642,9643,14,9664,9664,14,9728,9729,14,9732,9732,14,9735,9741,14,9743,9744,14,9746,9746,14,9750,9751,14,9753,9756,14,9758,9759,14,9761,9761,14,9764,9765,14,9767,9769,14,9771,9773,14,9775,9775,14,9784,9785,14,9787,9791,14,9793,9793,14,9795,9799,14,9812,9822,14,9824,9824,14,9827,9827,14,9829,9830,14,9832,9832,14,9851,9851,14,9854,9854,14,9856,9861,14,9874,9874,14,9876,9876,14,9878,9879,14,9881,9881,14,9883,9884,14,9888,9889,14,9895,9895,14,9898,9899,14,9904,9905,14,9917,9918,14,9924,9925,14,9928,9928,14,9934,9934,14,9936,9936,14,9938,9938,14,9940,9940,14,9961,9961,14,9963,9967,14,9970,9971,14,9973,9973,14,9975,9977,14,9979,9980,14,9982,9985,14,9987,9988,14,9992,9996,14,9998,9998,14,10000,10001,14,10004,10004,14,10013,10013,14,10024,10024,14,10052,10052,14,10060,10060,14,10067,10069,14,10083,10083,14,10085,10087,14,10145,10145,14,10175,10175,14,11013,11015,14,11088,11088,14,11503,11505,5,11744,11775,5,12334,12335,5,12349,12349,14,12951,12951,14,42607,42607,5,42612,42621,5,42736,42737,5,43014,43014,5,43043,43044,7,43047,43047,7,43136,43137,7,43204,43205,5,43263,43263,5,43335,43345,5,43360,43388,8,43395,43395,7,43444,43445,7,43450,43451,7,43454,43456,7,43561,43566,5,43569,43570,5,43573,43574,5,43596,43596,5,43644,43644,5,43698,43700,5,43710,43711,5,43755,43755,7,43758,43759,7,43766,43766,5,44005,44005,5,44008,44008,5,44012,44012,7,44032,44032,11,44060,44060,11,44088,44088,11,44116,44116,11,44144,44144,11,44172,44172,11,44200,44200,11,44228,44228,11,44256,44256,11,44284,44284,11,44312,44312,11,44340,44340,11,44368,44368,11,44396,44396,11,44424,44424,11,44452,44452,11,44480,44480,11,44508,44508,11,44536,44536,11,44564,44564,11,44592,44592,11,44620,44620,11,44648,44648,11,44676,44676,11,44704,44704,11,44732,44732,11,44760,44760,11,44788,44788,11,44816,44816,11,44844,44844,11,44872,44872,11,44900,44900,11,44928,44928,11,44956,44956,11,44984,44984,11,45012,45012,11,45040,45040,11,45068,45068,11,45096,45096,11,45124,45124,11,45152,45152,11,45180,45180,11,45208,45208,11,45236,45236,11,45264,45264,11,45292,45292,11,45320,45320,11,45348,45348,11,45376,45376,11,45404,45404,11,45432,45432,11,45460,45460,11,45488,45488,11,45516,45516,11,45544,45544,11,45572,45572,11,45600,45600,11,45628,45628,11,45656,45656,11,45684,45684,11,45712,45712,11,45740,45740,11,45768,45768,11,45796,45796,11,45824,45824,11,45852,45852,11,45880,45880,11,45908,45908,11,45936,45936,11,45964,45964,11,45992,45992,11,46020,46020,11,46048,46048,11,46076,46076,11,46104,46104,11,46132,46132,11,46160,46160,11,46188,46188,11,46216,46216,11,46244,46244,11,46272,46272,11,46300,46300,11,46328,46328,11,46356,46356,11,46384,46384,11,46412,46412,11,46440,46440,11,46468,46468,11,46496,46496,11,46524,46524,11,46552,46552,11,46580,46580,11,46608,46608,11,46636,46636,11,46664,46664,11,46692,46692,11,46720,46720,11,46748,46748,11,46776,46776,11,46804,46804,11,46832,46832,11,46860,46860,11,46888,46888,11,46916,46916,11,46944,46944,11,46972,46972,11,47000,47000,11,47028,47028,11,47056,47056,11,47084,47084,11,47112,47112,11,47140,47140,11,47168,47168,11,47196,47196,11,47224,47224,11,47252,47252,11,47280,47280,11,47308,47308,11,47336,47336,11,47364,47364,11,47392,47392,11,47420,47420,11,47448,47448,11,47476,47476,11,47504,47504,11,47532,47532,11,47560,47560,11,47588,47588,11,47616,47616,11,47644,47644,11,47672,47672,11,47700,47700,11,47728,47728,11,47756,47756,11,47784,47784,11,47812,47812,11,47840,47840,11,47868,47868,11,47896,47896,11,47924,47924,11,47952,47952,11,47980,47980,11,48008,48008,11,48036,48036,11,48064,48064,11,48092,48092,11,48120,48120,11,48148,48148,11,48176,48176,11,48204,48204,11,48232,48232,11,48260,48260,11,48288,48288,11,48316,48316,11,48344,48344,11,48372,48372,11,48400,48400,11,48428,48428,11,48456,48456,11,48484,48484,11,48512,48512,11,48540,48540,11,48568,48568,11,48596,48596,11,48624,48624,11,48652,48652,11,48680,48680,11,48708,48708,11,48736,48736,11,48764,48764,11,48792,48792,11,48820,48820,11,48848,48848,11,48876,48876,11,48904,48904,11,48932,48932,11,48960,48960,11,48988,48988,11,49016,49016,11,49044,49044,11,49072,49072,11,49100,49100,11,49128,49128,11,49156,49156,11,49184,49184,11,49212,49212,11,49240,49240,11,49268,49268,11,49296,49296,11,49324,49324,11,49352,49352,11,49380,49380,11,49408,49408,11,49436,49436,11,49464,49464,11,49492,49492,11,49520,49520,11,49548,49548,11,49576,49576,11,49604,49604,11,49632,49632,11,49660,49660,11,49688,49688,11,49716,49716,11,49744,49744,11,49772,49772,11,49800,49800,11,49828,49828,11,49856,49856,11,49884,49884,11,49912,49912,11,49940,49940,11,49968,49968,11,49996,49996,11,50024,50024,11,50052,50052,11,50080,50080,11,50108,50108,11,50136,50136,11,50164,50164,11,50192,50192,11,50220,50220,11,50248,50248,11,50276,50276,11,50304,50304,11,50332,50332,11,50360,50360,11,50388,50388,11,50416,50416,11,50444,50444,11,50472,50472,11,50500,50500,11,50528,50528,11,50556,50556,11,50584,50584,11,50612,50612,11,50640,50640,11,50668,50668,11,50696,50696,11,50724,50724,11,50752,50752,11,50780,50780,11,50808,50808,11,50836,50836,11,50864,50864,11,50892,50892,11,50920,50920,11,50948,50948,11,50976,50976,11,51004,51004,11,51032,51032,11,51060,51060,11,51088,51088,11,51116,51116,11,51144,51144,11,51172,51172,11,51200,51200,11,51228,51228,11,51256,51256,11,51284,51284,11,51312,51312,11,51340,51340,11,51368,51368,11,51396,51396,11,51424,51424,11,51452,51452,11,51480,51480,11,51508,51508,11,51536,51536,11,51564,51564,11,51592,51592,11,51620,51620,11,51648,51648,11,51676,51676,11,51704,51704,11,51732,51732,11,51760,51760,11,51788,51788,11,51816,51816,11,51844,51844,11,51872,51872,11,51900,51900,11,51928,51928,11,51956,51956,11,51984,51984,11,52012,52012,11,52040,52040,11,52068,52068,11,52096,52096,11,52124,52124,11,52152,52152,11,52180,52180,11,52208,52208,11,52236,52236,11,52264,52264,11,52292,52292,11,52320,52320,11,52348,52348,11,52376,52376,11,52404,52404,11,52432,52432,11,52460,52460,11,52488,52488,11,52516,52516,11,52544,52544,11,52572,52572,11,52600,52600,11,52628,52628,11,52656,52656,11,52684,52684,11,52712,52712,11,52740,52740,11,52768,52768,11,52796,52796,11,52824,52824,11,52852,52852,11,52880,52880,11,52908,52908,11,52936,52936,11,52964,52964,11,52992,52992,11,53020,53020,11,53048,53048,11,53076,53076,11,53104,53104,11,53132,53132,11,53160,53160,11,53188,53188,11,53216,53216,11,53244,53244,11,53272,53272,11,53300,53300,11,53328,53328,11,53356,53356,11,53384,53384,11,53412,53412,11,53440,53440,11,53468,53468,11,53496,53496,11,53524,53524,11,53552,53552,11,53580,53580,11,53608,53608,11,53636,53636,11,53664,53664,11,53692,53692,11,53720,53720,11,53748,53748,11,53776,53776,11,53804,53804,11,53832,53832,11,53860,53860,11,53888,53888,11,53916,53916,11,53944,53944,11,53972,53972,11,54000,54000,11,54028,54028,11,54056,54056,11,54084,54084,11,54112,54112,11,54140,54140,11,54168,54168,11,54196,54196,11,54224,54224,11,54252,54252,11,54280,54280,11,54308,54308,11,54336,54336,11,54364,54364,11,54392,54392,11,54420,54420,11,54448,54448,11,54476,54476,11,54504,54504,11,54532,54532,11,54560,54560,11,54588,54588,11,54616,54616,11,54644,54644,11,54672,54672,11,54700,54700,11,54728,54728,11,54756,54756,11,54784,54784,11,54812,54812,11,54840,54840,11,54868,54868,11,54896,54896,11,54924,54924,11,54952,54952,11,54980,54980,11,55008,55008,11,55036,55036,11,55064,55064,11,55092,55092,11,55120,55120,11,55148,55148,11,55176,55176,11,55216,55238,9,64286,64286,5,65056,65071,5,65438,65439,5,65529,65531,4,66272,66272,5,68097,68099,5,68108,68111,5,68159,68159,5,68900,68903,5,69446,69456,5,69632,69632,7,69634,69634,7,69744,69744,5,69759,69761,5,69808,69810,7,69815,69816,7,69821,69821,1,69837,69837,1,69927,69931,5,69933,69940,5,70003,70003,5,70018,70018,7,70070,70078,5,70082,70083,1,70094,70094,7,70188,70190,7,70194,70195,7,70197,70197,7,70206,70206,5,70368,70370,7,70400,70401,5,70459,70460,5,70463,70463,7,70465,70468,7,70475,70477,7,70498,70499,7,70512,70516,5,70712,70719,5,70722,70724,5,70726,70726,5,70832,70832,5,70835,70840,5,70842,70842,5,70845,70845,5,70847,70848,5,70850,70851,5,71088,71089,7,71096,71099,7,71102,71102,7,71132,71133,5,71219,71226,5,71229,71229,5,71231,71232,5,71340,71340,7,71342,71343,7,71350,71350,7,71453,71455,5,71462,71462,7,71724,71726,7,71736,71736,7,71984,71984,5,71991,71992,7,71997,71997,7,71999,71999,1,72001,72001,1,72003,72003,5,72148,72151,5,72156,72159,7,72164,72164,7,72243,72248,5,72250,72250,1,72263,72263,5,72279,72280,7,72324,72329,1,72343,72343,7,72751,72751,7,72760,72765,5,72767,72767,5,72873,72873,7,72881,72881,7,72884,72884,7,73009,73014,5,73020,73021,5,73030,73030,1,73098,73102,7,73107,73108,7,73110,73110,7,73459,73460,5,78896,78904,4,92976,92982,5,94033,94087,7,94180,94180,5,113821,113822,5,118528,118573,5,119141,119141,5,119143,119145,5,119150,119154,5,119163,119170,5,119210,119213,5,121344,121398,5,121461,121461,5,121499,121503,5,122880,122886,5,122907,122913,5,122918,122922,5,123566,123566,5,125136,125142,5,126976,126979,14,126981,127182,14,127184,127231,14,127279,127279,14,127344,127345,14,127374,127374,14,127405,127461,14,127489,127490,14,127514,127514,14,127538,127546,14,127561,127567,14,127570,127743,14,127757,127758,14,127760,127760,14,127762,127762,14,127766,127768,14,127770,127770,14,127772,127772,14,127775,127776,14,127778,127779,14,127789,127791,14,127794,127795,14,127798,127798,14,127819,127819,14,127824,127824,14,127868,127868,14,127870,127871,14,127892,127893,14,127896,127896,14,127900,127901,14,127904,127940,14,127942,127942,14,127944,127944,14,127946,127946,14,127951,127955,14,127968,127971,14,127973,127984,14,127987,127987,14,127989,127989,14,127991,127991,14,127995,127999,5,128008,128008,14,128012,128014,14,128017,128018,14,128020,128020,14,128022,128022,14,128042,128042,14,128063,128063,14,128065,128065,14,128101,128101,14,128108,128109,14,128173,128173,14,128182,128183,14,128236,128237,14,128239,128239,14,128245,128245,14,128248,128248,14,128253,128253,14,128255,128258,14,128260,128263,14,128265,128265,14,128277,128277,14,128300,128301,14,128326,128328,14,128331,128334,14,128336,128347,14,128360,128366,14,128369,128370,14,128378,128378,14,128391,128391,14,128394,128397,14,128400,128400,14,128405,128406,14,128420,128420,14,128422,128423,14,128425,128432,14,128435,128443,14,128445,128449,14,128453,128464,14,128468,128475,14,128479,128480,14,128482,128482,14,128484,128487,14,128489,128494,14,128496,128498,14,128500,128505,14,128507,128511,14,128513,128518,14,128521,128525,14,128527,128527,14,128529,128529,14,128533,128533,14,128535,128535,14,128537,128537,14]")}var yn;(function(e){e[e.zwj=8205]="zwj",e[e.emojiVariantSelector=65039]="emojiVariantSelector",e[e.enclosingKeyCap=8419]="enclosingKeyCap",e[e.space=32]="space"})(yn||(yn={}));var vl=class ae{static{this.c=new $t(()=>JSON.parse('{"_common":[8232,32,8233,32,5760,32,8192,32,8193,32,8194,32,8195,32,8196,32,8197,32,8198,32,8200,32,8201,32,8202,32,8287,32,8199,32,8239,32,2042,95,65101,95,65102,95,65103,95,8208,45,8209,45,8210,45,65112,45,1748,45,8259,45,727,45,8722,45,10134,45,11450,45,1549,44,1643,44,184,44,42233,44,894,59,2307,58,2691,58,1417,58,1795,58,1796,58,5868,58,65072,58,6147,58,6153,58,8282,58,1475,58,760,58,42889,58,8758,58,720,58,42237,58,451,33,11601,33,660,63,577,63,2429,63,5038,63,42731,63,119149,46,8228,46,1793,46,1794,46,42510,46,68176,46,1632,46,1776,46,42232,46,1373,96,65287,96,8219,96,1523,96,8242,96,1370,96,8175,96,65344,96,900,96,8189,96,8125,96,8127,96,8190,96,697,96,884,96,712,96,714,96,715,96,756,96,699,96,701,96,700,96,702,96,42892,96,1497,96,2036,96,2037,96,5194,96,5836,96,94033,96,94034,96,65339,91,10088,40,10098,40,12308,40,64830,40,65341,93,10089,41,10099,41,12309,41,64831,41,10100,123,119060,123,10101,125,65342,94,8270,42,1645,42,8727,42,66335,42,5941,47,8257,47,8725,47,8260,47,9585,47,10187,47,10744,47,119354,47,12755,47,12339,47,11462,47,20031,47,12035,47,65340,92,65128,92,8726,92,10189,92,10741,92,10745,92,119311,92,119355,92,12756,92,20022,92,12034,92,42872,38,708,94,710,94,5869,43,10133,43,66203,43,8249,60,10094,60,706,60,119350,60,5176,60,5810,60,5120,61,11840,61,12448,61,42239,61,8250,62,10095,62,707,62,119351,62,5171,62,94015,62,8275,126,732,126,8128,126,8764,126,65372,124,65293,45,118002,50,120784,50,120794,50,120804,50,120814,50,120824,50,130034,50,42842,50,423,50,1000,50,42564,50,5311,50,42735,50,119302,51,118003,51,120785,51,120795,51,120805,51,120815,51,120825,51,130035,51,42923,51,540,51,439,51,42858,51,11468,51,1248,51,94011,51,71882,51,118004,52,120786,52,120796,52,120806,52,120816,52,120826,52,130036,52,5070,52,71855,52,118005,53,120787,53,120797,53,120807,53,120817,53,120827,53,130037,53,444,53,71867,53,118006,54,120788,54,120798,54,120808,54,120818,54,120828,54,130038,54,11474,54,5102,54,71893,54,119314,55,118007,55,120789,55,120799,55,120809,55,120819,55,120829,55,130039,55,66770,55,71878,55,2819,56,2538,56,2666,56,125131,56,118008,56,120790,56,120800,56,120810,56,120820,56,120830,56,130040,56,547,56,546,56,66330,56,2663,57,2920,57,2541,57,3437,57,118009,57,120791,57,120801,57,120811,57,120821,57,120831,57,130041,57,42862,57,11466,57,71884,57,71852,57,71894,57,9082,97,65345,97,119834,97,119886,97,119938,97,119990,97,120042,97,120094,97,120146,97,120198,97,120250,97,120302,97,120354,97,120406,97,120458,97,593,97,945,97,120514,97,120572,97,120630,97,120688,97,120746,97,65313,65,117974,65,119808,65,119860,65,119912,65,119964,65,120016,65,120068,65,120120,65,120172,65,120224,65,120276,65,120328,65,120380,65,120432,65,913,65,120488,65,120546,65,120604,65,120662,65,120720,65,5034,65,5573,65,42222,65,94016,65,66208,65,119835,98,119887,98,119939,98,119991,98,120043,98,120095,98,120147,98,120199,98,120251,98,120303,98,120355,98,120407,98,120459,98,388,98,5071,98,5234,98,5551,98,65314,66,8492,66,117975,66,119809,66,119861,66,119913,66,120017,66,120069,66,120121,66,120173,66,120225,66,120277,66,120329,66,120381,66,120433,66,42932,66,914,66,120489,66,120547,66,120605,66,120663,66,120721,66,5108,66,5623,66,42192,66,66178,66,66209,66,66305,66,65347,99,8573,99,119836,99,119888,99,119940,99,119992,99,120044,99,120096,99,120148,99,120200,99,120252,99,120304,99,120356,99,120408,99,120460,99,7428,99,1010,99,11429,99,43951,99,66621,99,128844,67,71913,67,71922,67,65315,67,8557,67,8450,67,8493,67,117976,67,119810,67,119862,67,119914,67,119966,67,120018,67,120174,67,120226,67,120278,67,120330,67,120382,67,120434,67,1017,67,11428,67,5087,67,42202,67,66210,67,66306,67,66581,67,66844,67,8574,100,8518,100,119837,100,119889,100,119941,100,119993,100,120045,100,120097,100,120149,100,120201,100,120253,100,120305,100,120357,100,120409,100,120461,100,1281,100,5095,100,5231,100,42194,100,8558,68,8517,68,117977,68,119811,68,119863,68,119915,68,119967,68,120019,68,120071,68,120123,68,120175,68,120227,68,120279,68,120331,68,120383,68,120435,68,5024,68,5598,68,5610,68,42195,68,8494,101,65349,101,8495,101,8519,101,119838,101,119890,101,119942,101,120046,101,120098,101,120150,101,120202,101,120254,101,120306,101,120358,101,120410,101,120462,101,43826,101,1213,101,8959,69,65317,69,8496,69,117978,69,119812,69,119864,69,119916,69,120020,69,120072,69,120124,69,120176,69,120228,69,120280,69,120332,69,120384,69,120436,69,917,69,120492,69,120550,69,120608,69,120666,69,120724,69,11577,69,5036,69,42224,69,71846,69,71854,69,66182,69,119839,102,119891,102,119943,102,119995,102,120047,102,120099,102,120151,102,120203,102,120255,102,120307,102,120359,102,120411,102,120463,102,43829,102,42905,102,383,102,7837,102,1412,102,119315,70,8497,70,117979,70,119813,70,119865,70,119917,70,120021,70,120073,70,120125,70,120177,70,120229,70,120281,70,120333,70,120385,70,120437,70,42904,70,988,70,120778,70,5556,70,42205,70,71874,70,71842,70,66183,70,66213,70,66853,70,65351,103,8458,103,119840,103,119892,103,119944,103,120048,103,120100,103,120152,103,120204,103,120256,103,120308,103,120360,103,120412,103,120464,103,609,103,7555,103,397,103,1409,103,117980,71,119814,71,119866,71,119918,71,119970,71,120022,71,120074,71,120126,71,120178,71,120230,71,120282,71,120334,71,120386,71,120438,71,1292,71,5056,71,5107,71,42198,71,65352,104,8462,104,119841,104,119945,104,119997,104,120049,104,120101,104,120153,104,120205,104,120257,104,120309,104,120361,104,120413,104,120465,104,1211,104,1392,104,5058,104,65320,72,8459,72,8460,72,8461,72,117981,72,119815,72,119867,72,119919,72,120023,72,120179,72,120231,72,120283,72,120335,72,120387,72,120439,72,919,72,120494,72,120552,72,120610,72,120668,72,120726,72,11406,72,5051,72,5500,72,42215,72,66255,72,731,105,9075,105,65353,105,8560,105,8505,105,8520,105,119842,105,119894,105,119946,105,119998,105,120050,105,120102,105,120154,105,120206,105,120258,105,120310,105,120362,105,120414,105,120466,105,120484,105,618,105,617,105,953,105,8126,105,890,105,120522,105,120580,105,120638,105,120696,105,120754,105,1110,105,42567,105,1231,105,43893,105,5029,105,71875,105,65354,106,8521,106,119843,106,119895,106,119947,106,119999,106,120051,106,120103,106,120155,106,120207,106,120259,106,120311,106,120363,106,120415,106,120467,106,1011,106,1112,106,65322,74,117983,74,119817,74,119869,74,119921,74,119973,74,120025,74,120077,74,120129,74,120181,74,120233,74,120285,74,120337,74,120389,74,120441,74,42930,74,895,74,1032,74,5035,74,5261,74,42201,74,119844,107,119896,107,119948,107,120000,107,120052,107,120104,107,120156,107,120208,107,120260,107,120312,107,120364,107,120416,107,120468,107,8490,75,65323,75,117984,75,119818,75,119870,75,119922,75,119974,75,120026,75,120078,75,120130,75,120182,75,120234,75,120286,75,120338,75,120390,75,120442,75,922,75,120497,75,120555,75,120613,75,120671,75,120729,75,11412,75,5094,75,5845,75,42199,75,66840,75,1472,108,8739,73,9213,73,65512,73,1633,108,1777,73,66336,108,125127,108,118001,108,120783,73,120793,73,120803,73,120813,73,120823,73,130033,73,65321,73,8544,73,8464,73,8465,73,117982,108,119816,73,119868,73,119920,73,120024,73,120128,73,120180,73,120232,73,120284,73,120336,73,120388,73,120440,73,65356,108,8572,73,8467,108,119845,108,119897,108,119949,108,120001,108,120053,108,120105,73,120157,73,120209,73,120261,73,120313,73,120365,73,120417,73,120469,73,448,73,120496,73,120554,73,120612,73,120670,73,120728,73,11410,73,1030,73,1216,73,1493,108,1503,108,1575,108,126464,108,126592,108,65166,108,65165,108,1994,108,11599,73,5825,73,42226,73,93992,73,66186,124,66313,124,119338,76,8556,76,8466,76,117985,76,119819,76,119871,76,119923,76,120027,76,120079,76,120131,76,120183,76,120235,76,120287,76,120339,76,120391,76,120443,76,11472,76,5086,76,5290,76,42209,76,93974,76,71843,76,71858,76,66587,76,66854,76,65325,77,8559,77,8499,77,117986,77,119820,77,119872,77,119924,77,120028,77,120080,77,120132,77,120184,77,120236,77,120288,77,120340,77,120392,77,120444,77,924,77,120499,77,120557,77,120615,77,120673,77,120731,77,1018,77,11416,77,5047,77,5616,77,5846,77,42207,77,66224,77,66321,77,119847,110,119899,110,119951,110,120003,110,120055,110,120107,110,120159,110,120211,110,120263,110,120315,110,120367,110,120419,110,120471,110,1400,110,1404,110,65326,78,8469,78,117987,78,119821,78,119873,78,119925,78,119977,78,120029,78,120081,78,120185,78,120237,78,120289,78,120341,78,120393,78,120445,78,925,78,120500,78,120558,78,120616,78,120674,78,120732,78,11418,78,42208,78,66835,78,3074,111,3202,111,3330,111,3458,111,2406,111,2662,111,2790,111,3046,111,3174,111,3302,111,3430,111,3664,111,3792,111,4160,111,1637,111,1781,111,65359,111,8500,111,119848,111,119900,111,119952,111,120056,111,120108,111,120160,111,120212,111,120264,111,120316,111,120368,111,120420,111,120472,111,7439,111,7441,111,43837,111,959,111,120528,111,120586,111,120644,111,120702,111,120760,111,963,111,120532,111,120590,111,120648,111,120706,111,120764,111,11423,111,4351,111,1413,111,1505,111,1607,111,126500,111,126564,111,126596,111,65259,111,65260,111,65258,111,65257,111,1726,111,64428,111,64429,111,64427,111,64426,111,1729,111,64424,111,64425,111,64423,111,64422,111,1749,111,3360,111,4125,111,66794,111,71880,111,71895,111,66604,111,1984,79,2534,79,2918,79,12295,79,70864,79,71904,79,118000,79,120782,79,120792,79,120802,79,120812,79,120822,79,130032,79,65327,79,117988,79,119822,79,119874,79,119926,79,119978,79,120030,79,120082,79,120134,79,120186,79,120238,79,120290,79,120342,79,120394,79,120446,79,927,79,120502,79,120560,79,120618,79,120676,79,120734,79,11422,79,1365,79,11604,79,4816,79,2848,79,66754,79,42227,79,71861,79,66194,79,66219,79,66564,79,66838,79,9076,112,65360,112,119849,112,119901,112,119953,112,120005,112,120057,112,120109,112,120161,112,120213,112,120265,112,120317,112,120369,112,120421,112,120473,112,961,112,120530,112,120544,112,120588,112,120602,112,120646,112,120660,112,120704,112,120718,112,120762,112,120776,112,11427,112,65328,80,8473,80,117989,80,119823,80,119875,80,119927,80,119979,80,120031,80,120083,80,120187,80,120239,80,120291,80,120343,80,120395,80,120447,80,929,80,120504,80,120562,80,120620,80,120678,80,120736,80,11426,80,5090,80,5229,80,42193,80,66197,80,119850,113,119902,113,119954,113,120006,113,120058,113,120110,113,120162,113,120214,113,120266,113,120318,113,120370,113,120422,113,120474,113,1307,113,1379,113,1382,113,8474,81,117990,81,119824,81,119876,81,119928,81,119980,81,120032,81,120084,81,120188,81,120240,81,120292,81,120344,81,120396,81,120448,81,11605,81,119851,114,119903,114,119955,114,120007,114,120059,114,120111,114,120163,114,120215,114,120267,114,120319,114,120371,114,120423,114,120475,114,43847,114,43848,114,7462,114,11397,114,43905,114,119318,82,8475,82,8476,82,8477,82,117991,82,119825,82,119877,82,119929,82,120033,82,120189,82,120241,82,120293,82,120345,82,120397,82,120449,82,422,82,5025,82,5074,82,66740,82,5511,82,42211,82,94005,82,65363,115,119852,115,119904,115,119956,115,120008,115,120060,115,120112,115,120164,115,120216,115,120268,115,120320,115,120372,115,120424,115,120476,115,42801,115,445,115,1109,115,43946,115,71873,115,66632,115,65331,83,117992,83,119826,83,119878,83,119930,83,119982,83,120034,83,120086,83,120138,83,120190,83,120242,83,120294,83,120346,83,120398,83,120450,83,1029,83,1359,83,5077,83,5082,83,42210,83,94010,83,66198,83,66592,83,119853,116,119905,116,119957,116,120009,116,120061,116,120113,116,120165,116,120217,116,120269,116,120321,116,120373,116,120425,116,120477,116,8868,84,10201,84,128872,84,65332,84,117993,84,119827,84,119879,84,119931,84,119983,84,120035,84,120087,84,120139,84,120191,84,120243,84,120295,84,120347,84,120399,84,120451,84,932,84,120507,84,120565,84,120623,84,120681,84,120739,84,11430,84,5026,84,42196,84,93962,84,71868,84,66199,84,66225,84,66325,84,119854,117,119906,117,119958,117,120010,117,120062,117,120114,117,120166,117,120218,117,120270,117,120322,117,120374,117,120426,117,120478,117,42911,117,7452,117,43854,117,43858,117,651,117,965,117,120534,117,120592,117,120650,117,120708,117,120766,117,1405,117,66806,117,71896,117,8746,85,8899,85,117994,85,119828,85,119880,85,119932,85,119984,85,120036,85,120088,85,120140,85,120192,85,120244,85,120296,85,120348,85,120400,85,120452,85,1357,85,4608,85,66766,85,5196,85,42228,85,94018,85,71864,85,8744,118,8897,118,65366,118,8564,118,119855,118,119907,118,119959,118,120011,118,120063,118,120115,118,120167,118,120219,118,120271,118,120323,118,120375,118,120427,118,120479,118,7456,118,957,118,120526,118,120584,118,120642,118,120700,118,120758,118,1141,118,1496,118,71430,118,43945,118,71872,118,119309,86,1639,86,1783,86,8548,86,117995,86,119829,86,119881,86,119933,86,119985,86,120037,86,120089,86,120141,86,120193,86,120245,86,120297,86,120349,86,120401,86,120453,86,1140,86,11576,86,5081,86,5167,86,42719,86,42214,86,93960,86,71840,86,66845,86,623,119,119856,119,119908,119,119960,119,120012,119,120064,119,120116,119,120168,119,120220,119,120272,119,120324,119,120376,119,120428,119,120480,119,7457,119,1121,119,1309,119,1377,119,71434,119,71438,119,71439,119,43907,119,71910,87,71919,87,117996,87,119830,87,119882,87,119934,87,119986,87,120038,87,120090,87,120142,87,120194,87,120246,87,120298,87,120350,87,120402,87,120454,87,1308,87,5043,87,5076,87,42218,87,5742,120,10539,120,10540,120,10799,120,65368,120,8569,120,119857,120,119909,120,119961,120,120013,120,120065,120,120117,120,120169,120,120221,120,120273,120,120325,120,120377,120,120429,120,120481,120,5441,120,5501,120,5741,88,9587,88,66338,88,71916,88,65336,88,8553,88,117997,88,119831,88,119883,88,119935,88,119987,88,120039,88,120091,88,120143,88,120195,88,120247,88,120299,88,120351,88,120403,88,120455,88,42931,88,935,88,120510,88,120568,88,120626,88,120684,88,120742,88,11436,88,11613,88,5815,88,42219,88,66192,88,66228,88,66327,88,66855,88,611,121,7564,121,65369,121,119858,121,119910,121,119962,121,120014,121,120066,121,120118,121,120170,121,120222,121,120274,121,120326,121,120378,121,120430,121,120482,121,655,121,7935,121,43866,121,947,121,8509,121,120516,121,120574,121,120632,121,120690,121,120748,121,1199,121,4327,121,71900,121,65337,89,117998,89,119832,89,119884,89,119936,89,119988,89,120040,89,120092,89,120144,89,120196,89,120248,89,120300,89,120352,89,120404,89,120456,89,933,89,978,89,120508,89,120566,89,120624,89,120682,89,120740,89,11432,89,1198,89,5033,89,5053,89,42220,89,94019,89,71844,89,66226,89,119859,122,119911,122,119963,122,120015,122,120067,122,120119,122,120171,122,120223,122,120275,122,120327,122,120379,122,120431,122,120483,122,7458,122,43923,122,71876,122,71909,90,66293,90,65338,90,8484,90,8488,90,117999,90,119833,90,119885,90,119937,90,119989,90,120041,90,120197,90,120249,90,120301,90,120353,90,120405,90,120457,90,918,90,120493,90,120551,90,120609,90,120667,90,120725,90,5059,90,42204,90,71849,90,65282,34,65283,35,65284,36,65285,37,65286,38,65290,42,65291,43,65294,46,65295,47,65296,48,65298,50,65299,51,65300,52,65301,53,65302,54,65303,55,65304,56,65305,57,65308,60,65309,61,65310,62,65312,64,65316,68,65318,70,65319,71,65324,76,65329,81,65330,82,65333,85,65334,86,65335,87,65343,95,65346,98,65348,100,65350,102,65355,107,65357,109,65358,110,65361,113,65362,114,65364,116,65365,117,65367,119,65370,122,65371,123,65373,125,119846,109],"_default":[160,32,8211,45,65374,126,8218,44,65306,58,65281,33,8216,96,8217,96,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65288,40,65289,41,65292,44,65297,49,65307,59,65311,63],"cs":[65374,126,8218,44,65306,58,65281,33,8216,96,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,1093,120,1061,88,1091,121,1059,89,65288,40,65289,41,65292,44,65297,49,65307,59,65311,63],"de":[65374,126,65306,58,65281,33,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,1093,120,1061,88,1091,121,1059,89,65288,40,65289,41,65292,44,65297,49,65307,59,65311,63],"es":[8211,45,65374,126,8218,44,65306,58,65281,33,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65288,40,65289,41,65292,44,65297,49,65307,59,65311,63],"fr":[65374,126,8218,44,65306,58,65281,33,8216,96,8245,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65288,40,65289,41,65292,44,65297,49,65307,59,65311,63],"it":[160,32,8211,45,65374,126,8218,44,65306,58,65281,33,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65288,40,65289,41,65292,44,65297,49,65307,59,65311,63],"ja":[8211,45,8218,44,65281,33,8216,96,8245,96,180,96,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65292,44,65297,49,65307,59],"ko":[8211,45,65374,126,8218,44,65306,58,65281,33,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65288,40,65289,41,65292,44,65297,49,65307,59,65311,63],"pl":[65374,126,65306,58,65281,33,8216,96,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65288,40,65289,41,65292,44,65297,49,65307,59,65311,63],"pt-BR":[65374,126,8218,44,65306,58,65281,33,8216,96,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65288,40,65289,41,65292,44,65297,49,65307,59,65311,63],"qps-ploc":[160,32,8211,45,65374,126,8218,44,65306,58,65281,33,8216,96,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65288,40,65289,41,65292,44,65297,49,65307,59,65311,63],"ru":[65374,126,8218,44,65306,58,65281,33,8216,96,8245,96,180,96,12494,47,305,105,921,73,1009,112,215,120,65288,40,65289,41,65292,44,65297,49,65307,59,65311,63],"tr":[160,32,8211,45,65374,126,8218,44,65306,58,65281,33,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65288,40,65289,41,65292,44,65297,49,65307,59,65311,63],"zh-hans":[160,32,65374,126,8218,44,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65297,49],"zh-hant":[8211,45,65374,126,8218,44,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89]}'))}static{this.d=new Es({getCacheKey:JSON.stringify},t=>{function n(c){const h=new Map;for(let f=0;f<c.length;f+=2)h.set(c[f],c[f+1]);return h}function r(c,h){const f=new Map(c);for(const[g,m]of h)f.set(g,m);return f}function i(c,h){if(!c)return h;const f=new Map;for(const[g,m]of c)h.has(g)&&f.set(g,m);return f}const s=this.c.value;let o=t.filter(c=>!c.startsWith("_")&&c in s);o.length===0&&(o=["_default"]);let l;for(const c of o){const h=n(s[c]);l=i(l,h)}const u=n(s._common),a=r(u,l);return new ae(a)})}static getInstance(t){return ae.d.get(Array.from(t))}static{this.e=new $t(()=>Object.keys(ae.c.value).filter(t=>!t.startsWith("_")))}static getLocales(){return ae.e.value}constructor(t){this.f=t}isAmbiguous(t){return this.f.has(t)}containsAmbiguousCharacter(t){for(let n=0;n<t.length;n++){const r=t.codePointAt(n);if(typeof r=="number"&&this.isAmbiguous(r))return!0}return!1}getPrimaryConfusable(t){return this.f.get(t)}getConfusableCodePoints(){return new Set(this.f.keys())}},wl=class ue{static c(){return JSON.parse('{"_common":[11,12,13,127,847,1564,4447,4448,6068,6069,6155,6156,6157,6158,7355,7356,8192,8193,8194,8195,8196,8197,8198,8199,8200,8201,8202,8204,8205,8206,8207,8234,8235,8236,8237,8238,8239,8287,8288,8289,8290,8291,8292,8293,8294,8295,8296,8297,8298,8299,8300,8301,8302,8303,10240,12644,65024,65025,65026,65027,65028,65029,65030,65031,65032,65033,65034,65035,65036,65037,65038,65039,65279,65440,65520,65521,65522,65523,65524,65525,65526,65527,65528,65532,78844,119155,119156,119157,119158,119159,119160,119161,119162,917504,917505,917506,917507,917508,917509,917510,917511,917512,917513,917514,917515,917516,917517,917518,917519,917520,917521,917522,917523,917524,917525,917526,917527,917528,917529,917530,917531,917532,917533,917534,917535,917536,917537,917538,917539,917540,917541,917542,917543,917544,917545,917546,917547,917548,917549,917550,917551,917552,917553,917554,917555,917556,917557,917558,917559,917560,917561,917562,917563,917564,917565,917566,917567,917568,917569,917570,917571,917572,917573,917574,917575,917576,917577,917578,917579,917580,917581,917582,917583,917584,917585,917586,917587,917588,917589,917590,917591,917592,917593,917594,917595,917596,917597,917598,917599,917600,917601,917602,917603,917604,917605,917606,917607,917608,917609,917610,917611,917612,917613,917614,917615,917616,917617,917618,917619,917620,917621,917622,917623,917624,917625,917626,917627,917628,917629,917630,917631,917760,917761,917762,917763,917764,917765,917766,917767,917768,917769,917770,917771,917772,917773,917774,917775,917776,917777,917778,917779,917780,917781,917782,917783,917784,917785,917786,917787,917788,917789,917790,917791,917792,917793,917794,917795,917796,917797,917798,917799,917800,917801,917802,917803,917804,917805,917806,917807,917808,917809,917810,917811,917812,917813,917814,917815,917816,917817,917818,917819,917820,917821,917822,917823,917824,917825,917826,917827,917828,917829,917830,917831,917832,917833,917834,917835,917836,917837,917838,917839,917840,917841,917842,917843,917844,917845,917846,917847,917848,917849,917850,917851,917852,917853,917854,917855,917856,917857,917858,917859,917860,917861,917862,917863,917864,917865,917866,917867,917868,917869,917870,917871,917872,917873,917874,917875,917876,917877,917878,917879,917880,917881,917882,917883,917884,917885,917886,917887,917888,917889,917890,917891,917892,917893,917894,917895,917896,917897,917898,917899,917900,917901,917902,917903,917904,917905,917906,917907,917908,917909,917910,917911,917912,917913,917914,917915,917916,917917,917918,917919,917920,917921,917922,917923,917924,917925,917926,917927,917928,917929,917930,917931,917932,917933,917934,917935,917936,917937,917938,917939,917940,917941,917942,917943,917944,917945,917946,917947,917948,917949,917950,917951,917952,917953,917954,917955,917956,917957,917958,917959,917960,917961,917962,917963,917964,917965,917966,917967,917968,917969,917970,917971,917972,917973,917974,917975,917976,917977,917978,917979,917980,917981,917982,917983,917984,917985,917986,917987,917988,917989,917990,917991,917992,917993,917994,917995,917996,917997,917998,917999],"cs":[173,8203,12288],"de":[173,8203,12288],"es":[8203,12288],"fr":[173,8203,12288],"it":[160,173,12288],"ja":[173],"ko":[173,12288],"pl":[173,8203,12288],"pt-BR":[173,8203,12288],"qps-ploc":[160,173,8203,12288],"ru":[173,12288],"tr":[160,173,8203,12288],"zh-hans":[160,173,8203,12288],"zh-hant":[173,12288]}')}static{this.d=void 0}static e(){return this.d||(this.d=new Set([...Object.values(ue.c())].flat())),this.d}static isInvisibleCharacter(t){return ue.e().has(t)}static containsInvisibleCharacter(t){for(let n=0;n<t.length;n++){const r=t.codePointAt(n);if(typeof r=="number"&&(ue.isInvisibleCharacter(r)||r===32))return!0}return!1}static get codePoints(){return ue.e()}},l1="default",Hs="$initialize",An;(function(e){e[e.Request=0]="Request",e[e.Reply=1]="Reply",e[e.SubscribeEvent=2]="SubscribeEvent",e[e.Event=3]="Event",e[e.UnsubscribeEvent=4]="UnsubscribeEvent"})(An||(An={}));var Gs=class{constructor(e,t,n,r,i){this.vsWorker=e,this.req=t,this.channel=n,this.method=r,this.args=i,this.type=0}},xn=class{constructor(e,t,n,r){this.vsWorker=e,this.seq=t,this.res=n,this.err=r,this.type=1}},Ks=class{constructor(e,t,n,r,i){this.vsWorker=e,this.req=t,this.channel=n,this.eventName=r,this.arg=i,this.type=2}},Js=class{constructor(e,t,n){this.vsWorker=e,this.req=t,this.event=n,this.type=3}},Xs=class{constructor(e,t){this.vsWorker=e,this.req=t,this.type=4}},Qs=class{constructor(e){this.a=-1,this.g=e,this.b=0,this.c=Object.create(null),this.d=new Map,this.f=new Map}setWorkerId(e){this.a=e}sendMessage(e,t,n){const r=String(++this.b);return new Promise((i,s)=>{this.c[r]={resolve:i,reject:s},this.o(new Gs(this.a,r,e,t,n))})}listen(e,t,n){let r=null;const i=new ht({onWillAddFirstListener:()=>{r=String(++this.b),this.d.set(r,i),this.o(new Ks(this.a,r,e,t,n))},onDidRemoveLastListener:()=>{this.d.delete(r),this.o(new Xs(this.a,r)),r=null}});return i.event}handleMessage(e){!e||!e.vsWorker||this.a!==-1&&e.vsWorker!==this.a||this.h(e)}createProxyToRemoteChannel(e,t){const n={get:(r,i)=>(typeof i=="string"&&!r[i]&&(Nn(i)?r[i]=s=>this.listen(e,i,s):En(i)?r[i]=this.listen(e,i,void 0):i.charCodeAt(0)===36&&(r[i]=async(...s)=>(await t?.(),this.sendMessage(e,i,s)))),r[i])};return new Proxy(Object.create(null),n)}h(e){switch(e.type){case 1:return this.j(e);case 0:return this.k(e);case 2:return this.l(e);case 3:return this.m(e);case 4:return this.n(e)}}j(e){if(!this.c[e.seq]){console.warn("Got reply to unknown seq");return}const t=this.c[e.seq];if(delete this.c[e.seq],e.err){let n=e.err;e.err.$isError&&(n=new Error,n.name=e.err.name,n.message=e.err.message,n.stack=e.err.stack),t.reject(n);return}t.resolve(e.res)}k(e){const t=e.req;this.g.handleMessage(e.channel,e.method,e.args).then(r=>{this.o(new xn(this.a,t,r,void 0))},r=>{r.detail instanceof Error&&(r.detail=Be(r.detail)),this.o(new xn(this.a,t,void 0,Be(r)))})}l(e){const t=e.req,n=this.g.handleEvent(e.channel,e.eventName,e.arg)(r=>{this.o(new Js(this.a,t,r))});this.f.set(t,n)}m(e){if(!this.d.has(e.req)){console.warn("Got event for unknown req");return}this.d.get(e.req).fire(e.event)}n(e){if(!this.f.has(e.req)){console.warn("Got unsubscribe for unknown req");return}this.f.get(e.req).dispose(),this.f.delete(e.req)}o(e){const t=[];if(e.type===0)for(let n=0;n<e.args.length;n++)e.args[n]instanceof ArrayBuffer&&t.push(e.args[n]);else e.type===1&&e.res instanceof ArrayBuffer&&t.push(e.res);this.g.sendMessage(e,t)}};function En(e){return e[0]==="o"&&e[1]==="n"&&bn(e.charCodeAt(2))}function Nn(e){return/^onDynamic/.test(e)&&bn(e.charCodeAt(9))}var Zs=class{constructor(e,t){this.b=new Map,this.c=new Map,this.a=new Qs({sendMessage:(n,r)=>{e(n,r)},handleMessage:(n,r,i)=>this.d(n,r,i),handleEvent:(n,r,i)=>this.f(n,r,i)}),this.requestHandler=t(this)}onmessage(e){this.a.handleMessage(e)}d(e,t,n){if(e===l1&&t===Hs)return this.g(n[0]);const r=e===l1?this.requestHandler:this.b.get(e);if(!r)return Promise.reject(new Error(`Missing channel ${e} on worker thread`));if(typeof r[t]!="function")return Promise.reject(new Error(`Missing method ${t} on worker thread channel ${e}`));try{return Promise.resolve(r[t].apply(r,n))}catch(i){return Promise.reject(i)}}f(e,t,n){const r=e===l1?this.requestHandler:this.b.get(e);if(!r)throw new Error(`Missing channel ${e} on worker thread`);if(Nn(t)){const i=r[t].call(r,n);if(typeof i!="function")throw new Error(`Missing dynamic event ${t} on request handler.`);return i}if(En(t)){const i=r[t];if(typeof i!="function")throw new Error(`Missing event ${t} on request handler.`);return i}throw new Error(`Malformed event name ${t}`)}setChannel(e,t){this.b.set(e,t)}getChannel(e){if(!this.c.has(e)){const t=this.a.createProxyToRemoteChannel(e);this.c.set(e,t)}return this.c.get(e)}async g(e){this.a.setWorkerId(e)}},a1=!1;function Ys(e){if(a1)throw new Error("WebWorker already initialized!");a1=!0;const t=new Zs(n=>globalThis.postMessage(n),n=>e(n));return globalThis.onmessage=n=>{t.onmessage(n.data)},t}function to(e){globalThis.onmessage=t=>{a1||Ys(e)}}var at=class{constructor(e,t,n,r){this.originalStart=e,this.originalLength=t,this.modifiedStart=n,this.modifiedLength=r}getOriginalEnd(){return this.originalStart+this.originalLength}getModifiedEnd(){return this.modifiedStart+this.modifiedLength}},Qt=typeof Buffer<"u",eo=new $t(()=>new Uint8Array(256)),u1,h1,ye=class ut{static alloc(t){return Qt?new ut(Buffer.allocUnsafe(t)):new ut(new Uint8Array(t))}static wrap(t){return Qt&&!Buffer.isBuffer(t)&&(t=Buffer.from(t.buffer,t.byteOffset,t.byteLength)),new ut(t)}static fromString(t,n){return!(n?.dontUseNodeBuffer||!1)&&Qt?new ut(Buffer.from(t)):(u1||(u1=new TextEncoder),new ut(u1.encode(t)))}static fromByteArray(t){const n=ut.alloc(t.length);for(let r=0,i=t.length;r<i;r++)n.buffer[r]=t[r];return n}static concat(t,n){if(typeof n>"u"){n=0;for(let s=0,o=t.length;s<o;s++)n+=t[s].byteLength}const r=ut.alloc(n);let i=0;for(let s=0,o=t.length;s<o;s++){const l=t[s];r.set(l,i),i+=l.byteLength}return r}static isNativeBuffer(t){return Qt&&Buffer.isBuffer(t)}constructor(t){this.buffer=t,this.byteLength=this.buffer.byteLength}clone(){const t=ut.alloc(this.byteLength);return t.set(this),t}toString(){return Qt?this.buffer.toString():(h1||(h1=new TextDecoder),h1.decode(this.buffer))}slice(t,n){return new ut(this.buffer.subarray(t,n))}set(t,n){if(t instanceof ut)this.buffer.set(t.buffer,n);else if(t instanceof Uint8Array)this.buffer.set(t,n);else if(t instanceof ArrayBuffer)this.buffer.set(new Uint8Array(t),n);else if(ArrayBuffer.isView(t))this.buffer.set(new Uint8Array(t.buffer,t.byteOffset,t.byteLength),n);else throw new Error("Unknown argument 'array'")}readUInt32BE(t){return Ae(this.buffer,t)}writeUInt32BE(t,n){xe(this.buffer,t,n)}readUInt32LE(t){return so(this.buffer,t)}writeUInt32LE(t,n){oo(this.buffer,t,n)}readUInt8(t){return lo(this.buffer,t)}writeUInt8(t,n){ao(this.buffer,t,n)}indexOf(t,n=0){return no(this.buffer,t instanceof ut?t.buffer:t,n)}equals(t){return this===t?!0:this.byteLength!==t.byteLength?!1:this.buffer.every((n,r)=>n===t.buffer[r])}};function no(e,t,n=0){const r=t.byteLength,i=e.byteLength;if(r===0)return 0;if(r===1)return e.indexOf(t[0]);if(r>i-n)return-1;const s=eo.value;s.fill(t.length);for(let a=0;a<t.length;a++)s[t[a]]=t.length-a-1;let o=n+t.length-1,l=o,u=-1;for(;o<i;)if(e[o]===t[l]){if(l===0){u=o;break}o--,l--}else o+=Math.max(t.length-l,s[e[o]]),l=t.length-1;return u}function ro(e,t){return e[t+0]<<0>>>0|e[t+1]<<8>>>0}function io(e,t,n){e[n+0]=t&255,t=t>>>8,e[n+1]=t&255}function Ae(e,t){return e[t]*2**24+e[t+1]*2**16+e[t+2]*2**8+e[t+3]}function xe(e,t,n){e[n+3]=t,t=t>>>8,e[n+2]=t,t=t>>>8,e[n+1]=t,t=t>>>8,e[n]=t}function so(e,t){return e[t+0]<<0>>>0|e[t+1]<<8>>>0|e[t+2]<<16>>>0|e[t+3]<<24>>>0}function oo(e,t,n){e[n+0]=t&255,t=t>>>8,e[n+1]=t&255,t=t>>>8,e[n+2]=t&255,t=t>>>8,e[n+3]=t&255}function lo(e,t){return e[t]}function ao(e,t,n){e[n]=t}function In(e){let t=0,n=0,r=0;const i=new Uint8Array(Math.floor(e.length/4*3)),s=l=>{switch(n){case 3:i[r++]=t|l,n=0;break;case 2:i[r++]=t|l>>>2,t=l<<6,n=3;break;case 1:i[r++]=t|l>>>4,t=l<<4,n=2;break;default:t=l<<2,n=1}};for(let l=0;l<e.length;l++){const u=e.charCodeAt(l);if(u>=65&&u<=90)s(u-65);else if(u>=97&&u<=122)s(u-97+26);else if(u>=48&&u<=57)s(u-48+52);else if(u===43||u===45)s(62);else if(u===47||u===95)s(63);else{if(u===61)break;throw new SyntaxError(`Unexpected base64 character ${e[l]}`)}}const o=r;for(;n>0;)s(0);return ye.wrap(i).slice(0,o)}var uo="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",ho="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_";function Sn({buffer:e},t=!0,n=!1){const r=n?ho:uo;let i="";const s=e.byteLength%3;let o=0;for(;o<e.byteLength-s;o+=3){const l=e[o+0],u=e[o+1],a=e[o+2];i+=r[l>>>2],i+=r[(l<<4|u>>>4)&63],i+=r[(u<<2|a>>>6)&63],i+=r[a&63]}if(s===1){const l=e[o+0];i+=r[l>>>2],i+=r[l<<4&63],t&&(i+="==")}else if(s===2){const l=e[o+0],u=e[o+1];i+=r[l>>>2],i+=r[(l<<4|u>>>4)&63],i+=r[u<<2&63],t&&(i+="=")}return i}var $n="0123456789abcdef";function co({buffer:e}){let t="";for(let n=0;n<e.length;n++){const r=e[n];t+=$n[r>>>4],t+=$n[r&15]}return t}function fo(e){return ct(e,0)}function ct(e,t){switch(typeof e){case"object":return e===null?ot(349,t):Array.isArray(e)?mo(e,t):po(e,t);case"string":return c1(e,t);case"boolean":return go(e,t);case"number":return ot(e,t);case"undefined":return ot(937,t);default:return ot(617,t)}}function ot(e,t){return(t<<5)-t+e|0}function go(e,t){return ot(e?433:863,t)}function c1(e,t){t=ot(149417,t);for(let n=0,r=e.length;n<r;n++)t=ot(e.charCodeAt(n),t);return t}function mo(e,t){return t=ot(104579,t),e.reduce((n,r)=>ct(r,n),t)}function po(e,t){return t=ot(181387,t),Object.keys(e).sort().reduce((n,r)=>(n=c1(r,n),ct(e[r],n)),t)}var On;(function(e){e[e.BLOCK_SIZE=64]="BLOCK_SIZE",e[e.UNICODE_REPLACEMENT=65533]="UNICODE_REPLACEMENT"})(On||(On={}));function f1(e,t,n=32){const r=n-t,i=~((1<<r)-1);return(e<<t|(i&e)>>>r)>>>0}function Zt(e,t=32){return e instanceof ArrayBuffer?co(ye.wrap(new Uint8Array(e))):(e>>>0).toString(16).padStart(t/4,"0")}var Cl=class ai{static{this.g=new DataView(new ArrayBuffer(320))}constructor(){this.h=1732584193,this.l=4023233417,this.m=2562383102,this.n=271733878,this.o=3285377520,this.p=new Uint8Array(67),this.q=new DataView(this.p.buffer),this.r=0,this.t=0,this.u=0,this.v=!1}update(t){const n=t.length;if(n===0)return;const r=this.p;let i=this.r,s=this.u,o,l;for(s!==0?(o=s,l=-1,s=0):(o=t.charCodeAt(0),l=0);;){let u=o;if(i1(o))if(l+1<n){const a=t.charCodeAt(l+1);s1(a)?(l++,u=vn(o,a)):u=65533}else{s=o;break}else s1(o)&&(u=65533);if(i=this.w(r,i,u),l++,l<n)o=t.charCodeAt(l);else break}this.r=i,this.u=s}w(t,n,r){return r<128?t[n++]=r:r<2048?(t[n++]=192|(r&1984)>>>6,t[n++]=128|(r&63)>>>0):r<65536?(t[n++]=224|(r&61440)>>>12,t[n++]=128|(r&4032)>>>6,t[n++]=128|(r&63)>>>0):(t[n++]=240|(r&1835008)>>>18,t[n++]=128|(r&258048)>>>12,t[n++]=128|(r&4032)>>>6,t[n++]=128|(r&63)>>>0),n>=64&&(this.y(),n-=64,this.t+=64,t[0]=t[64],t[1]=t[65],t[2]=t[66]),n}digest(){return this.v||(this.v=!0,this.u&&(this.u=0,this.r=this.w(this.p,this.r,65533)),this.t+=this.r,this.x()),Zt(this.h)+Zt(this.l)+Zt(this.m)+Zt(this.n)+Zt(this.o)}x(){this.p[this.r++]=128,this.p.subarray(this.r).fill(0),this.r>56&&(this.y(),this.p.fill(0));const t=8*this.t;this.q.setUint32(56,Math.floor(t/4294967296),!1),this.q.setUint32(60,t%4294967296,!1),this.y()}y(){const t=ai.g,n=this.q;for(let h=0;h<64;h+=4)t.setUint32(h,n.getUint32(h,!1),!1);for(let h=64;h<320;h+=4)t.setUint32(h,f1(t.getUint32(h-12,!1)^t.getUint32(h-32,!1)^t.getUint32(h-56,!1)^t.getUint32(h-64,!1),1),!1);let r=this.h,i=this.l,s=this.m,o=this.n,l=this.o,u,a,c;for(let h=0;h<80;h++)h<20?(u=i&s|~i&o,a=1518500249):h<40?(u=i^s^o,a=1859775393):h<60?(u=i&s|i&o|s&o,a=2400959708):(u=i^s^o,a=3395469782),c=f1(r,5)+u+l+a+t.getUint32(h*4,!1)&4294967295,l=o,o=s,s=f1(i,30),i=r,r=c;this.h=this.h+r&4294967295,this.l=this.l+i&4294967295,this.m=this.m+s&4294967295,this.n=this.n+o&4294967295,this.o=this.o+l&4294967295}},Tt=class{static Assert(e,t){if(!e)throw new Error(t)}},Bt=class{static Copy(e,t,n,r,i){for(let s=0;s<i;s++)n[r+s]=e[t+s]}static Copy2(e,t,n,r,i){for(let s=0;s<i;s++)n[r+s]=e[t+s]}},_n;(function(e){e[e.MaxDifferencesHistory=1447]="MaxDifferencesHistory"})(_n||(_n={}));var Mn=class{constructor(){this.a=[],this.b=1073741824,this.c=1073741824,this.d=0,this.e=0}MarkNextChange(){(this.d>0||this.e>0)&&this.a.push(new at(this.b,this.d,this.c,this.e)),this.d=0,this.e=0,this.b=1073741824,this.c=1073741824}AddOriginalElement(e,t){this.b=Math.min(this.b,e),this.c=Math.min(this.c,t),this.d++}AddModifiedElement(e,t){this.b=Math.min(this.b,e),this.c=Math.min(this.c,t),this.e++}getChanges(){return(this.d>0||this.e>0)&&this.MarkNextChange(),this.a}getReverseChanges(){return(this.d>0||this.e>0)&&this.MarkNextChange(),this.a.reverse(),this.a}},Dn=class Gt{constructor(t,n,r=null){this.a=r,this.b=t,this.c=n;const[i,s,o]=Gt.p(t),[l,u,a]=Gt.p(n);this.d=o&&a,this.e=i,this.f=s,this.g=l,this.h=u,this.m=[],this.n=[]}static o(t){return t.length>0&&typeof t[0]=="string"}static p(t){const n=t.getElements();if(Gt.o(n)){const r=new Int32Array(n.length);for(let i=0,s=n.length;i<s;i++)r[i]=c1(n[i],0);return[n,r,!0]}return n instanceof Int32Array?[[],n,!1]:[[],new Int32Array(n),!1]}q(t,n){return this.f[t]!==this.h[n]?!1:this.d?this.e[t]===this.g[n]:!0}r(t,n){if(!this.q(t,n))return!1;const r=Gt.s(this.b,t),i=Gt.s(this.c,n);return r===i}static s(t,n){return typeof t.getStrictElement=="function"?t.getStrictElement(n):null}u(t,n){return this.f[t]!==this.f[n]?!1:this.d?this.e[t]===this.e[n]:!0}v(t,n){return this.h[t]!==this.h[n]?!1:this.d?this.g[t]===this.g[n]:!0}ComputeDiff(t){return this.w(0,this.f.length-1,0,this.h.length-1,t)}w(t,n,r,i,s){const o=[!1];let l=this.x(t,n,r,i,o);return s&&(l=this.A(l)),{quitEarly:o[0],changes:l}}x(t,n,r,i,s){for(s[0]=!1;t<=n&&r<=i&&this.q(t,r);)t++,r++;for(;n>=t&&i>=r&&this.q(n,i);)n--,i--;if(t>n||r>i){let h;return r<=i?(Tt.Assert(t===n+1,"originalStart should only be one more than originalEnd"),h=[new at(t,0,r,i-r+1)]):t<=n?(Tt.Assert(r===i+1,"modifiedStart should only be one more than modifiedEnd"),h=[new at(t,n-t+1,r,0)]):(Tt.Assert(t===n+1,"originalStart should only be one more than originalEnd"),Tt.Assert(r===i+1,"modifiedStart should only be one more than modifiedEnd"),h=[]),h}const o=[0],l=[0],u=this.z(t,n,r,i,o,l,s),a=o[0],c=l[0];if(u!==null)return u;if(!s[0]){const h=this.x(t,a,r,c,s);let f=[];return s[0]?f=[new at(a+1,n-(a+1)+1,c+1,i-(c+1)+1)]:f=this.x(a+1,n,c+1,i,s),this.I(h,f)}return[new at(t,n-t+1,r,i-r+1)]}y(t,n,r,i,s,o,l,u,a,c,h,f,g,m,p,d,w,b){let L=null,D=null,$=new Mn,z=n,C=r,v=g[0]-d[0]-i,I=-1073741824,q=this.m.length-1;do{const R=v+t;R===z||R<C&&a[R-1]<a[R+1]?(h=a[R+1],m=h-v-i,h<I&&$.MarkNextChange(),I=h,$.AddModifiedElement(h+1,m),v=R+1-t):(h=a[R-1]+1,m=h-v-i,h<I&&$.MarkNextChange(),I=h-1,$.AddOriginalElement(h,m+1),v=R-1-t),q>=0&&(a=this.m[q],t=a[0],z=1,C=a.length-1)}while(--q>=-1);if(L=$.getReverseChanges(),b[0]){let R=g[0]+1,O=d[0]+1;if(L!==null&&L.length>0){const It=L[L.length-1];R=Math.max(R,It.getOriginalEnd()),O=Math.max(O,It.getModifiedEnd())}D=[new at(R,f-R+1,O,p-O+1)]}else{$=new Mn,z=o,C=l,v=g[0]-d[0]-u,I=1073741824,q=w?this.n.length-1:this.n.length-2;do{const R=v+s;R===z||R<C&&c[R-1]>=c[R+1]?(h=c[R+1]-1,m=h-v-u,h>I&&$.MarkNextChange(),I=h+1,$.AddOriginalElement(h+1,m+1),v=R+1-s):(h=c[R-1],m=h-v-u,h>I&&$.MarkNextChange(),I=h,$.AddModifiedElement(h+1,m+1),v=R-1-s),q>=0&&(c=this.n[q],s=c[0],z=1,C=c.length-1)}while(--q>=-1);D=$.getChanges()}return this.I(L,D)}z(t,n,r,i,s,o,l){let u=0,a=0,c=0,h=0,f=0,g=0;t--,r--,s[0]=0,o[0]=0,this.m=[],this.n=[];const m=n-t+(i-r),p=m+1,d=new Int32Array(p),w=new Int32Array(p),b=i-r,L=n-t,D=t-r,$=n-i,C=(L-b)%2===0;d[b]=t,w[L]=n,l[0]=!1;for(let v=1;v<=m/2+1;v++){let I=0,q=0;c=this.K(b-v,v,b,p),h=this.K(b+v,v,b,p);for(let O=c;O<=h;O+=2){O===c||O<h&&d[O-1]<d[O+1]?u=d[O+1]:u=d[O-1]+1,a=u-(O-b)-D;const It=u;for(;u<n&&a<i&&this.q(u+1,a+1);)u++,a++;if(d[O]=u,u+a>I+q&&(I=u,q=a),!C&&Math.abs(O-L)<=v-1&&u>=w[O])return s[0]=u,o[0]=a,It<=w[O]&&v<=1448?this.y(b,c,h,D,L,f,g,$,d,w,u,n,s,a,i,o,C,l):null}const R=(I-t+(q-r)-v)/2;if(this.a!==null&&!this.a(I,R))return l[0]=!0,s[0]=I,o[0]=q,R>0&&v<=1448?this.y(b,c,h,D,L,f,g,$,d,w,u,n,s,a,i,o,C,l):(t++,r++,[new at(t,n-t+1,r,i-r+1)]);f=this.K(L-v,v,L,p),g=this.K(L+v,v,L,p);for(let O=f;O<=g;O+=2){O===f||O<g&&w[O-1]>=w[O+1]?u=w[O+1]-1:u=w[O-1],a=u-(O-L)-$;const It=u;for(;u>t&&a>r&&this.q(u,a);)u--,a--;if(w[O]=u,C&&Math.abs(O-b)<=v&&u<=d[O])return s[0]=u,o[0]=a,It>=d[O]&&v<=1448?this.y(b,c,h,D,L,f,g,$,d,w,u,n,s,a,i,o,C,l):null}if(v<=1447){let O=new Int32Array(h-c+2);O[0]=b-c+1,Bt.Copy2(d,c,O,1,h-c+1),this.m.push(O),O=new Int32Array(g-f+2),O[0]=L-f+1,Bt.Copy2(w,f,O,1,g-f+1),this.n.push(O)}}return this.y(b,c,h,D,L,f,g,$,d,w,u,n,s,a,i,o,C,l)}A(t){for(let n=0;n<t.length;n++){const r=t[n],i=n<t.length-1?t[n+1].originalStart:this.f.length,s=n<t.length-1?t[n+1].modifiedStart:this.h.length,o=r.originalLength>0,l=r.modifiedLength>0;for(;r.originalStart+r.originalLength<i&&r.modifiedStart+r.modifiedLength<s&&(!o||this.u(r.originalStart,r.originalStart+r.originalLength))&&(!l||this.v(r.modifiedStart,r.modifiedStart+r.modifiedLength));){const a=this.r(r.originalStart,r.modifiedStart);if(this.r(r.originalStart+r.originalLength,r.modifiedStart+r.modifiedLength)&&!a)break;r.originalStart++,r.modifiedStart++}const u=[null];if(n<t.length-1&&this.J(t[n],t[n+1],u)){t[n]=u[0],t.splice(n+1,1),n--;continue}}for(let n=t.length-1;n>=0;n--){const r=t[n];let i=0,s=0;if(n>0){const h=t[n-1];i=h.originalStart+h.originalLength,s=h.modifiedStart+h.modifiedLength}const o=r.originalLength>0,l=r.modifiedLength>0;let u=0,a=this.H(r.originalStart,r.originalLength,r.modifiedStart,r.modifiedLength);for(let h=1;;h++){const f=r.originalStart-h,g=r.modifiedStart-h;if(f<i||g<s||o&&!this.u(f,f+r.originalLength)||l&&!this.v(g,g+r.modifiedLength))break;const p=(f===i&&g===s?5:0)+this.H(f,r.originalLength,g,r.modifiedLength);p>a&&(a=p,u=h)}r.originalStart-=u,r.modifiedStart-=u;const c=[null];if(n>0&&this.J(t[n-1],t[n],c)){t[n-1]=c[0],t.splice(n,1),n++;continue}}if(this.d)for(let n=1,r=t.length;n<r;n++){const i=t[n-1],s=t[n],o=s.originalStart-i.originalStart-i.originalLength,l=i.originalStart,u=s.originalStart+s.originalLength,a=u-l,c=i.modifiedStart,h=s.modifiedStart+s.modifiedLength,f=h-c;if(o<5&&a<20&&f<20){const g=this.B(l,a,c,f,o);if(g){const[m,p]=g;(m!==i.originalStart+i.originalLength||p!==i.modifiedStart+i.modifiedLength)&&(i.originalLength=m-i.originalStart,i.modifiedLength=p-i.modifiedStart,s.originalStart=m+o,s.modifiedStart=p+o,s.originalLength=u-s.originalStart,s.modifiedLength=h-s.modifiedStart)}}}return t}B(t,n,r,i,s){if(n<s||i<s)return null;const o=t+n-s+1,l=r+i-s+1;let u=0,a=0,c=0;for(let h=t;h<o;h++)for(let f=r;f<l;f++){const g=this.C(h,f,s);g>0&&g>u&&(u=g,a=h,c=f)}return u>0?[a,c]:null}C(t,n,r){let i=0;for(let s=0;s<r;s++){if(!this.q(t+s,n+s))return 0;i+=this.e[t+s].length}return i}D(t){return t<=0||t>=this.f.length-1?!0:this.d&&/^\s*$/.test(this.e[t])}E(t,n){if(this.D(t)||this.D(t-1))return!0;if(n>0){const r=t+n;if(this.D(r-1)||this.D(r))return!0}return!1}F(t){return t<=0||t>=this.h.length-1?!0:this.d&&/^\s*$/.test(this.g[t])}G(t,n){if(this.F(t)||this.F(t-1))return!0;if(n>0){const r=t+n;if(this.F(r-1)||this.F(r))return!0}return!1}H(t,n,r,i){const s=this.E(t,n)?1:0,o=this.G(r,i)?1:0;return s+o}I(t,n){const r=[];if(t.length===0||n.length===0)return n.length>0?n:t;if(this.J(t[t.length-1],n[0],r)){const i=new Array(t.length+n.length-1);return Bt.Copy(t,0,i,0,t.length-1),i[t.length-1]=r[0],Bt.Copy(n,1,i,t.length,n.length-1),i}else{const i=new Array(t.length+n.length);return Bt.Copy(t,0,i,0,t.length),Bt.Copy(n,0,i,t.length,n.length),i}}J(t,n,r){if(Tt.Assert(t.originalStart<=n.originalStart,"Left change is not less than or equal to right change"),Tt.Assert(t.modifiedStart<=n.modifiedStart,"Left change is not less than or equal to right change"),t.originalStart+t.originalLength>=n.originalStart||t.modifiedStart+t.modifiedLength>=n.modifiedStart){const i=t.originalStart;let s=t.originalLength;const o=t.modifiedStart;let l=t.modifiedLength;return t.originalStart+t.originalLength>=n.originalStart&&(s=n.originalStart+n.originalLength-t.originalStart),t.modifiedStart+t.modifiedLength>=n.modifiedStart&&(l=n.modifiedStart+n.modifiedLength-t.modifiedStart),r[0]=new at(i,s,o,l),!0}else return r[0]=null,!1}K(t,n,r,i){if(t>=0&&t<i)return t;const s=r,o=i-r-1,l=n%2===0;if(t<0){const u=s%2===0;return l===u?0:1}else{const u=o%2===0;return l===u?i-1:i-2}}},bt=new Uint32Array(65536),bo=(e,t)=>{const n=e.length,r=t.length,i=1<<n-1;let s=-1,o=0,l=n,u=n;for(;u--;)bt[e.charCodeAt(u)]|=1<<u;for(u=0;u<r;u++){let a=bt[t.charCodeAt(u)];const c=a|o;a|=(a&s)+s^s,o|=~(a|s),s&=a,o&i&&l++,s&i&&l--,o=o<<1|1,s=s<<1|~(c|o),o&=c}for(u=n;u--;)bt[e.charCodeAt(u)]=0;return l};function vo(e,t){const n=e.length,r=t.length,i=[],s=[],o=Math.ceil(n/32),l=Math.ceil(r/32);for(let m=0;m<o;m++)i[m]=-1,s[m]=0;let u=0;for(;u<l-1;u++){let m=0,p=-1;const d=u*32,w=Math.min(32,r)+d;for(let b=d;b<w;b++)bt[t.charCodeAt(b)]|=1<<b;for(let b=0;b<n;b++){const L=bt[e.charCodeAt(b)],D=i[b/32|0]>>>b&1,$=s[b/32|0]>>>b&1,z=L|m,C=((L|$)&p)+p^p|L|$;let v=m|~(C|p),I=p&C;v>>>31^D&&(i[b/32|0]^=1<<b),I>>>31^$&&(s[b/32|0]^=1<<b),v=v<<1|D,I=I<<1|$,p=I|~(z|v),m=v&z}for(let b=d;b<w;b++)bt[t.charCodeAt(b)]=0}let a=0,c=-1;const h=u*32,f=Math.min(32,r-h)+h;for(let m=h;m<f;m++)bt[t.charCodeAt(m)]|=1<<m;let g=r;for(let m=0;m<n;m++){const p=bt[e.charCodeAt(m)],d=i[m/32|0]>>>m&1,w=s[m/32|0]>>>m&1,b=p|a,L=((p|w)&c)+c^c|p|w;let D=a|~(L|c),$=c&L;g+=D>>>r-1&1,g-=$>>>r-1&1,D>>>31^d&&(i[m/32|0]^=1<<m),$>>>31^w&&(s[m/32|0]^=1<<m),D=D<<1|d,$=$<<1|w,c=$|~(b|D),a=D&b}for(let m=h;m<f;m++)bt[t.charCodeAt(m)]=0;return g}function wo(e,t){if(e.length<t.length){const n=t;t=e,e=n}return t.length===0?e.length:e.length<=32?bo(e,t):vo(e,t)}var Rt,d1=globalThis.vscode;if(typeof d1<"u"&&typeof d1.process<"u"){const e=d1.process;Rt={get platform(){return e.platform},get arch(){return e.arch},get env(){return e.env},cwd(){return e.cwd()}}}else typeof process<"u"&&typeof process?.versions?.node=="string"?Rt={get platform(){return process.platform},get arch(){return process.arch},get env(){return process.env},cwd(){return process.env.VSCODE_CWD||process.cwd()}}:Rt={get platform(){return Dt?"win32":e1?"darwin":"linux"},get arch(){},get env(){return{}},cwd(){return"/"}};var Ee=Rt.cwd,Co=Rt.env,Lo=Rt.platform,Ll=Rt.arch,yo=65,Ao=97,xo=90,Eo=122,Pt=46,K=47,nt=92,vt=58,No=63,Rn=class extends Error{constructor(e,t,n){let r;typeof t=="string"&&t.indexOf("not ")===0?(r="must not be",t=t.replace(/^not /,"")):r="must be";const i=e.indexOf(".")!==-1?"property":"argument";let s=`The "${e}" ${i} ${r} of type ${t}`;s+=`. Received type ${typeof n}`,super(s),this.code="ERR_INVALID_ARG_TYPE"}};function Io(e,t){if(e===null||typeof e!="object")throw new Rn(t,"Object",e)}function B(e,t){if(typeof e!="string")throw new Rn(t,"string",e)}var rt=Lo==="win32";function _(e){return e===K||e===nt}function g1(e){return e===K}function wt(e){return e>=yo&&e<=xo||e>=Ao&&e<=Eo}function Ne(e,t,n,r){let i="",s=0,o=-1,l=0,u=0;for(let a=0;a<=e.length;++a){if(a<e.length)u=e.charCodeAt(a);else{if(r(u))break;u=K}if(r(u)){if(!(o===a-1||l===1))if(l===2){if(i.length<2||s!==2||i.charCodeAt(i.length-1)!==Pt||i.charCodeAt(i.length-2)!==Pt){if(i.length>2){const c=i.lastIndexOf(n);c===-1?(i="",s=0):(i=i.slice(0,c),s=i.length-1-i.lastIndexOf(n)),o=a,l=0;continue}else if(i.length!==0){i="",s=0,o=a,l=0;continue}}t&&(i+=i.length>0?`${n}..`:"..",s=2)}else i.length>0?i+=`${n}${e.slice(o+1,a)}`:i=e.slice(o+1,a),s=a-o-1;o=a,l=0}else u===Pt&&l!==-1?++l:l=-1}return i}function So(e){return e?`${e[0]==="."?"":"."}${e}`:""}function Pn(e,t){Io(t,"pathObject");const n=t.dir||t.root,r=t.base||`${t.name||""}${So(t.ext)}`;return n?n===t.root?`${n}${r}`:`${n}${e}${r}`:r}var V={resolve(...e){let t="",n="",r=!1;for(let i=e.length-1;i>=-1;i--){let s;if(i>=0){if(s=e[i],B(s,`paths[${i}]`),s.length===0)continue}else t.length===0?s=Ee():(s=Co[`=${t}`]||Ee(),(s===void 0||s.slice(0,2).toLowerCase()!==t.toLowerCase()&&s.charCodeAt(2)===nt)&&(s=`${t}\\`));const o=s.length;let l=0,u="",a=!1;const c=s.charCodeAt(0);if(o===1)_(c)&&(l=1,a=!0);else if(_(c))if(a=!0,_(s.charCodeAt(1))){let h=2,f=h;for(;h<o&&!_(s.charCodeAt(h));)h++;if(h<o&&h!==f){const g=s.slice(f,h);for(f=h;h<o&&_(s.charCodeAt(h));)h++;if(h<o&&h!==f){for(f=h;h<o&&!_(s.charCodeAt(h));)h++;(h===o||h!==f)&&(u=`\\\\${g}\\${s.slice(f,h)}`,l=h)}}}else l=1;else wt(c)&&s.charCodeAt(1)===vt&&(u=s.slice(0,2),l=2,o>2&&_(s.charCodeAt(2))&&(a=!0,l=3));if(u.length>0)if(t.length>0){if(u.toLowerCase()!==t.toLowerCase())continue}else t=u;if(r){if(t.length>0)break}else if(n=`${s.slice(l)}\\${n}`,r=a,a&&t.length>0)break}return n=Ne(n,!r,"\\",_),r?`${t}\\${n}`:`${t}${n}`||"."},normalize(e){B(e,"path");const t=e.length;if(t===0)return".";let n=0,r,i=!1;const s=e.charCodeAt(0);if(t===1)return g1(s)?"\\":e;if(_(s))if(i=!0,_(e.charCodeAt(1))){let l=2,u=l;for(;l<t&&!_(e.charCodeAt(l));)l++;if(l<t&&l!==u){const a=e.slice(u,l);for(u=l;l<t&&_(e.charCodeAt(l));)l++;if(l<t&&l!==u){for(u=l;l<t&&!_(e.charCodeAt(l));)l++;if(l===t)return`\\\\${a}\\${e.slice(u)}\\`;l!==u&&(r=`\\\\${a}\\${e.slice(u,l)}`,n=l)}}}else n=1;else wt(s)&&e.charCodeAt(1)===vt&&(r=e.slice(0,2),n=2,t>2&&_(e.charCodeAt(2))&&(i=!0,n=3));let o=n<t?Ne(e.slice(n),!i,"\\",_):"";if(o.length===0&&!i&&(o="."),o.length>0&&_(e.charCodeAt(t-1))&&(o+="\\"),!i&&r===void 0&&e.includes(":")){if(o.length>=2&&wt(o.charCodeAt(0))&&o.charCodeAt(1)===vt)return`.\\${o}`;let l=e.indexOf(":");do if(l===t-1||_(e.charCodeAt(l+1)))return`.\\${o}`;while((l=e.indexOf(":",l+1))!==-1)}return r===void 0?i?`\\${o}`:o:i?`${r}\\${o}`:`${r}${o}`},isAbsolute(e){B(e,"path");const t=e.length;if(t===0)return!1;const n=e.charCodeAt(0);return _(n)||t>2&&wt(n)&&e.charCodeAt(1)===vt&&_(e.charCodeAt(2))},join(...e){if(e.length===0)return".";let t,n;for(let s=0;s<e.length;++s){const o=e[s];B(o,"path"),o.length>0&&(t===void 0?t=n=o:t+=`\\${o}`)}if(t===void 0)return".";let r=!0,i=0;if(typeof n=="string"&&_(n.charCodeAt(0))){++i;const s=n.length;s>1&&_(n.charCodeAt(1))&&(++i,s>2&&(_(n.charCodeAt(2))?++i:r=!1))}if(r){for(;i<t.length&&_(t.charCodeAt(i));)i++;i>=2&&(t=`\\${t.slice(i)}`)}return V.normalize(t)},relative(e,t){if(B(e,"from"),B(t,"to"),e===t)return"";const n=V.resolve(e),r=V.resolve(t);if(n===r||(e=n.toLowerCase(),t=r.toLowerCase(),e===t))return"";if(n.length!==e.length||r.length!==t.length){const m=n.split("\\"),p=r.split("\\");m[m.length-1]===""&&m.pop(),p[p.length-1]===""&&p.pop();const d=m.length,w=p.length,b=d<w?d:w;let L;for(L=0;L<b&&m[L].toLowerCase()===p[L].toLowerCase();L++);return L===0?r:L===b?w>b?p.slice(L).join("\\"):d>b?"..\\".repeat(d-1-L)+"..":"":"..\\".repeat(d-L)+p.slice(L).join("\\")}let i=0;for(;i<e.length&&e.charCodeAt(i)===nt;)i++;let s=e.length;for(;s-1>i&&e.charCodeAt(s-1)===nt;)s--;const o=s-i;let l=0;for(;l<t.length&&t.charCodeAt(l)===nt;)l++;let u=t.length;for(;u-1>l&&t.charCodeAt(u-1)===nt;)u--;const a=u-l,c=o<a?o:a;let h=-1,f=0;for(;f<c;f++){const m=e.charCodeAt(i+f);if(m!==t.charCodeAt(l+f))break;m===nt&&(h=f)}if(f!==c){if(h===-1)return r}else{if(a>c){if(t.charCodeAt(l+f)===nt)return r.slice(l+f+1);if(f===2)return r.slice(l+f)}o>c&&(e.charCodeAt(i+f)===nt?h=f:f===2&&(h=3)),h===-1&&(h=0)}let g="";for(f=i+h+1;f<=s;++f)(f===s||e.charCodeAt(f)===nt)&&(g+=g.length===0?"..":"\\..");return l+=h,g.length>0?`${g}${r.slice(l,u)}`:(r.charCodeAt(l)===nt&&++l,r.slice(l,u))},toNamespacedPath(e){if(typeof e!="string"||e.length===0)return e;const t=V.resolve(e);if(t.length<=2)return e;if(t.charCodeAt(0)===nt){if(t.charCodeAt(1)===nt){const n=t.charCodeAt(2);if(n!==No&&n!==Pt)return`\\\\?\\UNC\\${t.slice(2)}`}}else if(wt(t.charCodeAt(0))&&t.charCodeAt(1)===vt&&t.charCodeAt(2)===nt)return`\\\\?\\${t}`;return t},dirname(e){B(e,"path");const t=e.length;if(t===0)return".";let n=-1,r=0;const i=e.charCodeAt(0);if(t===1)return _(i)?e:".";if(_(i)){if(n=r=1,_(e.charCodeAt(1))){let l=2,u=l;for(;l<t&&!_(e.charCodeAt(l));)l++;if(l<t&&l!==u){for(u=l;l<t&&_(e.charCodeAt(l));)l++;if(l<t&&l!==u){for(u=l;l<t&&!_(e.charCodeAt(l));)l++;if(l===t)return e;l!==u&&(n=r=l+1)}}}}else wt(i)&&e.charCodeAt(1)===vt&&(n=t>2&&_(e.charCodeAt(2))?3:2,r=n);let s=-1,o=!0;for(let l=t-1;l>=r;--l)if(_(e.charCodeAt(l))){if(!o){s=l;break}}else o=!1;if(s===-1){if(n===-1)return".";s=n}return e.slice(0,s)},basename(e,t){t!==void 0&&B(t,"suffix"),B(e,"path");let n=0,r=-1,i=!0,s;if(e.length>=2&&wt(e.charCodeAt(0))&&e.charCodeAt(1)===vt&&(n=2),t!==void 0&&t.length>0&&t.length<=e.length){if(t===e)return"";let o=t.length-1,l=-1;for(s=e.length-1;s>=n;--s){const u=e.charCodeAt(s);if(_(u)){if(!i){n=s+1;break}}else l===-1&&(i=!1,l=s+1),o>=0&&(u===t.charCodeAt(o)?--o===-1&&(r=s):(o=-1,r=l))}return n===r?r=l:r===-1&&(r=e.length),e.slice(n,r)}for(s=e.length-1;s>=n;--s)if(_(e.charCodeAt(s))){if(!i){n=s+1;break}}else r===-1&&(i=!1,r=s+1);return r===-1?"":e.slice(n,r)},extname(e){B(e,"path");let t=0,n=-1,r=0,i=-1,s=!0,o=0;e.length>=2&&e.charCodeAt(1)===vt&&wt(e.charCodeAt(0))&&(t=r=2);for(let l=e.length-1;l>=t;--l){const u=e.charCodeAt(l);if(_(u)){if(!s){r=l+1;break}continue}i===-1&&(s=!1,i=l+1),u===Pt?n===-1?n=l:o!==1&&(o=1):n!==-1&&(o=-1)}return n===-1||i===-1||o===0||o===1&&n===i-1&&n===r+1?"":e.slice(n,i)},format:Pn.bind(null,"\\"),parse(e){B(e,"path");const t={root:"",dir:"",base:"",ext:"",name:""};if(e.length===0)return t;const n=e.length;let r=0,i=e.charCodeAt(0);if(n===1)return _(i)?(t.root=t.dir=e,t):(t.base=t.name=e,t);if(_(i)){if(r=1,_(e.charCodeAt(1))){let h=2,f=h;for(;h<n&&!_(e.charCodeAt(h));)h++;if(h<n&&h!==f){for(f=h;h<n&&_(e.charCodeAt(h));)h++;if(h<n&&h!==f){for(f=h;h<n&&!_(e.charCodeAt(h));)h++;h===n?r=h:h!==f&&(r=h+1)}}}}else if(wt(i)&&e.charCodeAt(1)===vt){if(n<=2)return t.root=t.dir=e,t;if(r=2,_(e.charCodeAt(2))){if(n===3)return t.root=t.dir=e,t;r=3}}r>0&&(t.root=e.slice(0,r));let s=-1,o=r,l=-1,u=!0,a=e.length-1,c=0;for(;a>=r;--a){if(i=e.charCodeAt(a),_(i)){if(!u){o=a+1;break}continue}l===-1&&(u=!1,l=a+1),i===Pt?s===-1?s=a:c!==1&&(c=1):s!==-1&&(c=-1)}return l!==-1&&(s===-1||c===0||c===1&&s===l-1&&s===o+1?t.base=t.name=e.slice(o,l):(t.name=e.slice(o,s),t.base=e.slice(o,l),t.ext=e.slice(s,l))),o>0&&o!==r?t.dir=e.slice(0,o-1):t.dir=t.root,t},sep:"\\",delimiter:";",win32:null,posix:null},$o=(()=>{if(rt){const e=/\\/g;return()=>{const t=Ee().replace(e,"/");return t.slice(t.indexOf("/"))}}return()=>Ee()})(),U={resolve(...e){let t="",n=!1;for(let r=e.length-1;r>=0&&!n;r--){const i=e[r];B(i,`paths[${r}]`),i.length!==0&&(t=`${i}/${t}`,n=i.charCodeAt(0)===K)}if(!n){const r=$o();t=`${r}/${t}`,n=r.charCodeAt(0)===K}return t=Ne(t,!n,"/",g1),n?`/${t}`:t.length>0?t:"."},normalize(e){if(B(e,"path"),e.length===0)return".";const t=e.charCodeAt(0)===K,n=e.charCodeAt(e.length-1)===K;return e=Ne(e,!t,"/",g1),e.length===0?t?"/":n?"./":".":(n&&(e+="/"),t?`/${e}`:e)},isAbsolute(e){return B(e,"path"),e.length>0&&e.charCodeAt(0)===K},join(...e){if(e.length===0)return".";const t=[];for(let n=0;n<e.length;++n){const r=e[n];B(r,"path"),r.length>0&&t.push(r)}return t.length===0?".":U.normalize(t.join("/"))},relative(e,t){if(B(e,"from"),B(t,"to"),e===t||(e=U.resolve(e),t=U.resolve(t),e===t))return"";const n=1,r=e.length,i=r-n,s=1,o=t.length-s,l=i<o?i:o;let u=-1,a=0;for(;a<l;a++){const h=e.charCodeAt(n+a);if(h!==t.charCodeAt(s+a))break;h===K&&(u=a)}if(a===l)if(o>l){if(t.charCodeAt(s+a)===K)return t.slice(s+a+1);if(a===0)return t.slice(s+a)}else i>l&&(e.charCodeAt(n+a)===K?u=a:a===0&&(u=0));let c="";for(a=n+u+1;a<=r;++a)(a===r||e.charCodeAt(a)===K)&&(c+=c.length===0?"..":"/..");return`${c}${t.slice(s+u)}`},toNamespacedPath(e){return e},dirname(e){if(B(e,"path"),e.length===0)return".";const t=e.charCodeAt(0)===K;let n=-1,r=!0;for(let i=e.length-1;i>=1;--i)if(e.charCodeAt(i)===K){if(!r){n=i;break}}else r=!1;return n===-1?t?"/":".":t&&n===1?"//":e.slice(0,n)},basename(e,t){t!==void 0&&B(t,"suffix"),B(e,"path");let n=0,r=-1,i=!0,s;if(t!==void 0&&t.length>0&&t.length<=e.length){if(t===e)return"";let o=t.length-1,l=-1;for(s=e.length-1;s>=0;--s){const u=e.charCodeAt(s);if(u===K){if(!i){n=s+1;break}}else l===-1&&(i=!1,l=s+1),o>=0&&(u===t.charCodeAt(o)?--o===-1&&(r=s):(o=-1,r=l))}return n===r?r=l:r===-1&&(r=e.length),e.slice(n,r)}for(s=e.length-1;s>=0;--s)if(e.charCodeAt(s)===K){if(!i){n=s+1;break}}else r===-1&&(i=!1,r=s+1);return r===-1?"":e.slice(n,r)},extname(e){B(e,"path");let t=-1,n=0,r=-1,i=!0,s=0;for(let o=e.length-1;o>=0;--o){const l=e[o];if(l==="/"){if(!i){n=o+1;break}continue}r===-1&&(i=!1,r=o+1),l==="."?t===-1?t=o:s!==1&&(s=1):t!==-1&&(s=-1)}return t===-1||r===-1||s===0||s===1&&t===r-1&&t===n+1?"":e.slice(t,r)},format:Pn.bind(null,"/"),parse(e){B(e,"path");const t={root:"",dir:"",base:"",ext:"",name:""};if(e.length===0)return t;const n=e.charCodeAt(0)===K;let r;n?(t.root="/",r=1):r=0;let i=-1,s=0,o=-1,l=!0,u=e.length-1,a=0;for(;u>=r;--u){const c=e.charCodeAt(u);if(c===K){if(!l){s=u+1;break}continue}o===-1&&(l=!1,o=u+1),c===Pt?i===-1?i=u:a!==1&&(a=1):i!==-1&&(a=-1)}if(o!==-1){const c=s===0&&n?1:s;i===-1||a===0||a===1&&i===o-1&&i===s+1?t.base=t.name=e.slice(c,o):(t.name=e.slice(c,i),t.base=e.slice(c,o),t.ext=e.slice(i,o))}return s>0?t.dir=e.slice(0,s-1):n&&(t.dir="/"),t},sep:"/",delimiter:":",win32:null,posix:null};U.win32=V.win32=V,U.posix=V.posix=U;var Oo=rt?V.normalize:U.normalize,yl=rt?V.isAbsolute:U.isAbsolute,_o=rt?V.join:U.join,Mo=rt?V.resolve:U.resolve,Do=rt?V.relative:U.relative,Ro=rt?V.dirname:U.dirname,Al=rt?V.basename:U.basename,xl=rt?V.extname:U.extname,El=rt?V.format:U.format,Nl=rt?V.parse:U.parse,Il=rt?V.toNamespacedPath:U.toNamespacedPath,Ie=rt?V.sep:U.sep,Sl=rt?V.delimiter:U.delimiter,Po=/^\w[\w\d+.-]*$/,ko=/^\//,Fo=/^\/\//;function qo(e,t){if(!e.scheme&&t)throw new Error(`[UriError]: Scheme is missing: {scheme: "", authority: "${e.authority}", path: "${e.path}", query: "${e.query}", fragment: "${e.fragment}"}`);if(e.scheme&&!Po.test(e.scheme))throw new Error("[UriError]: Scheme contains illegal characters.");if(e.path){if(e.authority){if(!ko.test(e.path))throw new Error('[UriError]: If a URI contains an authority component, then the path component must either be empty or begin with a slash ("/") character')}else if(Fo.test(e.path))throw new Error('[UriError]: If a URI does not contain an authority component, then the path cannot begin with two slash characters ("//")')}}function Uo(e,t){return!e&&!t?"file":e}function jo(e,t){switch(e){case"https":case"http":case"file":t?t[0]!==ft&&(t=ft+t):t=ft;break}return t}var j="",ft="/",zo=/^(([^:/?#]+?):)?(\/\/([^/?#]*))?([^?#]*)(\?([^#]*))?(#(.*))?/,it=class qe{static isUri(t){return t instanceof qe?!0:!t||typeof t!="object"?!1:typeof t.authority=="string"&&typeof t.fragment=="string"&&typeof t.path=="string"&&typeof t.query=="string"&&typeof t.scheme=="string"&&typeof t.fsPath=="string"&&typeof t.with=="function"&&typeof t.toString=="function"}constructor(t,n,r,i,s,o=!1){typeof t=="object"?(this.scheme=t.scheme||j,this.authority=t.authority||j,this.path=t.path||j,this.query=t.query||j,this.fragment=t.fragment||j):(this.scheme=Uo(t,o),this.authority=n||j,this.path=jo(this.scheme,r||j),this.query=i||j,this.fragment=s||j,qo(this,o))}get fsPath(){return Se(this,!1)}with(t){if(!t)return this;let{scheme:n,authority:r,path:i,query:s,fragment:o}=t;return n===void 0?n=this.scheme:n===null&&(n=j),r===void 0?r=this.authority:r===null&&(r=j),i===void 0?i=this.path:i===null&&(i=j),s===void 0?s=this.query:s===null&&(s=j),o===void 0?o=this.fragment:o===null&&(o=j),n===this.scheme&&r===this.authority&&i===this.path&&s===this.query&&o===this.fragment?this:new Vt(n,r,i,s,o)}static parse(t,n=!1){const r=zo.exec(t);return r?new Vt(r[2]||j,$e(r[4]||j),$e(r[5]||j),$e(r[7]||j),$e(r[9]||j),n):new Vt(j,j,j,j,j)}static file(t){let n=j;if(Dt&&(t=t.replace(/\\/g,ft)),t[0]===ft&&t[1]===ft){const r=t.indexOf(ft,2);r===-1?(n=t.substring(2),t=ft):(n=t.substring(2,r),t=t.substring(r)||ft)}return new Vt("file",n,t,j,j)}static from(t,n){return new Vt(t.scheme,t.authority,t.path,t.query,t.fragment,n)}static joinPath(t,...n){if(!t.path)throw new Error("[UriError]: cannot call joinPath on URI without path");let r;return Dt&&t.scheme==="file"?r=qe.file(V.join(Se(t,!0),...n)).path:r=U.join(t.path,...n),t.with({path:r})}toString(t=!1){return m1(this,t)}toJSON(){return this}static revive(t){if(t){if(t instanceof qe)return t;{const n=new Vt(t);return n._formatted=t.external??null,n._fsPath=t._sep===kn?t.fsPath??null:null,n}}else return t}[Symbol.for("debug.description")](){return`URI(${this.toString()})`}},kn=Dt?1:void 0,Vt=class extends it{constructor(){super(...arguments),this._formatted=null,this._fsPath=null}get fsPath(){return this._fsPath||(this._fsPath=Se(this,!1)),this._fsPath}toString(e=!1){return e?m1(this,!0):(this._formatted||(this._formatted=m1(this,!1)),this._formatted)}toJSON(){const e={$mid:1};return this._fsPath&&(e.fsPath=this._fsPath,e._sep=kn),this._formatted&&(e.external=this._formatted),this.path&&(e.path=this.path),this.scheme&&(e.scheme=this.scheme),this.authority&&(e.authority=this.authority),this.query&&(e.query=this.query),this.fragment&&(e.fragment=this.fragment),e}},Fn={58:"%3A",47:"%2F",63:"%3F",35:"%23",91:"%5B",93:"%5D",64:"%40",33:"%21",36:"%24",38:"%26",39:"%27",40:"%28",41:"%29",42:"%2A",43:"%2B",44:"%2C",59:"%3B",61:"%3D",32:"%20"};function qn(e,t,n){let r,i=-1;for(let s=0;s<e.length;s++){const o=e.charCodeAt(s);if(o>=97&&o<=122||o>=65&&o<=90||o>=48&&o<=57||o===45||o===46||o===95||o===126||t&&o===47||n&&o===91||n&&o===93||n&&o===58)i!==-1&&(r+=encodeURIComponent(e.substring(i,s)),i=-1),r!==void 0&&(r+=e.charAt(s));else{r===void 0&&(r=e.substr(0,s));const l=Fn[o];l!==void 0?(i!==-1&&(r+=encodeURIComponent(e.substring(i,s)),i=-1),r+=l):i===-1&&(i=s)}}return i!==-1&&(r+=encodeURIComponent(e.substring(i))),r!==void 0?r:e}function To(e){let t;for(let n=0;n<e.length;n++){const r=e.charCodeAt(n);r===35||r===63?(t===void 0&&(t=e.substr(0,n)),t+=Fn[r]):t!==void 0&&(t+=e[n])}return t!==void 0?t:e}function Se(e,t){let n;return e.authority&&e.path.length>1&&e.scheme==="file"?n=`//${e.authority}${e.path}`:e.path.charCodeAt(0)===47&&(e.path.charCodeAt(1)>=65&&e.path.charCodeAt(1)<=90||e.path.charCodeAt(1)>=97&&e.path.charCodeAt(1)<=122)&&e.path.charCodeAt(2)===58?t?n=e.path.substr(1):n=e.path[1].toLowerCase()+e.path.substr(2):n=e.path,Dt&&(n=n.replace(/\//g,"\\")),n}function m1(e,t){const n=t?To:qn;let r="",{scheme:i,authority:s,path:o,query:l,fragment:u}=e;if(i&&(r+=i,r+=":"),(s||i==="file")&&(r+=ft,r+=ft),s){let a=s.indexOf("@");if(a!==-1){const c=s.substr(0,a);s=s.substr(a+1),a=c.lastIndexOf(":"),a===-1?r+=n(c,!1,!1):(r+=n(c.substr(0,a),!1,!1),r+=":",r+=n(c.substr(a+1),!1,!0)),r+="@"}s=s.toLowerCase(),a=s.lastIndexOf(":"),a===-1?r+=n(s,!1,!0):(r+=n(s.substr(0,a),!1,!0),r+=s.substr(a))}if(o){if(o.length>=3&&o.charCodeAt(0)===47&&o.charCodeAt(2)===58){const a=o.charCodeAt(1);a>=65&&a<=90&&(o=`/${String.fromCharCode(a+32)}:${o.substr(3)}`)}else if(o.length>=2&&o.charCodeAt(1)===58){const a=o.charCodeAt(0);a>=65&&a<=90&&(o=`${String.fromCharCode(a+32)}:${o.substr(2)}`)}r+=n(o,!0,!1)}return l&&(r+="?",r+=n(l,!1,!1)),u&&(r+="#",r+=t?u:qn(u,!1,!1)),r}function Un(e){try{return decodeURIComponent(e)}catch{return e.length>3?e.substr(0,3)+Un(e.substr(3)):e}}var jn=/(%[0-9A-Za-z][0-9A-Za-z])+/g;function $e(e){return e.match(jn)?e.replace(jn,t=>Un(t)):e}var Ct=class qt{constructor(t,n){this.lineNumber=t,this.column=n}with(t=this.lineNumber,n=this.column){return t===this.lineNumber&&n===this.column?this:new qt(t,n)}delta(t=0,n=0){return this.with(Math.max(1,this.lineNumber+t),Math.max(1,this.column+n))}equals(t){return qt.equals(this,t)}static equals(t,n){return!t&&!n?!0:!!t&&!!n&&t.lineNumber===n.lineNumber&&t.column===n.column}isBefore(t){return qt.isBefore(this,t)}static isBefore(t,n){return t.lineNumber<n.lineNumber?!0:n.lineNumber<t.lineNumber?!1:t.column<n.column}isBeforeOrEqual(t){return qt.isBeforeOrEqual(this,t)}static isBeforeOrEqual(t,n){return t.lineNumber<n.lineNumber?!0:n.lineNumber<t.lineNumber?!1:t.column<=n.column}static compare(t,n){const r=t.lineNumber|0,i=n.lineNumber|0;if(r===i){const s=t.column|0,o=n.column|0;return s-o}return r-i}clone(){return new qt(this.lineNumber,this.column)}toString(){return"("+this.lineNumber+","+this.column+")"}static lift(t){return new qt(t.lineNumber,t.column)}static isIPosition(t){return t&&typeof t.lineNumber=="number"&&typeof t.column=="number"}toJSON(){return{lineNumber:this.lineNumber,column:this.column}}},J=class W{constructor(t,n,r,i){t>r||t===r&&n>i?(this.startLineNumber=r,this.startColumn=i,this.endLineNumber=t,this.endColumn=n):(this.startLineNumber=t,this.startColumn=n,this.endLineNumber=r,this.endColumn=i)}isEmpty(){return W.isEmpty(this)}static isEmpty(t){return t.startLineNumber===t.endLineNumber&&t.startColumn===t.endColumn}containsPosition(t){return W.containsPosition(this,t)}static containsPosition(t,n){return!(n.lineNumber<t.startLineNumber||n.lineNumber>t.endLineNumber||n.lineNumber===t.startLineNumber&&n.column<t.startColumn||n.lineNumber===t.endLineNumber&&n.column>t.endColumn)}static strictContainsPosition(t,n){return!(n.lineNumber<t.startLineNumber||n.lineNumber>t.endLineNumber||n.lineNumber===t.startLineNumber&&n.column<=t.startColumn||n.lineNumber===t.endLineNumber&&n.column>=t.endColumn)}containsRange(t){return W.containsRange(this,t)}static containsRange(t,n){return!(n.startLineNumber<t.startLineNumber||n.endLineNumber<t.startLineNumber||n.startLineNumber>t.endLineNumber||n.endLineNumber>t.endLineNumber||n.startLineNumber===t.startLineNumber&&n.startColumn<t.startColumn||n.endLineNumber===t.endLineNumber&&n.endColumn>t.endColumn)}strictContainsRange(t){return W.strictContainsRange(this,t)}static strictContainsRange(t,n){return!(n.startLineNumber<t.startLineNumber||n.endLineNumber<t.startLineNumber||n.startLineNumber>t.endLineNumber||n.endLineNumber>t.endLineNumber||n.startLineNumber===t.startLineNumber&&n.startColumn<=t.startColumn||n.endLineNumber===t.endLineNumber&&n.endColumn>=t.endColumn)}plusRange(t){return W.plusRange(this,t)}static plusRange(t,n){let r,i,s,o;return n.startLineNumber<t.startLineNumber?(r=n.startLineNumber,i=n.startColumn):n.startLineNumber===t.startLineNumber?(r=n.startLineNumber,i=Math.min(n.startColumn,t.startColumn)):(r=t.startLineNumber,i=t.startColumn),n.endLineNumber>t.endLineNumber?(s=n.endLineNumber,o=n.endColumn):n.endLineNumber===t.endLineNumber?(s=n.endLineNumber,o=Math.max(n.endColumn,t.endColumn)):(s=t.endLineNumber,o=t.endColumn),new W(r,i,s,o)}intersectRanges(t){return W.intersectRanges(this,t)}static intersectRanges(t,n){let r=t.startLineNumber,i=t.startColumn,s=t.endLineNumber,o=t.endColumn;const l=n.startLineNumber,u=n.startColumn,a=n.endLineNumber,c=n.endColumn;return r<l?(r=l,i=u):r===l&&(i=Math.max(i,u)),s>a?(s=a,o=c):s===a&&(o=Math.min(o,c)),r>s||r===s&&i>o?null:new W(r,i,s,o)}equalsRange(t){return W.equalsRange(this,t)}static equalsRange(t,n){return!t&&!n?!0:!!t&&!!n&&t.startLineNumber===n.startLineNumber&&t.startColumn===n.startColumn&&t.endLineNumber===n.endLineNumber&&t.endColumn===n.endColumn}getEndPosition(){return W.getEndPosition(this)}static getEndPosition(t){return new Ct(t.endLineNumber,t.endColumn)}getStartPosition(){return W.getStartPosition(this)}static getStartPosition(t){return new Ct(t.startLineNumber,t.startColumn)}toString(){return"["+this.startLineNumber+","+this.startColumn+" -> "+this.endLineNumber+","+this.endColumn+"]"}setEndPosition(t,n){return new W(this.startLineNumber,this.startColumn,t,n)}setStartPosition(t,n){return new W(t,n,this.endLineNumber,this.endColumn)}collapseToStart(){return W.collapseToStart(this)}static collapseToStart(t){return new W(t.startLineNumber,t.startColumn,t.startLineNumber,t.startColumn)}collapseToEnd(){return W.collapseToEnd(this)}static collapseToEnd(t){return new W(t.endLineNumber,t.endColumn,t.endLineNumber,t.endColumn)}delta(t){return new W(this.startLineNumber+t,this.startColumn,this.endLineNumber+t,this.endColumn)}isSingleLine(){return this.startLineNumber===this.endLineNumber}static fromPositions(t,n=t){return new W(t.lineNumber,t.column,n.lineNumber,n.column)}static lift(t){return t?new W(t.startLineNumber,t.startColumn,t.endLineNumber,t.endColumn):null}static isIRange(t){return t&&typeof t.startLineNumber=="number"&&typeof t.startColumn=="number"&&typeof t.endLineNumber=="number"&&typeof t.endColumn=="number"}static areIntersectingOrTouching(t,n){return!(t.endLineNumber<n.startLineNumber||t.endLineNumber===n.startLineNumber&&t.endColumn<n.startColumn||n.endLineNumber<t.startLineNumber||n.endLineNumber===t.startLineNumber&&n.endColumn<t.startColumn)}static areIntersecting(t,n){return!(t.endLineNumber<n.startLineNumber||t.endLineNumber===n.startLineNumber&&t.endColumn<=n.startColumn||n.endLineNumber<t.startLineNumber||n.endLineNumber===t.startLineNumber&&n.endColumn<=t.startColumn)}static areOnlyIntersecting(t,n){return!(t.endLineNumber<n.startLineNumber-1||t.endLineNumber===n.startLineNumber&&t.endColumn<n.startColumn-1||n.endLineNumber<t.startLineNumber-1||n.endLineNumber===t.startLineNumber&&n.endColumn<t.startColumn-1)}static compareRangesUsingStarts(t,n){if(t&&n){const s=t.startLineNumber|0,o=n.startLineNumber|0;if(s===o){const l=t.startColumn|0,u=n.startColumn|0;if(l===u){const a=t.endLineNumber|0,c=n.endLineNumber|0;if(a===c){const h=t.endColumn|0,f=n.endColumn|0;return h-f}return a-c}return l-u}return s-o}return(t?1:0)-(n?1:0)}static compareRangesUsingEnds(t,n){return t.endLineNumber===n.endLineNumber?t.endColumn===n.endColumn?t.startLineNumber===n.startLineNumber?t.startColumn-n.startColumn:t.startLineNumber-n.startLineNumber:t.endColumn-n.endColumn:t.endLineNumber-n.endLineNumber}static spansMultipleLines(t){return t.endLineNumber>t.startLineNumber}toJSON(){return this}};function zn(e,t){const n=Object.create(null);for(const[r,i]of Object.entries(e))t(r,i)&&(n[r]=i);return n}var Tn;(function(e){e[e.Left=1]="Left",e[e.Center=2]="Center",e[e.Right=4]="Right",e[e.Full=7]="Full"})(Tn||(Tn={}));var Bn;(function(e){e[e.Left=1]="Left",e[e.Center=2]="Center",e[e.Right=3]="Right"})(Bn||(Bn={}));var Vn;(function(e){e[e.Inline=1]="Inline",e[e.Gutter=2]="Gutter"})(Vn||(Vn={}));var Wn;(function(e){e[e.Normal=1]="Normal",e[e.Underlined=2]="Underlined"})(Wn||(Wn={}));var Hn;(function(e){e[e.LTR=0]="LTR",e[e.RTL=1]="RTL"})(Hn||(Hn={}));var Gn;(function(e){e[e.Both=0]="Both",e[e.Right=1]="Right",e[e.Left=2]="Left",e[e.None=3]="None"})(Gn||(Gn={}));var Kn;(function(e){e[e.TextDefined=0]="TextDefined",e[e.LF=1]="LF",e[e.CRLF=2]="CRLF"})(Kn||(Kn={}));var Jn;(function(e){e[e.LF=1]="LF",e[e.CRLF=2]="CRLF"})(Jn||(Jn={}));var Xn;(function(e){e[e.LF=0]="LF",e[e.CRLF=1]="CRLF"})(Xn||(Xn={}));var p1=class{constructor(e,t){this._findMatchBrand=void 0,this.range=e,this.matches=t}},Qn;(function(e){e[e.AlwaysGrowsWhenTypingAtEdges=0]="AlwaysGrowsWhenTypingAtEdges",e[e.NeverGrowsWhenTypingAtEdges=1]="NeverGrowsWhenTypingAtEdges",e[e.GrowsOnlyWhenTypingBefore=2]="GrowsOnlyWhenTypingBefore",e[e.GrowsOnlyWhenTypingAfter=3]="GrowsOnlyWhenTypingAfter"})(Qn||(Qn={}));var Zn;(function(e){e[e.Left=0]="Left",e[e.Right=1]="Right",e[e.None=2]="None",e[e.LeftOfInjectedText=3]="LeftOfInjectedText",e[e.RightOfInjectedText=4]="RightOfInjectedText"})(Zn||(Zn={}));var Yn;(function(e){e[e.FIRST_LINE_DETECTION_LENGTH_LIMIT=1e3]="FIRST_LINE_DETECTION_LENGTH_LIMIT"})(Yn||(Yn={}));var Bo=class{constructor(e,t,n){this.regex=e,this.wordSeparators=t,this.simpleSearch=n}},Vo=class{constructor(e,t,n){this.reverseEdits=e,this.changes=t,this.trimAutoWhitespaceLineNumbers=n}},b1=class{constructor(e,t){this.piece=e,this.color=t,this.size_left=0,this.lf_left=0,this.parent=this,this.left=this,this.right=this}next(){if(this.right!==x)return v1(this.right);let e=this;for(;e.parent!==x&&e.parent.left!==e;)e=e.parent;return e.parent===x?x:e.parent}prev(){if(this.left!==x)return er(this.left);let e=this;for(;e.parent!==x&&e.parent.right!==e;)e=e.parent;return e.parent===x?x:e.parent}detach(){this.parent=null,this.left=null,this.right=null}},tr;(function(e){e[e.Black=0]="Black",e[e.Red=1]="Red"})(tr||(tr={}));var x=new b1(null,0);x.parent=x,x.left=x,x.right=x,x.color=0;function v1(e){for(;e.left!==x;)e=e.left;return e}function er(e){for(;e.right!==x;)e=e.right;return e}function w1(e){return e===x?0:e.size_left+e.piece.length+w1(e.right)}function C1(e){return e===x?0:e.lf_left+e.piece.lineFeedCnt+C1(e.right)}function L1(){x.parent=x}function Yt(e,t){const n=t.right;n.size_left+=t.size_left+(t.piece?t.piece.length:0),n.lf_left+=t.lf_left+(t.piece?t.piece.lineFeedCnt:0),t.right=n.left,n.left!==x&&(n.left.parent=t),n.parent=t.parent,t.parent===x?e.root=n:t.parent.left===t?t.parent.left=n:t.parent.right=n,n.left=t,t.parent=n}function te(e,t){const n=t.left;t.left=n.right,n.right!==x&&(n.right.parent=t),n.parent=t.parent,t.size_left-=n.size_left+(n.piece?n.piece.length:0),t.lf_left-=n.lf_left+(n.piece?n.piece.lineFeedCnt:0),t.parent===x?e.root=n:t===t.parent.right?t.parent.right=n:t.parent.left=n,n.right=t,t.parent=n}function Oe(e,t){let n,r;if(t.left===x?(r=t,n=r.right):t.right===x?(r=t,n=r.left):(r=v1(t.right),n=r.right),r===e.root){e.root=n,n.color=0,t.detach(),L1(),e.root.parent=x;return}const i=r.color===1;if(r===r.parent.left?r.parent.left=n:r.parent.right=n,r===t?(n.parent=r.parent,ee(e,n)):(r.parent===t?n.parent=r:n.parent=r.parent,ee(e,n),r.left=t.left,r.right=t.right,r.parent=t.parent,r.color=t.color,t===e.root?e.root=r:t===t.parent.left?t.parent.left=r:t.parent.right=r,r.left!==x&&(r.left.parent=r),r.right!==x&&(r.right.parent=r),r.size_left=t.size_left,r.lf_left=t.lf_left,ee(e,r)),t.detach(),n.parent.left===n){const o=w1(n),l=C1(n);if(o!==n.parent.size_left||l!==n.parent.lf_left){const u=o-n.parent.size_left,a=l-n.parent.lf_left;n.parent.size_left=o,n.parent.lf_left=l,Lt(e,n.parent,u,a)}}if(ee(e,n.parent),i){L1();return}let s;for(;n!==e.root&&n.color===0;)n===n.parent.left?(s=n.parent.right,s.color===1&&(s.color=0,n.parent.color=1,Yt(e,n.parent),s=n.parent.right),s.left.color===0&&s.right.color===0?(s.color=1,n=n.parent):(s.right.color===0&&(s.left.color=0,s.color=1,te(e,s),s=n.parent.right),s.color=n.parent.color,n.parent.color=0,s.right.color=0,Yt(e,n.parent),n=e.root)):(s=n.parent.left,s.color===1&&(s.color=0,n.parent.color=1,te(e,n.parent),s=n.parent.left),s.left.color===0&&s.right.color===0?(s.color=1,n=n.parent):(s.left.color===0&&(s.right.color=0,s.color=1,Yt(e,s),s=n.parent.left),s.color=n.parent.color,n.parent.color=0,s.left.color=0,te(e,n.parent),n=e.root));n.color=0,L1()}function nr(e,t){for(ee(e,t);t!==e.root&&t.parent.color===1;)if(t.parent===t.parent.parent.left){const n=t.parent.parent.right;n.color===1?(t.parent.color=0,n.color=0,t.parent.parent.color=1,t=t.parent.parent):(t===t.parent.right&&(t=t.parent,Yt(e,t)),t.parent.color=0,t.parent.parent.color=1,te(e,t.parent.parent))}else{const n=t.parent.parent.left;n.color===1?(t.parent.color=0,n.color=0,t.parent.parent.color=1,t=t.parent.parent):(t===t.parent.left&&(t=t.parent,te(e,t)),t.parent.color=0,t.parent.parent.color=1,Yt(e,t.parent.parent))}e.root.color=0}function Lt(e,t,n,r){for(;t!==e.root&&t!==x;)t.parent.left===t&&(t.parent.size_left+=n,t.parent.lf_left+=r),t=t.parent}function ee(e,t){let n=0,r=0;if(t!==e.root){for(;t!==e.root&&t===t.parent.right;)t=t.parent;if(t!==e.root)for(t=t.parent,n=w1(t.left)-t.size_left,r=C1(t.left)-t.lf_left,t.size_left+=n,t.lf_left+=r;t!==e.root&&(n!==0||r!==0);)t.parent.left===t&&(t.parent.size_left+=n,t.parent.lf_left+=r),t=t.parent}}var Wo=60,Ho=Wo*60,y1=Ho*24,$l=y1*7,Ol=y1*30,_l=y1*365,Go={DateTimeFormat(e,t){return new $t(()=>{try{return new Intl.DateTimeFormat(e,t)}catch{return new Intl.DateTimeFormat(void 0,t)}})},Collator(e,t){return new $t(()=>{try{return new Intl.Collator(e,t)}catch{return new Intl.Collator(void 0,t)}})},Segmenter(e,t){return new $t(()=>{try{return new Intl.Segmenter(e,t)}catch{return new Intl.Segmenter(void 0,t)}})},Locale(e,t){return new $t(()=>{try{return new Intl.Locale(e,t)}catch{return new Intl.Locale(Mt,t)}})},NumberFormat(e,t){return new $t(()=>{try{return new Intl.NumberFormat(e,t)}catch{return new Intl.NumberFormat(void 0,t)}})}},rr;(function(e){e[e.MAX_SAFE_SMALL_INTEGER=1073741824]="MAX_SAFE_SMALL_INTEGER",e[e.MIN_SAFE_SMALL_INTEGER=-1073741824]="MIN_SAFE_SMALL_INTEGER",e[e.MAX_UINT_8=255]="MAX_UINT_8",e[e.MAX_UINT_16=65535]="MAX_UINT_16",e[e.MAX_UINT_32=4294967295]="MAX_UINT_32",e[e.UNICODE_SUPPLEMENTARY_PLANE_BEGIN=65536]="UNICODE_SUPPLEMENTARY_PLANE_BEGIN"})(rr||(rr={}));function ir(e){return e<0?0:e>255?255:e|0}function Wt(e){return e<0?0:e>4294967295?4294967295:e|0}var Ko=class ui{constructor(t){const n=ir(t);this.c=n,this.a=ui.d(n),this.b=new Map}static d(t){const n=new Uint8Array(256);return n.fill(t),n}set(t,n){const r=ir(n);t>=0&&t<256?this.a[t]=r:this.b.set(t,r)}get(t){return t>=0&&t<256?this.a[t]:this.b.get(t)||this.c}clear(){this.a.fill(this.c),this.b.clear()}},sr;(function(e){e[e.False=0]="False",e[e.True=1]="True"})(sr||(sr={}));var or;(function(e){e[e.Regular=0]="Regular",e[e.Whitespace=1]="Whitespace",e[e.WordSeparator=2]="WordSeparator"})(or||(or={}));var Jo=class extends Ko{constructor(e,t){super(0),this.e=null,this.f=null,this.g=[],this.intlSegmenterLocales=t,this.intlSegmenterLocales.length>0?this.e=Go.Segmenter(this.intlSegmenterLocales,{granularity:"word"}):this.e=null;for(let n=0,r=e.length;n<r;n++)this.set(e.charCodeAt(n),2);this.set(32,1),this.set(9,1)}findPrevIntlWordBeforeOrAtOffset(e,t){let n=null;for(const r of this.h(e)){if(r.index>t)break;n=r}return n}findNextIntlWordAtOrAfterOffset(e,t){for(const n of this.h(e))if(!(n.index<t))return n;return null}h(e){return this.e?this.f===e?this.g:(this.f=e,this.g=this.j(this.e.value.segment(e)),this.g):[]}j(e){const t=[];for(const n of e)this.k(n)&&t.push(n);return t}k(e){return!!e.isWordLike}},lr=new Q1(10);function Xo(e,t){const n=`${e}/${t.join(",")}`;let r=lr.get(n);return r||(r=new Jo(e,t),lr.set(n,r)),r}var Qo=class{constructor(e,t,n,r){this.searchString=e,this.isRegex=t,this.matchCase=n,this.wordSeparators=r}parseSearchRequest(){if(this.searchString==="")return null;let e;this.isRegex?e=Zo(this.searchString):e=this.searchString.indexOf(`
`)>=0;let t=null;try{t=Ss(this.searchString,this.isRegex,{matchCase:this.matchCase,wholeWord:!1,multiline:e,global:!0,unicode:!0})}catch{return null}if(!t)return null;let n=!this.isRegex&&!e;return n&&this.searchString.toLowerCase()!==this.searchString.toUpperCase()&&(n=this.matchCase),new Bo(t,this.wordSeparators?Xo(this.wordSeparators,[]):null,n?this.searchString:null)}};function Zo(e){if(!e||e.length===0)return!1;for(let t=0,n=e.length;t<n;t++){const r=e.charCodeAt(t);if(r===10)return!0;if(r===92){if(t++,t>=n)break;const i=e.charCodeAt(t);if(i===110||i===114||i===87)return!0}}return!1}function ar(e,t,n){if(!n)return new p1(e,null);const r=[];for(let i=0,s=t.length;i<s;i++)r[i]=t[i];return new p1(e,r)}function Yo(e,t,n,r,i){if(r===0)return!0;const s=t.charCodeAt(r-1);if(e.get(s)!==0||s===13||s===10)return!0;if(i>0){const o=t.charCodeAt(r);if(e.get(o)!==0)return!0}return!1}function t0(e,t,n,r,i){if(r+i===n)return!0;const s=t.charCodeAt(r+i);if(e.get(s)!==0||s===13||s===10)return!0;if(i>0){const o=t.charCodeAt(r+i-1);if(e.get(o)!==0)return!0}return!1}function ur(e,t,n,r,i){return Yo(e,t,n,r,i)&&t0(e,t,n,r,i)}var e0=class{constructor(e,t){this._wordSeparators=e,this.a=t,this.b=-1,this.c=0}reset(e){this.a.lastIndex=e,this.b=-1,this.c=0}next(e){const t=e.length;let n;do{if(this.b+this.c===t||(n=this.a.exec(e),!n))return null;const r=n.index,i=n[0].length;if(r===this.b&&i===this.c){if(i===0){Ps(e,t,this.a.lastIndex)>65535?this.a.lastIndex+=2:this.a.lastIndex+=1;continue}return null}if(this.b=r,this.c=i,!this._wordSeparators||ur(this._wordSeparators,e,t,r,i))return n}while(n);return null}},yt=65535;function hr(e){let t;return e[e.length-1]<65536?t=new Uint16Array(e.length):t=new Uint32Array(e.length),t.set(e,0),t}var n0=class{constructor(e,t,n,r,i){this.lineStarts=e,this.cr=t,this.lf=n,this.crlf=r,this.isBasicASCII=i}};function At(e,t=!0){const n=[0];let r=1;for(let i=0,s=e.length;i<s;i++){const o=e.charCodeAt(i);o===13?i+1<s&&e.charCodeAt(i+1)===10?(n[r++]=i+2,i++):n[r++]=i+1:o===10&&(n[r++]=i+1)}return t?hr(n):n}function r0(e,t){e.length=0,e[0]=0;let n=1,r=0,i=0,s=0,o=!0;for(let u=0,a=t.length;u<a;u++){const c=t.charCodeAt(u);c===13?u+1<a&&t.charCodeAt(u+1)===10?(s++,e[n++]=u+2,u++):(r++,e[n++]=u+1):c===10?(i++,e[n++]=u+1):o&&c!==9&&(c<32||c>126)&&(o=!1)}const l=new n0(hr(e),r,i,s,o);return e.length=0,l}var Z=class{constructor(e,t,n,r,i){this.bufferIndex=e,this.start=t,this.end=n,this.lineFeedCnt=r,this.length=i}},kt=class{constructor(e,t){this.buffer=e,this.lineStarts=t}},i0=class{constructor(e,t){this.a=[],this.c=e,this.d=t,this.b=0,e.root!==x&&e.iterate(e.root,n=>(n!==x&&this.a.push(n.piece),!0))}read(){return this.a.length===0?this.b===0?(this.b++,this.d):null:this.b>this.a.length-1?null:this.b===0?this.d+this.c.getPieceContent(this.a[this.b++]):this.c.getPieceContent(this.a[this.b++])}},s0=class{constructor(e){this.a=e,this.b=[]}get(e){for(let t=this.b.length-1;t>=0;t--){const n=this.b[t];if(n.nodeStartOffset<=e&&n.nodeStartOffset+n.node.piece.length>=e)return n}return null}get2(e){for(let t=this.b.length-1;t>=0;t--){const n=this.b[t];if(n.nodeStartLineNumber&&n.nodeStartLineNumber<e&&n.nodeStartLineNumber+n.node.piece.lineFeedCnt>=e)return n}return null}set(e){this.b.length>=this.a&&this.b.shift(),this.b.push(e)}validate(e){let t=!1;const n=this.b;for(let r=0;r<n.length;r++){const i=n[r];if(i.node.parent===null||i.nodeStartOffset>=e){n[r]=null,t=!0;continue}}if(t){const r=[];for(const i of n)i!==null&&r.push(i);this.b=r}}},o0=class{constructor(e,t,n){this.create(e,t,n)}create(e,t,n){this.a=[new kt("",[0])],this.g={line:0,column:0},this.root=x,this.b=1,this.c=0,this.d=t,this.e=t.length,this.f=n;let r=null;for(let i=0,s=e.length;i<s;i++)if(e[i].buffer.length>0){e[i].lineStarts||(e[i].lineStarts=At(e[i].buffer));const o=new Z(i+1,{line:0,column:0},{line:e[i].lineStarts.length-1,column:e[i].buffer.length-e[i].lineStarts[e[i].lineStarts.length-1]},e[i].lineStarts.length-1,e[i].buffer.length);this.a.push(e[i]),r=this.S(r,o)}this.h=new s0(1),this.j={lineNumber:0,value:""},this.y()}normalizeEOL(e){const t=yt,n=t-Math.floor(t/3),r=n*2;let i="",s=0;const o=[];if(this.iterate(this.root,l=>{const u=this.R(l),a=u.length;if(s<=n||s+a<r)return i+=u,s+=a,!0;const c=i.replace(/\r\n|\r|\n/g,e);return o.push(new kt(c,At(c))),i=u,s=a,!0}),s>0){const l=i.replace(/\r\n|\r|\n/g,e);o.push(new kt(l,At(l)))}this.create(o,e,!0)}getEOL(){return this.d}setEOL(e){this.d=e,this.e=this.d.length,this.normalizeEOL(e)}createSnapshot(e){return new i0(this,e)}equal(e){if(this.getLength()!==e.getLength()||this.getLineCount()!==e.getLineCount())return!1;let t=0;return this.iterate(this.root,r=>{if(r===x)return!0;const i=this.R(r),s=i.length,o=e.G(t),l=e.G(t+s),u=e.getValueInRange2(o,l);return t+=s,i===u})}getOffsetAt(e,t){let n=0,r=this.root;for(;r!==x;)if(r.left!==x&&r.lf_left+1>=e)r=r.left;else if(r.lf_left+r.piece.lineFeedCnt+1>=e){n+=r.size_left;const i=this.B(r,e-r.lf_left-2);return n+=i+t-1}else e-=r.lf_left+r.piece.lineFeedCnt,n+=r.size_left+r.piece.length,r=r.right;return n}getPositionAt(e){e=Math.floor(e),e=Math.max(0,e);let t=this.root,n=0;const r=e;for(;t!==x;)if(t.size_left!==0&&t.size_left>=e)t=t.left;else if(t.size_left+t.piece.length>=e){const i=this.A(t,e-t.size_left);if(n+=t.lf_left+i.index,i.index===0){const s=this.getOffsetAt(n+1,1),o=r-s;return new Ct(n+1,o+1)}return new Ct(n+1,i.remainder+1)}else if(e-=t.size_left+t.piece.length,n+=t.lf_left+t.piece.lineFeedCnt,t.right===x){const i=this.getOffsetAt(n+1,1),s=r-e-i;return new Ct(n+1,s+1)}else t=t.right;return new Ct(1,1)}getValueInRange(e,t){if(e.startLineNumber===e.endLineNumber&&e.startColumn===e.endColumn)return"";const n=this.H(e.startLineNumber,e.startColumn),r=this.H(e.endLineNumber,e.endColumn),i=this.getValueInRange2(n,r);return t?t!==this.d||!this.f?i.replace(/\r\n|\r|\n/g,t):t===this.getEOL()&&this.f?i:i.replace(/\r\n|\r|\n/g,t):i}getValueInRange2(e,t){if(e.node===t.node){const o=e.node,l=this.a[o.piece.bufferIndex].buffer,u=this.u(o.piece.bufferIndex,o.piece.start);return l.substring(u+e.remainder,u+t.remainder)}let n=e.node;const r=this.a[n.piece.bufferIndex].buffer,i=this.u(n.piece.bufferIndex,n.piece.start);let s=r.substring(i+e.remainder,i+n.piece.length);for(n=n.next();n!==x;){const o=this.a[n.piece.bufferIndex].buffer,l=this.u(n.piece.bufferIndex,n.piece.start);if(n===t.node){s+=o.substring(l,l+t.remainder);break}else s+=o.substr(l,n.piece.length);n=n.next()}return s}getLinesContent(){const e=[];let t=0,n="",r=!1;return this.iterate(this.root,i=>{if(i===x)return!0;const s=i.piece;let o=s.length;if(o===0)return!0;const l=this.a[s.bufferIndex].buffer,u=this.a[s.bufferIndex].lineStarts,a=s.start.line,c=s.end.line;let h=u[a]+s.start.column;if(r&&(l.charCodeAt(h)===10&&(h++,o--),e[t++]=n,n="",r=!1,o===0))return!0;if(a===c)return!this.f&&l.charCodeAt(h+o-1)===13?(r=!0,n+=l.substr(h,o-1)):n+=l.substr(h,o),!0;n+=this.f?l.substring(h,Math.max(h,u[a+1]-this.e)):l.substring(h,u[a+1]).replace(/(\r\n|\r|\n)$/,""),e[t++]=n;for(let f=a+1;f<c;f++)n=this.f?l.substring(u[f],u[f+1]-this.e):l.substring(u[f],u[f+1]).replace(/(\r\n|\r|\n)$/,""),e[t++]=n;return!this.f&&l.charCodeAt(u[c]+s.end.column-1)===13?(r=!0,s.end.column===0?t--:n=l.substr(u[c],s.end.column-1)):n=l.substr(u[c],s.end.column),!0}),r&&(e[t++]=n,n=""),e[t++]=n,e}getLength(){return this.c}getLineCount(){return this.b}getLineContent(e){return this.j.lineNumber===e?this.j.value:(this.j.lineNumber=e,e===this.b?this.j.value=this.getLineRawContent(e):this.f?this.j.value=this.getLineRawContent(e,this.e):this.j.value=this.getLineRawContent(e).replace(/(\r\n|\r|\n)$/,""),this.j.value)}l(e){if(e.remainder===e.node.piece.length){const t=e.node.next();if(!t)return 0;const n=this.a[t.piece.bufferIndex],r=this.u(t.piece.bufferIndex,t.piece.start);return n.buffer.charCodeAt(r)}else{const t=this.a[e.node.piece.bufferIndex],r=this.u(e.node.piece.bufferIndex,e.node.piece.start)+e.remainder;return t.buffer.charCodeAt(r)}}getLineCharCode(e,t){const n=this.H(e,t+1);return this.l(n)}getLineLength(e){if(e===this.getLineCount()){const t=this.getOffsetAt(e,1);return this.getLength()-t}return this.getOffsetAt(e+1,1)-this.getOffsetAt(e,1)-this.e}getCharCode(e){const t=this.G(e);return this.l(t)}getNearestChunk(e){const t=this.G(e);if(t.remainder===t.node.piece.length){const n=t.node.next();if(!n||n===x)return"";const r=this.a[n.piece.bufferIndex],i=this.u(n.piece.bufferIndex,n.piece.start);return r.buffer.substring(i,i+n.piece.length)}else{const n=this.a[t.node.piece.bufferIndex],r=this.u(t.node.piece.bufferIndex,t.node.piece.start),i=r+t.remainder,s=r+t.node.piece.length;return n.buffer.substring(i,s)}}findMatchesInNode(e,t,n,r,i,s,o,l,u,a,c){const h=this.a[e.piece.bufferIndex],f=this.u(e.piece.bufferIndex,e.piece.start),g=this.u(e.piece.bufferIndex,i),m=this.u(e.piece.bufferIndex,s);let p;const d={line:0,column:0};let w,b;t._wordSeparators?(w=h.buffer.substring(g,m),b=L=>L+g,t.reset(0)):(w=h.buffer,b=L=>L,t.reset(g));do if(p=t.next(w),p){if(b(p.index)>=m)return a;this.s(e,b(p.index)-f,d);const L=this.t(e.piece.bufferIndex,i,d),D=d.line===i.line?d.column-i.column+r:d.column+1,$=D+p[0].length;if(c[a++]=ar(new J(n+L,D,n+L,$),p,l),b(p.index)+p[0].length>=m||a>=u)return a}while(p);return a}findMatchesLineByLine(e,t,n,r){const i=[];let s=0;const o=new e0(t.wordSeparators,t.regex);let l=this.H(e.startLineNumber,e.startColumn);if(l===null)return[];const u=this.H(e.endLineNumber,e.endColumn);if(u===null)return[];let a=this.s(l.node,l.remainder);const c=this.s(u.node,u.remainder);if(l.node===u.node)return this.findMatchesInNode(l.node,o,e.startLineNumber,e.startColumn,a,c,t,n,r,s,i),i;let h=e.startLineNumber,f=l.node;for(;f!==u.node;){const m=this.t(f.piece.bufferIndex,a,f.piece.end);if(m>=1){const d=this.a[f.piece.bufferIndex].lineStarts,w=this.u(f.piece.bufferIndex,f.piece.start),b=d[a.line+m],L=h===e.startLineNumber?e.startColumn:1;if(s=this.findMatchesInNode(f,o,h,L,a,this.s(f,b-w),t,n,r,s,i),s>=r)return i;h+=m}const p=h===e.startLineNumber?e.startColumn-1:0;if(h===e.endLineNumber){const d=this.getLineContent(h).substring(p,e.endColumn-1);return s=this.n(t,o,d,e.endLineNumber,p,s,i,n,r),i}if(s=this.n(t,o,this.getLineContent(h).substr(p),h,p,s,i,n,r),s>=r)return i;h++,l=this.H(h,1),f=l.node,a=this.s(l.node,l.remainder)}if(h===e.endLineNumber){const m=h===e.startLineNumber?e.startColumn-1:0,p=this.getLineContent(h).substring(m,e.endColumn-1);return s=this.n(t,o,p,e.endLineNumber,m,s,i,n,r),i}const g=h===e.startLineNumber?e.startColumn:1;return s=this.findMatchesInNode(u.node,o,h,g,a,c,t,n,r,s,i),i}n(e,t,n,r,i,s,o,l,u){const a=e.wordSeparators;if(!l&&e.simpleSearch){const h=e.simpleSearch,f=h.length,g=n.length;let m=-f;for(;(m=n.indexOf(h,m+f))!==-1;)if((!a||ur(a,n,g,m,f))&&(o[s++]=new p1(new J(r,m+1+i,r,m+1+f+i),null),s>=u))return s;return s}let c;t.reset(0);do if(c=t.next(n),c&&(o[s++]=ar(new J(r,c.index+1+i,r,c.index+1+c[0].length+i),c,l),s>=u))return s;while(c);return s}insert(e,t,n=!1){if(this.f=this.f&&n,this.j.lineNumber=0,this.j.value="",this.root!==x){const{node:r,remainder:i,nodeStartOffset:s}=this.G(e),o=r.piece,l=o.bufferIndex,u=this.s(r,i);if(r.piece.bufferIndex===0&&o.end.line===this.g.line&&o.end.column===this.g.column&&s+o.length===e&&t.length<yt){this.F(r,t),this.y();return}if(s===e)this.o(t,r),this.h.validate(e);else if(s+r.piece.length>e){const a=[];let c=new Z(o.bufferIndex,u,o.end,this.t(o.bufferIndex,u,o.end),this.u(l,o.end)-this.u(l,u));if(this.K()&&this.M(t)&&this.I(r,i)===10){const m={line:c.start.line+1,column:0};c=new Z(c.bufferIndex,m,c.end,this.t(c.bufferIndex,m,c.end),c.length-1),t+=`
`}if(this.K()&&this.L(t))if(this.I(r,i-1)===13){const m=this.s(r,i-1);this.C(r,m),t="\r"+t,r.piece.length===0&&a.push(r)}else this.C(r,u);else this.C(r,u);const h=this.w(t);c.length>0&&this.S(r,c);let f=r;for(let g=0;g<h.length;g++)f=this.S(f,h[g]);this.v(a)}else this.q(t,r)}else{const r=this.w(t);let i=this.T(null,r[0]);for(let s=1;s<r.length;s++)i=this.S(i,r[s])}this.y()}delete(e,t){if(this.j.lineNumber=0,this.j.value="",t<=0||this.root===x)return;const n=this.G(e),r=this.G(e+t),i=n.node,s=r.node;if(i===s){const h=this.s(i,n.remainder),f=this.s(i,r.remainder);if(n.nodeStartOffset===e){if(t===i.piece.length){const g=i.next();Oe(this,i),this.N(g),this.y();return}this.D(i,f),this.h.validate(e),this.N(i),this.y();return}if(n.nodeStartOffset+i.piece.length===e+t){this.C(i,h),this.O(i),this.y();return}this.E(i,h,f),this.y();return}const o=[],l=this.s(i,n.remainder);this.C(i,l),this.h.validate(e),i.piece.length===0&&o.push(i);const u=this.s(s,r.remainder);this.D(s,u),s.piece.length===0&&o.push(s);const a=i.next();for(let h=a;h!==x&&h!==s;h=h.next())o.push(h);const c=i.piece.length===0?i.prev():i;this.v(o),this.O(c),this.y()}o(e,t){const n=[];if(this.K()&&this.M(e)&&this.L(t)){const s=t.piece,o={line:s.start.line+1,column:0},l=new Z(s.bufferIndex,o,s.end,this.t(s.bufferIndex,o,s.end),s.length-1);t.piece=l,e+=`
`,Lt(this,t,-1,-1),t.piece.length===0&&n.push(t)}const r=this.w(e);let i=this.T(t,r[r.length-1]);for(let s=r.length-2;s>=0;s--)i=this.T(i,r[s]);this.N(i),this.v(n)}q(e,t){this.Q(e,t)&&(e+=`
`);const n=this.w(e),r=this.S(t,n[0]);let i=r;for(let s=1;s<n.length;s++)i=this.S(i,n[s]);this.N(r)}s(e,t,n){const r=e.piece,i=e.piece.bufferIndex,s=this.a[i].lineStarts,l=s[r.start.line]+r.start.column+t;let u=r.start.line,a=r.end.line,c=0,h=0,f=0;for(;u<=a&&(c=u+(a-u)/2|0,f=s[c],c!==a);)if(h=s[c+1],l<f)a=c-1;else if(l>=h)u=c+1;else break;return n?(n.line=c,n.column=l-f,null):{line:c,column:l-f}}t(e,t,n){if(n.column===0)return n.line-t.line;const r=this.a[e].lineStarts;if(n.line===r.length-1)return n.line-t.line;const i=r[n.line+1],s=r[n.line]+n.column;if(i>s+1)return n.line-t.line;const o=s-1;return this.a[e].buffer.charCodeAt(o)===13?n.line-t.line+1:n.line-t.line}u(e,t){return this.a[e].lineStarts[t.line]+t.column}v(e){for(let t=0;t<e.length;t++)Oe(this,e[t])}w(e){if(e.length>yt){const a=[];for(;e.length>yt;){const h=e.charCodeAt(yt-1);let f;h===13||h>=55296&&h<=56319?(f=e.substring(0,yt-1),e=e.substring(yt-1)):(f=e.substring(0,yt),e=e.substring(yt));const g=At(f);a.push(new Z(this.a.length,{line:0,column:0},{line:g.length-1,column:f.length-g[g.length-1]},g.length-1,f.length)),this.a.push(new kt(f,g))}const c=At(e);return a.push(new Z(this.a.length,{line:0,column:0},{line:c.length-1,column:e.length-c[c.length-1]},c.length-1,e.length)),this.a.push(new kt(e,c)),a}let t=this.a[0].buffer.length;const n=At(e,!1);let r=this.g;if(this.a[0].lineStarts[this.a[0].lineStarts.length-1]===t&&t!==0&&this.L(e)&&this.M(this.a[0].buffer)){this.g={line:this.g.line,column:this.g.column+1},r=this.g;for(let a=0;a<n.length;a++)n[a]+=t+1;this.a[0].lineStarts=this.a[0].lineStarts.concat(n.slice(1)),this.a[0].buffer+="_"+e,t+=1}else{if(t!==0)for(let a=0;a<n.length;a++)n[a]+=t;this.a[0].lineStarts=this.a[0].lineStarts.concat(n.slice(1)),this.a[0].buffer+=e}const i=this.a[0].buffer.length,s=this.a[0].lineStarts.length-1,o=i-this.a[0].lineStarts[s],l={line:s,column:o},u=new Z(0,r,l,this.t(0,r,l),i-t);return this.g=l,[u]}getLinesRawContent(){return this.U(this.root)}getLineRawContent(e,t=0){let n=this.root,r="";const i=this.h.get2(e);if(i){n=i.node;const s=this.B(n,e-i.nodeStartLineNumber-1),o=this.a[n.piece.bufferIndex].buffer,l=this.u(n.piece.bufferIndex,n.piece.start);if(i.nodeStartLineNumber+n.piece.lineFeedCnt===e)r=o.substring(l+s,l+n.piece.length);else{const u=this.B(n,e-i.nodeStartLineNumber);return o.substring(l+s,l+u-t)}}else{let s=0;const o=e;for(;n!==x;)if(n.left!==x&&n.lf_left>=e-1)n=n.left;else if(n.lf_left+n.piece.lineFeedCnt>e-1){const l=this.B(n,e-n.lf_left-2),u=this.B(n,e-n.lf_left-1),a=this.a[n.piece.bufferIndex].buffer,c=this.u(n.piece.bufferIndex,n.piece.start);return s+=n.size_left,this.h.set({node:n,nodeStartOffset:s,nodeStartLineNumber:o-(e-1-n.lf_left)}),a.substring(c+l,c+u-t)}else if(n.lf_left+n.piece.lineFeedCnt===e-1){const l=this.B(n,e-n.lf_left-2),u=this.a[n.piece.bufferIndex].buffer,a=this.u(n.piece.bufferIndex,n.piece.start);r=u.substring(a+l,a+n.piece.length);break}else e-=n.lf_left+n.piece.lineFeedCnt,s+=n.size_left+n.piece.length,n=n.right}for(n=n.next();n!==x;){const s=this.a[n.piece.bufferIndex].buffer;if(n.piece.lineFeedCnt>0){const o=this.B(n,0),l=this.u(n.piece.bufferIndex,n.piece.start);return r+=s.substring(l,l+o-t),r}else{const o=this.u(n.piece.bufferIndex,n.piece.start);r+=s.substr(o,n.piece.length)}n=n.next()}return r}y(){let e=this.root,t=1,n=0;for(;e!==x;)t+=e.lf_left+e.piece.lineFeedCnt,n+=e.size_left+e.piece.length,e=e.right;this.b=t,this.c=n,this.h.validate(this.c)}A(e,t){const n=e.piece,r=this.s(e,t),i=r.line-n.start.line;if(this.u(n.bufferIndex,n.end)-this.u(n.bufferIndex,n.start)===t){const s=this.t(e.piece.bufferIndex,n.start,r);if(s!==i)return{index:s,remainder:0}}return{index:i,remainder:r.column}}B(e,t){if(t<0)return 0;const n=e.piece,r=this.a[n.bufferIndex].lineStarts,i=n.start.line+t+1;return i>n.end.line?r[n.end.line]+n.end.column-r[n.start.line]-n.start.column:r[i]-r[n.start.line]-n.start.column}C(e,t){const n=e.piece,r=n.lineFeedCnt,i=this.u(n.bufferIndex,n.end),s=t,o=this.u(n.bufferIndex,s),l=this.t(n.bufferIndex,n.start,s),u=l-r,a=o-i,c=n.length+a;e.piece=new Z(n.bufferIndex,n.start,s,l,c),Lt(this,e,a,u)}D(e,t){const n=e.piece,r=n.lineFeedCnt,i=this.u(n.bufferIndex,n.start),s=t,o=this.t(n.bufferIndex,s,n.end),l=this.u(n.bufferIndex,s),u=o-r,a=i-l,c=n.length+a;e.piece=new Z(n.bufferIndex,s,n.end,o,c),Lt(this,e,a,u)}E(e,t,n){const r=e.piece,i=r.start,s=r.end,o=r.length,l=r.lineFeedCnt,u=t,a=this.t(r.bufferIndex,r.start,u),c=this.u(r.bufferIndex,t)-this.u(r.bufferIndex,i);e.piece=new Z(r.bufferIndex,r.start,u,a,c),Lt(this,e,c-o,a-l);const h=new Z(r.bufferIndex,n,s,this.t(r.bufferIndex,n,s),this.u(r.bufferIndex,s)-this.u(r.bufferIndex,n)),f=this.S(e,h);this.N(f)}F(e,t){this.Q(t,e)&&(t+=`
`);const n=this.K()&&this.L(t)&&this.M(e),r=this.a[0].buffer.length;this.a[0].buffer+=t;const i=At(t,!1);for(let f=0;f<i.length;f++)i[f]+=r;if(n){const f=this.a[0].lineStarts[this.a[0].lineStarts.length-2];this.a[0].lineStarts.pop(),this.g={line:this.g.line-1,column:r-f}}this.a[0].lineStarts=this.a[0].lineStarts.concat(i.slice(1));const s=this.a[0].lineStarts.length-1,o=this.a[0].buffer.length-this.a[0].lineStarts[s],l={line:s,column:o},u=e.piece.length+t.length,a=e.piece.lineFeedCnt,c=this.t(0,e.piece.start,l),h=c-a;e.piece=new Z(e.piece.bufferIndex,e.piece.start,l,c,u),this.g=l,Lt(this,e,t.length,h)}G(e){let t=this.root;const n=this.h.get(e);if(n)return{node:n.node,nodeStartOffset:n.nodeStartOffset,remainder:e-n.nodeStartOffset};let r=0;for(;t!==x;)if(t.size_left>e)t=t.left;else if(t.size_left+t.piece.length>=e){r+=t.size_left;const i={node:t,remainder:e-t.size_left,nodeStartOffset:r};return this.h.set(i),i}else e-=t.size_left+t.piece.length,r+=t.size_left+t.piece.length,t=t.right;return null}H(e,t){let n=this.root,r=0;for(;n!==x;)if(n.left!==x&&n.lf_left>=e-1)n=n.left;else if(n.lf_left+n.piece.lineFeedCnt>e-1){const i=this.B(n,e-n.lf_left-2),s=this.B(n,e-n.lf_left-1);return r+=n.size_left,{node:n,remainder:Math.min(i+t-1,s),nodeStartOffset:r}}else if(n.lf_left+n.piece.lineFeedCnt===e-1){const i=this.B(n,e-n.lf_left-2);if(i+t-1<=n.piece.length)return{node:n,remainder:i+t-1,nodeStartOffset:r};t-=n.piece.length-i;break}else e-=n.lf_left+n.piece.lineFeedCnt,r+=n.size_left+n.piece.length,n=n.right;for(n=n.next();n!==x;){if(n.piece.lineFeedCnt>0){const i=this.B(n,0),s=this.J(n);return{node:n,remainder:Math.min(t-1,i),nodeStartOffset:s}}else if(n.piece.length>=t-1){const i=this.J(n);return{node:n,remainder:t-1,nodeStartOffset:i}}else t-=n.piece.length;n=n.next()}return null}I(e,t){if(e.piece.lineFeedCnt<1)return-1;const n=this.a[e.piece.bufferIndex],r=this.u(e.piece.bufferIndex,e.piece.start)+t;return n.buffer.charCodeAt(r)}J(e){if(!e)return 0;let t=e.size_left;for(;e!==this.root;)e.parent.right===e&&(t+=e.parent.size_left+e.parent.piece.length),e=e.parent;return t}K(){return!(this.f&&this.d===`
`)}L(e){if(typeof e=="string")return e.charCodeAt(0)===10;if(e===x||e.piece.lineFeedCnt===0)return!1;const t=e.piece,n=this.a[t.bufferIndex].lineStarts,r=t.start.line,i=n[r]+t.start.column;return r===n.length-1||n[r+1]>i+1?!1:this.a[t.bufferIndex].buffer.charCodeAt(i)===10}M(e){return typeof e=="string"?e.charCodeAt(e.length-1)===13:e===x||e.piece.lineFeedCnt===0?!1:this.I(e,e.piece.length-1)===13}N(e){if(this.K()&&this.L(e)){const t=e.prev();this.M(t)&&this.P(t,e)}}O(e){if(this.K()&&this.M(e)){const t=e.next();this.L(t)&&this.P(e,t)}}P(e,t){const n=[],r=this.a[e.piece.bufferIndex].lineStarts;let i;e.piece.end.column===0?i={line:e.piece.end.line-1,column:r[e.piece.end.line]-r[e.piece.end.line-1]-1}:i={line:e.piece.end.line,column:e.piece.end.column-1};const s=e.piece.length-1,o=e.piece.lineFeedCnt-1;e.piece=new Z(e.piece.bufferIndex,e.piece.start,i,o,s),Lt(this,e,-1,-1),e.piece.length===0&&n.push(e);const l={line:t.piece.start.line+1,column:0},u=t.piece.length-1,a=this.t(t.piece.bufferIndex,l,t.piece.end);t.piece=new Z(t.piece.bufferIndex,l,t.piece.end,a,u),Lt(this,t,-1,-1),t.piece.length===0&&n.push(t);const c=this.w(`\r
`);this.S(e,c[0]);for(let h=0;h<n.length;h++)Oe(this,n[h])}Q(e,t){if(this.K()&&this.M(e)){const n=t.next();if(this.L(n)){if(e+=`
`,n.piece.length===1)Oe(this,n);else{const r=n.piece,i={line:r.start.line+1,column:0},s=r.length-1,o=this.t(r.bufferIndex,i,r.end);n.piece=new Z(r.bufferIndex,i,r.end,o,s),Lt(this,n,-1,-1)}return!0}}return!1}iterate(e,t){if(e===x)return t(x);const n=this.iterate(e.left,t);return n&&t(e)&&this.iterate(e.right,t)}R(e){if(e===x)return"";const t=this.a[e.piece.bufferIndex],n=e.piece,r=this.u(n.bufferIndex,n.start),i=this.u(n.bufferIndex,n.end);return t.buffer.substring(r,i)}getPieceContent(e){const t=this.a[e.bufferIndex],n=this.u(e.bufferIndex,e.start),r=this.u(e.bufferIndex,e.end);return t.buffer.substring(n,r)}S(e,t){const n=new b1(t,1);if(n.left=x,n.right=x,n.parent=x,n.size_left=0,n.lf_left=0,this.root===x)this.root=n,n.color=0;else if(e.right===x)e.right=n,n.parent=e;else{const i=v1(e.right);i.left=n,n.parent=i}return nr(this,n),n}T(e,t){const n=new b1(t,1);if(n.left=x,n.right=x,n.parent=x,n.size_left=0,n.lf_left=0,this.root===x)this.root=n,n.color=0;else if(e.left===x)e.left=n,n.parent=e;else{const r=er(e.left);r.right=n,n.parent=r}return nr(this,n),n}U(e){let t="";return this.iterate(e,n=>(t+=this.R(n),!0)),t}},cr;(function(e){e[e.Unknown=0]="Unknown",e[e.Invalid=3]="Invalid",e[e.LF=1]="LF",e[e.CRLF=2]="CRLF"})(cr||(cr={}));function A1(e){let t=0,n=0,r=0,i=0;for(let s=0,o=e.length;s<o;s++){const l=e.charCodeAt(s);l===13?(t===0&&(n=s),t++,s+1<o&&e.charCodeAt(s+1)===10?(i|=2,s++):i|=3,r=s+1):l===10&&(i|=1,t===0&&(n=s),t++,r=s+1)}return t===0&&(n=e.length),[t,n,e.length-r,i]}var x1;function l0(){return x1||(x1=new TextDecoder("UTF-16LE")),x1}function a0(e,t,n){const r=new Uint16Array(e.buffer,t,n);return n>0&&(r[0]===65279||r[0]===65534)?u0(e,t,n):l0().decode(r)}function u0(e,t,n){const r=[];let i=0;for(let s=0;s<n;s++){const o=ro(e,t);t+=2,r[i++]=String.fromCharCode(o)}return r.join("")}function _e(e){return e.replace(/\n/g,"\\n").replace(/\r/g,"\\r")}var h0=class gt{get oldLength(){return this.oldText.length}get oldEnd(){return this.oldPosition+this.oldText.length}get newLength(){return this.newText.length}get newEnd(){return this.newPosition+this.newText.length}constructor(t,n,r,i){this.oldPosition=t,this.oldText=n,this.newPosition=r,this.newText=i}toString(){return this.oldText.length===0?`(insert@${this.oldPosition} "${_e(this.newText)}")`:this.newText.length===0?`(delete@${this.oldPosition} "${_e(this.oldText)}")`:`(replace@${this.oldPosition} "${_e(this.oldText)}" with "${_e(this.newText)}")`}static a(t){return 4+2*t.length}static c(t,n,r){const i=n.length;xe(t,i,r),r+=4;for(let s=0;s<i;s++)io(t,n.charCodeAt(s),r),r+=2;return r}static d(t,n){const r=Ae(t,n);return n+=4,a0(t,n,r)}writeSize(){return 8+gt.a(this.oldText)+gt.a(this.newText)}write(t,n){return xe(t,this.oldPosition,n),n+=4,xe(t,this.newPosition,n),n+=4,n=gt.c(t,this.oldText,n),n=gt.c(t,this.newText,n),n}static read(t,n,r){const i=Ae(t,n);n+=4;const s=Ae(t,n);n+=4;const o=gt.d(t,n);n+=gt.a(o);const l=gt.d(t,n);return n+=gt.a(l),r.push(new gt(i,o,s,l)),n}},c0=class he extends zt{constructor(t,n,r,i,s,o,l){super(),this.m=this.B(new ht),this.onDidChangeContent=this.m.event,this.f=n,this.j=!o,this.g=i,this.h=s,this.c=new o0(t,r,l)}equals(t){return!(t instanceof he)||this.f!==t.f||this.getEOL()!==t.getEOL()?!1:this.c.equal(t.c)}mightContainRTL(){return this.g}mightContainUnusualLineTerminators(){return this.h}resetMightContainUnusualLineTerminators(){this.h=!1}mightContainNonBasicASCII(){return this.j}getBOM(){return this.f}getEOL(){return this.c.getEOL()}createSnapshot(t){return this.c.createSnapshot(t?this.f:"")}getOffsetAt(t,n){return this.c.getOffsetAt(t,n)}getPositionAt(t){return this.c.getPositionAt(t)}getRangeAt(t,n){const r=t+n,i=this.getPositionAt(t),s=this.getPositionAt(r);return new J(i.lineNumber,i.column,s.lineNumber,s.column)}getValueInRange(t,n=0){if(t.isEmpty())return"";const r=this.n(n);return this.c.getValueInRange(t,r)}getValueLengthInRange(t,n=0){if(t.isEmpty())return 0;if(t.startLineNumber===t.endLineNumber)return t.endColumn-t.startColumn;const r=this.getOffsetAt(t.startLineNumber,t.startColumn),i=this.getOffsetAt(t.endLineNumber,t.endColumn);let s=0;const o=this.n(n),l=this.getEOL();if(o.length!==l.length){const u=o.length-l.length,a=t.endLineNumber-t.startLineNumber;s=u*a}return i-r+s}getCharacterCountInRange(t,n=0){if(this.j){let r=0;const i=t.startLineNumber,s=t.endLineNumber;for(let o=i;o<=s;o++){const l=this.getLineContent(o),u=o===i?t.startColumn-1:0,a=o===s?t.endColumn-1:l.length;for(let c=u;c<a;c++)i1(l.charCodeAt(c))?(r=r+1,c=c+1):r=r+1}return r+=this.n(n).length*(s-i),r}return this.getValueLengthInRange(t,n)}getNearestChunk(t){return this.c.getNearestChunk(t)}getLength(){return this.c.getLength()}getLineCount(){return this.c.getLineCount()}getLinesContent(){return this.c.getLinesContent()}getLineContent(t){return this.c.getLineContent(t)}getLineCharCode(t,n){return this.c.getLineCharCode(t,n)}getCharCode(t){return this.c.getCharCode(t)}getLineLength(t){return this.c.getLineLength(t)}getLineMinColumn(t){return 1}getLineMaxColumn(t){return this.getLineLength(t)+1}getLineFirstNonWhitespaceColumn(t){const n=r1(this.getLineContent(t));return n===-1?0:n+1}getLineLastNonWhitespaceColumn(t){const n=Os(this.getLineContent(t));return n===-1?0:n+2}n(t){switch(t){case 1:return`
`;case 2:return`\r
`;case 0:return this.getEOL();default:throw new Error("Unknown EOL preference")}}setEOL(t){this.c.setEOL(t)}applyEdits(t,n,r){let i=this.g,s=this.h,o=this.j,l=!0,u=[];for(let p=0;p<t.length;p++){const d=t[p];l&&d._isTracked&&(l=!1);const w=d.range;if(d.text){let z=!0;o||(z=!qs(d.text),o=z),!i&&z&&(i=wn(d.text)),!s&&z&&(s=Cn(d.text))}let b="",L=0,D=0,$=0;if(d.text){let z;[L,D,$,z]=A1(d.text);const C=this.getEOL();z===0||z===(C===`\r
`?2:1)?b=d.text:b=d.text.replace(/\r\n|\r|\n/g,C)}u[p]={sortIndex:p,identifier:d.identifier||null,range:w,rangeOffset:this.getOffsetAt(w.startLineNumber,w.startColumn),rangeLength:this.getValueLengthInRange(w),text:b,eolCount:L,firstLineLength:D,lastLineLength:$,forceMoveMarkers:!!d.forceMoveMarkers,isAutoWhitespaceEdit:d.isAutoWhitespaceEdit||!1}}u.sort(he.u);let a=!1;for(let p=0,d=u.length-1;p<d;p++){const w=u[p].range.getEndPosition(),b=u[p+1].range.getStartPosition();if(b.isBeforeOrEqual(w)){if(b.isBefore(w))throw new Error("Overlapping ranges are not allowed!");a=!0}}l&&(u=this.s(u));const c=r||n?he._getInverseEditRanges(u):[],h=[];if(n)for(let p=0;p<u.length;p++){const d=u[p],w=c[p];if(d.isAutoWhitespaceEdit&&d.range.isEmpty())for(let b=w.startLineNumber;b<=w.endLineNumber;b++){let L="";b===w.startLineNumber&&(L=this.getLineContent(d.range.startLineNumber),r1(L)!==-1)||h.push({lineNumber:b,oldContent:L})}}let f=null;if(r){let p=0;f=[];for(let d=0;d<u.length;d++){const w=u[d],b=c[d],L=this.getValueInRange(w.range),D=w.rangeOffset+p;p+=w.text.length-L.length,f[d]={sortIndex:w.sortIndex,identifier:w.identifier,range:b,text:L,textChange:new h0(w.rangeOffset,L,D,w.text)}}a||f.sort((d,w)=>d.sortIndex-w.sortIndex)}this.g=i,this.h=s,this.j=o;const g=this.t(u);let m=null;if(n&&h.length>0){h.sort((p,d)=>d.lineNumber-p.lineNumber),m=[];for(let p=0,d=h.length;p<d;p++){const w=h[p].lineNumber;if(p>0&&h[p-1].lineNumber===w)continue;const b=h[p].oldContent,L=this.getLineContent(w);L.length===0||L===b||r1(L)!==-1||m.push(w)}}return this.m.fire(),new Vo(f,g,m)}s(t){return t.length<1e3?t:[this._toSingleEditOperation(t)]}_toSingleEditOperation(t){let n=!1;const r=t[0].range,i=t[t.length-1].range,s=new J(r.startLineNumber,r.startColumn,i.endLineNumber,i.endColumn);let o=r.startLineNumber,l=r.startColumn;const u=[];for(let g=0,m=t.length;g<m;g++){const p=t[g],d=p.range;n=n||p.forceMoveMarkers,u.push(this.getValueInRange(new J(o,l,d.startLineNumber,d.startColumn))),p.text.length>0&&u.push(p.text),o=d.endLineNumber,l=d.endColumn}const a=u.join(""),[c,h,f]=A1(a);return{sortIndex:0,identifier:t[0].identifier,range:s,rangeOffset:this.getOffsetAt(s.startLineNumber,s.startColumn),rangeLength:this.getValueLengthInRange(s,0),text:a,eolCount:c,firstLineLength:h,lastLineLength:f,forceMoveMarkers:n,isAutoWhitespaceEdit:!1}}t(t){t.sort(he.w);const n=[];for(let r=0;r<t.length;r++){const i=t[r],s=i.range.startLineNumber,o=i.range.startColumn,l=i.range.endLineNumber,u=i.range.endColumn;if(s===l&&o===u&&i.text.length===0)continue;i.text?(this.c.delete(i.rangeOffset,i.rangeLength),this.c.insert(i.rangeOffset,i.text,!0)):this.c.delete(i.rangeOffset,i.rangeLength);const a=new J(s,o,l,u);n.push({range:a,rangeLength:i.rangeLength,text:i.text,rangeOffset:i.rangeOffset,forceMoveMarkers:i.forceMoveMarkers})}return n}findMatchesLineByLine(t,n,r,i){return this.c.findMatchesLineByLine(t,n,r,i)}getPieceTree(){return this.c}static _getInverseEditRange(t,n){const r=t.startLineNumber,i=t.startColumn,[s,o,l]=A1(n);let u;if(n.length>0){const a=s+1;a===1?u=new J(r,i,r,i+o):u=new J(r,i,r+a-1,l+1)}else u=new J(r,i,r,i);return u}static _getInverseEditRanges(t){const n=[];let r=0,i=0,s=null;for(let o=0,l=t.length;o<l;o++){const u=t[o];let a,c;s?s.range.endLineNumber===u.range.startLineNumber?(a=r,c=i+(u.range.startColumn-s.range.endColumn)):(a=r+(u.range.startLineNumber-s.range.endLineNumber),c=u.range.startColumn):(a=u.range.startLineNumber,c=u.range.startColumn);let h;if(u.text.length>0){const f=u.eolCount+1;f===1?h=new J(a,c,a,c+u.firstLineLength):h=new J(a,c,a+f-1,u.lastLineLength+1)}else h=new J(a,c,a,c);r=h.endLineNumber,i=h.endColumn,n.push(h),s=u}return n}static u(t,n){const r=J.compareRangesUsingEnds(t.range,n.range);return r===0?t.sortIndex-n.sortIndex:r}static w(t,n){const r=J.compareRangesUsingEnds(t.range,n.range);return r===0?n.sortIndex-t.sortIndex:-r}},f0=class{constructor(e,t,n,r,i,s,o,l,u){this.a=e,this.b=t,this.c=n,this.d=r,this.e=i,this.f=s,this.g=o,this.h=l,this.j=u}k(e){const t=this.c+this.d+this.e,n=this.c+this.e;return t===0?e===1?`
`:`\r
`:n>t/2?`\r
`:`
`}create(e){const t=this.k(e),n=this.a;if(this.j&&(t===`\r
`&&(this.c>0||this.d>0)||t===`
`&&(this.c>0||this.e>0)))for(let i=0,s=n.length;i<s;i++){const o=n[i].buffer.replace(/\r\n|\r|\n/g,t),l=At(o);n[i]=new kt(o,l)}const r=new c0(n,this.b,t,this.f,this.g,this.h,this.j);return{textBuffer:r,disposable:r}}getFirstLineText(e){return this.a[0].buffer.substr(0,e).split(/\r\n|\r|\n/)[0]}},d0=class{constructor(){this.a=[],this.b="",this.c=!1,this.d=0,this.e=[],this.f=0,this.g=0,this.h=0,this.j=!1,this.k=!1,this.l=!0}acceptChunk(e){if(e.length===0)return;this.a.length===0&&Vs(e)&&(this.b=Bs,e=e.substr(1));const t=e.charCodeAt(e.length-1);t===13||t>=55296&&t<=56319?(this.m(e.substr(0,e.length-1),!1),this.c=!0,this.d=t):(this.m(e,!1),this.c=!1,this.d=t)}m(e,t){!t&&e.length===0||(this.c?this.n(String.fromCharCode(this.d)+e):this.n(e))}n(e){const t=r0(this.e,e);this.a.push(new kt(e,t.lineStarts)),this.f+=t.cr,this.g+=t.lf,this.h+=t.crlf,t.isBasicASCII||(this.l=!1,this.j||(this.j=wn(e)),this.k||(this.k=Cn(e)))}finish(e=!0){return this.o(),new f0(this.a,this.b,this.f,this.g,this.h,this.j,this.k,this.l,e)}o(){if(this.a.length===0&&this.m("",!0),this.c){this.c=!1;const e=this.a[this.a.length-1];e.buffer+=String.fromCharCode(this.d);const t=At(e.buffer);e.lineStarts=t,this.d===13&&this.f++}}};function Ot(e){return e===47||e===92}function fr(e){return e.replace(/[\\/]/g,U.sep)}function g0(e){return e.indexOf("/")===-1&&(e=fr(e)),/^[a-zA-Z]:(\/|$)/.test(e)&&(e="/"+e),e}function dr(e,t=U.sep){if(!e)return"";const n=e.length,r=e.charCodeAt(0);if(Ot(r)){if(Ot(e.charCodeAt(1))&&!Ot(e.charCodeAt(2))){let s=3;const o=s;for(;s<n&&!Ot(e.charCodeAt(s));s++);if(o!==s&&!Ot(e.charCodeAt(s+1))){for(s+=1;s<n;s++)if(Ot(e.charCodeAt(s)))return e.slice(0,s+1).replace(/[\\/]/g,t)}}return t}else if(m0(r)&&e.charCodeAt(1)===58)return Ot(e.charCodeAt(2))?e.slice(0,2)+t:e.slice(0,2);let i=e.indexOf("://");if(i!==-1){for(i+=3;i<n;i++)if(Ot(e.charCodeAt(i)))return e.slice(0,i+1)}return""}function gr(e,t,n,r=Ie){if(e===t)return!0;if(!e||!t||t.length>e.length)return!1;if(n){if(!Rs(e,t))return!1;if(t.length===e.length)return!0;let s=t.length;return t.charAt(t.length-1)===r&&s--,e.charAt(s)===r}return t.charAt(t.length-1)!==r&&(t+=r),e.indexOf(t)===0}function m0(e){return e>=65&&e<=90||e>=97&&e<=122}var P;(function(e){e.inMemory="inmemory",e.vscode="vscode",e.internal="private",e.walkThrough="walkThrough",e.walkThroughSnippet="walkThroughSnippet",e.http="http",e.https="https",e.file="file",e.mailto="mailto",e.untitled="untitled",e.data="data",e.command="command",e.vscodeRemote="vscode-remote",e.vscodeRemoteResource="vscode-remote-resource",e.vscodeManagedRemoteResource="vscode-managed-remote-resource",e.vscodeUserData="vscode-userdata",e.vscodeCustomEditor="vscode-custom-editor",e.vscodeNotebookCell="vscode-notebook-cell",e.vscodeNotebookCellMetadata="vscode-notebook-cell-metadata",e.vscodeNotebookCellMetadataDiff="vscode-notebook-cell-metadata-diff",e.vscodeNotebookCellOutput="vscode-notebook-cell-output",e.vscodeNotebookCellOutputDiff="vscode-notebook-cell-output-diff",e.vscodeNotebookMetadata="vscode-notebook-metadata",e.vscodeInteractiveInput="vscode-interactive-input",e.vscodeSettings="vscode-settings",e.vscodeWorkspaceTrust="vscode-workspace-trust",e.vscodeTerminal="vscode-terminal",e.vscodeChatCodeBlock="vscode-chat-code-block",e.vscodeChatCodeCompareBlock="vscode-chat-code-compare-block",e.vscodeChatEditor="vscode-chat-editor",e.vscodeChatInput="chatSessionInput",e.vscodeChatSession="vscode-chat-session",e.webviewPanel="webview-panel",e.vscodeWebview="vscode-webview",e.extension="extension",e.vscodeFileResource="vscode-file",e.tmp="tmp",e.vsls="vsls",e.vscodeSourceControl="vscode-scm",e.commentsInput="comment",e.codeSetting="code-setting",e.outputChannel="output",e.accessibleView="accessible-view"})(P||(P={}));var p0="tkn",b0=class{constructor(){this.a=Object.create(null),this.b=Object.create(null),this.c=Object.create(null),this.d="http",this.e=null,this.f="/"}setPreferredWebSchema(e){this.d=e}setDelegate(e){this.e=e}setServerRootPath(e,t){this.f=U.join(t??"/",w0(e))}getServerRootPath(){return this.f}get g(){return U.join(this.f,P.vscodeRemoteResource)}set(e,t,n){this.a[e]=t,this.b[e]=n}setConnectionToken(e,t){this.c[e]=t}getPreferredWebSchema(){return this.d}rewrite(e){if(this.e)try{return this.e(e)}catch(o){return ge(o),e}const t=e.authority;let n=this.a[t];n&&n.indexOf(":")!==-1&&n.indexOf("[")===-1&&(n=`[${n}]`);const r=this.b[t],i=this.c[t];let s=`path=${encodeURIComponent(e.path)}`;return typeof i=="string"&&(s+=`&${p0}=${encodeURIComponent(i)}`),it.from({scheme:n1?this.d:P.vscodeRemoteResource,authority:`${n}:${r}`,path:this.g,query:s})}},v0=new b0;function w0(e){return`${e.quality??"oss"}-${e.commit??"dev"}`}var C0="vscode-app",L0=class Ue{static{this.a=C0}asBrowserUri(t){const n=this.b(t);return this.uriToBrowserUri(n)}uriToBrowserUri(t){return t.scheme===P.vscodeRemote?v0.rewrite(t):t.scheme===P.file&&(ms||bs===`${P.vscodeFileResource}://${Ue.a}`)?t.with({scheme:P.vscodeFileResource,authority:t.authority||Ue.a,query:null,fragment:null}):t}asFileUri(t){const n=this.b(t);return this.uriToFileUri(n)}uriToFileUri(t){return t.scheme===P.vscodeFileResource?t.with({scheme:P.file,authority:t.authority!==Ue.a?t.authority:null,query:null,fragment:null}):t}b(t){if(it.isUri(t))return t;if(globalThis._VSCODE_FILE_ROOT){const n=globalThis._VSCODE_FILE_ROOT;if(/^\w[\w\d+.-]*:\/\//.test(n))return it.joinPath(it.parse(n,!0),t);const r=_o(n,t);return it.file(r)}throw new Error("Cannot determine URI for module id!")}},Ml=new L0,Dl=Object.freeze({"Cache-Control":"no-cache, no-store"}),Rl=Object.freeze({"Document-Policy":"include-js-call-stacks-in-crash-reports"}),mr;(function(e){const t=new Map([["1",{"Cross-Origin-Opener-Policy":"same-origin"}],["2",{"Cross-Origin-Embedder-Policy":"require-corp"}],["3",{"Cross-Origin-Opener-Policy":"same-origin","Cross-Origin-Embedder-Policy":"require-corp"}]]);e.CoopAndCoep=Object.freeze(t.get("3"));const n="vscode-coi";function r(s){let o;typeof s=="string"?o=new URL(s).searchParams:s instanceof URL?o=s.searchParams:it.isUri(s)&&(o=new URL(s.toString(!0)).searchParams);const l=o?.get(n);if(l)return t.get(l)}e.getHeadersFromQuery=r;function i(s,o,l){if(!globalThis.crossOriginIsolated)return;const u=o&&l?"3":l?"2":"1";s instanceof URLSearchParams?s.set(n,u):s[n]=u}e.addSearchParam=i})(mr||(mr={}));function xt(e){return Se(e,!0)}var E1=class{constructor(e){this.a=e}compare(e,t,n=!1){return e===t?0:_s(this.getComparisonKey(e,n),this.getComparisonKey(t,n))}isEqual(e,t,n=!1){return e===t?!0:!e||!t?!1:this.getComparisonKey(e,n)===this.getComparisonKey(t,n)}getComparisonKey(e,t=!1){return e.with({path:this.a(e)?e.path.toLowerCase():void 0,fragment:t?null:void 0}).toString()}ignorePathCasing(e){return this.a(e)}isEqualOrParent(e,t,n=!1){if(e.scheme===t.scheme){if(e.scheme===P.file)return gr(xt(e),xt(t),this.a(e))&&e.query===t.query&&(n||e.fragment===t.fragment);if(pr(e.authority,t.authority))return gr(e.path,t.path,this.a(e),"/")&&e.query===t.query&&(n||e.fragment===t.fragment)}return!1}joinPath(e,...t){return it.joinPath(e,...t)}basenameOrAuthority(e){return y0(e)||e.authority}basename(e){return U.basename(e.path)}extname(e){return U.extname(e.path)}dirname(e){if(e.path.length===0)return e;let t;return e.scheme===P.file?t=it.file(Ro(xt(e))).path:(t=U.dirname(e.path),e.authority&&t.length&&t.charCodeAt(0)!==47&&(console.error(`dirname("${e.toString})) resulted in a relative path`),t="/")),e.with({path:t})}normalizePath(e){if(!e.path.length)return e;let t;return e.scheme===P.file?t=it.file(Oo(xt(e))).path:t=U.normalize(e.path),e.with({path:t})}relativePath(e,t){if(e.scheme!==t.scheme||!pr(e.authority,t.authority))return;if(e.scheme===P.file){const i=Do(xt(e),xt(t));return Dt?fr(i):i}let n=e.path||"/";const r=t.path||"/";if(this.a(e)){let i=0;for(const s=Math.min(n.length,r.length);i<s&&!(n.charCodeAt(i)!==r.charCodeAt(i)&&n.charAt(i).toLowerCase()!==r.charAt(i).toLowerCase());i++);n=r.substr(0,i)+n.substr(i)}return U.relative(n,r)}resolvePath(e,t){if(e.scheme===P.file){const n=it.file(Mo(xt(e),t));return e.with({authority:n.authority,path:n.path})}return t=g0(t),e.with({path:U.resolve(e.path,t)})}isAbsolutePath(e){return!!e.path&&e.path[0]==="/"}isEqualAuthority(e,t){return e===t||e!==void 0&&t!==void 0&&Ds(e,t)}hasTrailingPathSeparator(e,t=Ie){if(e.scheme===P.file){const n=xt(e);return n.length>dr(n).length&&n[n.length-1]===t}else{const n=e.path;return n.length>1&&n.charCodeAt(n.length-1)===47&&!/^[a-zA-Z]:(\/$|\\$)/.test(e.fsPath)}}removeTrailingPathSeparator(e,t=Ie){return br(e,t)?e.with({path:e.path.substr(0,e.path.length-1)}):e}addTrailingPathSeparator(e,t=Ie){let n=!1;if(e.scheme===P.file){const r=xt(e);n=r!==void 0&&r.length===dr(r).length&&r[r.length-1]===t}else{t="/";const r=e.path;n=r.length===1&&r.charCodeAt(r.length-1)===47}return!n&&!br(e,t)?e.with({path:e.path+"/"}):e}},F=new E1(()=>!1),Pl=new E1(e=>e.scheme===P.file?!un:!0),kl=new E1(e=>!0),Fl=F.isEqual.bind(F),ql=F.isEqualOrParent.bind(F),Ul=F.getComparisonKey.bind(F),jl=F.basenameOrAuthority.bind(F),y0=F.basename.bind(F),zl=F.extname.bind(F),Tl=F.dirname.bind(F),Bl=F.joinPath.bind(F),Vl=F.normalizePath.bind(F),Wl=F.relativePath.bind(F),Hl=F.resolvePath.bind(F),Gl=F.isAbsolutePath.bind(F),pr=F.isEqualAuthority.bind(F),br=F.hasTrailingPathSeparator.bind(F),Kl=F.removeTrailingPathSeparator.bind(F),Jl=F.addTrailingPathSeparator.bind(F),vr;(function(e){e.META_DATA_LABEL="label",e.META_DATA_DESCRIPTION="description",e.META_DATA_SIZE="size",e.META_DATA_MIME="mime";function t(n){const r=new Map;n.path.substring(n.path.indexOf(";")+1,n.path.lastIndexOf(";")).split(";").forEach(o=>{const[l,u]=o.split(":");l&&u&&r.set(l,u)});const s=n.path.substring(0,n.path.indexOf(";"));return s&&r.set(e.META_DATA_MIME,s),r}e.parseMetaData=t})(vr||(vr={}));var Xl=Symbol("MicrotaskDelay"),A0,N1;(function(){const e=globalThis;typeof e.requestIdleCallback!="function"||typeof e.cancelIdleCallback!="function"?N1=(t,n,r)=>{ws(()=>{if(i)return;const s=Date.now()+15;n(Object.freeze({didTimeout:!0,timeRemaining(){return Math.max(0,s-Date.now())}}))});let i=!1;return{dispose(){i||(i=!0)}}}:N1=(t,n,r)=>{const i=t.requestIdleCallback(n,typeof r=="number"?{timeout:r}:void 0);let s=!1;return{dispose(){s||(s=!0,t.cancelIdleCallback(i))}}},A0=(t,n)=>N1(globalThis,t,n)})();var wr;(function(e){e[e.Resolved=0]="Resolved",e[e.Rejected=1]="Rejected"})(wr||(wr={}));var Cr;(function(e){async function t(r){let i;const s=await Promise.all(r.map(o=>o.then(l=>l,l=>{i||(i=l)})));if(typeof i<"u")throw i;return s}e.settled=t;function n(r){return new Promise(async(i,s)=>{try{await r(i,s)}catch(o){s(o)}})}e.withAsyncBody=n})(Cr||(Cr={}));var Lr;(function(e){e[e.Initial=0]="Initial",e[e.DoneOK=1]="DoneOK",e[e.DoneError=2]="DoneError"})(Lr||(Lr={}));var Ql=class lt{static fromArray(t){return new lt(n=>{n.emitMany(t)})}static fromPromise(t){return new lt(async n=>{n.emitMany(await t)})}static fromPromisesResolveOrder(t){return new lt(async n=>{await Promise.all(t.map(async r=>n.emitOne(await r)))})}static merge(t){return new lt(async n=>{await Promise.all(t.map(async r=>{for await(const i of r)n.emitOne(i)}))})}static{this.EMPTY=lt.fromArray([])}constructor(t,n){this.a=0,this.b=[],this.d=null,this.f=n,this.g=new ht,queueMicrotask(async()=>{const r={emitOne:i=>this.h(i),emitMany:i=>this.j(i),reject:i=>this.l(i)};try{await Promise.resolve(t(r)),this.k()}catch(i){this.l(i)}finally{r.emitOne=void 0,r.emitMany=void 0,r.reject=void 0}})}[Symbol.asyncIterator](){let t=0;return{next:async()=>{do{if(this.a===2)throw this.d;if(t<this.b.length)return{done:!1,value:this.b[t++]};if(this.a===1)return{done:!0,value:void 0};await pe.toPromise(this.g.event)}while(!0)},return:async()=>(this.f?.(),{done:!0,value:void 0})}}static map(t,n){return new lt(async r=>{for await(const i of t)r.emitOne(n(i))})}map(t){return lt.map(this,t)}static filter(t,n){return new lt(async r=>{for await(const i of t)n(i)&&r.emitOne(i)})}filter(t){return lt.filter(this,t)}static coalesce(t){return lt.filter(t,n=>!!n)}coalesce(){return lt.coalesce(this)}static async toPromise(t){const n=[];for await(const r of t)n.push(r);return n}toPromise(){return lt.toPromise(this)}h(t){this.a===0&&(this.b.push(t),this.g.fire())}j(t){this.a===0&&(this.b=this.b.concat(t),this.g.fire())}k(){this.a===0&&(this.a=1,this.g.fire())}l(t){this.a===0&&(this.a=2,this.d=t,this.g.fire())}},Zl=Symbol("AsyncReaderEndOfStream"),Yl=new Q1(1e4),Ht=Object.freeze({text:"text/plain",binary:"application/octet-stream",unknown:"application/unknown",markdown:"text/markdown",latex:"text/latex",uriList:"text/uri-list",html:"text/html"}),yr;(function(e){e[e.LParen=0]="LParen",e[e.RParen=1]="RParen",e[e.Neg=2]="Neg",e[e.Eq=3]="Eq",e[e.NotEq=4]="NotEq",e[e.Lt=5]="Lt",e[e.LtEq=6]="LtEq",e[e.Gt=7]="Gt",e[e.GtEq=8]="GtEq",e[e.RegexOp=9]="RegexOp",e[e.RegexStr=10]="RegexStr",e[e.True=11]="True",e[e.False=12]="False",e[e.In=13]="In",e[e.Not=14]="Not",e[e.And=15]="And",e[e.Or=16]="Or",e[e.Str=17]="Str",e[e.QuotedStr=18]="QuotedStr",e[e.Error=19]="Error",e[e.EOF=20]="EOF"})(yr||(yr={}));function I1(...e){switch(e.length){case 1:return tt(1841,null,e[0]);case 2:return tt(1842,null,e[0],e[1]);case 3:return tt(1843,null,e[0],e[1],e[2]);default:return}}var x0=tt(1844,null),E0=tt(1845,null),ne=class T1{constructor(){this.c="",this.d=0,this.e=0,this.f=[],this.g=[],this.m=/[a-zA-Z0-9_<>\-\./\\:\*\?\+\[\]\^,#@;"%\$\p{L}-]+/uy}static getLexeme(t){switch(t.type){case 0:return"(";case 1:return")";case 2:return"!";case 3:return t.isTripleEq?"===":"==";case 4:return t.isTripleEq?"!==":"!=";case 5:return"<";case 6:return"<=";case 7:return">=";case 8:return">=";case 9:return"=~";case 10:return t.lexeme;case 11:return"true";case 12:return"false";case 13:return"in";case 14:return"not";case 15:return"&&";case 16:return"||";case 17:return t.lexeme;case 18:return t.lexeme;case 19:return t.lexeme;case 20:return"EOF";default:throw _i(`unhandled token type: ${JSON.stringify(t)}; have you forgotten to add a case?`)}}static{this.a=new Set(["i","g","s","m","y","u"].map(t=>t.charCodeAt(0)))}static{this.b=new Map([["not",14],["in",13],["false",12],["true",11]])}get errors(){return this.g}reset(t){return this.c=t,this.d=0,this.e=0,this.f=[],this.g=[],this}scan(){for(;!this.r();)switch(this.d=this.e,this.i()){case 40:this.k(0);break;case 41:this.k(1);break;case 33:if(this.h(61)){const n=this.h(61);this.f.push({type:4,offset:this.d,isTripleEq:n})}else this.k(2);break;case 39:this.o();break;case 47:this.q();break;case 61:if(this.h(61)){const n=this.h(61);this.f.push({type:3,offset:this.d,isTripleEq:n})}else this.h(126)?this.k(9):this.l(I1("==","=~"));break;case 60:this.k(this.h(61)?6:5);break;case 62:this.k(this.h(61)?8:7);break;case 38:this.h(38)?this.k(15):this.l(I1("&&"));break;case 124:this.h(124)?this.k(16):this.l(I1("||"));break;case 32:case 13:case 9:case 10:case 160:break;default:this.n()}return this.d=this.e,this.k(20),Array.from(this.f)}h(t){return this.r()||this.c.charCodeAt(this.e)!==t?!1:(this.e++,!0)}i(){return this.c.charCodeAt(this.e++)}j(){return this.r()?0:this.c.charCodeAt(this.e)}k(t){this.f.push({type:t,offset:this.d})}l(t){const n=this.d,r=this.c.substring(this.d,this.e),i={type:19,offset:this.d,lexeme:r};this.g.push({offset:n,lexeme:r,additionalInfo:t}),this.f.push(i)}n(){this.m.lastIndex=this.d;const t=this.m.exec(this.c);if(t){this.e=this.d+t[0].length;const n=this.c.substring(this.d,this.e),r=T1.b.get(n);r?this.k(r):this.f.push({type:17,lexeme:n,offset:this.d})}}o(){for(;this.j()!==39&&!this.r();)this.i();if(this.r()){this.l(x0);return}this.i(),this.f.push({type:18,lexeme:this.c.substring(this.d+1,this.e-1),offset:this.d+1})}q(){let t=this.e,n=!1,r=!1;for(;;){if(t>=this.c.length){this.e=t,this.l(E0);return}const s=this.c.charCodeAt(t);if(n)n=!1;else if(s===47&&!r){t++;break}else s===91?r=!0:s===92?n=!0:s===93&&(r=!1);t++}for(;t<this.c.length&&T1.a.has(this.c.charCodeAt(t));)t++;this.e=t;const i=this.c.substring(this.d,this.e);this.f.push({type:10,lexeme:i,offset:this.d})}r(){return this.e>=this.c.length}},Et;(function(e){e.serviceIds=new Map,e.DI_TARGET="$di$target",e.DI_DEPENDENCIES="$di$dependencies";function t(n){return n[e.DI_DEPENDENCIES]||[]}e.getServiceDependencies=t})(Et||(Et={}));var ta=S1("instantiationService");function N0(e,t,n){t[Et.DI_TARGET]===t?t[Et.DI_DEPENDENCIES].push({id:e,index:n}):(t[Et.DI_DEPENDENCIES]=[{id:e,index:n}],t[Et.DI_TARGET]=t)}function S1(e){if(Et.serviceIds.has(e))return Et.serviceIds.get(e);const t=function(n,r,i){if(arguments.length!==3)throw new Error("@IServiceName-decorator can only be used to decorate a parameter");N0(t,n,i)};return t.toString=()=>e,Et.serviceIds.set(e,t),t}var H=new Map;H.set("false",!1),H.set("true",!0),H.set("isMac",e1),H.set("isLinux",un),H.set("isWindows",Dt),H.set("isWeb",n1),H.set("isMacNative",e1&&!n1),H.set("isEdge",ys),H.set("isFirefox",Cs),H.set("isChrome",fn),H.set("isSafari",Ls);var I0=Object.prototype.hasOwnProperty,Ar;(function(e){e[e.False=0]="False",e[e.True=1]="True",e[e.Defined=2]="Defined",e[e.Not=3]="Not",e[e.Equals=4]="Equals",e[e.NotEquals=5]="NotEquals",e[e.And=6]="And",e[e.Regex=7]="Regex",e[e.NotRegex=8]="NotRegex",e[e.Or=9]="Or",e[e.In=10]="In",e[e.NotIn=11]="NotIn",e[e.Greater=12]="Greater",e[e.GreaterEquals=13]="GreaterEquals",e[e.Smaller=14]="Smaller",e[e.SmallerEquals=15]="SmallerEquals"})(Ar||(Ar={}));var S0={regexParsingWithErrorRecovery:!0},$0=tt(1821,null),O0=tt(1822,null),_0=tt(1823,null),xr=tt(1824,null),M0=tt(1825,null),D0=tt(1826,null),R0=tt(1827,null),P0=tt(1828,null),k0=class ce{static{this.c=new Error}get lexingErrors(){return this.d.errors}get parsingErrors(){return this.h}constructor(t=S0){this.k=t,this.d=new ne,this.f=[],this.g=0,this.h=[],this.v=/g|y/g}parse(t){if(t===""){this.h.push({message:$0,offset:0,lexeme:"",additionalInfo:O0});return}this.f=this.d.reset(t).scan(),this.g=0,this.h=[];try{const n=this.l();if(!this.E()){const r=this.D(),i=r.type===17?D0:void 0;throw this.h.push({message:M0,offset:r.offset,lexeme:ne.getLexeme(r),additionalInfo:i}),ce.c}return n}catch(n){if(n!==ce.c)throw n;return}}l(){return this.m()}m(){const t=[this.o()];for(;this.y(16);){const n=this.o();t.push(n)}return t.length===1?t[0]:Q.or(...t)}o(){const t=[this.s()];for(;this.y(15);){const n=this.s();t.push(n)}return t.length===1?t[0]:Q.and(...t)}s(){if(this.y(2)){const t=this.D();switch(t.type){case 11:return this.z(),Y.INSTANCE;case 12:return this.z(),st.INSTANCE;case 0:{this.z();const n=this.l();return this.A(1,xr),n?.negate()}case 17:return this.z(),se.create(t.lexeme);default:throw this.B("KEY | true | false | '(' expression ')'",t)}}return this.t()}t(){const t=this.D();switch(t.type){case 11:return this.z(),Q.true();case 12:return this.z(),Q.false();case 0:{this.z();const n=this.l();return this.A(1,xr),n}case 17:{const n=t.lexeme;if(this.z(),this.y(9)){const i=this.D();if(!this.k.regexParsingWithErrorRecovery){if(this.z(),i.type!==10)throw this.B("REGEX",i);const s=i.lexeme,o=s.lastIndexOf("/"),l=o===s.length-1?void 0:this.w(s.substring(o+1));let u;try{u=new RegExp(s.substring(1,o),l)}catch{throw this.B("REGEX",i)}return R1.create(n,u)}switch(i.type){case 10:case 19:{const s=[i.lexeme];this.z();let o=this.D(),l=0;for(let f=0;f<i.lexeme.length;f++)i.lexeme.charCodeAt(f)===40?l++:i.lexeme.charCodeAt(f)===41&&l--;for(;!this.E()&&o.type!==15&&o.type!==16;){switch(o.type){case 0:l++;break;case 1:l--;break;case 10:case 18:for(let f=0;f<o.lexeme.length;f++)o.lexeme.charCodeAt(f)===40?l++:i.lexeme.charCodeAt(f)===41&&l--}if(l<0)break;s.push(ne.getLexeme(o)),this.z(),o=this.D()}const u=s.join(""),a=u.lastIndexOf("/"),c=a===u.length-1?void 0:this.w(u.substring(a+1));let h;try{h=new RegExp(u.substring(1,a),c)}catch{throw this.B("REGEX",i)}return Q.regex(n,h)}case 18:{const s=i.lexeme;this.z();let o=null;if(!Ns(s)){const l=s.indexOf("/"),u=s.lastIndexOf("/");if(l!==u&&l>=0){const a=s.slice(l+1,u),c=s[u+1]==="i"?"i":"";try{o=new RegExp(a,c)}catch{throw this.B("REGEX",i)}}}if(o===null)throw this.B("REGEX",i);return R1.create(n,o)}default:throw this.B("REGEX",this.D())}}if(this.y(14)){this.A(13,_0);const i=this.u();return Q.notIn(n,i)}switch(this.D().type){case 3:{this.z();const i=this.u();if(this.x().type===18)return Q.equals(n,i);switch(i){case"true":return Q.has(n);case"false":return Q.not(n);default:return Q.equals(n,i)}}case 4:{this.z();const i=this.u();if(this.x().type===18)return Q.notEquals(n,i);switch(i){case"true":return Q.not(n);case"false":return Q.has(n);default:return Q.notEquals(n,i)}}case 5:return this.z(),M1.create(n,this.u());case 6:return this.z(),D1.create(n,this.u());case 7:return this.z(),De.create(n,this.u());case 8:return this.z(),_1.create(n,this.u());case 13:return this.z(),Q.in(n,this.u());default:return Q.has(n)}}case 20:throw this.h.push({message:R0,offset:t.offset,lexeme:"",additionalInfo:P0}),ce.c;default:throw this.B(`true | false | KEY 
	| KEY '=~' REGEX 
	| KEY ('==' | '!=' | '<' | '<=' | '>' | '>=' | 'in' | 'not' 'in') value`,this.D())}}u(){const t=this.D();switch(t.type){case 17:case 18:return this.z(),t.lexeme;case 11:return this.z(),"true";case 12:return this.z(),"false";case 13:return this.z(),"in";default:return""}}w(t){return t.replaceAll(this.v,"")}x(){return this.f[this.g-1]}y(t){return this.C(t)?(this.z(),!0):!1}z(){return this.E()||this.g++,this.x()}A(t,n){if(this.C(t))return this.z();throw this.B(n,this.D())}B(t,n,r){const i=tt(1829,null,t,ne.getLexeme(n)),s=n.offset,o=ne.getLexeme(n);return this.h.push({message:i,offset:s,lexeme:o,additionalInfo:r}),ce.c}C(t){return this.D().type===t}D(){return this.f[this.g]}E(){return this.D().type===20}},Q=class{static false(){return Y.INSTANCE}static true(){return st.INSTANCE}static has(e){return ie.create(e)}static equals(e,t){return $1.create(e,t)}static notEquals(e,t){return O1.create(e,t)}static regex(e,t){return R1.create(e,t)}static in(e,t){return Er.create(e,t)}static notIn(e,t){return Nr.create(e,t)}static not(e){return se.create(e)}static and(...e){return Sr.create(e,null,!0)}static or(...e){return P1.create(e,null,!0)}static greater(e,t){return De.create(e,t)}static greaterEquals(e,t){return _1.create(e,t)}static smaller(e,t){return M1.create(e,t)}static smallerEquals(e,t){return D1.create(e,t)}static{this.c=new k0({regexParsingWithErrorRecovery:!1})}static deserialize(e){return e==null?void 0:this.c.parse(e)}};function re(e,t){return e.cmp(t)}var Y=class hi{static{this.INSTANCE=new hi}constructor(){this.type=0}cmp(t){return this.type-t.type}equals(t){return t.type===this.type}substituteConstants(){return this}evaluate(t){return!1}serialize(){return"false"}keys(){return[]}map(t){return this}negate(){return st.INSTANCE}},st=class ci{static{this.INSTANCE=new ci}constructor(){this.type=1}cmp(t){return this.type-t.type}equals(t){return t.type===this.type}substituteConstants(){return this}evaluate(t){return!0}serialize(){return"true"}keys(){return[]}map(t){return this}negate(){return Y.INSTANCE}},ie=class fi{static create(t,n=null){const r=H.get(t);return typeof r=="boolean"?r?st.INSTANCE:Y.INSTANCE:new fi(t,n)}constructor(t,n){this.key=t,this.c=n,this.type=2}cmp(t){return t.type!==this.type?this.type-t.type:Or(this.key,t.key)}equals(t){return t.type===this.type?this.key===t.key:!1}substituteConstants(){const t=H.get(this.key);return typeof t=="boolean"?t?st.INSTANCE:Y.INSTANCE:this}evaluate(t){return!!t.getValue(this.key)}serialize(){return this.key}keys(){return[this.key]}map(t){return t.mapDefined(this.key)}negate(){return this.c||(this.c=se.create(this.key,this)),this.c}},$1=class di{static create(t,n,r=null){if(typeof n=="boolean")return n?ie.create(t,r):se.create(t,r);const i=H.get(t);return typeof i=="boolean"?n===(i?"true":"false")?st.INSTANCE:Y.INSTANCE:new di(t,n,r)}constructor(t,n,r){this.c=t,this.d=n,this.f=r,this.type=4}cmp(t){return t.type!==this.type?this.type-t.type:Ft(this.c,this.d,t.c,t.d)}equals(t){return t.type===this.type?this.c===t.c&&this.d===t.d:!1}substituteConstants(){const t=H.get(this.c);if(typeof t=="boolean"){const n=t?"true":"false";return this.d===n?st.INSTANCE:Y.INSTANCE}return this}evaluate(t){return t.getValue(this.c)==this.d}serialize(){return`${this.c} == '${this.d}'`}keys(){return[this.c]}map(t){return t.mapEquals(this.c,this.d)}negate(){return this.f||(this.f=O1.create(this.c,this.d,this)),this.f}},Er=class gi{static create(t,n){return new gi(t,n)}constructor(t,n){this.d=t,this.f=n,this.type=10,this.c=null}cmp(t){return t.type!==this.type?this.type-t.type:Ft(this.d,this.f,t.d,t.f)}equals(t){return t.type===this.type?this.d===t.d&&this.f===t.f:!1}substituteConstants(){return this}evaluate(t){const n=t.getValue(this.f),r=t.getValue(this.d);return Array.isArray(n)?n.includes(r):typeof r=="string"&&typeof n=="object"&&n!==null?I0.call(n,r):!1}serialize(){return`${this.d} in '${this.f}'`}keys(){return[this.d,this.f]}map(t){return t.mapIn(this.d,this.f)}negate(){return this.c||(this.c=Nr.create(this.d,this.f)),this.c}},Nr=class mi{static create(t,n){return new mi(t,n)}constructor(t,n){this.d=t,this.f=n,this.type=11,this.c=Er.create(t,n)}cmp(t){return t.type!==this.type?this.type-t.type:this.c.cmp(t.c)}equals(t){return t.type===this.type?this.c.equals(t.c):!1}substituteConstants(){return this}evaluate(t){return!this.c.evaluate(t)}serialize(){return`${this.d} not in '${this.f}'`}keys(){return this.c.keys()}map(t){return t.mapNotIn(this.d,this.f)}negate(){return this.c}},O1=class pi{static create(t,n,r=null){if(typeof n=="boolean")return n?se.create(t,r):ie.create(t,r);const i=H.get(t);return typeof i=="boolean"?n===(i?"true":"false")?Y.INSTANCE:st.INSTANCE:new pi(t,n,r)}constructor(t,n,r){this.c=t,this.d=n,this.f=r,this.type=5}cmp(t){return t.type!==this.type?this.type-t.type:Ft(this.c,this.d,t.c,t.d)}equals(t){return t.type===this.type?this.c===t.c&&this.d===t.d:!1}substituteConstants(){const t=H.get(this.c);if(typeof t=="boolean"){const n=t?"true":"false";return this.d===n?Y.INSTANCE:st.INSTANCE}return this}evaluate(t){return t.getValue(this.c)!=this.d}serialize(){return`${this.c} != '${this.d}'`}keys(){return[this.c]}map(t){return t.mapNotEquals(this.c,this.d)}negate(){return this.f||(this.f=$1.create(this.c,this.d,this)),this.f}},se=class bi{static create(t,n=null){const r=H.get(t);return typeof r=="boolean"?r?Y.INSTANCE:st.INSTANCE:new bi(t,n)}constructor(t,n){this.c=t,this.d=n,this.type=3}cmp(t){return t.type!==this.type?this.type-t.type:Or(this.c,t.c)}equals(t){return t.type===this.type?this.c===t.c:!1}substituteConstants(){const t=H.get(this.c);return typeof t=="boolean"?t?Y.INSTANCE:st.INSTANCE:this}evaluate(t){return!t.getValue(this.c)}serialize(){return`!${this.c}`}keys(){return[this.c]}map(t){return t.mapNot(this.c)}negate(){return this.d||(this.d=ie.create(this.c,this)),this.d}};function Me(e,t){if(typeof e=="string"){const n=parseFloat(e);isNaN(n)||(e=n)}return typeof e=="string"||typeof e=="number"?t(e):Y.INSTANCE}var De=class vi{static create(t,n,r=null){return Me(n,i=>new vi(t,i,r))}constructor(t,n,r){this.c=t,this.d=n,this.f=r,this.type=12}cmp(t){return t.type!==this.type?this.type-t.type:Ft(this.c,this.d,t.c,t.d)}equals(t){return t.type===this.type?this.c===t.c&&this.d===t.d:!1}substituteConstants(){return this}evaluate(t){return typeof this.d=="string"?!1:parseFloat(t.getValue(this.c))>this.d}serialize(){return`${this.c} > ${this.d}`}keys(){return[this.c]}map(t){return t.mapGreater(this.c,this.d)}negate(){return this.f||(this.f=D1.create(this.c,this.d,this)),this.f}},_1=class wi{static create(t,n,r=null){return Me(n,i=>new wi(t,i,r))}constructor(t,n,r){this.c=t,this.d=n,this.f=r,this.type=13}cmp(t){return t.type!==this.type?this.type-t.type:Ft(this.c,this.d,t.c,t.d)}equals(t){return t.type===this.type?this.c===t.c&&this.d===t.d:!1}substituteConstants(){return this}evaluate(t){return typeof this.d=="string"?!1:parseFloat(t.getValue(this.c))>=this.d}serialize(){return`${this.c} >= ${this.d}`}keys(){return[this.c]}map(t){return t.mapGreaterEquals(this.c,this.d)}negate(){return this.f||(this.f=M1.create(this.c,this.d,this)),this.f}},M1=class Ci{static create(t,n,r=null){return Me(n,i=>new Ci(t,i,r))}constructor(t,n,r){this.c=t,this.d=n,this.f=r,this.type=14}cmp(t){return t.type!==this.type?this.type-t.type:Ft(this.c,this.d,t.c,t.d)}equals(t){return t.type===this.type?this.c===t.c&&this.d===t.d:!1}substituteConstants(){return this}evaluate(t){return typeof this.d=="string"?!1:parseFloat(t.getValue(this.c))<this.d}serialize(){return`${this.c} < ${this.d}`}keys(){return[this.c]}map(t){return t.mapSmaller(this.c,this.d)}negate(){return this.f||(this.f=_1.create(this.c,this.d,this)),this.f}},D1=class Li{static create(t,n,r=null){return Me(n,i=>new Li(t,i,r))}constructor(t,n,r){this.c=t,this.d=n,this.f=r,this.type=15}cmp(t){return t.type!==this.type?this.type-t.type:Ft(this.c,this.d,t.c,t.d)}equals(t){return t.type===this.type?this.c===t.c&&this.d===t.d:!1}substituteConstants(){return this}evaluate(t){return typeof this.d=="string"?!1:parseFloat(t.getValue(this.c))<=this.d}serialize(){return`${this.c} <= ${this.d}`}keys(){return[this.c]}map(t){return t.mapSmallerEquals(this.c,this.d)}negate(){return this.f||(this.f=De.create(this.c,this.d,this)),this.f}},R1=class yi{static create(t,n){return new yi(t,n)}constructor(t,n){this.d=t,this.f=n,this.type=7,this.c=null}cmp(t){if(t.type!==this.type)return this.type-t.type;if(this.d<t.d)return-1;if(this.d>t.d)return 1;const n=this.f?this.f.source:"",r=t.f?t.f.source:"";return n<r?-1:n>r?1:0}equals(t){if(t.type===this.type){const n=this.f?this.f.source:"",r=t.f?t.f.source:"";return this.d===t.d&&n===r}return!1}substituteConstants(){return this}evaluate(t){const n=t.getValue(this.d);return this.f?this.f.test(n):!1}serialize(){const t=this.f?`/${this.f.source}/${this.f.flags}`:"/invalid/";return`${this.d} =~ ${t}`}keys(){return[this.d]}map(t){return t.mapRegex(this.d,this.f)}negate(){return this.c||(this.c=F0.create(this)),this.c}},F0=class B1{static create(t){return new B1(t)}constructor(t){this.c=t,this.type=8}cmp(t){return t.type!==this.type?this.type-t.type:this.c.cmp(t.c)}equals(t){return t.type===this.type?this.c.equals(t.c):!1}substituteConstants(){return this}evaluate(t){return!this.c.evaluate(t)}serialize(){return`!(${this.c.serialize()})`}keys(){return this.c.keys()}map(t){return new B1(this.c.map(t))}negate(){return this.c}};function Ir(e){let t=null;for(let n=0,r=e.length;n<r;n++){const i=e[n].substituteConstants();if(e[n]!==i&&t===null){t=[];for(let s=0;s<n;s++)t[s]=e[s]}t!==null&&(t[n]=i)}return t===null?e:t}var Sr=class Kt{static create(t,n,r){return Kt.d(t,n,r)}constructor(t,n){this.expr=t,this.c=n,this.type=6}cmp(t){if(t.type!==this.type)return this.type-t.type;if(this.expr.length<t.expr.length)return-1;if(this.expr.length>t.expr.length)return 1;for(let n=0,r=this.expr.length;n<r;n++){const i=re(this.expr[n],t.expr[n]);if(i!==0)return i}return 0}equals(t){if(t.type===this.type){if(this.expr.length!==t.expr.length)return!1;for(let n=0,r=this.expr.length;n<r;n++)if(!this.expr[n].equals(t.expr[n]))return!1;return!0}return!1}substituteConstants(){const t=Ir(this.expr);return t===this.expr?this:Kt.create(t,this.c,!1)}evaluate(t){for(let n=0,r=this.expr.length;n<r;n++)if(!this.expr[n].evaluate(t))return!1;return!0}static d(t,n,r){const i=[];let s=!1;for(const o of t)if(o){if(o.type===1){s=!0;continue}if(o.type===0)return Y.INSTANCE;if(o.type===6){i.push(...o.expr);continue}i.push(o)}if(i.length===0&&s)return st.INSTANCE;if(i.length!==0){if(i.length===1)return i[0];i.sort(re);for(let o=1;o<i.length;o++)i[o-1].equals(i[o])&&(i.splice(o,1),o--);if(i.length===1)return i[0];for(;i.length>1;){const o=i[i.length-1];if(o.type!==9)break;i.pop();const l=i.pop(),u=i.length===0,a=P1.create(o.expr.map(c=>Kt.create([c,l],null,r)),null,u);a&&(i.push(a),i.sort(re))}if(i.length===1)return i[0];if(r){for(let o=0;o<i.length;o++)for(let l=o+1;l<i.length;l++)if(i[o].negate().equals(i[l]))return Y.INSTANCE;if(i.length===1)return i[0]}return new Kt(i,n)}}serialize(){return this.expr.map(t=>t.serialize()).join(" && ")}keys(){const t=[];for(const n of this.expr)t.push(...n.keys());return t}map(t){return new Kt(this.expr.map(n=>n.map(t)),null)}negate(){if(!this.c){const t=[];for(const n of this.expr)t.push(n.negate());this.c=P1.create(t,this,!0)}return this.c}},P1=class Ut{static create(t,n,r){return Ut.d(t,n,r)}constructor(t,n){this.expr=t,this.c=n,this.type=9}cmp(t){if(t.type!==this.type)return this.type-t.type;if(this.expr.length<t.expr.length)return-1;if(this.expr.length>t.expr.length)return 1;for(let n=0,r=this.expr.length;n<r;n++){const i=re(this.expr[n],t.expr[n]);if(i!==0)return i}return 0}equals(t){if(t.type===this.type){if(this.expr.length!==t.expr.length)return!1;for(let n=0,r=this.expr.length;n<r;n++)if(!this.expr[n].equals(t.expr[n]))return!1;return!0}return!1}substituteConstants(){const t=Ir(this.expr);return t===this.expr?this:Ut.create(t,this.c,!1)}evaluate(t){for(let n=0,r=this.expr.length;n<r;n++)if(this.expr[n].evaluate(t))return!0;return!1}static d(t,n,r){let i=[],s=!1;if(t){for(let o=0,l=t.length;o<l;o++){const u=t[o];if(u){if(u.type===0){s=!0;continue}if(u.type===1)return st.INSTANCE;if(u.type===9){i=i.concat(u.expr);continue}i.push(u)}}if(i.length===0&&s)return Y.INSTANCE;i.sort(re)}if(i.length!==0){if(i.length===1)return i[0];for(let o=1;o<i.length;o++)i[o-1].equals(i[o])&&(i.splice(o,1),o--);if(i.length===1)return i[0];if(r){for(let o=0;o<i.length;o++)for(let l=o+1;l<i.length;l++)if(i[o].negate().equals(i[l]))return st.INSTANCE;if(i.length===1)return i[0]}return new Ut(i,n)}}serialize(){return this.expr.map(t=>t.serialize()).join(" || ")}keys(){const t=[];for(const n of this.expr)t.push(...n.keys());return t}map(t){return new Ut(this.expr.map(n=>n.map(t)),null)}negate(){if(!this.c){const t=[];for(const n of this.expr)t.push(n.negate());for(;t.length>1;){const n=t.shift(),r=t.shift(),i=[];for(const s of _r(n))for(const o of _r(r))i.push(Sr.create([s,o],null,!1));t.unshift(Ut.create(i,null,!1))}this.c=Ut.create(t,this,!0)}return this.c}},$r=class je extends ie{static{this.d=[]}static all(){return je.d.values()}constructor(t,n,r){super(t,null),this.f=n,typeof r=="object"?je.d.push({...r,key:t}):r!==!0&&je.d.push({key:t,description:r,type:n!=null?typeof n:void 0})}bindTo(t){return t.createKey(this.key,this.f)}getValue(t){return t.getContextKeyValue(this.key)}toNegated(){return this.negate()}isEqualTo(t){return $1.create(this.key,t)}notEqualsTo(t){return O1.create(this.key,t)}greater(t){return De.create(this.key,t)}},ea=S1("contextKeyService");function Or(e,t){return e<t?-1:e>t?1:0}function Ft(e,t,n,r){return e<n?-1:e>n?1:t<r?-1:t>r?1:0}function _r(e){return e.type===9?e.expr:[e]}var Mr=class{constructor(e,t=[],n=!1){this.ctor=e,this.staticArguments=t,this.supportsDelayedInstantiation=n}},q0=[],Dr;(function(e){e[e.Eager=0]="Eager",e[e.Delayed=1]="Delayed"})(Dr||(Dr={}));function U0(e,t,n){t instanceof Mr||(t=new Mr(t,[],!!n)),q0.push([e,t])}var j0=S1("notebookDocumentService"),k1=["W","X","Y","Z","a","b","c","d","e","f"],z0=new RegExp(`^[${k1.join("")}]+`),Rr=7;function F1(e){if(e.scheme!==P.vscodeNotebookCell)return;const t=e.fragment.indexOf("s");if(t<0)return;const n=parseInt(e.fragment.substring(0,t).replace(z0,""),Rr),r=In(e.fragment.substring(t+1)).toString();if(!isNaN(n))return{handle:n,notebook:e.with({scheme:r,fragment:null})}}function T0(e,t){const n=t.toString(Rr),i=`${n.length<k1.length?k1[n.length-1]:"z"}${n}s${Sn(ye.fromString(e.scheme),!0,!0)}`;return e.with({scheme:P.vscodeNotebookCell,fragment:i})}function B0(e){if(e.scheme!==P.vscodeNotebookMetadata)return;const t=In(e.fragment).toString();return e.with({scheme:t,fragment:null})}function V0(e){const t=`${Sn(ye.fromString(e.scheme),!0,!0)}`;return e.with({scheme:P.vscodeNotebookMetadata,fragment:t})}function Pr(e){if(e.scheme!==P.vscodeNotebookCellOutput)return;const t=new URLSearchParams(e.query),n=t.get("openIn");if(!n)return;const r=t.get("outputId")??void 0,i=F1(e.with({scheme:P.vscodeNotebookCell,query:null})),s=t.get("outputIndex")?parseInt(t.get("outputIndex")||"",10):void 0,o=i?i.notebook:e.with({scheme:t.get("notebookScheme")||P.file,fragment:null,query:null}),l=t.get("cellIndex")?parseInt(t.get("cellIndex")||"",10):void 0;return{notebook:o,openIn:n,outputId:r,outputIndex:s,cellHandle:i?.handle,cellFragment:e.fragment,cellIndex:l}}var W0=class{constructor(){this.a=new Ge}getNotebook(e){if(e.scheme===P.vscodeNotebookCell){const t=F1(e);if(t){const n=this.a.get(t.notebook);if(n)return n}}if(e.scheme===P.vscodeNotebookCellOutput){const t=Pr(e);if(t){const n=this.a.get(t.notebook);if(n)return n}}return this.a.get(e)}addNotebookDocument(e){this.a.set(e.uri,e)}removeNotebookDocument(e){this.a.delete(e.uri)}};U0(j0,W0,1);var q1;(function(e){e[e.Markup=1]="Markup",e[e.Code=2]="Code"})(q1||(q1={}));var na=["application/json","application/javascript","text/html","image/svg+xml",Ht.latex,Ht.markdown,"image/png","image/jpeg",Ht.text],ra=[Ht.latex,Ht.markdown,"application/json","text/html","image/svg+xml","image/png","image/jpeg",Ht.text],kr;(function(e){e[e.Running=1]="Running",e[e.Idle=2]="Idle"})(kr||(kr={}));var Fr;(function(e){e[e.Unconfirmed=1]="Unconfirmed",e[e.Pending=2]="Pending",e[e.Executing=3]="Executing"})(Fr||(Fr={}));var qr;(function(e){e[e.Unconfirmed=1]="Unconfirmed",e[e.Pending=2]="Pending",e[e.Executing=3]="Executing"})(qr||(qr={}));var Ur;(function(e){e[e.WithHardKernelDependency=0]="WithHardKernelDependency",e[e.WithOptionalKernelDependency=1]="WithOptionalKernelDependency",e[e.Pure=2]="Pure",e[e.Never=3]="Never"})(Ur||(Ur={}));var jr;(function(e){e.Always="always",e.Never="never",e.Optional="optional"})(jr||(jr={}));var Nt;(function(e){e[e.ModelChange=1]="ModelChange",e[e.Move=2]="Move",e[e.ChangeCellLanguage=5]="ChangeCellLanguage",e[e.Initialize=6]="Initialize",e[e.ChangeCellMetadata=7]="ChangeCellMetadata",e[e.Output=8]="Output",e[e.OutputItem=9]="OutputItem",e[e.ChangeCellContent=10]="ChangeCellContent",e[e.ChangeDocumentMetadata=11]="ChangeDocumentMetadata",e[e.ChangeCellInternalMetadata=12]="ChangeCellInternalMetadata",e[e.ChangeCellMime=13]="ChangeCellMime",e[e.Unknown=100]="Unknown"})(Nt||(Nt={}));var zr;(function(e){e[e.Handle=0]="Handle",e[e.Index=1]="Index"})(zr||(zr={}));var Tr;(function(e){e[e.Replace=1]="Replace",e[e.Output=2]="Output",e[e.Metadata=3]="Metadata",e[e.CellLanguage=4]="CellLanguage",e[e.DocumentMetadata=5]="DocumentMetadata",e[e.Move=6]="Move",e[e.OutputItems=7]="OutputItems",e[e.PartialMetadata=8]="PartialMetadata",e[e.PartialInternalMetadata=9]="PartialInternalMetadata"})(Tr||(Tr={}));var Br;(function(e){e.scheme=P.vscodeNotebookMetadata;function t(r){return V0(r)}e.generate=t;function n(r){return B0(r)}e.parse=n})(Br||(Br={}));var Vr;(function(e){e.scheme=P.vscodeNotebookCell;function t(a,c){return T0(a,c)}e.generate=t;function n(a){return F1(a)}e.parse=n;function r(a,c){return a.with({scheme:P.vscodeNotebookCellOutput,query:new URLSearchParams({openIn:"editor",outputId:c??"",notebookScheme:a.scheme!==P.file?a.scheme:""}).toString()})}e.generateCellOutputUriWithId=r;function i(a,c,h){return a.with({scheme:P.vscodeNotebookCellOutput,fragment:c.fragment,query:new URLSearchParams({openIn:"notebook",outputIndex:String(h)}).toString()})}e.generateCellOutputUriWithIndex=i;function s(a,c,h,f,g){return a.with({scheme:P.vscodeNotebookCellOutput,query:new URLSearchParams({openIn:"notebookOutputEditor",notebook:a.toString(),cellIndex:String(h),outputId:f,outputIndex:String(g)}).toString()})}e.generateOutputEditorUri=s;function o(a){return Pr(a)}e.parseCellOutputUri=o;function l(a,c,h){return e.generate(a,c).with({scheme:h})}e.generateCellPropertyUri=l;function u(a,c){if(a.scheme===c)return e.parse(a.with({scheme:e.scheme}))}e.parseCellPropertyUri=u})(Vr||(Vr={}));var ia=new $r("notebookEditorCursorAtBoundary","none"),sa=new $r("notebookEditorCursorAtLineBoundary","none"),Wr;(function(e){e.default="default",e.option="option"})(Wr||(Wr={}));var Hr;(function(e){e.Cells="cells",e.Text="text",e.None="none"})(Hr||(Hr={}));var Gr;(function(e){e[e.Left=1]="Left",e[e.Right=2]="Right"})(Gr||(Gr={}));var oa=class ze{static{this.d="notebook/"}static create(t,n){return`${ze.d}${t}/${n??t}`}static parse(t){if(t.startsWith(ze.d)){const n=t.substring(ze.d.length).split("/");if(n.length===2)return{notebookType:n[0],viewType:n[1]}}}},la=new TextDecoder,H0="\x1B[A",aa=H0.split("").map(e=>e.charCodeAt(0)),ua=8,ha=13,G0="`~!@#$%^&*()-=+[{]}\\|;:'\",.<>/?";function K0(e=""){let t="(-?\\d*\\.\\d\\w*)|([^";for(const n of G0)e.indexOf(n)>=0||(t+="\\"+n);return t+="\\s]+)",new RegExp(t,"g")}var J0=K0();function Kr(e){let t=J0;if(e&&e instanceof RegExp)if(e.global)t=e;else{let n="g";e.ignoreCase&&(n+="i"),e.multiline&&(n+="m"),e.unicode&&(n+="u"),t=new RegExp(e.source,n)}return t.lastIndex=0,t}var Jr=new Gi;Jr.unshift({maxLen:1e3,windowSize:15,timeBudget:150});function Xr(e,t,n,r,i){if(t=Kr(t),i||(i=me.first(Jr)),n.length>i.maxLen){let a=e-i.maxLen/2;return a<0?a=0:r+=a,n=n.substring(a,e+i.maxLen/2),Xr(e,t,n,r,i)}const s=Date.now(),o=e-1-r;let l=-1,u=null;for(let a=1;!(Date.now()-s>=i.timeBudget);a++){const c=o-i.windowSize*a;t.lastIndex=Math.max(0,c);const h=X0(t,n,o,l);if(!h&&u||(u=h,c<=0))break;l=c}if(u){const a={word:u[0],startColumn:r+1+u.index,endColumn:r+1+u.index+u[0].length};return t.lastIndex=0,a}return null}function X0(e,t,n,r){let i;for(;i=e.exec(t);){const s=i.index||0;if(s<=n&&e.lastIndex>=n)return i;if(r>0&&s>r)return null}return null}var Q0=class{constructor(e){this.a=e,this.b=new Uint32Array(e.length),this.c=new Int32Array(1),this.c[0]=-1}getCount(){return this.a.length}insertValues(e,t){e=Wt(e);const n=this.a,r=this.b,i=t.length;return i===0?!1:(this.a=new Uint32Array(n.length+i),this.a.set(n.subarray(0,e),0),this.a.set(n.subarray(e),e+i),this.a.set(t,e),e-1<this.c[0]&&(this.c[0]=e-1),this.b=new Uint32Array(this.a.length),this.c[0]>=0&&this.b.set(r.subarray(0,this.c[0]+1)),!0)}setValue(e,t){return e=Wt(e),t=Wt(t),this.a[e]===t?!1:(this.a[e]=t,e-1<this.c[0]&&(this.c[0]=e-1),!0)}removeValues(e,t){e=Wt(e),t=Wt(t);const n=this.a,r=this.b;if(e>=n.length)return!1;const i=n.length-e;return t>=i&&(t=i),t===0?!1:(this.a=new Uint32Array(n.length-t),this.a.set(n.subarray(0,e),0),this.a.set(n.subarray(e+t),e),this.b=new Uint32Array(this.a.length),e-1<this.c[0]&&(this.c[0]=e-1),this.c[0]>=0&&this.b.set(r.subarray(0,this.c[0]+1)),!0)}getTotalSum(){return this.a.length===0?0:this.d(this.a.length-1)}getPrefixSum(e){return e<0?0:(e=Wt(e),this.d(e))}d(e){if(e<=this.c[0])return this.b[e];let t=this.c[0]+1;t===0&&(this.b[0]=this.a[0],t++),e>=this.a.length&&(e=this.a.length-1);for(let n=t;n<=e;n++)this.b[n]=this.b[n-1]+this.a[n];return this.c[0]=Math.max(this.c[0],e),this.b[e]}getIndexOf(e){e=Math.floor(e),this.getTotalSum();let t=0,n=this.a.length-1,r=0,i=0,s=0;for(;t<=n;)if(r=t+(n-t)/2|0,i=this.b[r],s=i-this.a[r],e<s)n=r-1;else if(e>=i)t=r+1;else break;return new Z0(r,e-s)}},Z0=class{constructor(e,t){this.index=e,this.remainder=t,this._prefixSumIndexOfResultBrand=void 0,this.index=e,this.remainder=t}},Y0=class{constructor(e,t,n,r){this.a=e,this.b=t,this.c=n,this.d=r,this.f=null,this.g=null}dispose(){this.b.length=0}get version(){return this.d}getText(){return this.g===null&&(this.g=this.b.join(this.c)),this.g}onEvents(e){e.eol&&e.eol!==this.c&&(this.c=e.eol,this.f=null);const t=e.changes;for(const n of t)this.k(n.range),this.l(new Ct(n.range.startLineNumber,n.range.startColumn),n.text);this.d=e.versionId,this.g=null}h(){if(!this.f){const e=this.c.length,t=this.b.length,n=new Uint32Array(t);for(let r=0;r<t;r++)n[r]=this.b[r].length+e;this.f=new Q0(n)}}j(e,t){this.b[e]=t,this.f&&this.f.setValue(e,this.b[e].length+this.c.length)}k(e){if(e.startLineNumber===e.endLineNumber){if(e.startColumn===e.endColumn)return;this.j(e.startLineNumber-1,this.b[e.startLineNumber-1].substring(0,e.startColumn-1)+this.b[e.startLineNumber-1].substring(e.endColumn-1));return}this.j(e.startLineNumber-1,this.b[e.startLineNumber-1].substring(0,e.startColumn-1)+this.b[e.endLineNumber-1].substring(e.endColumn-1)),this.b.splice(e.startLineNumber,e.endLineNumber-e.startLineNumber),this.f&&this.f.removeValues(e.startLineNumber,e.endLineNumber-e.startLineNumber)}l(e,t){if(t.length===0)return;const n=$s(t);if(n.length===1){this.j(e.lineNumber-1,this.b[e.lineNumber-1].substring(0,e.column-1)+n[0]+this.b[e.lineNumber-1].substring(e.column-1));return}n[n.length-1]+=this.b[e.lineNumber-1].substring(e.column-1),this.j(e.lineNumber-1,this.b[e.lineNumber-1].substring(0,e.column-1)+n[0]);const r=new Uint32Array(n.length-1);for(let i=1;i<n.length;i++)this.b.splice(e.lineNumber+i-1,0,n[i]),r[i-1]=n[i].length+this.c.length;this.f&&this.f.insertValues(e.lineNumber,r)}},ca=60*1e3,tl=class extends Y0{get uri(){return this.a}get eol(){return this.c}getValue(){return this.getText()}findMatches(e){const t=[];for(let n=0;n<this.b.length;n++){const r=this.b[n],i=this.offsetAt(new Ct(n+1,1)),s=r.matchAll(e);for(const o of s)(o.index||o.index===0)&&(o.index=o.index+i),t.push(o)}return t}getLinesContent(){return this.b.slice(0)}getLineCount(){return this.b.length}getLineContent(e){return this.b[e-1]}getWordAtPosition(e,t){const n=Xr(e.column,Kr(t),this.b[e.lineNumber-1],0);return n?new J(e.lineNumber,n.startColumn,e.lineNumber,n.endColumn):null}getWordUntilPosition(e,t){const n=this.getWordAtPosition(e,t);return n?{word:this.b[e.lineNumber-1].substring(n.startColumn-1,e.column-1),startColumn:n.startColumn,endColumn:e.column}:{word:"",startColumn:e.column,endColumn:e.column}}words(e){const t=this.b,n=this.m.bind(this);let r=0,i="",s=0,o=[];return{*[Symbol.iterator](){for(;;)if(s<o.length){const l=i.substring(o[s].start,o[s].end);s+=1,yield l}else if(r<t.length)i=t[r],o=n(i,e),s=0,r+=1;else break}}}getLineWords(e,t){const n=this.b[e-1],r=this.m(n,t),i=[];for(const s of r)i.push({word:n.substring(s.start,s.end),startColumn:s.start+1,endColumn:s.end+1});return i}m(e,t){const n=[];let r;for(t.lastIndex=0;(r=t.exec(e))&&r[0].length!==0;)n.push({start:r.index,end:r.index+r[0].length});return n}getValueInRange(e){if(e=this.n(e),e.startLineNumber===e.endLineNumber)return this.b[e.startLineNumber-1].substring(e.startColumn-1,e.endColumn-1);const t=this.c,n=e.startLineNumber-1,r=e.endLineNumber-1,i=[];i.push(this.b[n].substring(e.startColumn-1));for(let s=n+1;s<r;s++)i.push(this.b[s]);return i.push(this.b[r].substring(0,e.endColumn-1)),i.join(t)}offsetAt(e){return e=this.o(e),this.h(),this.f.getPrefixSum(e.lineNumber-2)+(e.column-1)}positionAt(e){e=Math.floor(e),e=Math.max(0,e),this.h();const t=this.f.getIndexOf(e),n=this.b[t.index].length;return{lineNumber:1+t.index,column:1+Math.min(t.remainder,n)}}n(e){const t=this.o({lineNumber:e.startLineNumber,column:e.startColumn}),n=this.o({lineNumber:e.endLineNumber,column:e.endColumn});return t.lineNumber!==e.startLineNumber||t.column!==e.startColumn||n.lineNumber!==e.endLineNumber||n.column!==e.endColumn?{startLineNumber:t.lineNumber,startColumn:t.column,endLineNumber:n.lineNumber,endColumn:n.column}:e}o(e){if(!Ct.isIPosition(e))throw new Error("bad position");let{lineNumber:t,column:n}=e,r=!1;if(t<1)t=1,n=1,r=!0;else if(t>this.b.length)t=this.b.length,n=this.b[t-1].length+1,r=!0;else{const i=this.b[t-1].length+1;n<1?(n=1,r=!0):n>i&&(n=i,r=!0)}return r?{lineNumber:t,column:n}:e}};function el(e,t){const n={modifiedToOriginal:new Map,originalToModified:new Map},r=[],i=new Map,s=new Set,o=new Map,l=(a,c)=>{if(i.has(a))return!1;const h=o.get(a)?.dist??Number.MAX_SAFE_INTEGER;return c.editCount<h},u=(a,c)=>{i.set(c,a),s.add(a)};for(let a=0;a<e.length;a++){const c=e[a],{index:h,editCount:f,percentage:g}=Qr({cell:c,index:a},t,!0,n,l);h>=0&&f===0?(u(a,h),r.push({modified:a,original:h,dist:f,percentage:g,possibleOriginal:h})):(o.set(h,{dist:f,modifiedIndex:a}),r.push({modified:a,original:-1,dist:f,percentage:g,possibleOriginal:h}))}return r.forEach((a,c)=>{if(a.original>=0)return;const h=c>0?r.slice(0,c).reverse().find(C=>C.original>=0):void 0,f=h?.original??-1,g=h?.modified??-1,m=r.slice(c+1).find(C=>C.original>=0),p=new Set,d=r.findIndex((C,v)=>v>c&&C.original>=0),w=d>=0?r[d].original:-1;t.forEach((C,v)=>{if(i.has(v)){p.add(v);return}m&&v>=m.original&&p.add(v),w>=0&&v>w&&p.add(v)});const b=e[c];if(a.original===-1&&a.possibleOriginal>=0&&!p.has(a.possibleOriginal)&&l(a.possibleOriginal,{editCount:a.dist})){u(c,a.possibleOriginal),a.original=a.possibleOriginal;return}if(f>0&&g>0&&f===g&&(d>=0?d:e.length-1)===(w>=0?w:t.length-1)&&!p.has(c)&&c<t.length){const C=(d>=0?d:e.length)-g,v=(w>=0?w:t.length)-f;if(C===v&&b.cellKind===t[c].cellKind){u(c,c),a.original=c;return}}const{index:L,percentage:D}=Qr({cell:b,index:c},t,!1,n,(C,v)=>{if(p.has(C))return!1;if(d>0||f>0){const I=n.originalToModified.get(C);if(I&&f<C&&Array.from(I).find(([R,O])=>R===c||R>=d||s.has(c)?!1:O.editCount<v.editCount))return!1}return!p.has(C)});if(L>=0&&c>0&&r[c-1].original===L-1){u(c,L),r[c].original=L;return}const $=c>0&&t.length>r[c-1].original?r[c-1].original+1:-1,z=c>0&&$>=0&&$<t.length?t[$].getValue():void 0;if(L>=0&&c>0&&typeof z=="string"&&!i.has($)&&(b.getValue().includes(z)||z.includes(b.getValue()))){u(c,$),r[c].original=$;return}if(D<90||c===0&&r.length===1){u(c,L),r[c].original=L;return}}),r}function Qr({cell:e,index:t},n,r,i,s){let o=1/0,l=-1;const u=e.internalMetadata?.internalId;if(u){const c=n.findIndex(h=>h.internalMetadata?.internalId===u);if(c>=0)return{index:c,editCount:0,percentage:Number.MAX_SAFE_INTEGER}}for(let c=0;c<n.length;c++){if(n[c].cellKind!==e.cellKind)continue;const h=n[c].getValue(),f=i.modifiedToOriginal.get(t)??new Map,g=f.get(c)??{editCount:nl(e,n[c])};f.set(c,g),i.modifiedToOriginal.set(t,f);const m=i.originalToModified.get(c)??new Map;if(m.set(t,g),i.originalToModified.set(c,m),!!s(c,g)&&!(h.length===0&&r)){if(h===e.getValue()&&e.getValue().length>0)return{index:c,editCount:0,percentage:0};g.editCount<o&&(o=g.editCount,l=c)}}if(l===-1)return{index:-1,editCount:Number.MAX_SAFE_INTEGER,percentage:Number.MAX_SAFE_INTEGER};const a=!e.getValue().length&&!n[l].getValue().length?0:e.getValue().length?o*100/e.getValue().length:Number.MAX_SAFE_INTEGER;return{index:l,editCount:o,percentage:a}}function nl(e,t){return e.getValue()===t.getValue()?0:wo(e.getValue(),t.getValue())}var U1=function(){if(typeof crypto.randomUUID=="function")return crypto.randomUUID.bind(crypto);const e=new Uint8Array(16),t=[];for(let n=0;n<256;n++)t.push(n.toString(16).padStart(2,"0"));return function(){crypto.getRandomValues(e),e[6]=e[6]&15|64,e[8]=e[8]&63|128;let r=0,i="";return i+=t[e[r++]],i+=t[e[r++]],i+=t[e[r++]],i+=t[e[r++]],i+="-",i+=t[e[r++]],i+=t[e[r++]],i+="-",i+=t[e[r++]],i+=t[e[r++]],i+="-",i+=t[e[r++]],i+=t[e[r++]],i+="-",i+=t[e[r++]],i+=t[e[r++]],i+=t[e[r++]],i+=t[e[r++]],i+=t[e[r++]],i+=t[e[r++]],i}}();function Zr(e,t,n){const r=n.cellsDiff.changes,i=[];let s=0,o=0,l=-1;for(let u=0;u<r.length;u++){const a=r[u];for(let h=0;h<a.originalStart-s;h++){const f=e.cells[s+h],g=t.cells[o+h];f.getHashValue()===g.getHashValue()?i.push({originalCellIndex:s+h,modifiedCellIndex:o+h,type:"unchanged"}):(l===-1&&(l=i.length),i.push({originalCellIndex:s+h,modifiedCellIndex:o+h,type:"modified"}))}const c=rl(a,e,t);c.length&&l===-1&&(l=i.length),i.push(...c),s=a.originalStart+a.originalLength,o=a.modifiedStart+a.modifiedLength}for(let u=s;u<e.cells.length;u++)i.push({originalCellIndex:u,modifiedCellIndex:u-s+o,type:"unchanged"});return{cellDiffInfo:i,firstChangeIndex:l}}function rl(e,t,n){const r=[],i=Math.min(e.originalLength,e.modifiedLength);for(let s=0;s<i;s++){const o=t.cells[e.originalStart+s],l=n.cells[e.modifiedStart+s];if(o.cellKind!==l.cellKind)r.push({originalCellIndex:e.originalStart+s,type:"delete"}),r.push({modifiedCellIndex:e.modifiedStart+s,type:"insert"});else{const u=o.equal(l);r.push({originalCellIndex:e.originalStart+s,modifiedCellIndex:e.modifiedStart+s,type:u?"unchanged":"modified"})}}for(let s=i;s<e.originalLength;s++)r.push({originalCellIndex:e.originalStart+s,type:"delete"});for(let s=i;s<e.modifiedLength;s++)r.push({modifiedCellIndex:e.modifiedStart+s,type:"insert"});return r}var Yr="unmatchedOriginalCell",ti=class{get eol(){return this.d===`\r
`?2:1}constructor(e,t,n,r,i,s,o,l,u,a){this.handle=e,this.d=r,this.language=s,this.cellKind=o,this.outputs=l,this.metadata=u,this.internalMetadata=a,this.a=new tl(t,n,r,i)}onEvents(e){this.a.onEvents(e),this.b=void 0}getValue(){return this.a.getValue()}getLinesContent(){return this.a.getLinesContent()}getComparisonValue(){return this.b??=this.f()}f(){let e=ot(104579,0);e=ct(this.language,e),e=ct(this.getValue(),e),e=ct(this.metadata,e),e=ct(this.internalMetadata?.internalId||"",e);for(const n of this.outputs){e=ct(n.metadata,e);for(const r of n.outputs)e=ct(r.mime,e)}const t=this.outputs.flatMap(n=>n.outputs.map(r=>fo(Array.from(r.data.buffer))));for(const n of t)e=ot(n,e);return e}},il=class{constructor(e,t,n,r){this.uri=e,this.cells=t,this.metadata=n,this.transientDocumentMetadata=r}acceptModelChanged(e){e.rawEvents.forEach(t=>{if(t.kind===Nt.ModelChange)this._spliceNotebookCells(t.changes);else if(t.kind===Nt.Move){const n=this.cells.splice(t.index,1);this.cells.splice(t.newIdx,0,...n)}else if(t.kind===Nt.Output){const n=this.cells[t.index];n.outputs=t.outputs}else if(t.kind===Nt.ChangeCellLanguage){this.a(t.index);const n=this.cells[t.index];n.language=t.language}else if(t.kind===Nt.ChangeCellMetadata){this.a(t.index);const n=this.cells[t.index];n.metadata=t.metadata}else if(t.kind===Nt.ChangeCellInternalMetadata){this.a(t.index);const n=this.cells[t.index];n.internalMetadata=t.internalMetadata}else t.kind===Nt.ChangeDocumentMetadata&&(this.metadata=t.metadata)})}a(e){if(e<0||e>=this.cells.length)throw new Error(`Illegal index ${e}. Cells length: ${this.cells.length}`)}_spliceNotebookCells(e){e.reverse().forEach(t=>{const r=t[2].map(i=>new ti(i.handle,it.parse(i.url),i.source,i.eol,i.versionId,i.language,i.cellKind,i.outputs,i.metadata));this.cells.splice(t[0],t[1],...r)})}},Re=class V1{static create(t){const n=t.cells.map(r=>r.getComparisonValue());return new V1(n)}static createWithCellId(t,n){const r=t.map(i=>n?`${ct(i.internalMetadata?.internalId,ot(104579,0))}#${i.getComparisonValue()}`:`${ct(i.internalMetadata?.internalId,ot(104579,0))}}`);return new V1(r)}constructor(t){this.hashValue=t}getElements(){return this.hashValue}},sl=class{constructor(){this.a=Object.create(null)}dispose(){}$acceptNewModel(e,t,n,r){this.a[e]=new il(it.parse(e),r.map(i=>new ti(i.handle,it.parse(i.url),i.source,i.eol,i.versionId,i.language,i.cellKind,i.outputs,i.metadata,i.internalMetadata)),t,n)}$acceptModelChanged(e,t){this.a[e]?.acceptModelChanged(t)}$acceptCellModelChanged(e,t,n){this.a[e].cells.find(i=>i.handle===t)?.onEvents(n)}$acceptRemovedModel(e){this.a[e]&&delete this.a[e]}async $computeDiff(e,t){const n=this.b(e),r=this.b(t),i=new ei(n),s=new ei(r),o=zn(n.metadata,d=>!n.transientDocumentMetadata[d]),l=zn(r.metadata,d=>!r.transientDocumentMetadata[d]),u=JSON.stringify(o)!==JSON.stringify(l),a=new Dn(Re.create(n),Re.create(r)).ComputeDiff(!1);if(a.changes.length===0)return{metadataChanged:u,cellsDiff:a};if(Zr(i,s,{cellsDiff:{changes:a.changes,quitEarly:!1},metadataChanged:!1}).cellDiffInfo.every(d=>d.type==="modified"))return{metadataChanged:u,cellsDiff:a};let h=this.canComputeDiffWithCellIds(n,r);if(!h){const d=el(r.cells,n.cells);d.some(w=>w.original!==-1)&&(this.updateCellIdsBasedOnMappings(d,n.cells,r.cells),h=!0)}if(!h)return{metadataChanged:u,cellsDiff:a};const f=new Dn(Re.createWithCellId(n.cells),Re.createWithCellId(r.cells)).ComputeDiff(!1),g=Zr(i,s,{cellsDiff:{changes:f.changes,quitEarly:!1},metadataChanged:!1}).cellDiffInfo;let m=0;const p=[];return f.changes.forEach(d=>{if(!d.originalLength&&d.modifiedLength){const w=g.findIndex(b=>b.type==="insert"&&b.modifiedCellIndex===d.modifiedStart);g.slice(m,w).forEach(b=>{if(b.type==="unchanged"||b.type==="modified"){const L=n.cells[b.originalCellIndex],D=r.cells[b.modifiedCellIndex];(b.type==="modified"||L.getComparisonValue()!==D.getComparisonValue())&&p.push(new at(b.originalCellIndex,1,b.modifiedCellIndex,1))}}),p.push(d),m=w+1}else if(d.originalLength&&!d.modifiedLength){const w=g.findIndex(b=>b.type==="delete"&&b.originalCellIndex===d.originalStart);g.slice(m,w).forEach(b=>{if(b.type==="unchanged"||b.type==="modified"){const L=n.cells[b.originalCellIndex],D=r.cells[b.modifiedCellIndex];(b.type==="modified"||L.getComparisonValue()!==D.getComparisonValue())&&p.push(new at(b.originalCellIndex,1,b.modifiedCellIndex,1))}}),p.push(d),m=w+1}else{const w=g.findIndex(b=>b.type==="delete"&&b.originalCellIndex===d.originalStart||b.type==="insert"&&b.modifiedCellIndex===d.modifiedStart);g.slice(m,w).forEach(b=>{if(b.type==="unchanged"||b.type==="modified"){const L=n.cells[b.originalCellIndex],D=r.cells[b.modifiedCellIndex];(b.type==="modified"||L.getComparisonValue()!==D.getComparisonValue())&&p.push(new at(b.originalCellIndex,1,b.modifiedCellIndex,1))}}),p.push(d),m=w+1}}),g.slice(m).forEach(d=>{if(d.type==="unchanged"||d.type==="modified"){const w=n.cells[d.originalCellIndex],b=r.cells[d.modifiedCellIndex];(d.type==="modified"||w.getComparisonValue()!==b.getComparisonValue())&&p.push(new at(d.originalCellIndex,1,d.modifiedCellIndex,1))}}),{metadataChanged:u,cellsDiff:{changes:p,quitEarly:!1}}}canComputeDiffWithCellIds(e,t){return this.canComputeDiffWithCellInternalIds(e,t)||this.canComputeDiffWithCellMetadataIds(e,t)}canComputeDiffWithCellInternalIds(e,t){const n=e.cells.map((i,s)=>({index:s,id:i.internalMetadata?.internalId||""})),r=t.cells.map((i,s)=>({index:s,id:i.internalMetadata?.internalId||""}));return n.some(i=>!i.id)||r.some(i=>!i.id)?!1:n.some(i=>r.find(s=>s.id===i.id))}canComputeDiffWithCellMetadataIds(e,t){const n=e.cells.map((i,s)=>({index:s,id:i.metadata?.id||""})),r=t.cells.map((i,s)=>({index:s,id:i.metadata?.id||""}));return n.some(i=>!i.id)||r.some(i=>!i.id)||n.every(i=>!r.find(s=>s.id===i.id))?!1:(e.cells.map((i,s)=>{i.internalMetadata=i.internalMetadata||{},i.internalMetadata.internalId=i.metadata?.id||""}),t.cells.map((i,s)=>{i.internalMetadata=i.internalMetadata||{},i.internalMetadata.internalId=i.metadata?.id||""}),!0)}isOriginalCellMatchedWithModifiedCell(e){return(e.internalMetadata?.internalId||"").startsWith(Yr)}updateCellIdsBasedOnMappings(e,t,n){const r=new Map;return t.map((i,s)=>{i.internalMetadata=i.internalMetadata||{internalId:""},i.internalMetadata.internalId=`${Yr}${U1()}`;const o=e.find(l=>l.original===s);o&&(i.internalMetadata.internalId=U1(),r.set(o.modified,i.internalMetadata.internalId))}),n.map((i,s)=>{i.internalMetadata=i.internalMetadata||{internalId:""},i.internalMetadata.internalId=r.get(s)??U1()}),!0}$canPromptRecommendation(e){const n=this.b(e).cells;for(let r=0;r<n.length;r++){const i=n[r];if(i.cellKind===q1.Markup||i.language!=="python")continue;const o=new Qo("import\\s*pandas|from\\s*pandas",!0,!1,null).parseSearchRequest();if(!o)continue;const l=new d0;l.acceptChunk(i.getValue());const a=l.finish(!0).create(i.eol).textBuffer,c=a.getLineCount(),h=Math.min(c,20),f=new J(1,1,h,a.getLineLength(h)+1);if(a.findMatchesLineByLine(f,o,!0,1).length>0)return!0}return!1}b(e){return this.a[e]}};function ol(){return new sl}var ei=class{constructor(e){this.notebook=e,this.cells=e.cells.map(t=>new ll(t))}},ll=class{get cellKind(){return this.a.cellKind}constructor(e){this.a=e}getHashValue(){return this.a.getComparisonValue()}equal(e){return e.cellKind!==this.cellKind?!1:this.getHashValue()===e.getHashValue()}};to(ol);

//# sourceMappingURL=https://main.vscode-cdn.net/sourcemaps/360a4e4fd251bfce169a4ddf857c7d25d1ad40da/core/vs/workbench/contrib/notebook/common/services/notebookWebWorkerMain.js.map
