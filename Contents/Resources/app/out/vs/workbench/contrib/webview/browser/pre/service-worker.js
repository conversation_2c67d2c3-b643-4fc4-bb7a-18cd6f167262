const sw=self,VERSION=4,resourceCacheName=`vscode-resource-cache-${VERSION}`,rootPath=sw.location.pathname.replace(/\/service-worker.js$/,""),searchParams=new URL(location.toString()).searchParams,remoteAuthority=searchParams.get("remoteAuthority");let outerIframeMessagePort;const resourceBaseAuthority=searchParams.get("vscode-resource-base-authority"),resolveTimeout=3e4;class RequestStore{constructor(){this.map=new Map,this.requestPool=0}create(){const t=++this.requestPool;let s;const o=new Promise(c=>s=c),r={resolve:s,promise:o};this.map.set(t,r);const i=setTimeout(()=>{clearTimeout(i);const c=this.map.get(t);c===r&&(c.resolve({status:"timeout"}),this.map.delete(t))},resolveTimeout);return{requestId:t,promise:o}}resolve(t,s){const o=this.map.get(t);return o?(o.resolve({status:"ok",value:s}),this.map.delete(t),!0):!1}}const resourceRequestStore=new RequestStore,localhostRequestStore=new RequestStore,unauthorized=()=>new Response("Unauthorized",{status:401}),notFound=()=>new Response("Not Found",{status:404}),methodNotAllowed=()=>new Response("Method Not Allowed",{status:405}),requestTimeout=()=>new Response("Request Timeout",{status:408});sw.addEventListener("message",async e=>{if(!e.source)return;const t=e.source;switch(e.data.channel){case"version":{outerIframeMessagePort=e.ports[0],sw.clients.get(t.id).then(s=>{s&&s.postMessage({channel:"version",version:VERSION})});return}case"did-load-resource":{const s=e.data.data;resourceRequestStore.resolve(s.id,s)||console.log("Could not resolve unknown resource",s.path);return}case"did-load-localhost":{const s=e.data.data;localhostRequestStore.resolve(s.id,s.location)||console.log("Could not resolve unknown localhost",s.origin);return}default:{console.log("Unknown message");return}}}),sw.addEventListener("fetch",e=>{const t=new URL(e.request.url);if(typeof resourceBaseAuthority=="string"&&t.protocol==="https:"&&t.hostname.endsWith("."+resourceBaseAuthority))switch(e.request.method){case"GET":case"HEAD":{const s=t.hostname.slice(0,t.hostname.length-(resourceBaseAuthority.length+1)),o=s.split("+",1)[0],r=s.slice(o.length+1);return e.respondWith(processResourceRequest(e,{scheme:o,authority:r,path:t.pathname,query:t.search.replace(/^\?/,"")}))}default:return e.respondWith(methodNotAllowed())}if(t.origin!==sw.origin&&t.host===remoteAuthority)switch(e.request.method){case"GET":case"HEAD":return e.respondWith(processResourceRequest(e,{path:t.pathname,scheme:t.protocol.slice(0,t.protocol.length-1),authority:t.host,query:t.search.replace(/^\?/,"")}));default:return e.respondWith(methodNotAllowed())}if(t.origin!==sw.origin&&t.host.match(/^(localhost|127.0.0.1|0.0.0.0):(\d+)$/))return e.respondWith(processLocalhostRequest(e,t))}),sw.addEventListener("install",e=>{e.waitUntil(sw.skipWaiting())}),sw.addEventListener("activate",e=>{e.waitUntil(sw.clients.claim())});async function processResourceRequest(e,t){let s=await sw.clients.get(e.clientId);if(!s&&(s=await getWorkerClientForId(e.clientId),!s))return console.error("Could not find inner client for request"),notFound();const o=getWebviewIdForClient(s);if(!o&&s.type!=="worker"&&s.type!=="sharedworker")return console.error("Could not resolve webview id"),notFound();const r=e.request.method==="GET",p=(n,h)=>{if(n.status==="timeout")return requestTimeout();const a=n.value;if(a.status===304){if(h)return h.clone();throw new Error("No cache found")}if(a.status===401)return unauthorized();if(a.status!==200)return notFound();const f={"Access-Control-Allow-Origin":"*"},g=a.data.byteLength,w=e.request.headers.get("range");if(w){const d=w.match(/^bytes\=(\d+)\-(\d+)?$/g);if(d){const C=Number(d[1]),q=Number(d[2])||g-1;return new Response(a.data.slice(C,q+1),{status:206,headers:{...f,"Content-range":`bytes 0-${q}/${g}`}})}else return new Response(null,{status:416,headers:{...f,"Content-range":`*/${g}`}})}const u={...f,"Content-Type":a.mime,"Content-Length":g.toString()};a.etag&&(u.ETag=a.etag,u["Cache-Control"]="no-cache"),a.mtime&&(u["Last-Modified"]=new Date(a.mtime).toUTCString());const m=new URL(e.request.url).searchParams.get("vscode-coi");m==="3"?(u["Cross-Origin-Opener-Policy"]="same-origin",u["Cross-Origin-Embedder-Policy"]="require-corp"):m==="2"?u["Cross-Origin-Embedder-Policy"]="require-corp":m==="1"&&(u["Cross-Origin-Opener-Policy"]="same-origin");const y=new Response(a.data,{status:200,headers:u});return r&&a.etag&&caches.open(resourceCacheName).then(d=>d.put(e.request,y)),y.clone()};let i;r&&(i=await(await caches.open(resourceCacheName)).match(e.request));const{requestId:c,promise:l}=resourceRequestStore.create();if(o){const n=await getOuterIframeClient(o);if(!n.length)return console.log("Could not find parent client for request"),notFound();for(const h of n)h.postMessage({channel:"load-resource",id:c,scheme:t.scheme,authority:t.authority,path:t.path,query:t.query,ifNoneMatch:i?.headers.get("ETag")})}else(s.type==="worker"||s.type==="sharedworker")&&outerIframeMessagePort?.postMessage({channel:"load-resource",id:c,scheme:t.scheme,authority:t.authority,path:t.path,query:t.query,ifNoneMatch:i?.headers.get("ETag")});return l.then(n=>p(n,i))}async function processLocalhostRequest(e,t){const s=await sw.clients.get(e.clientId);if(!s)return fetch(e.request);const o=getWebviewIdForClient(s);if(!o&&s.type!=="worker"&&s.type!=="sharedworker")return console.error("Could not resolve webview id"),fetch(e.request);const r=t.origin,p=async function(l){if(l.status!=="ok"||!l.value)return fetch(e.request);const n=l.value,h=e.request.url.replace(new RegExp(`^${t.origin}(/|$)`),`${n}$1`);return new Response(null,{status:302,headers:{Location:h}})},{requestId:i,promise:c}=localhostRequestStore.create();if(o){const l=await getOuterIframeClient(o);if(!l.length)return console.log("Could not find parent client for request"),notFound();for(const n of l)n.postMessage({channel:"load-localhost",origin:r,id:i})}else(s.type==="worker"||s.type==="sharedworker")&&outerIframeMessagePort?.postMessage({channel:"load-localhost",origin:r,id:i});return c.then(p)}function getWebviewIdForClient(e){return new URL(e.url).searchParams.get("id")}async function getOuterIframeClient(e){return(await sw.clients.matchAll({includeUncontrolled:!0})).filter(s=>{const o=new URL(s.url);return(o.pathname===`${rootPath}/`||o.pathname===`${rootPath}/index.html`||o.pathname===`${rootPath}/index-no-csp.html`)&&o.searchParams.get("id")===e})}async function getWorkerClientForId(e){const t=await sw.clients.matchAll({type:"worker"}),s=await sw.clients.matchAll({type:"sharedworker"});return[...t,...s].find(r=>r.id===e)}

//# sourceMappingURL=https://main.vscode-cdn.net/sourcemaps/360a4e4fd251bfce169a4ddf857c7d25d1ad40da/core/vs/workbench/contrib/webview/browser/pre/service-worker.js.map
