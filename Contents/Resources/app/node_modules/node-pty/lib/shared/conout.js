"use strict";
/**
 * Copyright (c) 2020, Microsoft Corporation (MIT License).
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.getWorkerPipeName = void 0;
function getWorkerPipeName(conoutPipeName) {
    return conoutPipeName + "-worker";
}
exports.getWorkerPipeName = getWorkerPipeName;//# sourceMappingURL=https://main.vscode-cdn.net/sourcemaps/360a4e4fd251bfce169a4ddf857c7d25d1ad40da/node_modules/node-pty/lib/shared/conout.js.map