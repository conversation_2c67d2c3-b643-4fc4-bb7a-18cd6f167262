!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t():"function"==typeof define&&define.amd?define([],t):"object"==typeof exports?exports.vscodetextmate=t():e.vscodetextmate=t()}(this,(()=>(()=>{"use strict";var e={185:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.UseOnigurumaFindOptions=t.DebugFlags=void 0,t.DebugFlags={InDebugMode:"undefined"!=typeof process&&!!process.env.VSCODE_TEXTMATE_DEBUG},t.UseOnigurumaFindOptions=!1},151:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.applyStateStackDiff=t.diffStateStacksRefEq=void 0;const s=n(752);t.diffStateStacksRefEq=function(e,t){let n=0;const s=[];let r=e,i=t;for(;r!==i;)r&&(!i||r.depth>=i.depth)?(n++,r=r.parent):(s.push(i.toStateStackFrame()),i=i.parent);return{pops:n,newFrames:s.reverse()}},t.applyStateStackDiff=function(e,t){let n=e;for(let e=0;e<t.pops;e++)n=n.parent;for(const e of t.newFrames)n=s.StateStackImpl.pushFrame(n,e);return n}},490:(e,t)=>{var n;Object.defineProperty(t,"__esModule",{value:!0}),t.toOptionalTokenType=t.EncodedTokenAttributes=void 0,(n=t.EncodedTokenAttributes||(t.EncodedTokenAttributes={})).toBinaryStr=function(e){return e.toString(2).padStart(32,"0")},n.print=function(e){const t=n.getLanguageId(e),s=n.getTokenType(e),r=n.getFontStyle(e),i=n.getForeground(e),o=n.getBackground(e);console.log({languageId:t,tokenType:s,fontStyle:r,foreground:i,background:o})},n.getLanguageId=function(e){return(255&e)>>>0},n.getTokenType=function(e){return(768&e)>>>8},n.containsBalancedBrackets=function(e){return!!(1024&e)},n.getFontStyle=function(e){return(30720&e)>>>11},n.getForeground=function(e){return(16744448&e)>>>15},n.getBackground=function(e){return(**********&e)>>>24},n.set=function(e,t,s,r,i,o,a){let c=n.getLanguageId(e),l=n.getTokenType(e),u=n.containsBalancedBrackets(e)?1:0,h=n.getFontStyle(e),p=n.getForeground(e),d=n.getBackground(e);return 0!==t&&(c=t),8!==s&&(l=s),null!==r&&(u=r?1:0),-1!==i&&(h=i),0!==o&&(p=o),0!==a&&(d=a),(c|l<<8|u<<10|h<<11|p<<15|d<<24)>>>0},t.toOptionalTokenType=function(e){return e}},214:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.BasicScopeAttributesProvider=t.BasicScopeAttributes=void 0;const s=n(807);class r{constructor(e,t){this.languageId=e,this.tokenType=t}}t.BasicScopeAttributes=r;class i{constructor(e,t){this._getBasicScopeAttributes=new s.CachedFn((e=>{const t=this._scopeToLanguage(e),n=this._toStandardTokenType(e);return new r(t,n)})),this._defaultAttributes=new r(e,8),this._embeddedLanguagesMatcher=new o(Object.entries(t||{}))}getDefaultAttributes(){return this._defaultAttributes}getBasicScopeAttributes(e){return null===e?i._NULL_SCOPE_METADATA:this._getBasicScopeAttributes.get(e)}_scopeToLanguage(e){return this._embeddedLanguagesMatcher.match(e)||0}_toStandardTokenType(e){const t=e.match(i.STANDARD_TOKEN_TYPE_REGEXP);if(!t)return 8;switch(t[1]){case"comment":return 1;case"string":return 2;case"regex":return 3;case"meta.embedded":return 0}throw new Error("Unexpected match for standard token type!")}}t.BasicScopeAttributesProvider=i,i._NULL_SCOPE_METADATA=new r(0,0),i.STANDARD_TOKEN_TYPE_REGEXP=/\b(comment|string|regex|meta\.embedded)\b/;class o{constructor(e){if(0===e.length)this.values=null,this.scopesRegExp=null;else{this.values=new Map(e);const t=e.map((([e,t])=>s.escapeRegExpCharacters(e)));t.sort(),t.reverse(),this.scopesRegExp=new RegExp(`^((${t.join(")|(")}))($|\\.)`,"")}}match(e){if(!this.scopesRegExp)return;const t=e.match(this.scopesRegExp);return t?this.values.get(t[1]):void 0}}},929:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.LineTokens=t.BalancedBracketSelectors=t.StateStackImpl=t.AttributedScopeStack=t.Grammar=t.createGrammar=void 0;const s=n(185),r=n(490),i=n(916),o=n(810),a=n(666),c=n(63),l=n(807),u=n(214),h=n(398);function p(e,t,n,s,r){const o=i.createMatchers(t,d),c=a.RuleFactory.getCompiledRuleId(n,s,r.repository);for(const n of o)e.push({debugSelector:t,matcher:n.matcher,ruleId:c,grammar:r,priority:n.priority})}function d(e,t){if(t.length<e.length)return!1;let n=0;return e.every((e=>{for(let s=n;s<t.length;s++)if(f(t[s],e))return n=s+1,!0;return!1}))}function f(e,t){if(!e)return!1;if(e===t)return!0;const n=t.length;return e.length>n&&e.substr(0,n)===t&&"."===e[n]}t.createGrammar=function(e,t,n,s,r,i,o,a){return new m(e,t,n,s,r,i,o,a)};class m{constructor(e,t,n,s,r,o,a,c){if(this._rootScopeName=e,this.balancedBracketSelectors=o,this._onigLib=c,this._basicScopeAttributesProvider=new u.BasicScopeAttributesProvider(n,s),this._rootId=-1,this._lastRuleId=0,this._ruleId2desc=[null],this._includedGrammars={},this._grammarRepository=a,this._grammar=g(t,null),this._injections=null,this._tokenTypeMatchers=[],r)for(const e of Object.keys(r)){const t=i.createMatchers(e,d);for(const n of t)this._tokenTypeMatchers.push({matcher:n.matcher,type:r[e]})}}get themeProvider(){return this._grammarRepository}dispose(){for(const e of this._ruleId2desc)e&&e.dispose()}createOnigScanner(e){return this._onigLib.createOnigScanner(e)}createOnigString(e){return this._onigLib.createOnigString(e)}getMetadataForScope(e){return this._basicScopeAttributesProvider.getBasicScopeAttributes(e)}_collectInjections(){const e=[],t=this._rootScopeName,n=(e=>e===this._rootScopeName?this._grammar:this.getExternalGrammar(e))(t);if(n){const s=n.injections;if(s)for(let t in s)p(e,t,s[t],this,n);const r=this._grammarRepository.injections(t);r&&r.forEach((t=>{const n=this.getExternalGrammar(t);if(n){const t=n.injectionSelector;t&&p(e,t,n,this,n)}}))}return e.sort(((e,t)=>e.priority-t.priority)),e}getInjections(){if(null===this._injections&&(this._injections=this._collectInjections(),s.DebugFlags.InDebugMode&&this._injections.length>0)){console.log(`Grammar ${this._rootScopeName} contains the following injections:`);for(const e of this._injections)console.log(`  - ${e.debugSelector}`)}return this._injections}registerRule(e){const t=++this._lastRuleId,n=e(a.ruleIdFromNumber(t));return this._ruleId2desc[t]=n,n}getRule(e){return this._ruleId2desc[a.ruleIdToNumber(e)]}getExternalGrammar(e,t){if(this._includedGrammars[e])return this._includedGrammars[e];if(this._grammarRepository){const n=this._grammarRepository.lookup(e);if(n)return this._includedGrammars[e]=g(n,t&&t.$base),this._includedGrammars[e]}}tokenizeLine(e,t,n=0){const s=this._tokenize(e,t,!1,n);return{tokens:s.lineTokens.getResult(s.ruleStack,s.lineLength),ruleStack:s.ruleStack,stoppedEarly:s.stoppedEarly}}tokenizeLine2(e,t,n=0){const s=this._tokenize(e,t,!0,n);return{tokens:s.lineTokens.getBinaryResult(s.ruleStack,s.lineLength),ruleStack:s.ruleStack,stoppedEarly:s.stoppedEarly}}_tokenize(e,t,n,s){let i;if(-1===this._rootId&&(this._rootId=a.RuleFactory.getCompiledRuleId(this._grammar.repository.$self,this,this._grammar.repository),this.getInjections()),t&&t!==b.NULL)i=!1,t.reset();else{i=!0;const e=this._basicScopeAttributesProvider.getDefaultAttributes(),n=this.themeProvider.getDefaults(),s=r.EncodedTokenAttributes.set(0,e.languageId,e.tokenType,null,n.fontStyle,n.foregroundId,n.backgroundId),o=this.getRule(this._rootId).getName(null,null);let a;a=o?_.createRootAndLookUpScopeName(o,s,this):_.createRoot("unknown",s),t=new b(null,this._rootId,-1,-1,!1,null,a,a)}e+="\n";const c=this.createOnigString(e),l=c.content.length,u=new y(n,e,this._tokenTypeMatchers,this.balancedBracketSelectors),p=h._tokenizeString(this,c,i,0,t,u,!0,s);return o.disposeOnigString(c),{lineLength:l,lineTokens:u,ruleStack:p.stack,stoppedEarly:p.stoppedEarly}}}function g(e,t){return(e=l.clone(e)).repository=e.repository||{},e.repository.$self={$vscodeTextmateLocation:e.$vscodeTextmateLocation,patterns:e.patterns,name:e.scopeName},e.repository.$base=t||e.repository.$self,e}t.Grammar=m;class _{constructor(e,t,n){this.parent=e,this.scopePath=t,this.tokenAttributes=n}static fromExtension(e,t){let n=e,s=e?.scopePath??null;for(const e of t)s=c.ScopeStack.push(s,e.scopeNames),n=new _(n,s,e.encodedTokenAttributes);return n}static createRoot(e,t){return new _(null,new c.ScopeStack(null,e),t)}static createRootAndLookUpScopeName(e,t,n){const s=n.getMetadataForScope(e),r=new c.ScopeStack(null,e),i=n.themeProvider.themeMatch(r),o=_.mergeAttributes(t,s,i);return new _(null,r,o)}get scopeName(){return this.scopePath.scopeName}toString(){return this.getScopeNames().join(" ")}equals(e){return _.equals(this,e)}static equals(e,t){for(;;){if(e===t)return!0;if(!e&&!t)return!0;if(!e||!t)return!1;if(e.scopeName!==t.scopeName||e.tokenAttributes!==t.tokenAttributes)return!1;e=e.parent,t=t.parent}}static mergeAttributes(e,t,n){let s=-1,i=0,o=0;return null!==n&&(s=n.fontStyle,i=n.foregroundId,o=n.backgroundId),r.EncodedTokenAttributes.set(e,t.languageId,t.tokenType,null,s,i,o)}pushAttributed(e,t){if(null===e)return this;if(-1===e.indexOf(" "))return _._pushAttributed(this,e,t);const n=e.split(/ /g);let s=this;for(const e of n)s=_._pushAttributed(s,e,t);return s}static _pushAttributed(e,t,n){const s=n.getMetadataForScope(t),r=e.scopePath.push(t),i=n.themeProvider.themeMatch(r),o=_.mergeAttributes(e.tokenAttributes,s,i);return new _(e,r,o)}getScopeNames(){return this.scopePath.getSegments()}getExtensionIfDefined(e){const t=[];let n=this;for(;n&&n!==e;)t.push({encodedTokenAttributes:n.tokenAttributes,scopeNames:n.scopePath.getExtensionIfDefined(n.parent?.scopePath??null)}),n=n.parent;return n===e?t.reverse():void 0}}t.AttributedScopeStack=_;class b{constructor(e,t,n,s,r,i,o,a){this.parent=e,this.ruleId=t,this.beginRuleCapturedEOL=r,this.endRule=i,this.nameScopesList=o,this.contentNameScopesList=a,this._stackElementBrand=void 0,this.depth=this.parent?this.parent.depth+1:1,this._enterPos=n,this._anchorPos=s}equals(e){return null!==e&&b._equals(this,e)}static _equals(e,t){return e===t||!!this._structuralEquals(e,t)&&_.equals(e.contentNameScopesList,t.contentNameScopesList)}static _structuralEquals(e,t){for(;;){if(e===t)return!0;if(!e&&!t)return!0;if(!e||!t)return!1;if(e.depth!==t.depth||e.ruleId!==t.ruleId||e.endRule!==t.endRule)return!1;e=e.parent,t=t.parent}}clone(){return this}static _reset(e){for(;e;)e._enterPos=-1,e._anchorPos=-1,e=e.parent}reset(){b._reset(this)}pop(){return this.parent}safePop(){return this.parent?this.parent:this}push(e,t,n,s,r,i,o){return new b(this,e,t,n,s,r,i,o)}getEnterPos(){return this._enterPos}getAnchorPos(){return this._anchorPos}getRule(e){return e.getRule(this.ruleId)}toString(){const e=[];return this._writeString(e,0),"["+e.join(",")+"]"}_writeString(e,t){return this.parent&&(t=this.parent._writeString(e,t)),e[t++]=`(${this.ruleId}, ${this.nameScopesList?.toString()}, ${this.contentNameScopesList?.toString()})`,t}withContentNameScopesList(e){return this.contentNameScopesList===e?this:this.parent.push(this.ruleId,this._enterPos,this._anchorPos,this.beginRuleCapturedEOL,this.endRule,this.nameScopesList,e)}withEndRule(e){return this.endRule===e?this:new b(this.parent,this.ruleId,this._enterPos,this._anchorPos,this.beginRuleCapturedEOL,e,this.nameScopesList,this.contentNameScopesList)}hasSameRuleAs(e){let t=this;for(;t&&t._enterPos===e._enterPos;){if(t.ruleId===e.ruleId)return!0;t=t.parent}return!1}toStateStackFrame(){return{ruleId:a.ruleIdToNumber(this.ruleId),beginRuleCapturedEOL:this.beginRuleCapturedEOL,endRule:this.endRule,nameScopesList:this.nameScopesList?.getExtensionIfDefined(this.parent?.nameScopesList??null)??[],contentNameScopesList:this.contentNameScopesList?.getExtensionIfDefined(this.nameScopesList)??[]}}static pushFrame(e,t){const n=_.fromExtension(e?.nameScopesList??null,t.nameScopesList);return new b(e,a.ruleIdFromNumber(t.ruleId),t.enterPos??-1,t.anchorPos??-1,t.beginRuleCapturedEOL,t.endRule,n,_.fromExtension(n,t.contentNameScopesList))}}t.StateStackImpl=b,b.NULL=new b(null,0,0,0,!1,null,null,null),t.BalancedBracketSelectors=class{constructor(e,t){this.allowAny=!1,this.balancedBracketScopes=e.flatMap((e=>"*"===e?(this.allowAny=!0,[]):i.createMatchers(e,d).map((e=>e.matcher)))),this.unbalancedBracketScopes=t.flatMap((e=>i.createMatchers(e,d).map((e=>e.matcher))))}get matchesAlways(){return this.allowAny&&0===this.unbalancedBracketScopes.length}get matchesNever(){return 0===this.balancedBracketScopes.length&&!this.allowAny}match(e){for(const t of this.unbalancedBracketScopes)if(t(e))return!1;for(const t of this.balancedBracketScopes)if(t(e))return!0;return this.allowAny}};class y{constructor(e,t,n,r){this.balancedBracketSelectors=r,this._emitBinaryTokens=e,this._tokenTypeOverrides=n,s.DebugFlags.InDebugMode?this._lineText=t:this._lineText=null,this._tokens=[],this._binaryTokens=[],this._lastTokenEndIndex=0}produce(e,t){this.produceFromScopes(e.contentNameScopesList,t)}produceFromScopes(e,t){if(this._lastTokenEndIndex>=t)return;if(this._emitBinaryTokens){let n=e?.tokenAttributes??0,i=!1;if(this.balancedBracketSelectors?.matchesAlways&&(i=!0),this._tokenTypeOverrides.length>0||this.balancedBracketSelectors&&!this.balancedBracketSelectors.matchesAlways&&!this.balancedBracketSelectors.matchesNever){const t=e?.getScopeNames()??[];for(const e of this._tokenTypeOverrides)e.matcher(t)&&(n=r.EncodedTokenAttributes.set(n,0,r.toOptionalTokenType(e.type),null,-1,0,0));this.balancedBracketSelectors&&(i=this.balancedBracketSelectors.match(t))}if(i&&(n=r.EncodedTokenAttributes.set(n,0,8,i,-1,0,0)),this._binaryTokens.length>0&&this._binaryTokens[this._binaryTokens.length-1]===n)return void(this._lastTokenEndIndex=t);if(s.DebugFlags.InDebugMode){const n=e?.getScopeNames()??[];console.log("  token: |"+this._lineText.substring(this._lastTokenEndIndex,t).replace(/\n$/,"\\n")+"|");for(let e=0;e<n.length;e++)console.log("      * "+n[e])}return this._binaryTokens.push(this._lastTokenEndIndex),this._binaryTokens.push(n),void(this._lastTokenEndIndex=t)}const n=e?.getScopeNames()??[];if(s.DebugFlags.InDebugMode){console.log("  token: |"+this._lineText.substring(this._lastTokenEndIndex,t).replace(/\n$/,"\\n")+"|");for(let e=0;e<n.length;e++)console.log("      * "+n[e])}this._tokens.push({startIndex:this._lastTokenEndIndex,endIndex:t,scopes:n}),this._lastTokenEndIndex=t}getResult(e,t){return this._tokens.length>0&&this._tokens[this._tokens.length-1].startIndex===t-1&&this._tokens.pop(),0===this._tokens.length&&(this._lastTokenEndIndex=-1,this.produce(e,t),this._tokens[this._tokens.length-1].startIndex=0),this._tokens}getBinaryResult(e,t){this._binaryTokens.length>0&&this._binaryTokens[this._binaryTokens.length-2]===t-1&&(this._binaryTokens.pop(),this._binaryTokens.pop()),0===this._binaryTokens.length&&(this._lastTokenEndIndex=-1,this.produce(e,t),this._binaryTokens[this._binaryTokens.length-2]=0);const n=new Uint32Array(this._binaryTokens.length);for(let e=0,t=this._binaryTokens.length;e<t;e++)n[e]=this._binaryTokens[e];return n}}t.LineTokens=y},784:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.parseInclude=t.TopLevelRepositoryReference=t.TopLevelReference=t.RelativeReference=t.SelfReference=t.BaseReference=t.ScopeDependencyProcessor=t.ExternalReferenceCollector=t.TopLevelRepositoryRuleReference=t.TopLevelRuleReference=void 0;const s=n(807);class r{constructor(e){this.scopeName=e}toKey(){return this.scopeName}}t.TopLevelRuleReference=r;class i{constructor(e,t){this.scopeName=e,this.ruleName=t}toKey(){return`${this.scopeName}#${this.ruleName}`}}t.TopLevelRepositoryRuleReference=i;class o{constructor(){this._references=[],this._seenReferenceKeys=new Set,this.visitedRule=new Set}get references(){return this._references}add(e){const t=e.toKey();this._seenReferenceKeys.has(t)||(this._seenReferenceKeys.add(t),this._references.push(e))}}function a(e,t,n,s){const i=n.lookup(e.scopeName);if(!i){if(e.scopeName===t)throw new Error(`No grammar provided for <${t}>`);return}const o=n.lookup(t);e instanceof r?l({baseGrammar:o,selfGrammar:i},s):c(e.ruleName,{baseGrammar:o,selfGrammar:i,repository:i.repository},s);const a=n.injections(e.scopeName);if(a)for(const e of a)s.add(new r(e))}function c(e,t,n){t.repository&&t.repository[e]&&u([t.repository[e]],t,n)}function l(e,t){e.selfGrammar.patterns&&Array.isArray(e.selfGrammar.patterns)&&u(e.selfGrammar.patterns,{...e,repository:e.selfGrammar.repository},t),e.selfGrammar.injections&&u(Object.values(e.selfGrammar.injections),{...e,repository:e.selfGrammar.repository},t)}function u(e,t,n){for(const o of e){if(n.visitedRule.has(o))continue;n.visitedRule.add(o);const e=o.repository?s.mergeObjects({},t.repository,o.repository):t.repository;Array.isArray(o.patterns)&&u(o.patterns,{...t,repository:e},n);const a=o.include;if(!a)continue;const h=g(a);switch(h.kind){case 0:l({...t,selfGrammar:t.baseGrammar},n);break;case 1:l(t,n);break;case 2:c(h.ruleName,{...t,repository:e},n);break;case 3:case 4:const s=h.scopeName===t.selfGrammar.scopeName?t.selfGrammar:h.scopeName===t.baseGrammar.scopeName?t.baseGrammar:void 0;if(s){const r={baseGrammar:t.baseGrammar,selfGrammar:s,repository:e};4===h.kind?c(h.ruleName,r,n):l(r,n)}else 4===h.kind?n.add(new i(h.scopeName,h.ruleName)):n.add(new r(h.scopeName))}}}t.ExternalReferenceCollector=o,t.ScopeDependencyProcessor=class{constructor(e,t){this.repo=e,this.initialScopeName=t,this.seenFullScopeRequests=new Set,this.seenPartialScopeRequests=new Set,this.seenFullScopeRequests.add(this.initialScopeName),this.Q=[new r(this.initialScopeName)]}processQueue(){const e=this.Q;this.Q=[];const t=new o;for(const n of e)a(n,this.initialScopeName,this.repo,t);for(const e of t.references)if(e instanceof r){if(this.seenFullScopeRequests.has(e.scopeName))continue;this.seenFullScopeRequests.add(e.scopeName),this.Q.push(e)}else{if(this.seenFullScopeRequests.has(e.scopeName))continue;if(this.seenPartialScopeRequests.has(e.toKey()))continue;this.seenPartialScopeRequests.add(e.toKey()),this.Q.push(e)}}};class h{constructor(){this.kind=0}}t.BaseReference=h;class p{constructor(){this.kind=1}}t.SelfReference=p;class d{constructor(e){this.ruleName=e,this.kind=2}}t.RelativeReference=d;class f{constructor(e){this.scopeName=e,this.kind=3}}t.TopLevelReference=f;class m{constructor(e,t){this.scopeName=e,this.ruleName=t,this.kind=4}}function g(e){if("$base"===e)return new h;if("$self"===e)return new p;const t=e.indexOf("#");if(-1===t)return new f(e);if(0===t)return new d(e.substring(1));{const n=e.substring(0,t),s=e.substring(t+1);return new m(n,s)}}t.TopLevelRepositoryReference=m,t.parseInclude=g},752:function(e,t,n){var s=this&&this.__createBinding||(Object.create?function(e,t,n,s){void 0===s&&(s=n),Object.defineProperty(e,s,{enumerable:!0,get:function(){return t[n]}})}:function(e,t,n,s){void 0===s&&(s=n),e[s]=t[n]}),r=this&&this.__exportStar||function(e,t){for(var n in e)"default"===n||Object.prototype.hasOwnProperty.call(t,n)||s(t,e,n)};Object.defineProperty(t,"__esModule",{value:!0}),r(n(929),t)},398:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.LocalStackElement=t._tokenizeString=void 0;const s=n(185),r=n(810),i=n(666),o=n(807);class a{constructor(e,t){this.stack=e,this.stoppedEarly=t}}function c(e,t,n,r,c,h,d,f){const m=t.content.length;let g=!1,_=-1;if(d){const o=function(e,t,n,r,o,a){let c=o.beginRuleCapturedEOL?0:-1;const l=[];for(let t=o;t;t=t.pop()){const n=t.getRule(e);n instanceof i.BeginWhileRule&&l.push({rule:n,stack:t})}for(let h=l.pop();h;h=l.pop()){const{ruleScanner:l,findOptions:d}=u(h.rule,e,h.stack.endRule,n,r===c),f=l.findNextMatchSync(t,r,d);if(s.DebugFlags.InDebugMode&&(console.log("  scanning for while rule"),console.log(l.toString())),!f){s.DebugFlags.InDebugMode&&console.log("  popping "+h.rule.debugName+" - "+h.rule.debugWhileRegExp),o=h.stack.pop();break}if(f.ruleId!==i.whileRuleId){o=h.stack.pop();break}f.captureIndices&&f.captureIndices.length&&(a.produce(h.stack,f.captureIndices[0].start),p(e,t,n,h.stack,a,h.rule.whileCaptures,f.captureIndices),a.produce(h.stack,f.captureIndices[0].end),c=f.captureIndices[0].end,f.captureIndices[0].end>r&&(r=f.captureIndices[0].end,n=!1))}return{stack:o,linePos:r,anchorPosition:c,isFirstLine:n}}(e,t,n,r,c,h);c=o.stack,r=o.linePos,n=o.isFirstLine,_=o.anchorPosition}const b=Date.now();for(;!g;){if(0!==f&&Date.now()-b>f)return new a(c,!0);y()}return new a(c,!1);function y(){s.DebugFlags.InDebugMode&&(console.log(""),console.log(`@@scanNext ${r}: |${t.content.substr(r).replace(/\n$/,"\\n")}|`));const a=function(e,t,n,r,i,a){const c=function(e,t,n,r,i,a){const c=i.getRule(e),{ruleScanner:u,findOptions:h}=l(c,e,i.endRule,n,r===a);let p=0;s.DebugFlags.InDebugMode&&(p=o.performanceNow());const d=u.findNextMatchSync(t,r,h);if(s.DebugFlags.InDebugMode){const e=o.performanceNow()-p;e>5&&console.warn(`Rule ${c.debugName} (${c.id}) matching took ${e} against '${t}'`),console.log(`  scanning for (linePos: ${r}, anchorPosition: ${a})`),console.log(u.toString()),d&&console.log(`matched rule id: ${d.ruleId} from ${d.captureIndices[0].start} to ${d.captureIndices[0].end}`)}return d?{captureIndices:d.captureIndices,matchedRuleId:d.ruleId}:null}(e,t,n,r,i,a),u=e.getInjections();if(0===u.length)return c;const h=function(e,t,n,r,i,o,a){let c,u=Number.MAX_VALUE,h=null,p=0;const d=o.contentNameScopesList.getScopeNames();for(let o=0,f=e.length;o<f;o++){const f=e[o];if(!f.matcher(d))continue;const m=t.getRule(f.ruleId),{ruleScanner:g,findOptions:_}=l(m,t,null,r,i===a),b=g.findNextMatchSync(n,i,_);if(!b)continue;s.DebugFlags.InDebugMode&&(console.log(`  matched injection: ${f.debugSelector}`),console.log(g.toString()));const y=b.captureIndices[0].start;if(!(y>=u)&&(u=y,h=b.captureIndices,c=b.ruleId,p=f.priority,u===i))break}return h?{priorityMatch:-1===p,captureIndices:h,matchedRuleId:c}:null}(u,e,t,n,r,i,a);if(!h)return c;if(!c)return h;const p=c.captureIndices[0].start,d=h.captureIndices[0].start;return d<p||h.priorityMatch&&d===p?h:c}(e,t,n,r,c,_);if(!a)return s.DebugFlags.InDebugMode&&console.log("  no more matches."),h.produce(c,m),void(g=!0);const u=a.captureIndices,d=a.matchedRuleId,f=!!(u&&u.length>0)&&u[0].end>r;if(d===i.endRuleId){const i=c.getRule(e);s.DebugFlags.InDebugMode&&console.log("  popping "+i.debugName+" - "+i.debugEndRegExp),h.produce(c,u[0].start),c=c.withContentNameScopesList(c.nameScopesList),p(e,t,n,c,h,i.endCaptures,u),h.produce(c,u[0].end);const o=c;if(c=c.parent,_=o.getAnchorPos(),!f&&o.getEnterPos()===r)return s.DebugFlags.InDebugMode&&console.error("[1] - Grammar is in an endless loop - Grammar pushed & popped a rule without advancing"),c=o,h.produce(c,m),void(g=!0)}else{const o=e.getRule(d);h.produce(c,u[0].start);const a=c,l=o.getName(t.content,u),b=c.contentNameScopesList.pushAttributed(l,e);if(c=c.push(d,r,_,u[0].end===m,null,b,b),o instanceof i.BeginEndRule){const r=o;s.DebugFlags.InDebugMode&&console.log("  pushing "+r.debugName+" - "+r.debugBeginRegExp),p(e,t,n,c,h,r.beginCaptures,u),h.produce(c,u[0].end),_=u[0].end;const i=r.getContentName(t.content,u),l=b.pushAttributed(i,e);if(c=c.withContentNameScopesList(l),r.endHasBackReferences&&(c=c.withEndRule(r.getEndWithResolvedBackReferences(t.content,u))),!f&&a.hasSameRuleAs(c))return s.DebugFlags.InDebugMode&&console.error("[2] - Grammar is in an endless loop - Grammar pushed the same rule without advancing"),c=c.pop(),h.produce(c,m),void(g=!0)}else if(o instanceof i.BeginWhileRule){const r=o;s.DebugFlags.InDebugMode&&console.log("  pushing "+r.debugName),p(e,t,n,c,h,r.beginCaptures,u),h.produce(c,u[0].end),_=u[0].end;const i=r.getContentName(t.content,u),l=b.pushAttributed(i,e);if(c=c.withContentNameScopesList(l),r.whileHasBackReferences&&(c=c.withEndRule(r.getWhileWithResolvedBackReferences(t.content,u))),!f&&a.hasSameRuleAs(c))return s.DebugFlags.InDebugMode&&console.error("[3] - Grammar is in an endless loop - Grammar pushed the same rule without advancing"),c=c.pop(),h.produce(c,m),void(g=!0)}else{const r=o;if(s.DebugFlags.InDebugMode&&console.log("  matched "+r.debugName+" - "+r.debugMatchRegExp),p(e,t,n,c,h,r.captures,u),h.produce(c,u[0].end),c=c.pop(),!f)return s.DebugFlags.InDebugMode&&console.error("[4] - Grammar is in an endless loop - Grammar is not advancing, nor is it pushing/popping"),c=c.safePop(),h.produce(c,m),void(g=!0)}}u[0].end>r&&(r=u[0].end,n=!1)}}function l(e,t,n,r,i){return s.UseOnigurumaFindOptions?{ruleScanner:e.compile(t,n),findOptions:h(r,i)}:{ruleScanner:e.compileAG(t,n,r,i),findOptions:0}}function u(e,t,n,r,i){return s.UseOnigurumaFindOptions?{ruleScanner:e.compileWhile(t,n),findOptions:h(r,i)}:{ruleScanner:e.compileWhileAG(t,n,r,i),findOptions:0}}function h(e,t){let n=0;return e||(n|=1),t||(n|=4),n}function p(e,t,n,s,i,o,a){if(0===o.length)return;const l=t.content,u=Math.min(o.length,a.length),h=[],p=a[0].end;for(let t=0;t<u;t++){const u=o[t];if(null===u)continue;const f=a[t];if(0===f.length)continue;if(f.start>p)break;for(;h.length>0&&h[h.length-1].endPos<=f.start;)i.produceFromScopes(h[h.length-1].scopes,h[h.length-1].endPos),h.pop();if(h.length>0?i.produceFromScopes(h[h.length-1].scopes,f.start):i.produce(s,f.start),u.retokenizeCapturedWithRuleId){const t=u.getName(l,a),o=s.contentNameScopesList.pushAttributed(t,e),h=u.getContentName(l,a),p=o.pushAttributed(h,e),d=s.push(u.retokenizeCapturedWithRuleId,f.start,-1,!1,null,o,p),m=e.createOnigString(l.substring(0,f.end));c(e,m,n&&0===f.start,f.start,d,i,!1,0),r.disposeOnigString(m);continue}const m=u.getName(l,a);if(null!==m){const t=(h.length>0?h[h.length-1].scopes:s.contentNameScopesList).pushAttributed(m,e);h.push(new d(t,f.end))}}for(;h.length>0;)i.produceFromScopes(h[h.length-1].scopes,h[h.length-1].endPos),h.pop()}t._tokenizeString=c;class d{constructor(e,t){this.scopes=e,this.endPos=t}}t.LocalStackElement=d},726:(e,t)=>{function n(e,t){throw new Error("Near offset "+e.pos+": "+t+" ~~~"+e.source.substr(e.pos,50)+"~~~")}Object.defineProperty(t,"__esModule",{value:!0}),t.parseJSON=void 0,t.parseJSON=function(e,t,o){let a=new s(e),c=new r,l=0,u=null,h=[],p=[];function d(){h.push(l),p.push(u)}function f(){l=h.pop(),u=p.pop()}function m(e){n(a,e)}for(;i(a,c);){if(0===l){if(null!==u&&m("too many constructs in root"),3===c.type){u={},o&&(u.$vscodeTextmateLocation=c.toLocation(t)),d(),l=1;continue}if(2===c.type){u=[],d(),l=4;continue}m("unexpected token in root")}if(2===l){if(5===c.type){f();continue}if(7===c.type){l=3;continue}m("expected , or }")}if(1===l||3===l){if(1===l&&5===c.type){f();continue}if(1===c.type){let e=c.value;if(i(a,c)&&6===c.type||m("expected colon"),i(a,c)||m("expected value"),l=2,1===c.type){u[e]=c.value;continue}if(8===c.type){u[e]=null;continue}if(9===c.type){u[e]=!0;continue}if(10===c.type){u[e]=!1;continue}if(11===c.type){u[e]=parseFloat(c.value);continue}if(2===c.type){let t=[];u[e]=t,d(),l=4,u=t;continue}if(3===c.type){let n={};o&&(n.$vscodeTextmateLocation=c.toLocation(t)),u[e]=n,d(),l=1,u=n;continue}}m("unexpected token in dict")}if(5===l){if(4===c.type){f();continue}if(7===c.type){l=6;continue}m("expected , or ]")}if(4===l||6===l){if(4===l&&4===c.type){f();continue}if(l=5,1===c.type){u.push(c.value);continue}if(8===c.type){u.push(null);continue}if(9===c.type){u.push(!0);continue}if(10===c.type){u.push(!1);continue}if(11===c.type){u.push(parseFloat(c.value));continue}if(2===c.type){let e=[];u.push(e),d(),l=4,u=e;continue}if(3===c.type){let e={};o&&(e.$vscodeTextmateLocation=c.toLocation(t)),u.push(e),d(),l=1,u=e;continue}m("unexpected token in array")}m("unknown state")}return 0!==p.length&&m("unclosed constructs"),u};class s{constructor(e){this.source=e,this.pos=0,this.len=e.length,this.line=1,this.char=0}}class r{constructor(){this.value=null,this.type=0,this.offset=-1,this.len=-1,this.line=-1,this.char=-1}toLocation(e){return{filename:e,line:this.line,char:this.char}}}function i(e,t){t.value=null,t.type=0,t.offset=-1,t.len=-1,t.line=-1,t.char=-1;let s,r=e.source,i=e.pos,o=e.len,a=e.line,c=e.char;for(;;){if(i>=o)return!1;if(s=r.charCodeAt(i),32!==s&&9!==s&&13!==s){if(10!==s)break;i++,a++,c=0}else i++,c++}if(t.offset=i,t.line=a,t.char=c,34===s){for(t.type=1,i++,c++;;){if(i>=o)return!1;if(s=r.charCodeAt(i),i++,c++,92!==s){if(34===s)break}else i++,c++}t.value=r.substring(t.offset+1,i-1).replace(/\\u([0-9A-Fa-f]{4})/g,((e,t)=>String.fromCodePoint(parseInt(t,16)))).replace(/\\(.)/g,((t,s)=>{switch(s){case'"':return'"';case"\\":return"\\";case"/":return"/";case"b":return"\b";case"f":return"\f";case"n":return"\n";case"r":return"\r";case"t":return"\t";default:n(e,"invalid escape sequence")}throw new Error("unreachable")}))}else if(91===s)t.type=2,i++,c++;else if(123===s)t.type=3,i++,c++;else if(93===s)t.type=4,i++,c++;else if(125===s)t.type=5,i++,c++;else if(58===s)t.type=6,i++,c++;else if(44===s)t.type=7,i++,c++;else if(110===s){if(t.type=8,i++,c++,s=r.charCodeAt(i),117!==s)return!1;if(i++,c++,s=r.charCodeAt(i),108!==s)return!1;if(i++,c++,s=r.charCodeAt(i),108!==s)return!1;i++,c++}else if(116===s){if(t.type=9,i++,c++,s=r.charCodeAt(i),114!==s)return!1;if(i++,c++,s=r.charCodeAt(i),117!==s)return!1;if(i++,c++,s=r.charCodeAt(i),101!==s)return!1;i++,c++}else if(102===s){if(t.type=10,i++,c++,s=r.charCodeAt(i),97!==s)return!1;if(i++,c++,s=r.charCodeAt(i),108!==s)return!1;if(i++,c++,s=r.charCodeAt(i),115!==s)return!1;if(i++,c++,s=r.charCodeAt(i),101!==s)return!1;i++,c++}else for(t.type=11;;){if(i>=o)return!1;if(s=r.charCodeAt(i),!(46===s||s>=48&&s<=57||101===s||69===s||45===s||43===s))break;i++,c++}return t.len=i-t.offset,null===t.value&&(t.value=r.substr(t.offset,t.len)),e.pos=i,e.line=a,e.char=c,!0}},625:function(e,t,n){var s=this&&this.__createBinding||(Object.create?function(e,t,n,s){void 0===s&&(s=n),Object.defineProperty(e,s,{enumerable:!0,get:function(){return t[n]}})}:function(e,t,n,s){void 0===s&&(s=n),e[s]=t[n]}),r=this&&this.__exportStar||function(e,t){for(var n in e)"default"===n||Object.prototype.hasOwnProperty.call(t,n)||s(t,e,n)};Object.defineProperty(t,"__esModule",{value:!0}),t.applyStateStackDiff=t.diffStateStacksRefEq=t.parseRawGrammar=t.INITIAL=t.Registry=void 0;const i=n(752),o=n(150),a=n(583),c=n(63),l=n(784),u=n(151);Object.defineProperty(t,"applyStateStackDiff",{enumerable:!0,get:function(){return u.applyStateStackDiff}}),Object.defineProperty(t,"diffStateStacksRefEq",{enumerable:!0,get:function(){return u.diffStateStacksRefEq}}),r(n(810),t),t.Registry=class{constructor(e){this._options=e,this._syncRegistry=new a.SyncRegistry(c.Theme.createFromRawTheme(e.theme,e.colorMap),e.onigLib),this._ensureGrammarCache=new Map}dispose(){this._syncRegistry.dispose()}setTheme(e,t){this._syncRegistry.setTheme(c.Theme.createFromRawTheme(e,t))}getColorMap(){return this._syncRegistry.getColorMap()}loadGrammarWithEmbeddedLanguages(e,t,n){return this.loadGrammarWithConfiguration(e,t,{embeddedLanguages:n})}loadGrammarWithConfiguration(e,t,n){return this._loadGrammar(e,t,n.embeddedLanguages,n.tokenTypes,new i.BalancedBracketSelectors(n.balancedBracketSelectors||[],n.unbalancedBracketSelectors||[]))}loadGrammar(e){return this._loadGrammar(e,0,null,null,null)}async _loadGrammar(e,t,n,s,r){const i=new l.ScopeDependencyProcessor(this._syncRegistry,e);for(;i.Q.length>0;)await Promise.all(i.Q.map((e=>this._loadSingleGrammar(e.scopeName)))),i.processQueue();return this._grammarForScopeName(e,t,n,s,r)}async _loadSingleGrammar(e){return this._ensureGrammarCache.has(e)||this._ensureGrammarCache.set(e,this._doLoadSingleGrammar(e)),this._ensureGrammarCache.get(e)}async _doLoadSingleGrammar(e){const t=await this._options.loadGrammar(e);if(t){const n="function"==typeof this._options.getInjections?this._options.getInjections(e):void 0;this._syncRegistry.addGrammar(t,n)}}async addGrammar(e,t=[],n=0,s=null){return this._syncRegistry.addGrammar(e,t),await this._grammarForScopeName(e.scopeName,n,s)}_grammarForScopeName(e,t=0,n=null,s=null,r=null){return this._syncRegistry.grammarForScopeName(e,t,n,s,r)}},t.INITIAL=i.StateStackImpl.NULL,t.parseRawGrammar=o.parseRawGrammar},916:(e,t)=>{function n(e){return!!e&&!!e.match(/[\w\.:]+/)}Object.defineProperty(t,"__esModule",{value:!0}),t.createMatchers=void 0,t.createMatchers=function(e,t){const s=[],r=function(e){let t=/([LR]:|[\w\.:][\w\.:\-]*|[\,\|\-\(\)])/g,n=t.exec(e);return{next:()=>{if(!n)return null;const s=n[0];return n=t.exec(e),s}}}(e);let i=r.next();for(;null!==i;){let e=0;if(2===i.length&&":"===i.charAt(1)){switch(i.charAt(0)){case"R":e=1;break;case"L":e=-1;break;default:console.log(`Unknown priority ${i} in scope selector`)}i=r.next()}let t=a();if(s.push({matcher:t,priority:e}),","!==i)break;i=r.next()}return s;function o(){if("-"===i){i=r.next();const e=o();return t=>!!e&&!e(t)}if("("===i){i=r.next();const e=function(){const e=[];let t=a();for(;t&&(e.push(t),"|"===i||","===i);){do{i=r.next()}while("|"===i||","===i);t=a()}return t=>e.some((e=>e(t)))}();return")"===i&&(i=r.next()),e}if(n(i)){const e=[];do{e.push(i),i=r.next()}while(n(i));return n=>t(e,n)}return null}function a(){const e=[];let t=o();for(;t;)e.push(t),t=o();return t=>e.every((e=>e(t)))}}},810:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.disposeOnigString=void 0,t.disposeOnigString=function(e){"function"==typeof e.dispose&&e.dispose()}},150:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.parseRawGrammar=void 0;const s=n(578),r=n(185),i=n(726);t.parseRawGrammar=function(e,t=null){return null!==t&&/\.json$/.test(t)?(n=e,o=t,r.DebugFlags.InDebugMode?i.parseJSON(n,o,!0):JSON.parse(n)):function(e,t){return r.DebugFlags.InDebugMode?s.parseWithLocation(e,t,"$vscodeTextmateLocation"):s.parsePLIST(e)}(e,t);var n,o}},578:(e,t)=>{function n(e,t,n){const s=e.length;let r=0,i=1,o=0;function a(t){if(null===n)r+=t;else for(;t>0;)10===e.charCodeAt(r)?(r++,i++,o=0):(r++,o++),t--}function c(e){null===n?r=e:a(e-r)}function l(){for(;r<s;){let t=e.charCodeAt(r);if(32!==t&&9!==t&&13!==t&&10!==t)break;a(1)}}function u(t){return e.substr(r,t.length)===t&&(a(t.length),!0)}function h(t){let n=e.indexOf(t,r);c(-1!==n?n+t.length:s)}function p(t){let n=e.indexOf(t,r);if(-1!==n){let s=e.substring(r,n);return c(n+t.length),s}{let t=e.substr(r);return c(s),t}}s>0&&65279===e.charCodeAt(0)&&(r=1);let d=0,f=null,m=[],g=[],_=null;function b(e,t){m.push(d),g.push(f),d=e,f=t}function y(){if(0===m.length)return S("illegal state stack");d=m.pop(),f=g.pop()}function S(t){throw new Error("Near offset "+r+": "+t+" ~~~"+e.substr(r,50)+"~~~")}const k=function(){if(null===_)return S("missing <key>");let e={};null!==n&&(e[n]={filename:t,line:i,char:o}),f[_]=e,_=null,b(1,e)},C=function(){if(null===_)return S("missing <key>");let e=[];f[_]=e,_=null,b(2,e)},R=function(){let e={};null!==n&&(e[n]={filename:t,line:i,char:o}),f.push(e),b(1,e)},A=function(){let e=[];f.push(e),b(2,e)};function w(){if(1!==d)return S("unexpected </dict>");y()}function P(){return 1===d||2!==d?S("unexpected </array>"):void y()}function I(e){if(1===d){if(null===_)return S("missing <key>");f[_]=e,_=null}else 2===d?f.push(e):f=e}function v(e){if(isNaN(e))return S("cannot parse float");if(1===d){if(null===_)return S("missing <key>");f[_]=e,_=null}else 2===d?f.push(e):f=e}function N(e){if(isNaN(e))return S("cannot parse integer");if(1===d){if(null===_)return S("missing <key>");f[_]=e,_=null}else 2===d?f.push(e):f=e}function x(e){if(1===d){if(null===_)return S("missing <key>");f[_]=e,_=null}else 2===d?f.push(e):f=e}function T(e){if(1===d){if(null===_)return S("missing <key>");f[_]=e,_=null}else 2===d?f.push(e):f=e}function G(e){if(1===d){if(null===_)return S("missing <key>");f[_]=e,_=null}else 2===d?f.push(e):f=e}function E(){let e=p(">"),t=!1;return 47===e.charCodeAt(e.length-1)&&(t=!0,e=e.substring(0,e.length-1)),{name:e.trim(),isClosed:t}}function L(e){if(e.isClosed)return"";let t=p("</");return h(">"),t.replace(/&#([0-9]+);/g,(function(e,t){return String.fromCodePoint(parseInt(t,10))})).replace(/&#x([0-9a-f]+);/g,(function(e,t){return String.fromCodePoint(parseInt(t,16))})).replace(/&amp;|&lt;|&gt;|&quot;|&apos;/g,(function(e){switch(e){case"&amp;":return"&";case"&lt;":return"<";case"&gt;":return">";case"&quot;":return'"';case"&apos;":return"'"}return e}))}for(;r<s&&(l(),!(r>=s));){const c=e.charCodeAt(r);if(a(1),60!==c)return S("expected <");if(r>=s)return S("unexpected end of input");const p=e.charCodeAt(r);if(63===p){a(1),h("?>");continue}if(33===p){if(a(1),u("--")){h("--\x3e");continue}h(">");continue}if(47===p){if(a(1),l(),u("plist")){h(">");continue}if(u("dict")){h(">"),w();continue}if(u("array")){h(">"),P();continue}return S("unexpected closed tag")}let m=E();switch(m.name){case"dict":1===d?k():2===d?R():(f={},null!==n&&(f[n]={filename:t,line:i,char:o}),b(1,f)),m.isClosed&&w();continue;case"array":1===d?C():2===d?A():(f=[],b(2,f)),m.isClosed&&P();continue;case"key":M=L(m),1!==d?S("unexpected <key>"):null!==_?S("too many <key>"):_=M;continue;case"string":I(L(m));continue;case"real":v(parseFloat(L(m)));continue;case"integer":N(parseInt(L(m),10));continue;case"date":x(new Date(L(m)));continue;case"data":T(L(m));continue;case"true":L(m),G(!0);continue;case"false":L(m),G(!1);continue}if(!/^plist/.test(m.name))return S("unexpected opened tag "+m.name)}var M;return f}Object.defineProperty(t,"__esModule",{value:!0}),t.parsePLIST=t.parseWithLocation=void 0,t.parseWithLocation=function(e,t,s){return n(e,t,s)},t.parsePLIST=function(e){return n(e,null,null)}},583:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.SyncRegistry=void 0;const s=n(752);t.SyncRegistry=class{constructor(e,t){this._onigLibPromise=t,this._grammars=new Map,this._rawGrammars=new Map,this._injectionGrammars=new Map,this._theme=e}dispose(){for(const e of this._grammars.values())e.dispose()}setTheme(e){this._theme=e}getColorMap(){return this._theme.getColorMap()}addGrammar(e,t){this._rawGrammars.set(e.scopeName,e),t&&this._injectionGrammars.set(e.scopeName,t)}lookup(e){return this._rawGrammars.get(e)}injections(e){return this._injectionGrammars.get(e)}getDefaults(){return this._theme.getDefaults()}themeMatch(e){return this._theme.match(e)}async grammarForScopeName(e,t,n,r,i){if(!this._grammars.has(e)){let o=this._rawGrammars.get(e);if(!o)return null;this._grammars.set(e,s.createGrammar(e,o,t,n,r,i,this,await this._onigLibPromise))}return this._grammars.get(e)}}},666:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.CompiledRule=t.RegExpSourceList=t.RegExpSource=t.RuleFactory=t.BeginWhileRule=t.BeginEndRule=t.IncludeOnlyRule=t.MatchRule=t.CaptureRule=t.Rule=t.ruleIdToNumber=t.ruleIdFromNumber=t.whileRuleId=t.endRuleId=void 0;const s=n(807),r=n(784),i=/\\(\d+)/,o=/\\(\d+)/g;Symbol("RuleId"),t.endRuleId=-1,t.whileRuleId=-2,t.ruleIdFromNumber=function(e){return e},t.ruleIdToNumber=function(e){return e};class a{constructor(e,t,n,r){this.$location=e,this.id=t,this._name=n||null,this._nameIsCapturing=s.RegexSource.hasCaptures(this._name),this._contentName=r||null,this._contentNameIsCapturing=s.RegexSource.hasCaptures(this._contentName)}get debugName(){const e=this.$location?`${s.basename(this.$location.filename)}:${this.$location.line}`:"unknown";return`${this.constructor.name}#${this.id} @ ${e}`}getName(e,t){return this._nameIsCapturing&&null!==this._name&&null!==e&&null!==t?s.RegexSource.replaceCaptures(this._name,e,t):this._name}getContentName(e,t){return this._contentNameIsCapturing&&null!==this._contentName?s.RegexSource.replaceCaptures(this._contentName,e,t):this._contentName}}t.Rule=a;class c extends a{constructor(e,t,n,s,r){super(e,t,n,s),this.retokenizeCapturedWithRuleId=r}dispose(){}collectPatterns(e,t){throw new Error("Not supported!")}compile(e,t){throw new Error("Not supported!")}compileAG(e,t,n,s){throw new Error("Not supported!")}}t.CaptureRule=c;class l extends a{constructor(e,t,n,s,r){super(e,t,n,null),this._match=new f(s,this.id),this.captures=r,this._cachedCompiledPatterns=null}dispose(){this._cachedCompiledPatterns&&(this._cachedCompiledPatterns.dispose(),this._cachedCompiledPatterns=null)}get debugMatchRegExp(){return`${this._match.source}`}collectPatterns(e,t){t.push(this._match)}compile(e,t){return this._getCachedCompiledPatterns(e).compile(e)}compileAG(e,t,n,s){return this._getCachedCompiledPatterns(e).compileAG(e,n,s)}_getCachedCompiledPatterns(e){return this._cachedCompiledPatterns||(this._cachedCompiledPatterns=new m,this.collectPatterns(e,this._cachedCompiledPatterns)),this._cachedCompiledPatterns}}t.MatchRule=l;class u extends a{constructor(e,t,n,s,r){super(e,t,n,s),this.patterns=r.patterns,this.hasMissingPatterns=r.hasMissingPatterns,this._cachedCompiledPatterns=null}dispose(){this._cachedCompiledPatterns&&(this._cachedCompiledPatterns.dispose(),this._cachedCompiledPatterns=null)}collectPatterns(e,t){for(const n of this.patterns)e.getRule(n).collectPatterns(e,t)}compile(e,t){return this._getCachedCompiledPatterns(e).compile(e)}compileAG(e,t,n,s){return this._getCachedCompiledPatterns(e).compileAG(e,n,s)}_getCachedCompiledPatterns(e){return this._cachedCompiledPatterns||(this._cachedCompiledPatterns=new m,this.collectPatterns(e,this._cachedCompiledPatterns)),this._cachedCompiledPatterns}}t.IncludeOnlyRule=u;class h extends a{constructor(e,t,n,s,r,i,o,a,c,l){super(e,t,n,s),this._begin=new f(r,this.id),this.beginCaptures=i,this._end=new f(o||"￿",-1),this.endHasBackReferences=this._end.hasBackReferences,this.endCaptures=a,this.applyEndPatternLast=c||!1,this.patterns=l.patterns,this.hasMissingPatterns=l.hasMissingPatterns,this._cachedCompiledPatterns=null}dispose(){this._cachedCompiledPatterns&&(this._cachedCompiledPatterns.dispose(),this._cachedCompiledPatterns=null)}get debugBeginRegExp(){return`${this._begin.source}`}get debugEndRegExp(){return`${this._end.source}`}getEndWithResolvedBackReferences(e,t){return this._end.resolveBackReferences(e,t)}collectPatterns(e,t){t.push(this._begin)}compile(e,t){return this._getCachedCompiledPatterns(e,t).compile(e)}compileAG(e,t,n,s){return this._getCachedCompiledPatterns(e,t).compileAG(e,n,s)}_getCachedCompiledPatterns(e,t){if(!this._cachedCompiledPatterns){this._cachedCompiledPatterns=new m;for(const t of this.patterns)e.getRule(t).collectPatterns(e,this._cachedCompiledPatterns);this.applyEndPatternLast?this._cachedCompiledPatterns.push(this._end.hasBackReferences?this._end.clone():this._end):this._cachedCompiledPatterns.unshift(this._end.hasBackReferences?this._end.clone():this._end)}return this._end.hasBackReferences&&(this.applyEndPatternLast?this._cachedCompiledPatterns.setSource(this._cachedCompiledPatterns.length()-1,t):this._cachedCompiledPatterns.setSource(0,t)),this._cachedCompiledPatterns}}t.BeginEndRule=h;class p extends a{constructor(e,n,s,r,i,o,a,c,l){super(e,n,s,r),this._begin=new f(i,this.id),this.beginCaptures=o,this.whileCaptures=c,this._while=new f(a,t.whileRuleId),this.whileHasBackReferences=this._while.hasBackReferences,this.patterns=l.patterns,this.hasMissingPatterns=l.hasMissingPatterns,this._cachedCompiledPatterns=null,this._cachedCompiledWhilePatterns=null}dispose(){this._cachedCompiledPatterns&&(this._cachedCompiledPatterns.dispose(),this._cachedCompiledPatterns=null),this._cachedCompiledWhilePatterns&&(this._cachedCompiledWhilePatterns.dispose(),this._cachedCompiledWhilePatterns=null)}get debugBeginRegExp(){return`${this._begin.source}`}get debugWhileRegExp(){return`${this._while.source}`}getWhileWithResolvedBackReferences(e,t){return this._while.resolveBackReferences(e,t)}collectPatterns(e,t){t.push(this._begin)}compile(e,t){return this._getCachedCompiledPatterns(e).compile(e)}compileAG(e,t,n,s){return this._getCachedCompiledPatterns(e).compileAG(e,n,s)}_getCachedCompiledPatterns(e){if(!this._cachedCompiledPatterns){this._cachedCompiledPatterns=new m;for(const t of this.patterns)e.getRule(t).collectPatterns(e,this._cachedCompiledPatterns)}return this._cachedCompiledPatterns}compileWhile(e,t){return this._getCachedCompiledWhilePatterns(e,t).compile(e)}compileWhileAG(e,t,n,s){return this._getCachedCompiledWhilePatterns(e,t).compileAG(e,n,s)}_getCachedCompiledWhilePatterns(e,t){return this._cachedCompiledWhilePatterns||(this._cachedCompiledWhilePatterns=new m,this._cachedCompiledWhilePatterns.push(this._while.hasBackReferences?this._while.clone():this._while)),this._while.hasBackReferences&&this._cachedCompiledWhilePatterns.setSource(0,t||"￿"),this._cachedCompiledWhilePatterns}}t.BeginWhileRule=p;class d{static createCaptureRule(e,t,n,s,r){return e.registerRule((e=>new c(t,e,n,s,r)))}static getCompiledRuleId(e,t,n){return e.id||t.registerRule((r=>{if(e.id=r,e.match)return new l(e.$vscodeTextmateLocation,e.id,e.name,e.match,d._compileCaptures(e.captures,t,n));if(void 0===e.begin){e.repository&&(n=s.mergeObjects({},n,e.repository));let r=e.patterns;return void 0===r&&e.include&&(r=[{include:e.include}]),new u(e.$vscodeTextmateLocation,e.id,e.name,e.contentName,d._compilePatterns(r,t,n))}return e.while?new p(e.$vscodeTextmateLocation,e.id,e.name,e.contentName,e.begin,d._compileCaptures(e.beginCaptures||e.captures,t,n),e.while,d._compileCaptures(e.whileCaptures||e.captures,t,n),d._compilePatterns(e.patterns,t,n)):new h(e.$vscodeTextmateLocation,e.id,e.name,e.contentName,e.begin,d._compileCaptures(e.beginCaptures||e.captures,t,n),e.end,d._compileCaptures(e.endCaptures||e.captures,t,n),e.applyEndPatternLast,d._compilePatterns(e.patterns,t,n))})),e.id}static _compileCaptures(e,t,n){let s=[];if(e){let r=0;for(const t in e){if("$vscodeTextmateLocation"===t)continue;const e=parseInt(t,10);e>r&&(r=e)}for(let e=0;e<=r;e++)s[e]=null;for(const r in e){if("$vscodeTextmateLocation"===r)continue;const i=parseInt(r,10);let o=0;e[r].patterns&&(o=d.getCompiledRuleId(e[r],t,n)),s[i]=d.createCaptureRule(t,e[r].$vscodeTextmateLocation,e[r].name,e[r].contentName,o)}}return s}static _compilePatterns(e,t,n){let s=[];if(e)for(let i=0,o=e.length;i<o;i++){const o=e[i];let a=-1;if(o.include){const e=r.parseInclude(o.include);switch(e.kind){case 0:case 1:a=d.getCompiledRuleId(n[o.include],t,n);break;case 2:let s=n[e.ruleName];s&&(a=d.getCompiledRuleId(s,t,n));break;case 3:case 4:const r=e.scopeName,i=4===e.kind?e.ruleName:null,c=t.getExternalGrammar(r,n);if(c)if(i){let e=c.repository[i];e&&(a=d.getCompiledRuleId(e,t,c.repository))}else a=d.getCompiledRuleId(c.repository.$self,t,c.repository)}}else a=d.getCompiledRuleId(o,t,n);if(-1!==a){const e=t.getRule(a);let n=!1;if((e instanceof u||e instanceof h||e instanceof p)&&e.hasMissingPatterns&&0===e.patterns.length&&(n=!0),n)continue;s.push(a)}}return{patterns:s,hasMissingPatterns:(e?e.length:0)!==s.length}}}t.RuleFactory=d;class f{constructor(e,t){if(e){const t=e.length;let n=0,s=[],r=!1;for(let i=0;i<t;i++)if("\\"===e.charAt(i)&&i+1<t){const t=e.charAt(i+1);"z"===t?(s.push(e.substring(n,i)),s.push("$(?!\\n)(?<!\\n)"),n=i+2):"A"!==t&&"G"!==t||(r=!0),i++}this.hasAnchor=r,0===n?this.source=e:(s.push(e.substring(n,t)),this.source=s.join(""))}else this.hasAnchor=!1,this.source=e;this.hasAnchor?this._anchorCache=this._buildAnchorCache():this._anchorCache=null,this.ruleId=t,this.hasBackReferences=i.test(this.source)}clone(){return new f(this.source,this.ruleId)}setSource(e){this.source!==e&&(this.source=e,this.hasAnchor&&(this._anchorCache=this._buildAnchorCache()))}resolveBackReferences(e,t){let n=t.map((t=>e.substring(t.start,t.end)));return o.lastIndex=0,this.source.replace(o,((e,t)=>s.escapeRegExpCharacters(n[parseInt(t,10)]||"")))}_buildAnchorCache(){let e,t,n,s,r=[],i=[],o=[],a=[];for(e=0,t=this.source.length;e<t;e++)n=this.source.charAt(e),r[e]=n,i[e]=n,o[e]=n,a[e]=n,"\\"===n&&e+1<t&&(s=this.source.charAt(e+1),"A"===s?(r[e+1]="￿",i[e+1]="￿",o[e+1]="A",a[e+1]="A"):"G"===s?(r[e+1]="￿",i[e+1]="G",o[e+1]="￿",a[e+1]="G"):(r[e+1]=s,i[e+1]=s,o[e+1]=s,a[e+1]=s),e++);return{A0_G0:r.join(""),A0_G1:i.join(""),A1_G0:o.join(""),A1_G1:a.join("")}}resolveAnchors(e,t){return this.hasAnchor&&this._anchorCache?e?t?this._anchorCache.A1_G1:this._anchorCache.A1_G0:t?this._anchorCache.A0_G1:this._anchorCache.A0_G0:this.source}}t.RegExpSource=f;class m{constructor(){this._items=[],this._hasAnchors=!1,this._cached=null,this._anchorCache={A0_G0:null,A0_G1:null,A1_G0:null,A1_G1:null}}dispose(){this._disposeCaches()}_disposeCaches(){this._cached&&(this._cached.dispose(),this._cached=null),this._anchorCache.A0_G0&&(this._anchorCache.A0_G0.dispose(),this._anchorCache.A0_G0=null),this._anchorCache.A0_G1&&(this._anchorCache.A0_G1.dispose(),this._anchorCache.A0_G1=null),this._anchorCache.A1_G0&&(this._anchorCache.A1_G0.dispose(),this._anchorCache.A1_G0=null),this._anchorCache.A1_G1&&(this._anchorCache.A1_G1.dispose(),this._anchorCache.A1_G1=null)}push(e){this._items.push(e),this._hasAnchors=this._hasAnchors||e.hasAnchor}unshift(e){this._items.unshift(e),this._hasAnchors=this._hasAnchors||e.hasAnchor}length(){return this._items.length}setSource(e,t){this._items[e].source!==t&&(this._disposeCaches(),this._items[e].setSource(t))}compile(e){if(!this._cached){let t=this._items.map((e=>e.source));this._cached=new g(e,t,this._items.map((e=>e.ruleId)))}return this._cached}compileAG(e,t,n){return this._hasAnchors?t?n?(this._anchorCache.A1_G1||(this._anchorCache.A1_G1=this._resolveAnchors(e,t,n)),this._anchorCache.A1_G1):(this._anchorCache.A1_G0||(this._anchorCache.A1_G0=this._resolveAnchors(e,t,n)),this._anchorCache.A1_G0):n?(this._anchorCache.A0_G1||(this._anchorCache.A0_G1=this._resolveAnchors(e,t,n)),this._anchorCache.A0_G1):(this._anchorCache.A0_G0||(this._anchorCache.A0_G0=this._resolveAnchors(e,t,n)),this._anchorCache.A0_G0):this.compile(e)}_resolveAnchors(e,t,n){let s=this._items.map((e=>e.resolveAnchors(t,n)));return new g(e,s,this._items.map((e=>e.ruleId)))}}t.RegExpSourceList=m;class g{constructor(e,t,n){this.regExps=t,this.rules=n,this.scanner=e.createOnigScanner(t)}dispose(){"function"==typeof this.scanner.dispose&&this.scanner.dispose()}toString(){const e=[];for(let t=0,n=this.rules.length;t<n;t++)e.push("   - "+this.rules[t]+": "+this.regExps[t]);return e.join("\n")}findNextMatchSync(e,t,n){const s=this.scanner.findNextMatchSync(e,t,n);return s?{ruleId:this.rules[s.index],captureIndices:s.captureIndices}:null}}t.CompiledRule=g},63:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ThemeTrieElement=t.ThemeTrieElementRule=t.ColorMap=t.fontStyleToString=t.ParsedThemeRule=t.parseTheme=t.StyleAttributes=t.ScopeStack=t.Theme=void 0;const s=n(807);class r{constructor(e,t,n){this._colorMap=e,this._defaults=t,this._root=n,this._cachedMatchRoot=new s.CachedFn((e=>this._root.match(e)))}static createFromRawTheme(e,t){return this.createFromParsedTheme(c(e),t)}static createFromParsedTheme(e,t){return function(e,t){e.sort(((e,t)=>{let n=s.strcmp(e.scope,t.scope);return 0!==n?n:(n=s.strArrCmp(e.parentScopes,t.parentScopes),0!==n?n:e.index-t.index)}));let n=0,i="#000000",o="#ffffff";for(;e.length>=1&&""===e[0].scope;){let t=e.shift();-1!==t.fontStyle&&(n=t.fontStyle),null!==t.foreground&&(i=t.foreground),null!==t.background&&(o=t.background)}let c=new u(t),l=new a(n,c.getId(i),c.getId(o)),h=new d(new p(0,null,-1,0,0),[]);for(let t=0,n=e.length;t<n;t++){let n=e[t];h.insert(0,n.scope,n.parentScopes,n.fontStyle,c.getId(n.foreground),c.getId(n.background))}return new r(c,l,h)}(e,t)}getColorMap(){return this._colorMap.getColorMap()}getDefaults(){return this._defaults}match(e){if(null===e)return this._defaults;const t=e.scopeName,n=this._cachedMatchRoot.get(t).find((t=>function(e,t){if(0===t.length)return!0;for(let n=0;n<t.length;n++){let s=t[n],r=!1;if(">"===s){if(n===t.length-1)return!1;s=t[++n],r=!0}for(;e&&!o(e.scopeName,s);){if(r)return!1;e=e.parent}if(!e)return!1;e=e.parent}return!0}(e.parent,t.parentScopes)));return n?new a(n.fontStyle,n.foreground,n.background):null}}t.Theme=r;class i{constructor(e,t){this.parent=e,this.scopeName=t}static push(e,t){for(const n of t)e=new i(e,n);return e}static from(...e){let t=null;for(let n=0;n<e.length;n++)t=new i(t,e[n]);return t}push(e){return new i(this,e)}getSegments(){let e=this;const t=[];for(;e;)t.push(e.scopeName),e=e.parent;return t.reverse(),t}toString(){return this.getSegments().join(" ")}extends(e){return this===e||null!==this.parent&&this.parent.extends(e)}getExtensionIfDefined(e){const t=[];let n=this;for(;n&&n!==e;)t.push(n.scopeName),n=n.parent;return n===e?t.reverse():void 0}}function o(e,t){return t===e||e.startsWith(t)&&"."===e[t.length]}t.ScopeStack=i;class a{constructor(e,t,n){this.fontStyle=e,this.foregroundId=t,this.backgroundId=n}}function c(e){if(!e)return[];if(!e.settings||!Array.isArray(e.settings))return[];let t=e.settings,n=[],r=0;for(let e=0,i=t.length;e<i;e++){let i,o=t[e];if(!o.settings)continue;if("string"==typeof o.scope){let e=o.scope;e=e.replace(/^[,]+/,""),e=e.replace(/[,]+$/,""),i=e.split(",")}else i=Array.isArray(o.scope)?o.scope:[""];let a=-1;if("string"==typeof o.settings.fontStyle){a=0;let e=o.settings.fontStyle.split(" ");for(let t=0,n=e.length;t<n;t++)switch(e[t]){case"italic":a|=1;break;case"bold":a|=2;break;case"underline":a|=4;break;case"strikethrough":a|=8}}let c=null;"string"==typeof o.settings.foreground&&s.isValidHexColor(o.settings.foreground)&&(c=o.settings.foreground);let u=null;"string"==typeof o.settings.background&&s.isValidHexColor(o.settings.background)&&(u=o.settings.background);for(let t=0,s=i.length;t<s;t++){let s=i[t].trim().split(" "),o=s[s.length-1],h=null;s.length>1&&(h=s.slice(0,s.length-1),h.reverse()),n[r++]=new l(o,h,e,a,c,u)}}return n}t.StyleAttributes=a,t.parseTheme=c;class l{constructor(e,t,n,s,r,i){this.scope=e,this.parentScopes=t,this.index=n,this.fontStyle=s,this.foreground=r,this.background=i}}t.ParsedThemeRule=l,t.fontStyleToString=function(e){if(-1===e)return"not set";let t="";return 1&e&&(t+="italic "),2&e&&(t+="bold "),4&e&&(t+="underline "),8&e&&(t+="strikethrough "),""===t&&(t="none"),t.trim()};class u{constructor(e){if(this._lastColorId=0,this._id2color=[],this._color2id=Object.create(null),Array.isArray(e)){this._isFrozen=!0;for(let t=0,n=e.length;t<n;t++)this._color2id[e[t]]=t,this._id2color[t]=e[t]}else this._isFrozen=!1}getId(e){if(null===e)return 0;e=e.toUpperCase();let t=this._color2id[e];if(t)return t;if(this._isFrozen)throw new Error(`Missing color in color map - ${e}`);return t=++this._lastColorId,this._color2id[e]=t,this._id2color[t]=e,t}getColorMap(){return this._id2color.slice(0)}}t.ColorMap=u;const h=Object.freeze([]);class p{constructor(e,t,n,s,r){this.scopeDepth=e,this.parentScopes=t||h,this.fontStyle=n,this.foreground=s,this.background=r}clone(){return new p(this.scopeDepth,this.parentScopes,this.fontStyle,this.foreground,this.background)}static cloneArr(e){let t=[];for(let n=0,s=e.length;n<s;n++)t[n]=e[n].clone();return t}acceptOverwrite(e,t,n,s){this.scopeDepth>e?console.log("how did this happen?"):this.scopeDepth=e,-1!==t&&(this.fontStyle=t),0!==n&&(this.foreground=n),0!==s&&(this.background=s)}}t.ThemeTrieElementRule=p;class d{constructor(e,t=[],n={}){this._mainRule=e,this._children=n,this._rulesWithParentScopes=t}static _cmpBySpecificity(e,t){if(e.scopeDepth!==t.scopeDepth)return t.scopeDepth-e.scopeDepth;let n=0,s=0;for(;">"===e.parentScopes[n]&&n++,">"===t.parentScopes[s]&&s++,!(n>=e.parentScopes.length||s>=t.parentScopes.length);){const r=t.parentScopes[s].length-e.parentScopes[n].length;if(0!==r)return r;n++,s++}return t.parentScopes.length-e.parentScopes.length}match(e){if(""!==e){let t,n,s=e.indexOf(".");if(-1===s?(t=e,n=""):(t=e.substring(0,s),n=e.substring(s+1)),this._children.hasOwnProperty(t))return this._children[t].match(n)}const t=this._rulesWithParentScopes.concat(this._mainRule);return t.sort(d._cmpBySpecificity),t}insert(e,t,n,s,r,i){if(""===t)return void this._doInsertHere(e,n,s,r,i);let o,a,c,l=t.indexOf(".");-1===l?(o=t,a=""):(o=t.substring(0,l),a=t.substring(l+1)),this._children.hasOwnProperty(o)?c=this._children[o]:(c=new d(this._mainRule.clone(),p.cloneArr(this._rulesWithParentScopes)),this._children[o]=c),c.insert(e+1,a,n,s,r,i)}_doInsertHere(e,t,n,r,i){if(null!==t){for(let o=0,a=this._rulesWithParentScopes.length;o<a;o++){let a=this._rulesWithParentScopes[o];if(0===s.strArrCmp(a.parentScopes,t))return void a.acceptOverwrite(e,n,r,i)}-1===n&&(n=this._mainRule.fontStyle),0===r&&(r=this._mainRule.foreground),0===i&&(i=this._mainRule.background),this._rulesWithParentScopes.push(new p(e,t,n,r,i))}else this._mainRule.acceptOverwrite(e,n,r,i)}}t.ThemeTrieElement=d},807:(e,t)=>{function n(e){return Array.isArray(e)?function(e){let t=[];for(let s=0,r=e.length;s<r;s++)t[s]=n(e[s]);return t}(e):"object"==typeof e?function(e){let t={};for(let s in e)t[s]=n(e[s]);return t}(e):e}Object.defineProperty(t,"__esModule",{value:!0}),t.performanceNow=t.CachedFn=t.escapeRegExpCharacters=t.isValidHexColor=t.strArrCmp=t.strcmp=t.RegexSource=t.basename=t.mergeObjects=t.clone=void 0,t.clone=function(e){return n(e)},t.mergeObjects=function(e,...t){return t.forEach((t=>{for(let n in t)e[n]=t[n]})),e},t.basename=function e(t){const n=~t.lastIndexOf("/")||~t.lastIndexOf("\\");return 0===n?t:~n==t.length-1?e(t.substring(0,t.length-1)):t.substr(1+~n)};let s=/\$(\d+)|\${(\d+):\/(downcase|upcase)}/g;function r(e,t){return e<t?-1:e>t?1:0}t.RegexSource=class{static hasCaptures(e){return null!==e&&(s.lastIndex=0,s.test(e))}static replaceCaptures(e,t,n){return e.replace(s,((e,s,r,i)=>{let o=n[parseInt(s||r,10)];if(!o)return e;{let e=t.substring(o.start,o.end);for(;"."===e[0];)e=e.substring(1);switch(i){case"downcase":return e.toLowerCase();case"upcase":return e.toUpperCase();default:return e}}}))}},t.strcmp=r,t.strArrCmp=function(e,t){if(null===e&&null===t)return 0;if(!e)return-1;if(!t)return 1;let n=e.length,s=t.length;if(n===s){for(let s=0;s<n;s++){let n=r(e[s],t[s]);if(0!==n)return n}return 0}return n-s},t.isValidHexColor=function(e){return!!(/^#[0-9a-f]{6}$/i.test(e)||/^#[0-9a-f]{8}$/i.test(e)||/^#[0-9a-f]{3}$/i.test(e)||/^#[0-9a-f]{4}$/i.test(e))},t.escapeRegExpCharacters=function(e){return e.replace(/[\-\\\{\}\*\+\?\|\^\$\.\,\[\]\(\)\#\s]/g,"\\$&")},t.CachedFn=class{constructor(e){this.fn=e,this.cache=new Map}get(e){if(this.cache.has(e))return this.cache.get(e);const t=this.fn(e);return this.cache.set(e,t),t}},t.performanceNow="undefined"==typeof performance?function(){return Date.now()}:function(){return performance.now()}}},t={};return function n(s){var r=t[s];if(void 0!==r)return r.exports;var i=t[s]={exports:{}};return e[s].call(i.exports,i,i.exports,n),i.exports}(625)})()));//# sourceMappingURL=https://main.vscode-cdn.net/sourcemaps/360a4e4fd251bfce169a4ddf857c7d25d1ad40da/node_modules/vscode-textmate/release/main.js.map