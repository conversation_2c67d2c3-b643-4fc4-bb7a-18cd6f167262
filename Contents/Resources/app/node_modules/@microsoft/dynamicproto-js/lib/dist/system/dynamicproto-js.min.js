/*!
 * Microsoft Dynamic Proto Utility, 1.1.9
 * Copyright (c) Microsoft and contributors. All rights reserved.
 */
System.register("Microsoft.DynamicProto-JS",[],function(c){"use strict";return{execute:function(){c("default",B);var n,t="undefined",o="constructor",d="prototype",h="function",v="_dynInstFuncs",_="_isDynProxy",g="_dynClass",w="_dynCls$",P="_dynInstChk",b=P,m="_dfOpts",r="_unknown_",e="__proto__",i="_dyn"+e,u="__dynProto$Gbl",f="_dynInstProto",I="useBaseInst",O="setInstFuncs",s=Object,C=s.getPrototypeOf,a=s.getOwnPropertyNames,t=(n=(n=(n=(n=typeof globalThis!=t?globalThis:n)||typeof self==t?n:self)||typeof window==t?n:window)||typeof global==t?n:global)||{},k=t[u]||(t[u]={o:((n={})[O]=!0,n[I]=!0,n),n:1e3});function D(n,t){return n&&s[d].hasOwnProperty.call(n,t)}function F(n){return n&&(n===s[d]||n===Array[d])}function T(n){return F(n)||n===Function[d]}function $(n){if(n){if(C)return C(n);var t=n[e]||n[d]||(n[o]?n[o][d]:null),r=n[i]||t;D(n,i)||(delete n[f],r=n[i]=n[f]||n[i],n[f]=t)}return r}function x(n,t){var r=[];if(a)r=a(n);else for(var o in n)"string"==typeof o&&D(n,o)&&r.push(o);if(r&&0<r.length)for(var e=0;e<r.length;e++)t(r[e])}function M(n,t,r){return t!==o&&typeof n[t]===h&&(r||D(n,t))}function S(n){throw new TypeError("DynamicProto: "+n)}function j(n,t){for(var r=n.length-1;0<=r;r--)if(n[r]===t)return 1}function A(n,t){return D(n,d)?n.name||t||r:((n||{})[o]||{}).name||t||r}function B(n,o,t,r){D(n,d)||S("theClass is an invalid class definition.");var e,i,u,f,s,a,c=n[d],l=(function(n){if(!C)return 1;for(var t=[],r=$(o);r&&!T(r)&&!j(t,r);){if(r===n)return 1;t.push(r),r=$(r)}}(c)||S("["+A(n)+"] not in hierarchy of ["+A(o)+"]"),null),n=(D(c,g)?l=c[g]:(l=w+A(n,"_")+"$"+k.n,k.n++,c[g]=l),B[m]),y=!!n[I],p=(y&&r&&r[I]!==undefined&&(y=!!r[I]),i={},x(e=o,function(n){!i[n]&&M(e,n,!1)&&(i[n]=e[n])}),i),y=(t(o,function(n,t,r,i){function o(n,t,r){var o,e=t[r];return e[_]&&i&&!1!==(o=n[v]||{})[b]&&(e=(o[t[g]]||{})[r]||e),function(){return e.apply(n,arguments)}}for(var e={},u=(x(r,function(n){e[n]=o(t,r,n)}),$(n)),f=[];u&&!T(u)&&!j(f,u);)x(u,function(n){!e[n]&&M(u,n,!C)&&(e[n]=o(t,u,n))}),f.push(u),u=$(u);return e}(c,o,p,y)),!!C&&!!n[O]);u=c,t=l,f=o,s=p,n=0!=(y&&r?!!r[O]:y),F(u)||(c=f[v]=f[v]||{},a=c[t]=c[t]||{},!1!==c[b]&&(c[b]=!!n),x(f,function(n){var r,o,e;M(f,n,!1)&&f[n]!==s[n]&&(a[n]=f[n],delete f[n],D(u,n)&&(!u[n]||u[n][_])||(u[n]=(r=u,o=n,(e=function(){var n,t;return(function(n,t,r,o){var e=null;if(n&&D(r,g)){var i=n[v]||{};if((e=(i[r[g]]||{})[t])||S("Missing ["+t+"] "+h),!e[P]&&!1!==i[b]){for(var u=!D(n,t),f=$(n),s=[];u&&f&&!T(f)&&!j(s,f);){var a=f[t];if(a){u=a===o;break}s.push(f),f=$(f)}try{u&&(n[t]=e),e[P]=1}catch(c){i[b]=!1}}}return e}(this,o,r,e)||(typeof(t=(t=r[n=o])===e?$(r)[n]:t)!==h&&S("["+n+"] is not a "+h),t)).apply(this,arguments)})[_]=1,e)))}))}B[m]=k.o}}});//# sourceMappingURL=https://main.vscode-cdn.net/sourcemaps/360a4e4fd251bfce169a4ddf857c7d25d1ad40da/node_modules/@microsoft/dynamicproto-js/lib/dist/system/dynamicproto-js.min.js.map
