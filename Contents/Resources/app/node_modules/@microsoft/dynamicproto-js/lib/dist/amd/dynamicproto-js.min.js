/*!
 * Microsoft Dynamic Proto Utility, 1.1.9
 * Copyright (c) Microsoft and contributors. All rights reserved.
 */
define(function(){"use strict";var n,t="undefined",o="constructor",d="prototype",h="function",v="_dynInstFuncs",_="_isDynProxy",g="_dynClass",w="_dynInstChk",b=w,P="_dfOpts",r="_unknown_",e="__proto__",i="_dyn"+e,f="__dynProto$Gbl",u="_dynInstProto",I="useBaseInst",O="setInstFuncs",s=Object,m=s.getPrototypeOf,a=s.getOwnPropertyNames,t=(n=(n=(n=(n=typeof globalThis!=t?globalThis:n)||typeof self==t?n:self)||typeof window==t?n:window)||typeof global==t?n:global)||{},C=t[f]||(t[f]={o:((n={})[O]=!0,n[I]=!0,n),n:1e3});function k(n,t){return n&&s[d].hasOwnProperty.call(n,t)}function F(n){return n&&(n===s[d]||n===Array[d])}function T(n){return F(n)||n===Function[d]}function $(n){if(n){if(m)return m(n);var t=n[e]||n[d]||(n[o]?n[o][d]:null),r=n[i]||t;k(n,i)||(delete n[u],r=n[i]=n[u]||n[i],n[u]=t)}return r}function D(n,t){var r=[];if(a)r=a(n);else for(var o in n)"string"==typeof o&&k(n,o)&&r.push(o);if(r&&0<r.length)for(var e=0;e<r.length;e++)t(r[e])}function j(n,t,r){return t!==o&&typeof n[t]===h&&(r||k(n,t))}function x(n){throw new TypeError("DynamicProto: "+n)}function A(n,t){for(var r=n.length-1;0<=r;r--)if(n[r]===t)return 1}function B(n,t){return k(n,d)?n.name||t||r:((n||{})[o]||{}).name||t||r}function E(n,o,t,r){k(n,d)||x("theClass is an invalid class definition.");var e,i,f,u,s,a,c=n[d],l=(function(n){if(!m)return 1;for(var t=[],r=$(o);r&&!T(r)&&!A(t,r);){if(r===n)return 1;t.push(r),r=$(r)}}(c)||x("["+B(n)+"] not in hierarchy of ["+B(o)+"]"),null),n=(k(c,g)?l=c[g]:(l="_dynCls$"+B(n,"_")+"$"+C.n,C.n++,c[g]=l),E[P]),y=!!n[I],p=(y&&r&&r[I]!==undefined&&(y=!!r[I]),i={},D(e=o,function(n){!i[n]&&j(e,n,!1)&&(i[n]=e[n])}),i),y=(t(o,function(n,t,r,i){function o(n,t,r){var o,e=t[r];return e[_]&&i&&!1!==(o=n[v]||{})[b]&&(e=(o[t[g]]||{})[r]||e),function(){return e.apply(n,arguments)}}for(var e={},f=(D(r,function(n){e[n]=o(t,r,n)}),$(n)),u=[];f&&!T(f)&&!A(u,f);)D(f,function(n){!e[n]&&j(f,n,!m)&&(e[n]=o(t,f,n))}),u.push(f),f=$(f);return e}(c,o,p,y)),!!m&&!!n[O]);f=c,t=l,u=o,s=p,n=0!=(y&&r?!!r[O]:y),F(f)||(c=u[v]=u[v]||{},a=c[t]=c[t]||{},!1!==c[b]&&(c[b]=!!n),D(u,function(n){var r,o,e;j(u,n,!1)&&u[n]!==s[n]&&(a[n]=u[n],delete u[n],k(f,n)&&(!f[n]||f[n][_])||(f[n]=(r=f,o=n,(e=function(){var n,t;return(function(n,t,r,o){var e=null;if(n&&k(r,g)){var i=n[v]||{};if((e=(i[r[g]]||{})[t])||x("Missing ["+t+"] "+h),!e[w]&&!1!==i[b]){for(var f=!k(n,t),u=$(n),s=[];f&&u&&!T(u)&&!A(s,u);){var a=u[t];if(a){f=a===o;break}s.push(u),u=$(u)}try{f&&(n[t]=e),e[w]=1}catch(c){i[b]=!1}}}return e}(this,o,r,e)||(typeof(t=(t=r[n=o])===e?$(r)[n]:t)!==h&&x("["+n+"] is not a "+h),t)).apply(this,arguments)})[_]=1,e)))}))}return E[P]=C.o,E});//# sourceMappingURL=https://main.vscode-cdn.net/sourcemaps/360a4e4fd251bfce169a4ddf857c7d25d1ad40da/node_modules/@microsoft/dynamicproto-js/lib/dist/amd/dynamicproto-js.min.js.map
