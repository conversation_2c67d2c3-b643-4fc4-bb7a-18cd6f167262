/*!
 * 1DS JS SDK POST plugin, 3.2.13
 * Copyright (c) Microsoft and contributors. All rights reserved.
 * (Microsoft Internal Only)
 */
"use strict";var n,e=function(c){!function(n,e,t){var r=Object.defineProperty;if(r)try{return r(n,e,t)}catch(i){}typeof t.value!==undefined&&(n[e]=t.value)}(c,"__esModule",{value:!0});var l="function",V="object",G="undefined",u="prototype",f="hasOwnProperty",t=Object,T=t[u],C=t.assign,n=(t.create,t.defineProperty),x=T[f],E=null;function P(n){n=!1===(n=void 0===n||n)?null:E;return n||((n=(n=(n=typeof globalThis!==G?globalThis:n)||typeof self===G?n:self)||typeof window===G?n:window)||typeof global===G||(n=global),E=n),n}function R(n){throw new TypeError(n)}(P()||{}).Symbol,(P()||{}).Reflect;var k=function(n,e){return(k=t.setPrototypeOf||({__proto__:[]}instanceof Array?function(n,e){n.__proto__=e}:function(n,e){for(var t in e)e[f](t)&&(n[t]=e[t])}))(n,e)},e="undefined",O="constructor",h="prototype",M="function",K="_dynInstFuncs",A="_isDynProxy",I="_dynClass",L="_dynInstChk",B=L,N="_dfOpts",H="_unknown_",D="__proto__",q="_dyn"+D,r="__dynProto$Gbl",U="_dynInstProto",z="useBaseInst",F="setInstFuncs",j=Object,X=j.getPrototypeOf,Q=j.getOwnPropertyNames,e=(o=(o=(o=(o=typeof globalThis!=e?globalThis:o)||typeof self==e?o:self)||typeof window==e?o:window)||typeof global==e?o:global)||{},J=e[r]||(e[r]={o:((o={})[F]=!0,o[z]=!0,o),n:1e3});function p(n,e){return n&&j[h].hasOwnProperty.call(n,e)}function $(n){return n&&(n===j[h]||n===Array[h])}function W(n){return $(n)||n===Function[h]}function dn(n){if(n){if(X)return X(n);var e=n[D]||n[h]||(n[O]?n[O][h]:null),t=n[q]||e;p(n,q)||(delete n[U],t=n[q]=n[U]||n[q],n[U]=e)}return t}function vn(n,e){var t=[];if(Q)t=Q(n);else for(var r in n)"string"==typeof r&&p(n,r)&&t.push(r);if(t&&0<t.length)for(var i=0;i<t.length;i++)e(t[i])}function hn(n,e,t){return e!==O&&typeof n[e]===M&&(t||p(n,e))}function pn(n){throw new TypeError("DynamicProto: "+n)}function yn(n,e){for(var t=n.length-1;0<=t;t--)if(n[t]===e)return 1}function gn(n,e){return p(n,h)?n.name||e||H:((n||{})[O]||{}).name||e||H}function y(n,r,e,t){p(n,h)||pn("theClass is an invalid class definition.");var i,o,u,a,c,s,l=n[h],f=(function(n){if(!X)return 1;for(var e=[],t=dn(r);t&&!W(t)&&!yn(e,t);){if(t===n)return 1;e.push(t),t=dn(t)}}(l)||pn("["+gn(n)+"] not in hierarchy of ["+gn(r)+"]"),null),n=(p(l,I)?f=l[I]:(f="_dynCls$"+gn(n,"_")+"$"+J.n,J.n++,l[I]=f),y[N]),d=!!n[z],v=(d&&t&&t[z]!==undefined&&(d=!!t[z]),o={},vn(i=r,function(n){!o[n]&&hn(i,n,!1)&&(o[n]=i[n])}),o),d=(e(r,function(n,e,t,o){function r(n,e,t){var r,i=e[t];return i[A]&&o&&!1!==(r=n[K]||{})[B]&&(i=(r[e[I]]||{})[t]||i),function(){return i.apply(n,arguments)}}for(var i={},u=(vn(t,function(n){i[n]=r(e,t,n)}),dn(n)),a=[];u&&!W(u)&&!yn(a,u);)vn(u,function(n){!i[n]&&hn(u,n,!X)&&(i[n]=r(e,u,n))}),a.push(u),u=dn(u);return i}(l,r,v,d)),!!X&&!!n[F]);u=l,e=f,a=r,c=v,n=0!=(d&&t?!!t[F]:d),$(u)||(l=a[K]=a[K]||{},s=l[e]=l[e]||{},!1!==l[B]&&(l[B]=!!n),vn(a,function(n){var t,r,i;hn(a,n,!1)&&a[n]!==c[n]&&(s[n]=a[n],delete a[n],p(u,n)&&(!u[n]||u[n][A])||(u[n]=(t=u,r=n,(i=function(){var n,e;return(function(n,e,t,r){var i=null;if(n&&p(t,I)){var o=n[K]||{};if((i=(o[t[I]]||{})[e])||pn("Missing ["+e+"] "+M),!i[L]&&!1!==o[B]){for(var u=!p(n,e),a=dn(n),c=[];u&&a&&!W(a)&&!yn(c,a);){var s=a[e];if(s){u=s===r;break}c.push(a),a=dn(a)}try{u&&(n[e]=i),i[L]=1}catch(l){o[B]=!1}}}return i}(this,r,t,i)||(typeof(e=(e=t[n=r])===i?dn(t)[n]:e)!==M&&pn("["+n+"] is not a "+M),e)).apply(this,arguments)})[A]=1,i)))}))}y[N]=J.o;var mn="name",bn="identifier",g="push",_n="isInitialized",wn="config",Sn="logger",nn="length",xn="time",m="processNext",Tn="getPlugin",Cn="teardown",En="messageId",Pn="message",Rn="isAsync",kn="_doTeardown",On="update",Mn="getNext",Kn="diagLog",An="setNextPlugin",In="createNew",Ln="userAgent",Bn="split",Nn="nodeType",i="replace",Hn="enableDebugExceptions",v="call",b="type",Dn="handler",qn="isChildEvt",Un="getCtx",s="setCtx",zn="complete",d="",_="core",Fn="disabled",jn="extensionConfig",Xn="processTelemetry",Qn="priority",Jn="errorToConsole",$n="warnToConsole",Wn="getPerfMgr",Vn="startsWith",Gn="indexOf",Yn="trim",r="toString",Zn="constructor",ne=n,ee=t.freeze,te=t.keys,e=String[u],re=e[Yn],ie=e[Vn],o=Array.isArray,oe=T[r],ue=x[r],ae=ue[v](t),ce=/-([a-z])/g,se=/([^\w\d_$])/g,le=/^(\d+[\w\d_$])/,fe=Object.getPrototypeOf;function Y(n){return n===undefined||typeof n===G}function Z(n){return null===n||Y(n)}function de(n){return!(!n||typeof n!==V)}function w(n){return!(!n||typeof n!==l)}function ve(n){var e=n;return n&&ye(n)?(e=(e=n[i](ce,function(n,e){return e.toUpperCase()}))[i](se,"_"))[i](le,function(n,e){return"_"+e}):e}function en(n,e){if(n)for(var t in n)x[v](n,t)&&e[v](n,t,n[t])}function he(n,e){var t=!1;if(n&&e&&!(t=n===e)){if(ie)return n[Vn](e);var r=n,i=e,n=!1,o=i?i[nn]:0;if(r&&o&&r[nn]>=o&&!(n=r===i)){for(var u=0;u<o;u++)if(r[u]!==i[u])return!1;n=!0}return n}return t}function pe(n,e){return!(!n||!e||!~n.indexOf(e))}var tn=o||function(n){return!(!n||"[object Array]"!==oe[v](n))};function ye(n){return"string"==typeof n}function ge(n){return"number"==typeof n}function me(n){return"boolean"==typeof n}function be(n){return n&&"object"==typeof n&&(!(n=(fe||function(n){if(n){if(fe)return fe(n);n=n.__proto__||n[u]||n[Zn];if(n)return n}return null})(n))||typeof(n=n[Zn]&&x[v](n,Zn)?n[Zn]:n)===l&&ue[v](n)===ae)}function rn(n,e,t){var r=n[nn];try{for(var i=0;i<r&&!(i in n&&-1===e[v](t||n,n[i],i,n));i++);}catch(o){}}function _e(n,e,t){if(n){if(n[Gn])return n[Gn](e,t);var r=n[nn],t=t||0;try{for(var i=Math.max(0<=t?t:r-Math.abs(t),0);i<r;i++)if(i in n&&n[i]===e)return i}catch(o){}}return-1}function we(n){return n&&(re&&n[Yn]?n[Yn]():n[i]?n[i](/^\s+|(?=\s)\s+$/g,d):n)}var Se=!{toString:null}.propertyIsEnumerable("toString"),xe=["toString","toLocaleString","valueOf","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","constructor"];function Te(n){var e=typeof n;if(e===l||e===V&&null!==n||R("objKeys called on non-object"),!Se&&te)return te(n);var t,r=[];for(t in n)n&&x[v](n,t)&&r[g](t);if(Se)for(var i=xe[nn],o=0;o<i;o++)n&&x[v](n,xe[o])&&r[g](xe[o]);return r}function Ce(n,e,t,r){if(ne)try{var i={enumerable:!0,configurable:!0};return t&&(i.get=t),r&&(i.set=r),ne(n,e,i),!0}catch(o){}return!1}var Ee=ee||function(n){return n};function Pe(){var n=Date;return n.now?n.now():(new n).getTime()}function Re(n,e){return Z(n)?e:n}function ke(n){return n&&C?t(C({},n)):n}Oe={},en({Unknown:0,NonRetryableStatus:1,InvalidEvent:2,SizeLimitExceeded:3,KillSwitch:4,QueueFull:5},function(n,e){Oe[n]=e,Oe[e]=n}),e=Oe,ee&&en(e,function(n,e){(tn(e)||de(e))&&ee(e)});var Oe,Me=Ee(e),Ke="window",Ae="document",Ie="navigator",Le="console",Be="performance",Ne="JSON",He="ReactNative",De="XMLHttpRequest",qe=null,Ue=null,ze=null,Fe=null;function je(n,e){var t,r=!1;if(n){try{(r=e in n)||(t=n[u])&&(r=e in t)}catch(i){}if(!r)try{r=!Y((new n)[e])}catch(i){}}return r}function on(n){var e=P();return e&&e[n]?e[n]:n===Ke&&Xe()?window:null}function Xe(){return typeof window===V&&window}function Qe(){return Xe()?window:on(Ke)}function Je(){return typeof document===V&&document?document:on(Ae)}function $e(){return!(typeof navigator!==V||!navigator)}function We(){return $e()?navigator:on(Ie)}function Ve(){return typeof JSON===V&&JSON||null!==on(Ne)}function Ge(){return Ve()?JSON||on(Ne):null}function Ye(){var n=We();return n&&n.product&&n.product===He}function un(n){var e=Object[u].toString[v](n),t=d;return"[object Error]"===e?t="{ stack: '"+n.stack+"', message: '"+n.message+"', name: '"+n[mn]+"'":Ve()&&(t=Ge().stringify(n)),e+t}function Ze(){return Fe=null===Fe?$e()&&!!We().sendBeacon:Fe}function nt(n){var e=!1;try{var e=!!on("fetch"),t=on("Request");e&&n&&t&&(e=je(t,"keepalive"))}catch(r){}return e}function et(){var n=!1;try{n=!!on(De)}catch(e){}return n}var tt=null;function rt(n){return n?'"'+n[i](/\"/g,d)+'"':d}function it(n,e){var t,r=typeof console!==G?console:on(Le);r&&(t="log",w(r[t=r[n]?n:t]))&&r[t](e)}ut.dataType="MessageData";var ot=ut;function ut(n,e,t,r){void 0===t&&(t=!1),this[En]=n,this[Pn]=(t?"AI: ":"AI (Internal): ")+n;t=d,Ve()&&(t=Ge().stringify(r)),n=(e?" message:"+rt(e):d)+(r?" props:"+rt(t):d);this[Pn]+=n}ct.__ieDyn=1;var at=ct;function ct(i){this.identifier="DiagnosticLogger",this.queue=[];var c,s,l,f,d=0,v={};y(ct,this,function(o){var n;function u(n,e){var t,r;l<=d||(r=!0,t="AITR_"+e[En],v[t]?r=!1:v[t]=!0,r&&(n<=s&&(o.queue[g](e),d++,a(1===n?"error":"warn",e)),d===l)&&(r=new ot(23,t="Internal events throttle limit per PageView reached for this app.",!1),o.queue[g](r),1===n?o[Jn](t):o[$n](t)))}function a(n,e){var t,r=(t=(t=tt)||!0===(i||{}).disableDbgExt?t:tt||(r=on("Microsoft"),tt=r?r.ApplicationInsights:tt))?t.ChromeDbgExt:null;r&&r[Kn]&&r[Kn](n,e)}c=Re((n=i||{}).loggingLevelConsole,0),s=Re(n.loggingLevelTelemetry,1),l=Re(n.maxMessageLimit,25),f=Re(n.enableDebug,Re(n[Hn],!1)),o.consoleLoggingLevel=function(){return c},o.telemetryLoggingLevel=function(){return s},o.maxInternalMessageLimit=function(){return l},o[Hn]=function(){return f},o.throwInternal=function(n,e,t,r,i){e=new ot(e,t,i=void 0!==i&&i,r);if(f)throw un(e);t=1===n?Jn:$n;Y(e[Pn])?a("throw"+(1===n?"Critical":"Warning"),e):(i?(r=+e[En],!v[r]&&n<=c&&(o[t](e[Pn]),v[r]=!0)):n<=c&&o[t](e[Pn]),u(n,e))},o[$n]=function(n){it("warn",n),a("warning",n)},o[Jn]=function(n){it("error",n),a("error",n)},o.resetInternalMessageCount=function(){d=0,v={}},o.logInternalMessage=u})}function st(n){return n||new at}function an(n,e,t,r,i,o){void 0===o&&(o=!1),st(n).throwInternal(e,t,r,i,o)}function lt(n,e){st(n)[$n](e)}var ft="ParentContextKey",dt="ChildrenContextKey",vt=(a.ParentContextKey="parent",a.ChildrenContextKey="childEvts",a);function a(n,i,e){var t,o=this,u=!1;o.start=Pe(),o[mn]=n,o[Rn]=e,o[qn]=function(){return!1},w(i)&&(u=Ce(o,"payload",function(){return!t&&w(i)&&(t=i(),i=null),t})),o[Un]=function(n){return n?(n===a[ft]||n===a[dt]?o:o.ctx||{})[n]:null},o[s]=function(n,e){n&&(n===a[ft]?(o[n]||(o[qn]=function(){return!0}),o[n]=e):n===a[dt]?o[n]=e:(o.ctx=o.ctx||{})[n]=e)},o[zn]=function(){var n=0,e=o[Un](a[dt]);if(tn(e))for(var t=0;t<e[nn];t++){var r=e[t];r&&(n+=r[xn])}o[xn]=Pe()-o.start,o.exTime=o[xn]-n,o[zn]=function(){},!u&&w(i)&&(o.payload=i())}}var ht="CoreUtils.doPerf";function cn(n,e,t,r,i){if(n)if(n=n[Wn]?n[Wn]():n){var o,u=void 0,a=n[Un](ht);try{if(u=n.create(e(),r,i))return a&&u[s]&&(u[s](vt[ft],a),a[Un])&&a[s]&&((o=a[Un](vt[dt]))||(o=[],a[s](vt[dt],o)),o[g](u)),n[s](ht,u),t(u)}catch(c){u&&u[s]&&u[s]("exception",c)}finally{u&&n.fire(u),n[s](ht,a)}}return t()}var pt=4294967296,yt=4294967295,gt=!1,mt=123456789,bt=987654321;function _t(n){var e,t=0,r=on("crypto")||on("msCrypto");if(0===(t=r&&r.getRandomValues?r.getRandomValues(new Uint32Array(1))[0]&yt:t)&&(!(r=We())||r[Ln]===Ue&&null!==qe||(r=((Ue=r[Ln])||d).toLowerCase(),qe=pe(r,"msie")||pe(r,"trident/")),qe)){if(!gt)try{var i=2147483647&Pe();(e=(Math.random()*pt^i)+i)<0&&(e>>>=0),mt=123456789+e&yt,bt=987654321-e&yt,gt=!0}catch(o){}r=((bt=36969*(65535&bt)+(bt>>16)&yt)<<16)+(65535&(mt=18e3*(65535&mt)+(mt>>16)&yt))>>>0&yt|0,t=(r>>>=0)&yt}return 0===t&&(t=Math.floor(pt*Math.random()|0)),n||(t>>>=0),t}var wt=n,St="."+function(n){void 0===n&&(n=22);for(var e=_t()>>>0,t=0,r=d;r[nn]<n;)r+="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/"[0|63&e]||"",e>>>=6,5==++t&&(e=(_t()<<2&4294967295|3&e)>>>0,t=0);return r}(6),xt=0;function Tt(n){return 1===n[Nn]||9===n[Nn]||!+n[Nn]}function Ct(n,e){return ve(n+xt+++((e=void 0!==e&&e)?".2.8.15":d)+St)}function Et(n){var o={id:Ct("_aiData-"+(n||d)+".2.8.15"),accept:Tt,get:function(n,e,t,r){var i=n[o.id];return i?i[ve(e)]:(r&&((i=function(n,e){var t=e[n.id];if(!t){t={};try{Tt(e)&&!function(n,e,t){if(wt)try{return wt(n,e,{value:t,enumerable:!1,configurable:!0}),1}catch(r){}}(e,n.id,t)&&(e[n.id]=t)}catch(r){}}return t}(o,n))[ve(e)]=t),t)},kill:function(n,e){if(n&&n[e])try{delete n[e]}catch(t){}}};return o}var Pt="attachEvent",Rt="addEventListener",kt="detachEvent",Ot="removeEventListener",Mt="events",Kt="visibilitychange",At="pagehide",It="pageshow",Lt="beforeunload",Bt=Ct("aiEvtPageHide"),Nt=Ct("aiEvtPageShow"),Ht=/\.[\.]+/g,Dt=/[\.]+$/,qt=1,Ut=Et("events"),zt=/^([^.]*)(?:\.(.+)|)/;function Ft(n){return n&&n[i]?n[i](/^[\s\.]+|(?=[\s\.])[\.\s]+$/g,d):n}function jt(n,e){e&&(t=d,tn(e)?(t=d,rn(e,function(n){(n=Ft(n))&&("."!==n[0]&&(n="."+n),t+=n)})):t=Ft(e),t)&&("."!==t[0]&&(t="."+t),n=(n||d)+t);var t,e=zt.exec(n||d)||[],n={};return n[b]=e[1],n.ns=(e[2]||d).replace(Ht,".").replace(Dt,d)[Bn](".").sort().join("."),n}function Xt(n,e,t){n=Ut.get(n,Mt,{},t=void 0===t||t);return n[e]||(n[e]=[])}function Qt(n,e,t,r){n&&e&&e[b]&&(n[Ot]?n[Ot](e[b],t,r):n[kt]&&n[kt]("on"+e[b],t))}function Jt(n,e,t,r){for(var i=e[nn];i--;){var o=e[i];!o||t.ns&&t.ns!==o.evtName.ns||r&&!r(o)||(Qt(n,o.evtName,o[Dn],o.capture),e.splice(i,1))}}function $t(n,e){return e?jt("xx",tn(e)?[n].concat(e):[n,e]).ns[Bn]("."):n}function Wt(n,e,t,r,i){void 0===i&&(i=!1);var o,u,a=!1;if(n)try{var c,s=jt(e,r),l=s,f=t,d=i,v=!1;(u=n)&&l&&l[b]&&f&&(u[Rt]?(u[Rt](l[b],f,d),v=!0):u[Pt]&&(u[Pt]("on"+l[b],f),v=!0)),(a=v)&&Ut.accept(n)&&((o={guid:qt++,evtName:s})[Dn]=t,o.capture=i,c=o,Xt(n,s.type)[g](c))}catch(h){}return a}function Vt(n,e,t,r,i){if(void 0===i&&(i=!1),n)try{var o=jt(e,r),u=!1,a=n,c=function(n){return!((!o.ns||t)&&n[Dn]!==t)&&(u=!0)};(s=o)[b]?Jt(a,Xt(a,s[b]),s,c):(en(l=Ut.get(a,Mt,{}),function(n,e){Jt(a,e,s,c)}),0===Te(l)[nn]&&Ut.kill(a,Mt)),u||Qt(n,o,t,i)}catch(f){}var s,l}function Gt(n,o,u,a){var c=!1;return o&&n&&0<n[nn]&&rn(n,function(n){var e,t,r,i;!n||u&&-1!==_e(u,n)||(n=n,e=o,t=a,r=!1,(i=Qe())&&(r=Wt(i,n,e,t),r=Wt(i.body,n,e,t)||r),i=Je(),c=(r=i&&Wt(i,n,e,t)||r)||c)}),c}function Yt(n,i,o){n&&tn(n)&&rn(n,function(n){var e,t,r;n&&(n=n,e=i,t=o,(r=Qe())&&(Vt(r,n,e,t),Vt(r.body,n,e,t)),r=Je())&&Vt(r,n,e,t)})}var Zt=Et("plugin");function nr(n){return Zt.get(n,"state",{},!0)}var er="TelemetryPluginChain",tr="_hasRun",rr="_getTelCtx",ir=0;function or(n,u,t,e){var r=null,i=[],o=(null!==e&&(r=e?function(n,e,t){for(;n;){if(n[Tn]()===t)return n;n=n[Mn]()}return ar([t],e[wn]||{},e)}(n,t,e):n),{_next:function(){var n,e=r;return r=e?e[Mn]():null,e||(n=i)&&0<n[nn]&&(rn(n,function(n){try{n.func[v](n.self,n.args)}catch(e){an(t[Sn],2,73,"Unexpected Exception during onComplete - "+un(e))}}),i=[]),e},ctx:{core:function(){return t},diagLog:function(){return(t||{})[Sn]||new at(u)},getCfg:function(){return u},getExtCfg:a,getConfig:function(n,e,t){void 0===t&&(t=!1);var r,n=a(n,null);return n&&!Z(n[e])?r=n[e]:u&&!Z(u[e])&&(r=u[e]),Z(r)?t:r},hasNext:function(){return!!r},getNext:function(){return r},setNext:function(n){r=n},iterate:function(n){for(;e=o._next();){var e=e[Tn]();e&&n(e)}},onComplete:function(n,e){for(var t=[],r=2;r<arguments.length;r++)t[r-2]=arguments[r];n&&i[g]({func:n,self:Y(e)?o.ctx:e,args:t})}}});function a(n,e,t){var r,i,o;return void 0===e&&(e={}),void 0===t&&(t=0),(r=u&&(i=u[jn])&&n?i[n]:r)?de(e)&&0!==t&&(o=function d(){var n=arguments,e=n[0]||{},t=n[nn],r=!1,i=1;for(0<t&&me(e)&&(r=e,e=n[i]||{},i++),de(e)||(e={});i<t;i++){var o,u,a,c,s=n[i],l=tn(s),f=de(s);for(o in s)(l&&o in s||f&&x[v](s,o))&&(u=s[o],a=void 0,r&&u&&((a=tn(u))||be(u))&&(c=e[o],a?tn(c)||(c=[]):be(c)||(c={}),u=d(r,c,u)),u!==undefined)&&(e[o]=u)}return e}(!0,e,r),u&&2===t&&en(e,function(n){var e;Z(o[n])&&!Z(e=u[n])&&(o[n]=e)}),r=o):r=e,r}return o}function ur(n,t,r,e){var i=or(n,t,r,e),o=i.ctx;return o[m]=function(n){var e=i._next();return e&&e[Xn](n,o),!e},o[In]=function(n,e){return ur((n=tn(n=void 0===n?null:n)?ar(n,t,r,e):n)||o[Mn](),t,r,e)},o}function ar(n,e,t,r){var i,o=null,u=!r;return tn(n)&&0<n[nn]&&(i=null,rn(n,function(n){(u=!u&&r===n||u)&&n&&w(n[Xn])&&(n=cr(n,e,t),o=o||n,i&&i._setNext(n),i=n)})),r&&!o?ar([r],e,t):o}function cr(c,r,i){var s=null,o=w(c[Xn]),u=w(c[An]),l=c?c[bn]+"-"+c[Qn]+"-"+ir++:"Unknown-0-"+ir++,a={getPlugin:function(){return c},getNext:function(){return s},processTelemetry:function(t,n){var e;f(n=n||(e=(e=c&&w(c[rr])?c[rr]():e)||ur(a,r,i)),function(n){var e;return!(!c||!o||(e=nr(c))[Cn]||e[Fn]||(u&&c[An](s),c[Xn](t,n),0))},"processTelemetry",function(){return{item:t}},!t.sync)||n[m](t)},unload:function(r,i){f(r,function(){var n,e,t=!1;return c&&(n=nr(c),e=c[_]||n[_],!c||e&&e!==r.core()||n[Cn]||(n[_]=null,n[Cn]=!0,n[_n]=!1,c[Cn]&&!0===c[Cn](r,i)&&(t=!0))),t},"unload",function(){},i[Rn])||r[m](i)},update:function(r,i){f(r,function(){var n,e,t=!1;return c&&(n=nr(c),e=c[_]||n[_],!c||e&&e!==r.core()||n[Cn]||c[On]&&!0===c[On](r,i)&&(t=!0)),t},"update",function(){},!1)||r[m](i)},_id:l,_setNext:function(n){s=n}};function f(t,r,i,n,e){var o=!1,u=c?c[bn]:er,a=(a=t[tr])||(t[tr]={});return t.setNext(s),c&&cn(t[_](),function(){return u+":"+i},function(){a[l]=!0;try{var n=s?s._id:d;n&&(a[n]=!1),o=r(t)}catch(e){n=!s||a[s._id];n&&(o=!0),s&&n||an(t[Kn](),1,73,"Plugin ["+u+"] failed during "+i+" - "+un(e)+", run flags: "+un(a))}},n,e),o}return Ee(a)}function sr(){var e=[];return{add:function(n){n&&e[g](n)},run:function(t,r){rn(e,function(n){try{n(t,r)}catch(e){an(t[Kn](),2,73,"Unexpected error calling unload handler - "+un(e))}}),e=[]}}}var S="getPlugin",T=(lr.__ieDyn=1,lr);function lr(){var o,u,l,f,d,a=this;function e(n){var e,n=n=void 0===n?null:n;return n||(e=u||ur(null,{},a[_]),n=l&&l[S]?e[In](null,l[S]):e[In](null,l)),n}function c(n,e,t){n&&(r=i=[],n)&&(r=n[jn])!==i&&Z(r)&&(n[jn]=r=i),!t&&e&&(t=e.getProcessTelContext()[Mn]());var r,i=l;l&&l[S]&&(i=l[S]()),a[_]=e,u=ur(t,n,e,i)}function v(){o=!1,a[_]=null,l=u=null,d=[],f=sr()}v(),y(lr,a,function(s){var n,e,t,r,i;s.initialize=function(n,e,t,r){c(n,e,r),o=!0},s[Cn]=function(n,e){var t,r,i,o,u=s[_];if(u&&(!n||u===n[_]()))return r=!1,i=n||function c(n,t,e){var r=t[wn]||{},i=or(n,r,t,e),o=i.ctx;return o[m]=function(n){var e=i._next();return e&&e.unload(o,n),!e},o[In]=function(n,e){return c((n=tn(n=void 0===n?null:n)?ar(n,r,t,e):n)||o[Mn](),t,e)},o}(null,u,l&&l[S]?l[S]():l),o=e||((n={reason:0})[Rn]=!1,n),s[kn]&&!0===s[kn](i,o,a)?t=!0:a(),t;function a(){var n;r||(r=!0,f.run(i,e),n=d,d=[],rn(n,function(n){n.rm()}),!0===t&&i[m](o),v())}},s[On]=function(n,e){var t,r,i,o=s[_];if(o&&(!n||o===n[_]()))return r=!1,i=n||function a(n,t,e){var r=t[wn]||{},i=or(n,r,t,e).ctx;return i[m]=function(e){return i.iterate(function(n){w(n[On])&&n[On](i,e)})},i[In]=function(n,e){return a((n=tn(n=void 0===n?null:n)?ar(n,r,t,e):n)||i[Mn](),t,e)},i}(null,o,l&&l[S]?l[S]():l),s._doUpdate&&!0===s._doUpdate(i,e||{reason:0},u)?t=!0:u(),t;function u(){r||(r=!0,c(i.getCfg(),i.core(),i[Mn]()))}},s._addHook=function(n){n&&(tn(n)?d=d.concat(n):d[g](n))},t="_addUnloadCb",e=function(){return f},(n=s)&&(n[t]=(i=r=null,w(t=e)?r=t:i=t,function(){var n=arguments;if(i=r?r():i)return i.add.apply(i,n)}))}),a[Kn]=function(n){return e(n)[Kn]()},a[_n]=function(){return o},a.setInitialized=function(n){o=n},a[An]=function(n){l=n},a[m]=function(n,e){e?e[m](n):l&&w(l[Xn])&&l[Xn](n,null)},a._getTelCtx=e}var fr="",dr="withCredentials",vr=((r={})[0]=0,r[2]=6,r[1]=1,r[3]=7,r[4098]=6,r[4097]=1,r[4099]=7,r);function sn(n){return n!==fr&&!Z(n)}function hr(n,e,t){var r=-1;return Y(n)||(0<e&&(32===e?r=8192:e<=13&&(r=e<<5)),0<=t&&t<=9?(-1===r&&(r=0),r|=t):(e=vr[function i(n){var e,t=0;return null!==n&&n!==undefined&&("string"==(e=typeof n)?t=1:"number"==e?t=2:"boolean"==e?t=3:e===V&&(t=4,tn(n)?(t=4096,0<n.length&&(t|=i(n[0]))):x.call(n,"value")&&(t=8192|i(n.value)))),t}(n)]||-1,-1!==r&&-1!==e?r|=e:6===e&&(r=e))),r}function pr(){var r={},e=!1,n=0,t=arguments.length,i=Object[u],o=arguments;for("[object Boolean]"===i.toString.call(o[0])&&(e=o[0],n++);n<t;n++)en(o[n],function(t,n){e&&n&&de(n)?tn(n)?(r[t]=r[t]||[],rn(n,function(n,e){n&&de(n)?r[t][e]=pr(!0,r[t][e],n):r[t][e]=n})):r[t]=pr(!0,r[t],n):r[t]=n});return r}Je(),Qe();var ln=function(){var n=on(Be);return n&&n.now?n.now():Pe()};function yr(n,e){n.timings=n.timings||{},n.timings.processTelemetryStart=n.timings.processTelemetryStart||{},n.timings.processTelemetryStart[e]=ln()}var gr="REAL_TIME",fn="",mr="drop",o="requeue",br="application/x-json-stream",_r="cache-control",wr="content-type",Sr="kill-duration",xr="time-delta-millis",Tr="client-version",Cr="client-id",Er="time-delta-to-apply-millis",Pr="upload-time",Rr="apikey",kr="AuthMsaDeviceTicket",e="AuthXToken",Or="msfpc",Mr="user";function Kr(n){n=(n.ext||{}).intweb;return n&&sn(n[Or])?n[Or]:null}function Ar(n){for(var e=null,t=0;null===e&&t<n.length;t++)e=Kr(n[t]);return e}Lr.create=function(n,e){return new Lr(n,e)};var Ir=Lr;function Lr(r,n){var i=n?[].concat(n):[],n=this,o=Ar(i);n.iKey=function(){return r},n.Msfpc=function(){return o||fn},n.count=function(){return i.length},n.events=function(){return i},n.addEvent=function(n){return!!n&&(i.push(n),o=o||Kr(n),!0)},n.split=function(n,e){var t;return n<i.length&&(t=i.length-n,Z(e)||(t=e<t?e:t),t=i.splice(n,t),o=Ar(i)),new Lr(r,t)}}function Br(){var e=!0,t=!0,r=!0,i="use-collector-delta",o=!1;y(Br,this,function(n){n.allowRequestSending=function(){return e},n.firstRequestSent=function(){r&&(r=!1,o||(e=!1))},n.shouldAddClockSkewHeaders=function(){return t},n.getClockSkewHeaderValue=function(){return i},n.setClockSkew=function(n){o||(n?(i=n,o=t=!0):t=!1,e=!0)}})}Br.__ieDyn=1;var Nr=Br;function Hr(){var c={};y(Hr,this,function(n){n.setKillSwitchTenants=function(n,e){if(n&&e)try{o=n.split(","),u=[],o&&rn(o,function(n){u.push(we(n))});var t=u;if("this-request-only"===e)return t;for(var r=1e3*parseInt(e,10),i=0;i<t.length;++i)c[t[i]]=Pe()+r}catch(a){}var o,u;return[]},n.isTenantKilled=function(n){var e=c,n=we(n);return e[n]!==undefined&&e[n]>Pe()||(delete e[n],!1)}})}Hr.__ieDyn=1;var Dr=Hr,qr=.8,Ur=1.2,zr=3e3,Fr=6e5;function jr(n){var e=zr*qr;return Math.min(Math.pow(2,n)*(Math.floor(Math.random()*(zr*Ur-e))+e),Fr)}var Xr="metadata",Qr="f",Jr=/\./,$r=(Wr.__ieDyn=1,Wr);function Wr(e,n,b,t){var o="baseData",_=!!t,w=n,S={};y(Wr,this,function(g){function m(n,f,d,v,h,p,y){en(n,function(n,e){if(e||sn(e)){var t,r=d,i=n,o=h,u=f;if(_&&!v&&Jr.test(n)){var a=n.split("."),c=a.length;if(1<c){for(var o=o&&o.slice(),s=0;s<c-1;s++){var l=a[s],u=u[l]=u[l]||{};r+="."+l,o&&o.push(l)}i=a[c-1]}}(n=v&&((t=S[n=r])===undefined&&(7<=n.length&&(t=he(n,"ext.metadata")||he(n,"ext.web")),S[n]=t),t)||!w||!w.handleField(r,i)?function(n,e){if(!n&&!sn(n)||"string"!=typeof i)return null;var t=typeof n;if("string"==t||"number"==t||"boolean"==t||tn(n))n={value:n};else if("object"!=t||x.call(n,"value")){if(Z(n.value)||n.value===fr||!ye(n.value)&&!ge(n.value)&&!me(n.value)&&!tn(n.value))return null}else n={value:e?JSON.stringify(n):n};if(tn(n.value)&&!(0<n.value.length))return null;if(!Z(n.kind)){if(tn(n.value)||!(0===(t=n.kind)||0<t&&t<=13||32===t))return null;n.value=n.value.toString()}return n}(e,b):w.value(r,i,e,b))&&(t=n.value,u[i]=t,p&&p(o,i,n),y)&&"object"==typeof t&&!tn(t)&&((n=o)&&(n=o.slice()).push(i),m(e,t,r+"."+i,v,n,p,y))}})}g.createPayload=function(n,e,t,r,i,o){return{apiKeys:[],payloadBlob:fn,overflow:null,sizeExceed:[],failedEvts:[],batches:[],numEvents:0,retryCnt:n,isTeardown:e,isSync:t,isBeacon:r,sendType:o,sendReason:i}},g.appendPayload=function(h,p,y){var n=h&&p&&!h.overflow;return n&&cn(e,function(){return"Serializer:appendPayload"},function(){for(var n=p.events(),e=h.payloadBlob,t=h.numEvents,r=!1,i=[],o=[],u=h.isBeacon,a=u?65e3:3984588,c=u?65e3:2e6,s=0,l=0;s<n.length;){var f=n[s];if(f){if(y<=t){h.overflow=p.split(s);break}var d=g.getEventBlob(f);if(d&&d.length<=c){var v=d.length;if(a<e.length+v){h.overflow=p.split(s);break}e&&(e+="\n"),e+=d,20<++l&&(e.substr(0,1),l=0),r=!0,t++}else(d?i:o).push(f),n.splice(s,1),s--}s++}i&&0<i.length&&h.sizeExceed.push(Ir.create(p.iKey(),i)),o&&0<o.length&&h.failedEvts.push(Ir.create(p.iKey(),o)),r&&(h.batches.push(p),h.payloadBlob=e,h.numEvents=t,u=p.iKey(),-1===_e(h.apiKeys,u))&&h.apiKeys.push(u)},function(){return{payload:h,theBatch:{iKey:p.iKey(),evts:p.events()},max:y}}),n},g.getEventBlob=function(i){try{return cn(e,function(){return"Serializer.getEventBlob"},function(){var n={},r=(n.name=i.name,n.time=i.time,n.ver=i.ver,n.iKey="o:"+function(n){if(n){var e=n.indexOf("-");if(-1<e)return n.substring(0,e)}return fr}(i.iKey),{}),e=i.ext,e=(e&&(n.ext=r,en(e,function(n,e){m(e,r[n]={},"ext."+n,!0,null,null,!0)})),n.data={}),t=(e.baseType=i.baseType,e[o]={});return m(i.baseData,t,o,!1,[o],function(n,e,t){Vr(r,n,e,t)},!0),m(i.data,e,"data",!1,[],function(n,e,t){Vr(r,n,e,t)},!0),JSON.stringify(n)},function(){return{item:i}})}catch(n){return null}}})}function Vr(n,e,t,r){if(r&&n){var i=hr(r.value,r.kind,r.propertyType);if(-1<i){var o=n[Xr],u=(u=(o=o||(n[Xr]={f:{}}))[Qr])||(o[Qr]={});if(e)for(var a=0;a<e.length;a++)var c=e[a],u=(u[c]||(u[c]={f:{}}),u[c][Qr]||(u[c][Qr]={}));u=u[t]={},tn(r.value)?u.a={t:i}:u.t=i}}}var Gr="sendAttempt",Yr="&NoResponseBody=true",Zr=((n={})[1]=o,n[100]=o,n[200]="sent",n[8004]=mr,n[8003]=mr,n),ni={},ei={};function ti(n,e,t){ni[n]=e,!1!==t&&(ei[e]=n)}function ri(n){try{return n.responseText}catch(e){}return fn}function ii(n,e){var t,r=!1;if(n&&e){var i=Te(n);if(i&&0<i.length)for(var o=e.toLowerCase(),u=0;u<i.length;u++){var a=i[u];if(a&&(t=a,e)&&x[v](e,t)&&a.toLowerCase()==o){r=!0;break}}}return r}function oi(n,e,t,r){e&&t&&0<t.length&&(r&&ni[e]?(n.hdrs[ni[e]]=t,n.useHdrs=!0):n.url+="&"+e+"="+t)}function ui(n,e){return e&&(ge(e)?n=[e].concat(n):tn(e)&&(n=e.concat(n))),n}ti(kr,kr,!1),ti(Tr,Tr),ti(Cr,"Client-Id"),ti(Rr,Rr),ti(Er,Er),ti(Pr,Pr),ti(e,e),ci.__ieDyn=1;var ai=ci;function ci(T,n,C,l,f){this._responseHandlers=[];var E,P,R,k,O,M,K,A,I,L,B="?cors=true&content-type="+br,N=new Dr,H=!1,D=new Nr,q=!1,U=0,z=!0,F=[],j={},X=[],Q=null,J=!1,$=!1,W=!1;y(ci,this,function(y){var d=!0;function a(n,e){for(var t=0,r=null,i=0;null==r&&i<n.length;)1===(t=n[i])?(ze=null===ze&&(ze=typeof XDomainRequest!==G)&&et()?ze&&!je(on(De),"withCredentials"):ze)?r=o:et()&&(r=c):2!==t||!nt(e)||e&&A?q&&3===t&&Ze()&&(r=s):r=u,i++;return r?{_transport:t,_isSync:e,sendPOST:r}:null}function o(n,e,t){var r=new XDomainRequest;r.open("POST",n.urlString),n.timeout&&(r.timeout=n.timeout),r.onload=function(){var n=ri(r);g(e,200,{},n),S(n)},r.onerror=function(){g(e,400,{})},r.ontimeout=function(){g(e,500,{})},r.onprogress=function(){},t?r.send(n.data):f.set(function(){r.send(n.data)},0)}function u(n,i,e){var t,r=n.urlString,o=!1,u=!1;(t={body:n.data,method:"POST"}).Microsoft_ApplicationInsights_BypassAjaxInstrumentation=!0;e&&(t.keepalive=!0,2===n._sendReason)&&(o=!0,L)&&(r+=Yr),d&&(t.credentials="include"),n.headers&&0<Te(n.headers).length&&(t.headers=n.headers),fetch(r,t).then(function(n){var t={},e=fn,r=n.headers;r&&r.forEach(function(n,e){t[e]=n}),n.body&&n.text().then(function(n){e=n}),u||(u=!0,g(i,n.status,t,e),S(e))})["catch"](function(n){u||(u=!0,g(i,0,{}))}),o&&!u&&(u=!0,g(i,200,{})),!u&&0<n.timeout&&f.set(function(){u||(u=!0,g(i,500,{}))},n.timeout)}function c(n,o,e){var t=n.urlString;function u(n,e,t){return!n[t]&&e&&e.getResponseHeader&&(e=e.getResponseHeader(t))&&(n[t]=we(e)),n}function r(n,e){var t,r,i;g(o,n.status,(i={},(n=n).getAllResponseHeaders?(t=n.getAllResponseHeaders(),r={},ye(t)&&rn(we(t).split(/[\r\n]+/),function(n){var e,t;n&&(-1!=(t=n.indexOf(": "))?(e=we(n.substring(0,t)).toLowerCase(),t=we(n.substring(1+t)),r[e]=t):r[we(n)]=1)}),r):(i=u(i,n,xr),i=u(i,n,Sr),u(i,n,"kill-duration-seconds"))),e)}e&&n.disableXhrSync&&(e=!1),t=t,i=d,e=e,c=n.timeout,a=!0,void 0===e&&(e=!1),s=new XMLHttpRequest,a&&f(s,"Microsoft_ApplicationInsights_BypassAjaxInstrumentation",a),i&&f(s,dr,i),s.open("POST",t,!e),i&&f(s,dr,i),!e&&c&&f(s,"timeout",c);var i,a,c,s,l=s;function f(n,e,t){try{n[e]=t}catch(r){}}en(n.headers,function(n,e){l.setRequestHeader(n,e)}),l.onload=function(){var n=ri(l);r(l,n),S(n)},l.onerror=function(){r(l)},l.ontimeout=function(){r(l)},l.send(n.data)}function g(n,e,t,r){try{n(e,t,r)}catch(i){an(P,2,518,un(i))}}function s(n,e,t){var r=200,i=n._thePayload,o=n.urlString+(L?Yr:fn);try{var u,a=We();a.sendBeacon(o,n.data)||(i?(u=[],rn(i.batches,function(n){if(u&&n&&0<n.count()){for(var e=n.events(),t=0;t<e.length;t++)if(!a.sendBeacon(o,Q.getEventBlob(e[t]))){u.push(n.split(t));break}}else u.push(n.split(0))}),x(u,8003,i.sendType,!0)):r=0)}catch(c){lt(P,"Failed to send telemetry using sendBeacon API. Ex:"+un(c)),r=0}finally{g(e,r,{},fn)}}function m(n){return 2===n||3===n}function r(n){return $&&m(n)?2:n}function i(){return!H&&U<n}function t(){var n=X;return X=[],n}function v(n,e,t){return n&&0<n.length&&!H&&R[e]&&Q&&(0!==e||i()&&(0<t||D.allowRequestSending()))}function h(n){var t={};return n&&rn(n,function(n,e){t[e]={iKey:n.iKey(),evts:n.events()}}),t}function b(a,c,s,l,f){if(a&&0!==a.length)if(H)x(a,1,l);else{l=r(l);try{var n=a,d=0!==l;cn(k,function(){return"HttpManager:_sendBatches"},function(n){n&&(a=a.slice(0));for(var e=[],t=null,r=ln(),n=R[l]||(d?R[1]:R[0]),i=n&&n._transport,o=I&&($||m(l)||3===i||n._isSync&&2===i);v(a,l,c);){var u=a.shift();u&&0<u.count()&&(N.isTenantKilled(u.iKey())?e.push(u):(t=t||Q.createPayload(c,s,d,o,f,l),Q.appendPayload(t,u,T)?null!==t.overflow&&(a=[t.overflow].concat(a),t.overflow=null,p(t,r,ln(),f),r=ln(),t=null):(p(t,r,ln(),f),r=ln(),a=[u].concat(a),t=null)))}t&&p(t,r,ln(),f),0<a.length&&(X=a.concat(X)),x(e,8004,l)},function(){return{batches:h(n),retryCount:c,isTeardown:s,isSynchronous:d,sendReason:f,useSendBeacon:m(l),sendType:l}},!d)}catch(e){an(P,2,48,"Unexpected Exception sending batch: "+un(e))}}}function _(n,e,t){n[e]=n[e]||{},n[e][E.identifier]=t}function p(v,a,c,h){var s,l,f,d,p;v&&v.payloadBlob&&0<v.payloadBlob.length&&(s=!!y.sendHook,l=R[v.sendType],!m(v.sendType)&&v.isBeacon&&2===v.sendReason&&(l=R[2]||R[3]||l),d=W,!v.isBeacon&&3!==l._transport||(d=!1),f=function(n,e){var t={url:B,hdrs:{},useHdrs:!1},r=(e?(t.hdrs=pr(t.hdrs,j),t.useHdrs=0<Te(t.hdrs).length):en(j,function(n,e){ei[n]?oi(t,ei[n],e,!1):(t.hdrs[n]=e,t.useHdrs=!0)}),oi(t,Cr,"NO_AUTH",e),oi(t,Tr,"1DS-Web-JS-3.2.13",e),fn),n=(rn(n.apiKeys,function(n){0<r.length&&(r+=","),r+=n}),oi(t,Rr,r,e),oi(t,Pr,Pe().toString(),e),function(n){for(var e=0;e<n.batches.length;e++){var t=n.batches[e].Msfpc();if(t)return encodeURIComponent(t)}return fn}(n));sn(n)&&(t.url+="&ext.intweb.msfpc="+n),D.shouldAddClockSkewHeaders()&&oi(t,Er,D.getClockSkewHeaderValue(),e),k.getWParam&&0<=(n=k.getWParam())&&(t.url+="&w="+n);for(var i=0;i<F.length;i++)t.url+="&"+F[i].name+"="+F[i].value;return t}(v,d),d=d||f.useHdrs,p=ln(),cn(k,function(){return"HttpManager:_doPayloadSend"},function(){for(var n=0;n<v.batches.length;n++)for(var e=v.batches[n].events(),t=0;t<e.length;t++){var r,i=e[t];J&&(_(r=i.timings=i.timings||{},"sendEventStart",p),_(r,"serializationStart",a),_(r,"serializationCompleted",c)),0<i[Gr]?i[Gr]++:i[Gr]=1}x(v.batches,1e3+(h||0),v.sendType,!0);var o={data:v.payloadBlob,urlString:f.url,headers:f.hdrs,_thePayload:v,_sendReason:h,timeout:M,disableXhrSync:K,disableFetchKeepAlive:A},u=(d&&(ii(o.headers,_r)||(o.headers[_r]="no-cache, no-store"),ii(o.headers,wr)||(o.headers[wr]=br)),null);l&&(u=function(n){D.firstRequestSent();var e=function(n,e){var t,r,i=v,o=h,u=9e3,a=null,c=!1,s=!1;try{var l,f=!0;if(typeof n!==G){if(e&&(D.setClockSkew(e[xr]),d=e[Sr]||e["kill-duration-seconds"],rn(N.setKillSwitchTenants(e["kill-tokens"],d),function(e){rn(i.batches,function(n){n.iKey()===e&&(a=a||[],n=n.split(0),i.numEvents-=n.count(),a.push(n))})})),200==n||204==n)return void(u=200);(300<=n&&n<500&&408!=n&&429!=n||501==n||505==n||i.numEvents<=0)&&(f=!1),u=9e3+n%1e3}f&&(u=100,l=i.retryCnt,0===i.sendType)&&(l<C?(c=!0,w(function(){0===i.sendType&&U--,b(i.batches,l+1,i.isTeardown,$?2:i.sendType,5)},$,jr(l))):(s=!0,$&&(u=8001)))}finally{if(!c){D.setClockSkew();var e=i,d=u,n=o,f=s;try{f&&E._backOffTransmission(),200===d&&(f||e.isSync||E._clearBackOff(),t=e.batches,J)&&(r=ln(),rn(t,function(n){var e;n&&0<n.count()&&(n=n.events(),e=r,J)&&rn(n,function(n){_(n.timings=n.timings||{},"sendEventCompleted",e)})})),x(e.batches,d,e.sendType,!0)}finally{0===e.sendType&&(U--,5!==n)&&y.sendQueuedRequests(e.sendType,n)}}x(a,8004,i.sendType)}},t=v.isTeardown||v.isSync;try{l.sendPOST(n,e,t),y.sendListener&&y.sendListener(o,n,t,v.isBeacon)}catch(r){lt(P,"Unexpected exception sending payload. Ex:"+un(r)),g(e,0,{})}}),cn(k,function(){return"HttpManager:_doPayloadSend.sender"},function(){var e,t;u&&(0===v.sendType&&U++,s&&!v.isBeacon&&3!==l._transport?(e={data:o.data,urlString:o.urlString,headers:pr({},o.headers),timeout:o.timeout,disableXhrSync:o.disableXhrSync,disableFetchKeepAlive:o.disableFetchKeepAlive},t=!1,cn(k,function(){return"HttpManager:_doPayloadSend.sendHook"},function(){try{y.sendHook(e,function(n){t=!0,z||n._thePayload||(n._thePayload=n._thePayload||o._thePayload,n._sendReason=n._sendReason||o._sendReason),u(n)},v.isSync||v.isTeardown)}catch(n){t||u(o)}})):u(o))})},function(){return{thePayload:v,serializationStart:a,serializationCompleted:c,sendReason:h}},v.isSync)),v.sizeExceed&&0<v.sizeExceed.length&&x(v.sizeExceed,8003,v.sendType),v.failedEvts&&0<v.failedEvts.length&&x(v.failedEvts,8002,v.sendType)}function w(n,e,t){e?n():f.set(n,t)}function S(n){var e=y._responseHandlers;try{for(var t,r=0;r<e.length;r++)try{e[r](n)}catch(i){an(P,1,519,"Response handler failed: "+i)}n&&sn((t=JSON.parse(n)).webResult)&&sn(t.webResult[Or])&&O.set("MSFPC",t.webResult[Or],31536e3)}catch(o){}}function x(e,t,r,n){var i,o,u;e&&0<e.length&&l&&(i=l[sn(u=Zr[t])||(u="oth",9e3<=t&&t<=9999?u="rspFail":8e3<=t&&t<=8999?u=mr:1e3<=t&&t<=1999&&(u="send")),u])&&cn(k,function(){return"HttpManager:_sendBatchesNotification"},function(){w(function(){try{i.call(l,e,t,o,r)}catch(n){an(P,1,74,"send request notification failed: "+n)}},n||o,0)},function(){return{batches:h(e),reason:t,isSync:o,sendSync:n,sendType:r}},!(o=0!==r))}y.initialize=function(n,e,t,r,i){B=n+B,W=!!Y((i=i||{}).avoidOptions)||!i.avoidOptions,O=(k=e).getCookieMgr(),J=!k.config.disableEventTimings;var o,n=!!k.config.enableCompoundKey,e=(P=(E=t).diagLog(),i.valueSanitizer),t=i.stringifyObjects,e=(Y(i.enableCompoundKey)||(n=!!i.enableCompoundKey),M=i.xhrTimeout,K=!!i.disableXhrSync,A=!!i.disableFetchKeepAlive,L=!1!==i.addNoResponse,q=!Ye(),Q=new $r(k,e,t,n),Z(i.useSendBeacon)||(q=!!i.useSendBeacon),r),t=i.alwaysUseXhrOverride?r:null,n=i.alwaysUseXhrOverride?r:null,u=[3,2];r||(z=!1,(o=typeof location===V&&location?location:on("location"))&&o.protocol&&"file:"==o.protocol.toLowerCase()&&(d=!1),o=[],Ye()?(o=[2,1],u=[2,1,3]):o=[1,2,3],(r=a(o=ui(o,i.transports),!1))||lt(P,"No available transport to send events"),e=a(o,!0)),t=t||a(u=ui(u,i.unloadTransports),!0),I=!z&&(q&&Ze()||!A&&nt(!0)),(o={})[0]=r,o[1]=e||a([1,2,3],!0),o[2]=t||e||a([1],!0),o[3]=n||a([2,3],!0)||e||a([1],!0),R=o},y._getDbgPlgTargets=function(){return[R[0],N,Q,R]},y.addQueryStringParameter=function(n,e){for(var t=0;t<F.length;t++)if(F[t].name===n)return void(F[t].value=e);F.push({name:n,value:e})},y.addHeader=function(n,e){j[n]=e},y.canSendRequest=function(){return i()&&D.allowRequestSending()},y.sendQueuedRequests=function(n,e){Y(n)&&(n=0),$&&(n=r(n),e=2),v(X,n,0)&&b(t(),0,!1,n,e||0)},y.isCompletelyIdle=function(){return!H&&0===U&&0===X.length},y.setUnloading=function(n){$=n},y.addBatch=function(n){if(n&&0<n.count()){if(N.isTenantKilled(n.iKey()))return!1;X.push(n)}return!0},y.teardown=function(){0<X.length&&b(t(),0,!0,2,2)},y.pause=function(){H=!0},y.resume=function(){H=!1,y.sendQueuedRequests(0,4)},y.sendSynchronousBatch=function(n,e,t){n&&0<n.count()&&(Z(e)&&(e=1),$&&(e=r(e),t=2),b([n],0,!1,e,t||0))}})}function si(n,e){for(var t=[],r=2;r<arguments.length;r++)t[r-2]=arguments[r];return setTimeout(n,e,t)}function li(n){clearTimeout(n)}function fi(n,e){return{set:n||si,clear:e||li}}var di,vi,hi="eventsDiscarded",pi="overrideInstrumentationKey",yi="maxEventRetryAttempts",gi="maxUnloadEventRetryAttempts";function mi(){var E,P,R,k,O,M,K,A,n=di.call(this)||this,I=(n.identifier="PostChannel",n.priority=1011,!(n.version="3.2.13")),L=[],B=null,N=!1,H=0,D=500,q=0,U=1e4,z={},F=gr,j=null,X=null,Q=0,J=0,$={},W=-1,V=!0,G=!1,Y=6,Z=2;return y(mi,n,function(p,y){function g(n){"beforeunload"!==(n||Qe().event).type&&(G=!0,R.setUnloading(G)),s(2,2)}function m(n){G=!1,R.setUnloading(G)}function i(n,e){if(n.sendAttempt||(n.sendAttempt=0),n.latency||(n.latency=1),n.ext&&n.ext.trace&&delete n.ext.trace,n.ext&&n.ext[Mr]&&n.ext[Mr].id&&delete n.ext[Mr].id,V&&(n.ext=ke(n.ext),n.baseData&&(n.baseData=ke(n.baseData)),n.data)&&(n.data=ke(n.data)),n.sync)if(Q||N)n.latency=3,n.sync=!1;else if(R)return V&&(n=ke(n)),R.sendSynchronousBatch(Ir.create(n.iKey,[n]),!0===n.sync?1:n.sync,3);var t=n.latency,r=q,i=U,o=(4===t&&(r=H,i=D),!1);(o=r<i||(r=1,i=20,4===t&&(r=4,i=1),o=!0,function(n,e,t,r){for(;t<=e;){var i=l(n,e,!0);if(i&&0<i.count()){var i=i.split(0,r),o=i.count();if(0<o)return 4===t?H-=o:q-=o,_(hi,[i],Me.QueueFull),1}t++}return d(),0}(n.iKey,n.latency,r,i))?(t=n,r=e,i=(t=V?ke(n):t).latency,!((e=l(t.iKey,i,!0)).addEvent(t)&&(4!==i?(q++,r&&0===t.sendAttempt&&f(!t.sync,0<O&&e.count()>=O)):H++,1))):o)&&b(hi,[n],Me.QueueFull)}function o(n,e,t){v(n,e,t),R.sendQueuedRequests(e,t)}function u(){0<=W&&v(W,0,M)&&R.sendQueuedRequests(0,M),0<H&&!X&&!N&&0<=(n=z[F][2])&&(X=a(function(){X=null,o(4,0,1),u()},n));var n=z[F][1];!j&&!B&&0<=n&&!N&&(0<q?j=a(function(){j=null,o(0===J?3:1,0,1),J++,J%=2,u()},n):J=0)}function r(){I=!1,N=!(L=[]),D=500,U=1e4,z={},F=gr,J=Q=q=H=0,P=X=j=B=E=null,$={},k=undefined,O=0,W=-1,G=!(V=!(M=null)),Y=6,K=null,A=fi(),R=new ai(500,Z=2,1,{requeue:n,send:t,sent:w,drop:S,rspFail:x,oth:T},A),e(),$[4]={batches:[],iKeyMap:{}},$[3]={batches:[],iKeyMap:{}},$[2]={batches:[],iKeyMap:{}},$[1]={batches:[],iKeyMap:{}},C()}function a(n,e){0===e&&Q&&(e=1);var t=1e3;return Q&&(t=jr(Q-1)),A.set(n,e*t)}function c(){return null!==j&&(A.clear(j),j=null,!(J=0))}function s(n,e){c(),B&&(A.clear(B),B=null),N||o(1,n,e)}function l(n,e,t){var r=$[e],e=(r=r||$[e=1]).iKeyMap[n];return!e&&t&&(e=Ir.create(n),r.batches.push(e),r.iKeyMap[n]=e),e}function f(n,e){R.canSendRequest()&&!Q&&(e=0<k&&k<q||e)&&null==B&&p.flush(n,null,20)}function d(){for(var t=0,r=0,n=1;n<=4;n++)!function(e){var n=$[e];n&&n.batches&&rn(n.batches,function(n){4===e?t+=n.count():r+=n.count()})}(n);q=r,H=t}function v(r,n,e){var i=!1,t=0===n;return!t||R.canSendRequest()?cn(p.core,function(){return"PostChannel._queueBatches"},function(){for(var e=[],t=4;r<=t;){var n=$[t];n&&n.batches&&0<n.batches.length&&(rn(n.batches,function(n){R.addBatch(n)?i=i||n&&0<n.count():e=e.concat(n.events()),4===t?H-=n.count():q-=n.count()}),n.batches=[],n.iKeyMap={}),t--}0<e.length&&b(hi,e,Me.KillSwitch),i&&r<=W&&(W=-1,M=0)},function(){return{latency:r,sendType:n,sendReason:e}},!t):(W=0<=W?Math.min(W,r):r,M=Math.max(M,e)),i}function e(){z={REAL_TIME:[2,1,0],NEAR_REAL_TIME:[6,3,0],BEST_EFFORT:[18,9,0]}}function n(n,e){var t=[],r=G?Z:Y;rn(n,function(n){n&&0<n.count()&&rn(n.events(),function(n){n&&(n.sync&&(n.latency=4,n.sync=!1),n.sendAttempt<r?(yr(n,p.identifier),i(n,!1)):t.push(n))})}),0<t.length&&b(hi,t,Me.NonRetryableStatus),G&&s(2,2)}function h(n,e){var t=p._notificationManager||{},r=t[n];if(r)try{r.apply(t,e)}catch(i){an(p.diagLog(),1,74,n+" notification failed: "+i)}}function b(n,e){for(var t=[],r=2;r<arguments.length;r++)t[r-2]=arguments[r];e&&0<e.length&&h(n,[e].concat(t))}function _(e,n){for(var t=[],r=2;r<arguments.length;r++)t[r-2]=arguments[r];n&&0<n.length&&rn(n,function(n){n&&0<n.count()&&h(e,[n.events()].concat(t))})}function t(n,e,t){n&&0<n.length&&h("eventsSendRequest",[1e3<=e&&e<=1999?e-1e3:0,!0!==t])}function w(n,e){_("eventsSent",n,e),u()}function S(n,e){_(hi,n,8e3<=e&&e<=8999?e-8e3:Me.Unknown)}function x(n){_(hi,n,Me.NonRetryableStatus),u()}function T(n,e){_(hi,n,Me.Unknown),u()}function C(){O=E&&E.disableAutoBatchFlushLimit?0:Math.max(1500,U/6)}r(),p._getDbgPlgTargets=function(){return[R]},p.initialize=function(d,v,h){cn(v,function(){return"PostChannel:initialize"},function(){var e,n=v;y.initialize(d,v,h);try{v.addUnloadCb,K=$t(Ct(p.identifier),v.evtNamespace&&v.evtNamespace());var t,r=p._getTelCtx(),i=(d.extensionConfig[p.identifier]=d.extensionConfig[p.identifier]||{},E=r.getExtCfg(p.identifier),A=fi(E.setTimeoutOverride,E.clearTimeoutOverride),V=!E.disableOptimizeObj&&!!on("chrome"),e=n.getWParam,n.getWParam=function(){var n=0;return E.ignoreMc1Ms0CookieProcessing&&(n|=2),n|e()},0<E.eventsLimitInMem&&(U=E.eventsLimitInMem),0<E.immediateEventLimit&&(D=E.immediateEventLimit),0<E.autoFlushEventsLimit&&(k=E.autoFlushEventsLimit),ge(E[yi])&&(Y=E[yi]),ge(E[gi])&&(Z=E[gi]),C(),E.httpXHROverride&&E.httpXHROverride.sendPOST&&(P=E.httpXHROverride),sn(d.anonCookieName)&&R.addQueryStringParameter("anoncknm",d.anonCookieName),R.sendHook=E.payloadPreprocessor,R.sendListener=E.payloadListener,E.overrideEndpointUrl||d.endpointUrl),o=(p._notificationManager=v.getNotifyMgr(),R.initialize(i,p.core,p,P,E),d.disablePageUnloadEvents||[]),u=o,a=K,c=g;tn(t=[Lt,"unload",At])&&!Gt(t,c,u,a)&&u&&0<u[nn]&&Gt(t,c,null,a),function s(t,n,e){var r=$t(Bt,e),i=Gt([At],t,n,r);return!(i=(!n||-1===_e(n,Kt))&&Gt([Kt],function(n){var e=Je();t&&e&&"hidden"===e.visibilityState&&t(n)},n,r)||i)&&n?s(t,null,e):i}(g,o,K),function l(t,n,e){var r=$t(Nt,e),i=Gt([It],t,n,r);return!(i=Gt([Kt],function(n){var e=Je();t&&e&&"visible"===e.visibilityState&&t(n)},n,r)||i)&&n?l(t,null,e):i}(m,d.disablePageShowEvents,K)}catch(f){throw p.setInitialized(!1),f}},function(){return{coreConfig:d,core:v,extensions:h}})},p.processTelemetry=function(n,e){yr(n,p.identifier);var t=(e=p._getTelCtx(e)).getExtCfg(p.identifier),r=!!E.disableTelemetry;(t?r||t.disableTelemetry:r)||I||(E[pi]&&(n.iKey=E[pi]),t&&t[pi]&&(n.iKey=t[pi]),i(n,!0),G?s(2,2):u()),p.processNext(n,e)},p._doTeardown=function(n,e){var t;s(2,2),I=!0,R.teardown(),Yt([Lt,"unload",At],null,K),t=$t(Bt,K),Yt([At],null,t),Yt([Kt],null,t),t=$t(Nt,K),Yt([It],null,t),Yt([Kt],null,t),r()},p.setEventQueueLimits=function(n,e){U=0<n?n:1e4,k=0<e?e:0,C();var t=n<q;if(!t&&0<O)for(var r=1;!t&&r<=3;r++){var i=$[r];i&&i.batches&&rn(i.batches,function(n){n&&n.count()>=O&&(t=!0)})}f(!0,t)},p.pause=function(){c(),N=!0,R.pause()},p.resume=function(){N=!1,R.resume(),u()},p.addResponseHandler=function(n){R._responseHandlers.push(n)},p._loadTransmitProfiles=function(n){c(),e(),F=gr,u(),en(n,function(n,e){var t,r=e.length;2<=r&&(t=2<r?e[2]:0,e.splice(0,r-2),e[1]<0&&(e[0]=-1),0<e[1]&&0<e[0]&&(r=e[0]/e[1],e[0]=Math.ceil(r)*e[1]),0<=t&&0<=e[1]&&t>e[1]&&(t=e[1]),e.push(t),z[n]=e)})},p.flush=function(n,e,t){void 0===n&&(n=!0),N||(t=t||1,n?null==B?(c(),v(1,0,t),B=a(function(){B=null,function r(n,e){o(1,0,e),d(),function t(n){R.isCompletelyIdle()?n():B=a(function(){B=null,t(n)},.25)}(function(){n&&n(),0<L.length?B=a(function(){B=null,r(L.shift(),e)},0):(B=null,u())})}(e,t)},0)):L.push(e):(n=c(),o(1,1,t),null!==e&&e!==undefined&&e(),n&&u()))},p.setMsaAuthTicket=function(n){R.addHeader(kr,n)},p.hasEvents=function(){return 0<q},p._setTransmitProfile=function(n){F!==n&&z[n]!==undefined&&(c(),F=n,u())},p._backOffTransmission=function(){Q<4&&(Q++,c(),u())},p._clearBackOff=function(){Q&&(Q=0,c(),u())},Ce(p,"_setTimeoutOverride",function(){return A.set},function(n){A=fi(n,A.clear)}),Ce(p,"_clearTimeoutOverride",function(){return A.clear},function(n){A=fi(A.set,n)})}),n}function bi(){this.constructor=vi}vi=mi,typeof(r=di=T)!==l&&R("Class extends value "+r+" is not a constructor or null"),k(vi,r),vi[u]=(bi[u]=r[u],new bi),mi.__ieDyn=1;o=mi;c.BE_PROFILE="BEST_EFFORT",c.NRT_PROFILE="NEAR_REAL_TIME",c.PostChannel=o,c.RT_PROFILE=gr},t={},r="__ms$mod__",i={},o=i.esm_ms_post_3_2_13={},u="3.2.13",a="oneDS3",c=(c=this)[a]=c[a]||{},s=(s=this)[a="oneDS"]=s[a]||{},a=c[r]=c[r]||{},l=a.v=a.v||[],a=s[r]=s[r]||{},f=a.v=a.v||[];for(n in(a.o=a.o||[]).push(i),e(t),t)c[n]=t[n],l[n]=u,s[n]=t[n],f[n]=u,(o.n=o.n||[]).push(n);//# sourceMappingURL=https://main.vscode-cdn.net/sourcemaps/360a4e4fd251bfce169a4ddf857c7d25d1ad40da/node_modules/@microsoft/1ds-post-js/bundle/ms.post-3.2.13.gbl.min.js.map
