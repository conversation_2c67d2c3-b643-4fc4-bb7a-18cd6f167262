// Copyright (c) Microsoft Corporation. All rights reserved.
// Licensed under the MIT License.
export { strShimFunction, strShimObject, strShimUndefined, strShimPrototype, strShimHasOwnProperty, strDefault, ObjClass, ObjProto, Obj<PERSON>sign, Obj<PERSON>reate, ObjDefineProperty, ObjHasOwnProperty } from "./Constants";
export { throwTypeError, objCreateFn, getGlobal } from "./Helpers";
export { __assignFn, __extendsFn, __restFn, __spreadArrayFn, __spreadArraysFn, __decorateFn, __paramFn, __metadataFn, __createBindingFn, __valuesFn, __readFn, __makeTemplateObjectFn, __importDefaultFn, __importStarFn, __exportStarFn } from "./TsLibShims";
export { __exposeGlobalTsLib } from "./TsLibGlobals";
//# sourceMappingURL=https://main.vscode-cdn.net/sourcemaps/360a4e4fd251bfce169a4ddf857c7d25d1ad40da/node_modules/@microsoft/applicationinsights-shims/dist-esm/applicationinsights-shims.js.map