!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t():"function"==typeof define&&define.amd?define([],t):"object"==typeof exports?exports.ProgressAddon=t():e.ProgressAddon=t()}(globalThis,(()=>(()=>{"use strict";var e={};return(()=>{var t=e;function s(e){let t=0;for(let s=0;s<e.length;++s){const r=e.charCodeAt(s);if(r<48||57<r)return-1;t=10*t+r-48}return t}Object.defineProperty(t,"__esModule",{value:!0}),t.ProgressAddon=void 0,t.ProgressAddon=class{constructor(){this._st=0,this._pr=0}dispose(){this._seqHandler?.dispose(),this._onChange?.dispose()}activate(e){this._seqHandler=e.parser.registerOscHandler(9,(e=>{if(!e.startsWith("4;"))return!1;const t=e.split(";");if(t.length>3)return!0;2===t.length&&t.push("");const r=s(t[1]),o=s(t[2]);switch(r){case 0:this.progress={state:r,value:0};break;case 1:if(o<0)return!0;this.progress={state:r,value:o};break;case 2:case 4:if(o<0)return!0;this.progress={state:r,value:o||this._pr};break;case 3:this.progress={state:r,value:this._pr}}return!0})),this._onChange=new e._core._onData.constructor,this.onChange=this._onChange.event}get progress(){return{state:this._st,value:this._pr}}set progress(e){e.state<0||e.state>4?console.warn("progress state out of bounds, not applied"):(this._st=e.state,this._pr=Math.min(Math.max(e.value,0),100),this._onChange?.fire({state:this._st,value:this._pr}))}}})(),e})()));//# sourceMappingURL=https://main.vscode-cdn.net/sourcemaps/360a4e4fd251bfce169a4ddf857c7d25d1ad40da/node_modules/@xterm/addon-progress/lib/addon-progress.js.map