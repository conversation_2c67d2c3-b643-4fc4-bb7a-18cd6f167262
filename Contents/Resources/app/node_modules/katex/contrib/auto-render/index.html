<!DOCTYPE html>
<!--To run this example from a clone of the repository, run `yarn start`
    in the root KaTeX directory and then visit with your web browser:
    http://localhost:7936/contrib/auto-render/index.html
  -->
<html>
  <head>
    <meta charset="UTF-8">
    <title>Auto-render test</title>
    <script src="/katex.js" type="text/javascript"></script>
    <script src="/contrib/auto-render.js" type="text/javascript"></script>
    <style type="text/css">
      body {
          margin: 0px;
          padding: 0px;
          font-size: 36px;
      }

      #test > .blue {
          color: blue;
      }
    </style>
  </head>
  <body>
    <div id="test">
      This is some text $math \frac12$ other text $\unsupported$
      <span class="blue">
        Other node \[ \text{displaymath} \frac{1}{2} \] blah $$ \int_2^3 $$
      </span>
      and some <!-- comment --> more text \(and math\) blah. And $math with a
      \$ sign$.
      <pre>
        Stuff in a $pre tag$
      </pre>
      <p>An AMS environment without <code>$$…$$</code> delimiters.</p>
      <p>\begin{equation} \begin{split} a &=b+c\\ &=e+f \end{split} \end{equation}</p>
    </div>
    <script>
      renderMathInElement(
          document.getElementById("test"),
          {
              delimiters: [
                  {left: "$$", right: "$$", display: true},
                  {left: "$", right: "$", display: false},
                  {left: "\\begin{equation}", right: "\\end{equation}", display: true},
                  {left: "\\begin{align}", right: "\\end{align}", display: true},
                  {left: "\\begin{alignat}", right: "\\end{alignat}", display: true},
                  {left: "\\begin{gather}", right: "\\end{gather}", display: true},
                  {left: "\\(", right: "\\)", display: false},
                  {left: "\\[", right: "\\]", display: true}
              ]
          }
      );
    </script>
  </body>
</html>
